### POST QC Rate
POST https://quoters-intg-irca-backend.apps.np.iad.ca.inet/irca/v2/quotes/rates?language=en&province=qc&actionToken=pp&
    apiKey=PkTAzgzKJvHcQQTgh9dSAJRq
Accept: application/json, text/plain, */*
Accept-Language: en-US,en;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Origin: http://localhost:4200
Pragma: no-cache
Referer: http://localhost:4200/
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: cross-site
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
recaptcha-response-token: token
recaptcha-site-key: 0cAFcWeA5LC4fBqvURioCOVJ7ZjFHwO84PkgysPWM22BFu6-r9BKl772QIHm2D-wI3Rb3V38HnUpkYat-ulJlIbm0iqX8eot192JlXuLhxjuuFRy6bAUdyflKY8_8Xs6zEmczWTxVHuB6KL5BtWPwNL52xStc4aAysnHJzz8olMi39Jtqc7i-_shlPq22MaUVEsbNRxA5NmgtSLuTcaViCv1S_ViaMJRMD306NDwGtvXxwJ7H8e5Oa4yg-zv4wJVoVBEFVhrgo6la6iM3moxvOlOwQ5xTtvVpyAbS_wWaWMYls17roOt9TpDDYk9EKSqjkYh_OS84QNiebTKaGd8vS7AQLNy0ndUE2E__Zwo-768YesaPT6dmnTh-JeygFnJj6LBO10AMLnCNQHvzLTh_Lth1Hl8IgyJUP5cvLXi6SXWoth6OVVSeaIxH3XPQsHQrFIaZv19I7YetJQ8C_3LcuT0P3_IrF7UkxqzIE0h6muY9DDeG2sOGEFlGYX11rxSmWf6YO227BXvB2PSGyUvRGfiBPKJAZHqnTuoh4v-XrKTEFixYufydQpFFmYfzJ4txQhVMFWEymLOQm6yMptHxvup2Z-V8twamYCbBxTcDXrP2FwgEDQ_7ycsUGqA48BKiQFW43c82FjzaJvKvLuY9z-0JbKoeqHr5vixK6ZUXDln1ttMX1iF5HQkBSG8DIS1vroe8ojcPdABYuUJr0S28doleMbMQBt_yboJqrZlC2qNwDWVGIdOYIXiMiqYUwyjJltN2SgrTU6oKZHn1DAL5MMnwxkiahO3AXpIHvwFwyyL15Xkuvi_dG9Awwpxs_rzRluwxccuz-5LqX_IKl5jkt9H2eSECZJUoGKjZ5BkwyiA2_KYsbfd0G8lqC-J6WOQeCM8V-WhXvtb8wy9VI7hG1hYS_5nqaMWlJ9LGRpP02JouqX8Ct-gE3sXmMAL0tvnKzxZKetxec1DgDiOKTzPhSU-anETIEVfQ9myxjW95S35oiXQunA1boxvZfEMCg5ZrllLjJ8CtA1ACRXKwUGAfo5CcLlmK3178o312E4xwBGhI9-FN2n8kuN3j6ErWbCKqNR60lUNR5CkuuBDJ72rWCxCbwkzARxuqGysRJBN293XLEw0vmdV4LZFgADaMhLgOrZzOlX4KQ38PME4gJOvPL-71n759XzshpfGbIG8Z1zEWbOxrGW6RuS5w5Umwwo9XQvOLRur4mZL3jIUuGdNjMLD6CxfwPHOAyBZtp5BgXvkzxYaGJfFplsQQ7jnFFzKh9dfFhVKvM9StIYM3xg2Xb4bjQYaSQ9-jxVeTTzl0-iOMpm-KlbIMHCqf16p_HBSi6OyQjQOl_wjlW9NhCRL6WVcFjSTCLuKNyRJEJCYPz9-9ha6Tb1fUdVhL2Uss71BSnr0RR5Ug-GXeSsezjwY4xKp2LXfpxJaUoILlWsAxXUGCAEViuwOcyAw1B-HS8mgrBR-M1D-PjmfmWpDwkZUaHlVQwD8X1_iHQEmapsjczEiTyNE9cZmOctb9Pd0zATUCxIWVwmnJgq-c7patIWUpAoPKdDr1KVnjY4YneFNpivPf2_rofaECAKb30MfiYE6HeDcjnb5zSjCDlK2qpmqvdw2bUauW2jqkDuPmJd8zmYd7eRAJW92kXYrb6WI6uIwZc2K8ykK4wiF1YqoVYdyW6csbpPJ70NcQkkjFBvdfEhLFP_6EhhHmRG2L04kwYSflOhragOv_JaXXHpzCOup5an9v-SSkFpu7FboLIREiyxhwNOudslToWFGu56iXwDvaUzbV46na5oshc
sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
Content-Type: application/json

{
  "id": null,
  "number": null,
  "pvId": null,
  "parties": [
    {
      "id": 0,
      "partyType": "COMPANY",
      "consents": [
        {
          "consentType": "CREDIT_SCORE",
          "consentInd": false
        }
      ],
      "unstructuredName": "DefaultBusinessName",
      "address": {
        "city": "LONGUEUIL",
        "municipalityCode": "566500",
        "postalCode": "J4J3S9",
        "province": "QC",
        "street": "SAINT-ALEXANDRE"
      },
      "phoneNumber": "************"
    },
    {
      "id": 1,
      "partyType": "PERSON",
      "gender": "M",
      "partyRoles": [
        {
          "roleType": "PRINCIPAL_DRIVER",
          "riskId": 0
        },
        {
          "roleType": "BUSINESS_OWNER",
          "partyId": 0
        }
      ],
      "firstName": "bob",
      "lastName": "bob",
      "dateOfBirth": "1990-02-02"
    }
  ],
  "drivers": [
    {
      "partyId": 1
    }
  ],
  "policyHolders": [
    {
      "partyId": 0,
      "numberOfYearsWithCurrentInsurer": 0
    }
  ],
  "risks": [
    {
      "id": 0,
      "type": "VEHICLE",
      "commercialUsageCategoryCd": "CONTRACT",
      "commercialUsageCd": null,
      "commercialUsageSpecificCd": null,
      "year": 2024,
      "make": "HONDA",
      "model": "020900",
      "modelCode": "020900",
      "kmPerYear": "1000"
    }
  ]
}

###

