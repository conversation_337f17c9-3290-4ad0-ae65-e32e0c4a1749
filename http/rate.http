# Invalid Recaptcha
#@recaptcha-token = 03AFcWeA7CagEdEWIQeKDcC8d8w3Txh9xJPOrqvduF1X9SivdGNFsLKsKXi1VzXH-apr52KfJErPeH57muE2o-Cq2WnEsHdRBrzwkAvpMYii174qKXqSnzL03Ep8hgj7o50185PyrYZwiCBfaB8SvNmkZH2HHTrf_Vc8HRhn_knFG-wm0GQc24i5VS5NBF58XmcDMHQTbOR-TH8CVcln9UDMC05wCjscw7izOQDKejswwlvp5LN5gsqsauUnX5IEwMIg0JUSIRduWfTUYVBW6ahbJ0QQ4U_TAYevrKuYGpJ1H7pQDYGkj6i13bH1amZex99o1hNywNrlTJzPOH5bl1T50a_otLyyfFAxmhXfhwCGilVAf-HThozSVUL6xn_vypHo1_B4tkNqSIZrwgQmqKmIIU1fUJtTSh0GzwpiEgNDKPEITfGP5aZePpuBxqP28sBzgVZI_j9o3V48eZFMTxe9fEXnNbJz8L2qZe0q_LyLwc2EleQ0VU_0nNTA1isNIaamJzaTvXD9Sfo8bOFf9daAp4orEfDiQNAzc6LXpAegnrTFczVlLqsRiXGpdhP9w0hAu0McU5rdrEjn-BZt8EhPQWAZSq9Gtoebvs3h8cEtqSRGGo6h2yi9G4sKaWakZFqUKtJzuvdkpXQsnMtnbmZ5E-8CSHw6v5gg3EQ1bxzxZq5mrdll0ptEzFNyJF699jGxX8hCqCLsRZaeNO3JQ4DOBESP24rJNf23O2dtAWQFqEDNbCEdHjtzidpZ_KHVUFf62rPic6PwTaLDI1VLkKrMkxqbS6vPC-ilTHc4uHfX9lt4H7yRNzs_lZ3CBu05ZloE2QBPEKB2xA0VIjXmdRPN6hbnA00jrM8qkaB21igZ05NOGXC7RvmzcRiBABlxVCa4CCXQgxHqgZwrerLLyQjq_gVnF8o9JypNDDp7i4FLIi9Cbeyg1t3cE97U_EZdgkbicAJEd5OxWV6e0HtWRXEyJDwlAQAHnQnu5mBmjkPVQ46DMGazfl95K7p5h5h1GtI16IjapW2O09Bt2XILLXVVXHraztbkgtpZrybZiEpeQL4qzML-pzSgQtHjBbDzoiRA9Mubd7CXdc2UcIjS8L77V1u-3khy3zP4RVmhVXAfDUMCEtYMOpUpQe7Fd-h2qMAdasWwczIdCsmG9c8vt0Uj8WyUG7DYXZAnzKqRfBQsNDsVAYQS8ZBpNRTATyDA4zL9xZpSHPgTJyclcDmADMYE6-FWiSK7XRKqJgD_UfblSP_VCowtKvx-KpgU9-JFH8Ejp64_EJsfSHNXUnzQuN7X6_4gORKa8A8mqCyKVl0C4ZTLjveHXzbQGwk8gdW9VqLcadoCNUQltFDI3BhqsD0rpnUkJEleDLab1bDsKBPzfT86P5hJKX6nw6VgKwLxHTE2kuJ5ZvWDezpsEJmeM-dntS2DcZ8GmqL4A3lmKEw5bZqiS6_l5yPqV_hNUnybu5zKlEDYMZsVzXe5UNeAEA_E7OLX1LCieO3brJeTOARIXAWlSkyHROC0b06CrblyZBg8qfF0Y8yYtrPyYUvGWeETogM0IMC0kaafx8A_AQu470I0tQvH2yWAZMeiXffP_Ttp9I_I5-6OUXI6nvB4YZjKYUDmP5_4_ReH1awYG77FCkbo-F5NPAEzsE1VXNZ-W2q55AfcGB1m7swOc8VXOO25pcAGToW0OQZ0fr3mdq6qRmUJb0T2cnVUzi6I0Vz3PJRhg6HRjX6k6oqmgT8R8zRNrGcpIL3d-sUp3p7303SxRMVKU6d3Dmib5_i0Hf1mqErnN9p0JzcB4vdN9u7Hzx-0Rck6vhlVz9xj-_vOMmNSlJO1AojjcofDCITgHCvcX-1rU2gcQfMjxNgDUGkDvJtz6um_GdODUS53J-qcZau2oeETlrxsJRDksutQgB5LzzYiL-a1qih3Wr0rk63O8BieFHrv-4TCxBH0UJcddJSp--YvWLUpjN1cFY5gN8rIMgnp_TNYO1wf9xkw0M_J7orQgBfEgfn8BPfnkdCBtTSUfbrtR-2dzGYzLUMl-vF-VH4gJkJJ1u80VUHsrLW_RdL67AbrgiauCPvJlYbuIVhBA2JqqcF05Mhj5vWg9cWp8oFna3ABKGJHFJJnhUW52eSxi3Fia69es-u0dEQYA3ivXbP7y1qA-8tpAWJZvRL9zcw572EnNcl4ngKZxEZjul5kva5e28vPXHQtpnyY3zqCHZWjDIBZ-I54julw1Nerwb-ljzs42jtoRuKijgF1rodHHtN7Vkh7XJAWTT1ilcXV-5QNiuBOkF5WsciSu3WJPGvy1eTknOouN5cg7JIsjDXx31gRSxWiDU1jKc3S9r8zly_Mo68BGmftU
#@recaptcha-site-key = 6Lcmhc4bAAAAABflGJG8QTp-YlB23hNrQil3PcPp

### POST ON Rate
POST {{url}}/irca/v2/quotes/rates?
    actionToken=nothing&
    apiKey=a&language=en&province=on
Content-Type: application/json;charset=UTF-8
recaptcha-response-token: {{recaptcha-token}}
recaptcha-site-key: {{recaptcha-site-key}}

{
  "id": null,
  "number": null,
  "pvId": null,
  "parties": [
    {
      "id": 0,
      "partyType": "COMPANY",
      "consents": [
        {
          "consentType": "CREDIT_SCORE",
          "consentInd": null
        }
      ],
      "unstructuredName": "Finan",
      "address": {
        "city": "AJAX",
        "municipalityCode": "AR0050",
        "postalCode": "L1T3H4",
        "province": "ON",
        "street": "WESTNEY"
      },
      "phoneNumber": "************",
      "emailAddress": ""
    },
    {
      "id": 1,
      "partyType": "PERSON",
      "gender": "M",
      "partyRoles": [
        {
          "roleType": "PRINCIPAL_DRIVER",
          "riskId": 0
        },
        {
          "roleType": "BUSINESS_OWNER",
          "partyId": 0
        }
      ],
      "firstName": "Harinder",
      "lastName": "Singh",
      "dateOfBirth": "1995-12-11"
    }
  ],
  "policyHolders": [
    {
      "partyId": 0
    }
  ],
  "drivers": [
    {
      "partyId": 1,
      "licenseObtentionDate": "2014-12-01",
      "driverLicenseType": "R"
    }
  ],
  "risks": [
    {
      "id": 0,
      "type": "VEHICLE",
      "commercialUsageCategoryCd": "CONTRACT",
      "commercialUsageCd": "FLORIST",
      "commercialUsageSpecificCd": null,
      "year": 2019,
      "make": "FORD",
      "model": "329001",
      "modelCode": "329001"
    }
  ]
}

>>! respose/post-rate.json

### PUT Rate
PUT {{url}}/quickquote/v2/quotes/A33F04DC-7C75-4713-8254-0F51D4AF0F07/rates?
    actionToken=03AFcWeA6vP_ziKPNfbb2UG0QROA-kAsfq0gScwp1tNWgMvv2wiYhDCampdRmkmk7YuvCXIBOFRywuO8oUmv_w5Ib0I2EOjt5p16mI0mvbKaSrd1zrU5CTIe4__cVuUWrUDzJwmj7SRv6FIkDYUbAGnxqso89oU-KDkuByBV97D9gecScsaojPOErSXl-z_ph77hXCkJ2_gdEFeXTb4ANCkvUjh1kf2jVKTc7DsAaVPE4M2pTJQm7aaSHO_-QBFfd9ngVHYxhdmf9so0bxG9vdmDsjlnAIcDcnEXh_axe8NkGrQRqZwK8lsCtqMvSjAkDu6VGqakPVVQ8ewgncQZg3AZ7DJocMqbhanVK3HoW9GIJ9rFnsJ8aZQk4dvkXakT6o-HEepi9bVMzoZlMbcTonqZVouh1-BYXTwPaMIVM2npzMAmtRnShC75XtqdxOj9sEazosJ7nvkgcFmNLWAkrsM8sujKuFzyrPqzs4Y5fjw08tDL2FZZPZliNfDdZmp-xSE0f-r4kQ-aYmz8kYcXp4OuDd9UMyr5lIQagCu0dF7HlydZCe12BK7owucWkWZxsz5HOWVNjFRolzV0jpyu8PWj9ibTJ9azAeooX1uiHUnlYMFUg4RRdOq8U23_UkN_0Ee8eN_tOqOaJqWWLyEWFHMm0S4cHMwyyzTNPJFn9PrJZOi0EaQ2s5DeXwfMkTeh8hP8or6KlTsiTWTAMXHrJgrqlT4cm3DmtEYMU6VXmO6f2WHYw37iLN-64QknUYF1Z0Gpj2tUOJBEkJbZ_ZjlhPRbEimOK3fW1lb-LApFavjOPLhh9FsGFjT5hcHdH8Jj3L0ysuTob6atuEi5flkQmGTGDlQ-sWvVPMkNSZ4kjGGRkqq0Y-0i8cM5CdUAi8vCq8KC2zq71zb57m4jX7v5pLA11HmkL13ym2HWsdUgsrdfK_J54QdGMuTYOoSwa1mkqNLeRfM5bmMpRdQAGGQDBPznRT-bQt1TUcIrygHlMPgQpWtUvp0rHrwHd1fehmHs9IrOUzCYHschIKdAxisguJWlxuWU2oFYsBPbZirUKIwzoVEhNpAvKUryPuPLcGc7ReHpw1dv3UJXC0p2UYLmnpbNgDhFmIwJyfLpq983yskLhy-T_S8Lk8te-poNXdW1dsDthQWimAp7RZ7iIT6quLCT8na4acJjY94gOJGYPQux4McqTRM9_lEfKt9EG6D5A4xC2JiUXSKYzAqWkG9mPcEh4ZbnkiV3luWSHlCRa53oiYXITmHdAAobj1bdxR7nKUHNcum3rTY16LdS-HfT1XokGYqJU8Tk9NsIWnLvamgMYx5wZqrk5G-qFouBbr1OiVcsNsywfCe_R5lf4zknTP5u38Ra1QO2QoE1HlYvr-hHSGZJRI1NnBqtevn2x_JqKKGMSfrP5MJGO4B77Pe3yl6WJRrDgWdN6upzbb0Eac4XhbwSo5zvX9Ov7OzOTJGUq6ql0582GubH5W5cFKf0gHz4csY82Gopb3993kDXMBCfxHrp3okWmxgOGJ51PTbFAvH5GUSi2KdWaHb-Dn2TAtybT6Qq-zi7JkTEjrZw_5uclnLMpMIYj-dLrG-IwWP2_2SkD5PKKc0kLoFfuhSgD854b4HL_hAWpNXmum_b6Gl6OvTzCiO54EXX1qvmx4tvbUaCOuZLxVTqCd6SNvYRR-EQBFMxCujphMTEG0CY6Oo0Ee9CVzuf6UeMOBu24hLpOPuf5LeBKjzwLtZSz49Be83i38WuhAfOKWI8hTfpgLO-Zlbew8n83Vtm1rUFNh4lrpOBUf83olXQe79bAaqX4jJJlHo8_r7bUWz_skxCzALzp5_uJ4gjoVaHVBuRQGQ6qvt3rYMraa3a73SSqryFhY4jx32AkKNq7r70J1c-lsx1JPXCGBbm-G8N4x49RvKUN9MdkDId60Fdq1K5lytvVMno_wzZv7NUtz8XJ1mLmklieJWlQplenaSd1QwSNvFb2ieaRtJpM0geBg8eXd4PZfuUIC3OX6GZ15lijI-1OmSIC0IPK_FJyZ500o0RJS2zwAXgEmonCQGzKQhq1fxWUSRVB4gcAA32B9XaGcVAFTv-9O1141H3uazpi276iVT1h5SPKsfhnPmyBZt7rxEYp-K3pczDdLEpCXXo9Uq3MLumZHI7-nwV7hf_EW07q-vicFevANtU5_UcVZIVa7rIkTZFeuSvIac5bHjoDjofYzFmZgAlAu4E10KlEWPn0AizrVGaY_ZmutrBcJnE4S993KjSl4t_SqtXlE351VHAqV42BjaswqYSHEptV69-d_e4DrDmu5_HR83hWuLnkTYOwsIBfD_xSqMBpaObfaS7nS40uijBCZwq0argw&
    apiKey=PkTAzgzKJvHcQQTgh9dSAJRo&language=en&province=on
Content-Type: application/json;charset=UTF-8
recaptcha-response-token: {{recaptcha-token}}
recaptcha-site-key: {{recaptcha-site-key}}

{
  "id": null,
  "number": null,
  "pvId": null,
  "parties": [
    {
      "id": 0,
      "partyType": "COMPANY",
      "consents": [
        {
          "consentType": "CREDIT_SCORE",
          "consentInd": null
        }
      ],
      "unstructuredName": "Finan",
      "address": {
        "city": "AJAX",
        "municipalityCode": "AR0050",
        "postalCode": "L1T3H4",
        "province": "ON",
        "street": "WESTNEY"
      },
      "phoneNumber": "************",
      "emailAddress": ""
    },
    {
      "id": 1,
      "partyType": "PERSON",
      "gender": "M",
      "partyRoles": [
        {
          "roleType": "PRINCIPAL_DRIVER",
          "riskId": 0
        },
        {
          "roleType": "BUSINESS_OWNER",
          "partyId": 0
        }
      ],
      "firstName": "Harinder",
      "lastName": "Singh",
      "dateOfBirth": "1995-12-11"
    }
  ],
  "policyHolders": [
    {
      "partyId": 0
    }
  ],
  "drivers": [
    {
      "partyId": 1,
      "licenseObtentionDate": "2014-12-01",
      "driverLicenseType": "R"
    }
  ],
  "risks": [
    {
      "id": 0,
      "type": "VEHICLE",
      "commercialUsageCategoryCd": "CONTRACT",
      "commercialUsageCd": "FLORIST",
      "commercialUsageSpecificCd": null,
      "year": 2019,
      "make": "FORD",
      "model": "329001",
      "modelCode": "329001"
    }
  ]
}

>>! respose/put-rate.json

### POST QC Rate
POST{{url}}/quickquote/v2/quotes/rates?
    actionToken=HFdzEyZEgVE1RNZjYHWRsWWFZLEnxUYEdzAGE1IDZsaCpKAEtfJhN7fVksW2sSN0JXH3QTE0JBXgQETBV_ECRQMzV0cBFvf2h4EB8XSzMwbyQbdAcqUjUXDh8kOxkdUwRWBzwMbQYHFXU2ZXE9IDwyKw4aSBx8dTJnCHMtJQ8-QAMNJWA1WhEdUQYYRzxDEDIhW3c1J3UmNWoRXhkfMWMaPghnHDVbIDNACHRjXAAlAjUvaSdjRD8iWApgcxtydnt1UiNcDWB2KUpYIlpHVSEfTFl3VkJDBkBDEEgmLlBWAHdVIUA&
    apiKey=PkTAzgzKJvHcQQTgh9dSAJRq&language=en&province=qc
Content-Type: application/json;charset=UTF-8
recaptcha-response-token: {{recaptcha-token}}
recaptcha-site-key: {{recaptcha-site-key}}

{
  "id": null,
  "number": null,
  "pvId": null,
  "parties": [
    {
      "id": 0,
      "partyType": "COMPANY",
      "consents": [
        {
          "consentType": "CREDIT_SCORE",
          "consentInd": false
        }
      ],
      "unstructuredName": "DefaultBusinessName",
      "address": {
        "city": "VAUDREUIL-DORION",
        "municipalityCode": "722500",
        "postalCode": "J7V0M7",
        "province": "QC",
        "street": "BACH"
      },
      "phoneNumber": "************"
    },
    {
      "id": 1,
      "partyType": "PERSON",
      "gender": "M",
      "partyRoles": [
        {
          "roleType": "PRINCIPAL_DRIVER",
          "riskId": 0
        },
        {
          "roleType": "BUSINESS_OWNER",
          "partyId": 0
        }
      ],
      "firstName": "john",
      "lastName": "doe",
      "dateOfBirth": "1997-11-28"
    }
  ],
  "policyHolders": [
    {
      "partyId": 0
    }
  ],
  "drivers": [
    {
      "partyId": 1
    }
  ],
  "risks": [
    {
      "id": 0,
      "type": "VEHICLE",
      "commercialUsageCategoryCd": "SERVICES",
      "commercialUsageCd": "ARCHI",
      "commercialUsageSpecificCd": null,
      "year": 2020,
      "make": "HONDA",
      "model": "021001",
      "modelCode": "021001",
      "kmPerYear": "15000"
    }
  ]
}

### POST AC Rate
POST {{url}}/quickquote/v2/quotes/rates?
    actionToken=HFZGNxdxpWAAYOdWRESklVSwQIAS4XcxUwEzB9NGAveX0OGhAcNUE4bgtvSDlRJBAUDCZQABACTVZHX0c8A3YTIGc3Y0MsbDo7A01UWGFzfHZYZ1VpQWdUHU1nKEteQFYVFG5PflREBid1diN-M25xOFxZW04_ZmAkGyFuNl19U1FONjJ2SUNeQlRbVG4AA2BiSCV2NCdlJjhSTUtcIjFZPVskD2cYM2EDGyZ7ElNEF1pmaE4rSkZ9V0lBMj0BRnY-T34fHjI1OhgbMQ8D&
    apiKey=PkTAzgzKJvHcQQTgh9dSAJRq&language=en&province=ab
Content-Type: application/json;charset=UTF-8
recaptcha-response-token: {{recaptcha-token}}
recaptcha-site-key: {{recaptcha-site-key}}

{
  "id": null,
  "number": null,
  "pvId": null,
  "parties": [
    {
      "id": 0,
      "partyType": "COMPANY",
      "consents": [
        {
          "consentType": "CREDIT_SCORE",
          "consentInd": null
        }
      ],
      "address": {
        "city": "PEACE RIVER",
        "municipalityCode": "PS0380",
        "postalCode": "T8S1T1",
        "province": "AB",
        "street": null
      },
      "unstructuredName": "Intells",
      "phoneNumber": "************"
    },
    {
      "id": 1,
      "partyType": "PERSON",
      "gender": "M",
      "partyRoles": [
        {
          "roleType": "PRINCIPAL_DRIVER",
          "riskId": 0
        },
        {
          "roleType": "BUSINESS_OWNER",
          "partyId": 0
        }
      ],
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1995-12-23"
    }
  ],
  "policyHolders": [
    {
      "partyId": 0
    }
  ],
  "drivers": [
    {
      "partyId": 1,
      "licenseObtentionDate": "2014-12-01"
    }
  ],
  "risks": [
    {
      "id": 0,
      "type": "VEHICLE",
      "commercialUsageCategoryCd": "CONTRACT",
      "commercialUsageCd": "CONSTR",
      "commercialUsageSpecificCd": null,
      "year": 2021,
      "make": "FORD",
      "model": "354100",
      "modelCode": "354100"
    }
  ]
}

###




