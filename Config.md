## Configuration

### Manual code formatting

`mvn git-code-format:format-code -Dgcf.globPattern=**/*`

### Cucumber

#### Running Cucumber tests locally

To run the Cucumber tests from your computer, you first need to create a new "Run/Debug configuration" by going into _Run_ → _Edit Configurations_ and creating a new one. The new configuration must look like this. ![run_config](https://githubifc.iad.ca.inet/Intact/cucumber-qaa/blob/master/resources/documentation/run_config.JPG)

The VM options used are the following :

| Command                                        | Use                                                                                                                                                                                                           |
|------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| _-ea_                                          | Used to enable assertions.                                                                                                                                                                                    |
| _-Dtest-e2e-environment=${env}_                | Used to specify the environment config you will be using. [more details](https://githubifc.iad.ca.inet/Intact/cucumber-qaa/blob/master/README.md#EnvironmentUtilsjava)                                               |
| _-Ddataproviderthreadcount=${numberOfThreads}_ | Used to specify the maximum number of threads which is equivalant to the maximum number of tests that can be run simultaneously. It is recommended to use 4 threads.                                          |
| _-DPPP.OutputStyle=${style}_                   | (Optional) Used to specify the desired output style and control the level of detail given on each test. [more details](https://githubifc.iad.ca.inet/Intact/cucumber-qaa/blob/master/README.md#PrettyParallelOutputjava) |

Once your configuration is all set, you can simply run your configuration to execute every existing tests. However, if you only want to run some tests, you can specify the desired tag or tags associated with the tests and add them to the "tags" parameter as shown below, further documentation can be found [here](https://cucumber.io/docs/cucumber/api/#tags).
```java
@CucumberOptions(plugin = {"io.cucumber.core.plugin.ZephyrLink:${FileName}.properties"},
features = "classpath:features",
glue = "$path.to.steps",
tags = "@tag")
public class RunCucumber extends AbstractTestNGCucumberTests{...}
```

#### Rerunning failing tests

To be able to rerun failing tests, you first need to create a new "Run/Debug configuration" just like mentioned above, but instead of pointing your class on "RunCucumber" you have to point it on "ReRunCucumber". Then, you have to run the configuration pointing on "RunCucumber" and execute the desired tests. Once this is done, a list of every failing test can be found in the file : `\target\rerun\rerun.txt`. After that, to be able to rerun those tests, you simply have to run the configuration pointing on "ReRunCucumber" and only failing tests will be executed. If some tests now pass, the list will be updated, so it only reruns the failing ones during the next run.


#### Mutation Test - PITest

To run the mutation tests, the following command should be executed in your terminal in the folder of your app :

`mvn org.pitest:pitest-maven:mutationCoverage`

The report should be generated in the folder `/target/pit-reports/`
