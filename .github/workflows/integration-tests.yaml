name: Integration tests
run-name: Integration tests - ${{ github.event.inputs.environment || 'uat - started by cron' }}

on:
#  schedule:
 #  - cron: '0 10 * * 1-5'
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        options:
          - dev
          - dev-preview
          - intg
          - intg-preview
          - uat
          - uat-preview
          - prep
          - prep-preview
          - uat-cp
          - uat-cp-preview
        default: uat
        description: Which environment to target?
      tags:
        required: false
        type: string
jobs:
  integration-test-job:
    uses: lab-se/labci-workflows/.github/workflows/test.integration.maven.yaml@main
    with:
      environment: ${{ github.event.inputs.environment || 'uat' }}
      tags: ${{ github.event.inputs.tags }}
      project: autoquote-backend
      module: autoquote-backend-integration-tests
      email: Automated Pipelines - Megatron Squad <<EMAIL>>
