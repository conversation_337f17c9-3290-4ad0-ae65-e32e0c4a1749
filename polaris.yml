version: '1'
project:
  name: ${scm.git.repo}
  branch: ${scm.git.branch}
  revision:
    name: ${scm.git.commit}
    date: ${scm.git.commit.date}
  groups:
    SE_INTACT_LAB: Contributor
  properties:
    ownership: SE_INTACT_LAB|acquisition|AEGIS
    environment: prod
capture:
  build:
    cleanCommands:
    - shell: [mvn, -B, -f, pom.xml, clean]
    buildCommands:
    - shell: [mvn, -B, -f, pom.xml, install, -DskipTests, -DskipITs, -Dspring-boot.run.skip=true]
  fileSystem:
    java:
      files:
      - directory: ${project.projectDir}
analyze:
  mode: central
  coverity:
    cov-analyze:
    - --webapp-security
    - --security
    - --concurrency
    - --enable
    - HARDCODED_CREDENTIALS
    - --enable
    - ORM_LOST_UPDATE
install:
  coverity:
    version: default
serverUrl: https://s0013400001xyqzv.polaris.synopsys.com
