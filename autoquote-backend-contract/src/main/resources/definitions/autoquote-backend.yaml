openapi: 3.1.0
info:
  title: Autoquote-Backend
  description: Autoquote Backend Rest for IRCA
  version: "1"
servers:
- url: http://localhost:8080
  description: Generated server url
paths:
  /quickquote/v2/quotes/rates:
    post:
      tags:
      - vehicle-controller
      operationId: rateQuote
      parameters:
      - name: actionToken
        in: query
        required: true
        schema:
          type: string
      - name: apiKey
        in: query
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: subBroker
        in: query
        required: false
        schema:
          type: string
      - name: organizationSource
        in: query
        required: false
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QuoteDTO"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CommunicationObjectModel"
  /quickquote/v2/vehicles/{year}/{makeId}/models:
    get:
      tags:
      - vehicle-controller
      summary: Get the vehicule models
      operationId: getVehicleModels
      parameters:
      - name: year
        in: path
        required: true
        schema:
          type: string
      - name: makeId
        in: path
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved a list of vehicule models
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
  /quickquote/v2/vehicle/{year}/makes:
    get:
      tags:
      - vehicle-controller
      summary: Get the vehicle makes
      operationId: getVehicleMakeList
      parameters:
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: year
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: Successfully retrieved a list of vehicule maker
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
components:
  schemas:
    AddressDTO:
      type: object
      properties:
        postalCode:
          type: string
        municipalityCode:
          type: string
        addressLine:
          type: string
        city:
          type: string
        civicNbr:
          type: string
        country:
          type: string
        floor:
          type: string
        province:
          type: string
        street:
          type: string
        streetDirection:
          type: string
        streetType:
          type: string
        suiteNbr:
          type: string
    ClaimDTO:
      type: object
      properties:
        claimSequence:
          type: string
        dateOfLoss:
          type: string
        nature:
          type: string
          enum:
          - AA
          - NA
          - AAF
          - AWP
          - SWD
          - HR
          - FV
          - GR
          - GW
          - TH
          - WN
          - AP
          - AB
          - BI
          - FIR
          - VAN
          - IA
          - ONC
    ConsentDTO:
      type: object
      properties:
        consentInd:
          type: boolean
        consentType:
          type: string
          enum:
          - PROFILE
          - MARKETING
          - CREDIT_SCORE
    ConvictionDTO:
      type: object
      properties:
        convictionSequence:
          type: string
        nbYearsOld:
          type: string
        type:
          type: string
          enum:
          - MAJ
          - MIN
          - DIS
    CoverageDTO:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
        selectedInd:
          type: boolean
        selectableInd:
          type: boolean
        eligibleInd:
          type: boolean
        coverageValues:
          type: array
          items:
            $ref: "#/components/schemas/CoverageValueDTO"
        ihvCode:
          type: string
    CoverageValueDTO:
      type: object
      properties:
        amount:
          type: integer
          format: int32
        coverageValueType:
          type: string
          enum:
          - LIMIT_AMOUNT
          - DEDUCTIBLE_AMOUNT
          - LIMIT_MULTIL_DEATH_AMOUNT
          - LIMIT_MED_EXPENSES_AMOUNT
        selectedInd:
          type: boolean
    DriverDTO:
      type: object
      properties:
        dateOfBirth:
          type: string
          format: date
        firstName:
          type: string
        lastName:
          type: string
        gender:
          type: string
        partyType:
          type: string
          enum:
          - "[code=PERSON]"
          - "[code=COMPANY]"
          - "[code=DRIVER]"
        unstructuredName:
          type: string
        id:
          type: integer
          format: int32
        address:
          $ref: "#/components/schemas/AddressDTO"
        emailAddress:
          type: string
        phoneNumber:
          type: string
        partyRoles:
          type: array
          items:
            $ref: "#/components/schemas/PartyRoleDTO"
        consents:
          type: array
          items:
            $ref: "#/components/schemas/ConsentDTO"
        principalInsuredSinceCode:
          type: string
        numberOfMinorInfractions:
          type: integer
          format: int32
        numberOfMajorInfractions:
          type: integer
          format: int32
        numberOfRelevantClaims:
          type: integer
          format: int32
        interestedByUbiInd:
          type: boolean
        partyId:
          type: integer
          format: int32
        licenseNbr:
          type: string
        driverLicenseType:
          type: string
        licenseObtentionDate:
          type: string
          format: date
        claims:
          type: array
          items:
            $ref: "#/components/schemas/ClaimDTO"
        convictions:
          type: array
          items:
            $ref: "#/components/schemas/ConvictionDTO"
    OfferDTO:
      type: object
      properties:
        offerCode:
          type: string
        selectedInd:
          type: boolean
        annualPremium:
          type: number
        monthlyPremium:
          type: number
        coverages:
          type: array
          items:
            $ref: "#/components/schemas/CoverageDTO"
    PartyDTO:
      type: object
      properties:
        dateOfBirth:
          type: string
          format: date
        firstName:
          type: string
        lastName:
          type: string
        gender:
          type: string
        partyType:
          type: string
          enum:
          - "[code=PERSON]"
          - "[code=COMPANY]"
          - "[code=DRIVER]"
        unstructuredName:
          type: string
        id:
          type: integer
          format: int32
        address:
          $ref: "#/components/schemas/AddressDTO"
        emailAddress:
          type: string
        phoneNumber:
          type: string
        partyRoles:
          type: array
          items:
            $ref: "#/components/schemas/PartyRoleDTO"
        consents:
          type: array
          items:
            $ref: "#/components/schemas/ConsentDTO"
    PartyRoleDTO:
      type: object
      properties:
        partyId:
          type: integer
          format: int32
        riskId:
          type: integer
          format: int32
        roleType:
          type: string
          enum:
          - "[code=PRINCIPAL_DRIVER]"
          - "[code=OCCASIONAL_DRIVER]"
          - "[code=REGISTERED_OWNER]"
          - "[code=BUSINESS_OWNER]"
    PolicyHolderDTO:
      type: object
      properties:
        partyId:
          type: integer
          format: int32
        numberOfYearsWithCurrentInsurer:
          type: integer
          format: int32
    QuoteDTO:
      type: object
      properties:
        id:
          type: string
        number:
          type: string
        pvId:
          type: string
        subBrokerNbr:
          type: string
        parties:
          type: array
          items:
            $ref: "#/components/schemas/PartyDTO"
        policyHolders:
          type: array
          items:
            $ref: "#/components/schemas/PolicyHolderDTO"
        risks:
          type: array
          items:
            $ref: "#/components/schemas/VehicleDTO"
        drivers:
          type: array
          items:
            $ref: "#/components/schemas/DriverDTO"
        policyDiscountCode:
          type: string
    VehicleDTO:
      type: object
      properties:
        id:
          type: integer
          format: int32
        modificationCode:
          type: string
          enum:
          - NEW
          - REMOVED
          - SUBSTITUTED
          - MODIFIED
        commercialUsageCategoryCd:
          type: string
        commercialUsageCd:
          type: string
        commercialUsageSpecificCd:
          type: string
        offers:
          type: array
          items:
            $ref: "#/components/schemas/OfferDTO"
        year:
          type: integer
          format: int32
        make:
          type: string
        model:
          type: string
        modelCode:
          type: string
        usageModified:
          type: boolean
        purchasedDate:
          type: string
          format: date
        businessKmPerYear:
          type: integer
          format: int32
        kmPerYear:
          type: integer
          format: int32
        trackingSystemInd:
          type: boolean
        trackingSystemCode:
          type: string
          enum:
          - BANSHEE_ICALOCK
          - ARMED_GUARD_SHERLOCK
          - VIPER
          - MERLIN
          - HAWK_200_AUTOLAND
          - OTHER
          - ADAV
          - AUTOGRAPH
          - AMERI_KOP
          - AUTOLUCK
          - AUTOMOBILE_MANUFACTURER
          - GUARDIAN
          - LARLOK
          - OTOPROTEC
          - SHERLOCK
          - VIN_LOCK
          - ULTRACAR
          - BOOMERANG_1
          - BOOMERANG_2
          - INTERCEPTER_STAR_TRACK
          - LYNX
          - MOBILIUS
          - NAVLYNX_AUTOGUARD
          - ON_STAR
          - SATELINX
          - SPAVTRACK
          - ECONOTRACK
          - TAG
          - DATADOTDNA
          - GLOBALGLOBALI
          - MICRODOTDNA
          - THREEEYETRACKING
          - CELLUTRACK
          - LOJACKBOOMERANG
          - MLINK
          - ORCA
          - VIGILGPS
          - BARRACUDA
          - KOLOMBO
        normalRadiusKm:
          type: integer
          format: int32
        grossVehicleWeight:
          type: number
          format: double
        useOfVehicleCode:
          type: string
        midHaulingDaysPerMonth:
          type: integer
          format: int32
        multiVehicleDiscountEligibilityInd:
          type: boolean
        maximumRadiusKm:
          type: integer
          format: int32
    ComAddress:
      type: object
      properties:
        postalCode:
          type: string
        civicNumber:
          type: string
        arterialRoad:
          type: string
        streetName:
          type: string
        appartmentNumber:
          type: string
        city:
          type: string
        cityCode:
          type: string
        province:
          type: string
        country:
          type: string
        addressFormatCode:
          type: string
        poBoxNumber:
          type: string
        ruralRouteNumber:
          type: string
    ComAdvisor:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        city:
          type: string
        province:
          type: string
    ComBrokerInfo:
      type: object
      properties:
        postalCode:
          type: string
        province:
          type: string
        companyNumber:
          type: string
        subBrokerNumber:
          type: string
        subBrokerName:
          type: string
        masterBrokerNumber:
          type: string
        phoneNumber:
          type: string
        logo:
          type: string
          format: byte
        callBackAvailable:
          type: boolean
        homeQuoteAvailable:
          type: boolean
        brokerWebsiteOrigin:
          type: string
          enum:
          - INTACT
          - BROKER
        addressLine1:
          type: string
        addressLine2:
          type: string
        addressLine3:
          type: string
    ComCallMe:
      type: object
      properties:
        policyVersionId:
          type: integer
          format: int64
        cifClientId:
          type: integer
          format: int64
        prefTimeToBeContacted:
          type: string
        phoneAreaCode:
          type: string
        phonePointer:
          type: string
        phoneExchange:
          type: string
        phoneExtension:
          type: string
        comments:
          type: string
        userLastAccessedPage:
          type: string
          enum:
          - DRIVER1
          - DRIVER2
          - DRIVER3
          - VEHICLE1
          - VEHICLE2
          - USAGE
          - BIND
          - BINDVAL1
          - BINDVAL2
          - OFFER
          - PAYMENT
        firstName:
          type: string
        lastName:
          type: string
    ComContext:
      type: object
      properties:
        language:
          type: string
          enum:
          - FRENCH
          - ENGLISH
        province:
          type: string
          enum:
          - ALBERTA
          - BRITISH_COLUMBIA
          - MANITOBA
          - NEW_BRUNSWICK
          - NEWFOUNDLAND_AND_LABRADOR
          - NOVA_SCOTIA
          - ONTARIO
          - PRINCE_EDWARD_ISLAND
          - QUEBEC
          - SASKATCHEWAN
          - NORTHWEST_TERRITORIES
          - YUKON
          - NUNAVUT
          - OTHER
          - USA
          - EUROPE
        application:
          type: string
          enum:
          - OTHER
          - QUICKQUOTE
          - AUTOQUOTE
          - WEBQUOTE
          - CHECKOUT
        trackingNumber:
          type: string
        sessionNumber:
          type: string
        userAgent:
          type: string
        clientIPNumber:
          type: string
        testDataInd:
          type: boolean
        clientXForwardIPNbr:
          type: string
        mobile:
          type: boolean
        tablet:
          type: boolean
        distributionChannel:
          type: string
          enum:
          - THROUGH_BROKERS
          - DIRECT_SELLER
        partnershipId:
          type: integer
          format: int64
        marketingPromotionCode:
          type: string
        company:
          type: string
          enum:
          - OTHER
          - BELAIR
          - INTACT
        partnership:
          type: string
        referer:
          type: string
        refererPromoCode:
          type: string
        brokerInfo:
          $ref: "#/components/schemas/ComBrokerInfo"
        distributor:
          type: string
          enum:
          - BNA
          - BEL
        lineOfBusiness:
          type: string
          enum:
          - COMMERCIAL_LINES
          - PERSONAL_LINES
        refererUrl:
          type: string
        useAllCoveragesStructure:
          type: boolean
    ComCoverageItem:
      type: object
      properties:
        coverageName:
          type: string
          enum:
          - LIABILITY
          - COLLISION
          - COMPREHENSIVE
          - ACCIDENT_BENEFIT
          - ROADSIDE_ASSISTANCE
          - ROADSIDE_ASSISTANCE_SAV
          - AUTOCOMFORT
          - AUTOCOMFORTCODE_SAV
          - AUTOCOMFORTCODE
          - WAIVER_DEPRECIATION
          - CRASHPROOF
          - CRASHPROOF_SAV
          - CRASHPROOF_OPENROAD
          - SUPERCOVERAGE
          - SUPERCOVERAGE_OPENROAD
          - NEW_CRASHPROOF
          - NEW_CRASHPROOF_OPENROAD
          - REPLACEMENT_COST
          - REPLACEMENT_COST_SAV
          - DCPD
          - LIMITED_GLASS
          - LIMITED_GLASS_SAV
          - FAMILY_PROTECTION
          - MULTIVEHICLE
          - PLUSPAC_C
          - PLUSPAC_D
          - MY_ACHIEVER
          - OPTIONAL_AB
          - CONVICTION_WAIVER
          - CONVICTION_WAIVER_SAV
          - FULL_GLASS
          - UBI
          - UBO
          - DUOFLEX
          - HAIL_COVERAGE_REMOVAL
          - HAIL_COVERAGE_REMOVAL_SAV
          - FAMILY_PROTECTION_SAV
          - EXCESS_UMP
          - INOV
          - WINSHIELD_REPAIR
          - CLAIMS_ADVG
        valueType:
          type: string
        editable:
          type: boolean
        choices:
          type: array
          items:
            $ref: "#/components/schemas/ComItemChoice"
        coverageValue:
          type: string
        coverageOtherValue:
          type: string
        coverageOtherValueType:
          type: string
          enum:
          - LIMIT_AMOUNT
          - DEDUCTIBLE_AMOUNT
        coverageOtherChoices:
          type: array
          items:
            $ref: "#/components/schemas/ComItemChoice"
        eligible:
          type: boolean
        coverageInternalCode:
          type: string
        coverageIhvCode:
          type: string
        selectable:
          type: boolean
    ComDate:
      type: object
      properties:
        day:
          type: string
        month:
          type: string
        year:
          type: string
    ComDriver:
      type: object
      properties:
        persisted:
          type: boolean
        plpId:
          type: integer
          format: int64
        driverId:
          type: integer
          format: int32
        firstName:
          type: string
        webMsgId:
          type: integer
          format: int32
        isDriver:
          type: boolean
        lastName:
          type: string
        entreprise:
          type: string
        entrepriseOwnerId:
          type: integer
          format: int32
        policyHolderInd:
          type: boolean
        cifClientId:
          type: integer
          format: int64
        groupCode:
          type: string
        homePhoneNumber:
          type: string
        cellPhoneNumber:
          type: string
        currentAddress:
          $ref: "#/components/schemas/ComAddress"
        yearsAtThisAddress:
          type: string
        daysAtThisAddress:
          type: string
        homeAndAutoInd:
          type: boolean
        policyRefusedInd:
          type: boolean
        licenseRevokeInd:
          type: boolean
        previousAddress:
          $ref: "#/components/schemas/ComAddress"
        marketingConsent:
          type: boolean
        emailAddress:
          type: string
        dateOfBirth:
          $ref: "#/components/schemas/ComDate"
        ageGotLicense:
          type: string
        gender:
          type: string
        maritalStatus:
          type: string
        driverLicenseType:
          type: string
        driverLicenseClass:
          type: string
        dateLicenseObtained:
          $ref: "#/components/schemas/ComDate"
        drivingCourseInd:
          type: boolean
        lossesYearsInd:
          type: boolean
        infractionsPast3YearsInd:
          type: boolean
        minorInfractionCount:
          type: string
        majorInfractionCount:
          type: string
        penaltyPointsAmount:
          type: string
        convictionsCount:
          type: string
        currentInsurer:
          type: string
        currentInsurerPolicyNumber:
          type: string
        relationshipToPolicyHolder:
          type: string
        policyHolderName:
          type: string
        driverClaims:
          type: array
          items:
            $ref: "#/components/schemas/ComDriverClaim"
        monthsInsuredWithOurCompany:
          type: string
        retiredInd:
          type: boolean
        fullTimeStudentInd:
          type: boolean
        goodStudentInd:
          type: boolean
        permanentAddressInd:
          type: boolean
        school100KmFromParentInd:
          type: boolean
        durationOfInsuranceTerm:
          type: string
        holderAutoInsuranceIndicator:
          type: boolean
        insuredContinuously:
          type: string
        consentInd:
          type: boolean
        ubiConsentInd:
          type: boolean
        driverConvictions:
          type: array
          items:
            $ref: "#/components/schemas/ComDriverConviction"
        insuredLastXYearsWithSameInsurerInd:
          type: boolean
        policyNumberNonPayment:
          type: string
        policyNonPaymentInd:
          type: boolean
        severeInfractionsCount:
          type: string
        ageInsured:
          type: string
        reasonPolicyRefused:
          type: string
        monthsNotLivingWithParents:
          type: string
        caaMemberInd:
          type: boolean
        caaMemberNumber:
          type: string
        otherDriversWithUsCarInsuranceInd:
          type: boolean
        otherDriverPolicyNumber:
          type: string
        obtainedGorG2LicenseLastYearInd:
          type: boolean
        fullTermInsuranceInd:
          type: boolean
        createProfileConsentInd:
          type: boolean
        homeInsuredInd:
          type: boolean
        homePolicyNumber:
          type: string
        homeInsuranceExpiryDate:
          $ref: "#/components/schemas/ComDate"
        universityDegreeInd:
          type: boolean
        driverOccupation:
          $ref: "#/components/schemas/ComDriverOccupation"
        insuredLastXMonthsInd:
          type: boolean
        outstandingPremiumInd:
          type: boolean
        interruptionInLastPeriodInd:
          type: boolean
        alreadyBeenPrincipalDriverInd:
          type: boolean
        principalInsuredSinceCode:
          type: string
        paymentMethod:
          type: string
        holderAutoInsPrincipalDriverInd:
          type: boolean
        universityOrCollegeCurrentlyStudying:
          type: string
        studentNumber:
          type: string
        licenseNumber:
          type: string
        partnershipConsentInd:
          type: boolean
        criminalRecordInd:
          type: boolean
        drivingWeekendOnly:
          type: boolean
        residenceInd:
          type: string
        customerInterestedByUBI:
          type: boolean
        antispamConsent:
          type: boolean
        quoteContactConsent:
          type: boolean
        riskReportConsent:
          type: boolean
        priorGridLevel:
          type: string
        partnershipCode:
          type: string
        gridLevelQty:
          type: integer
          format: int32
        priorGridLevelQty:
          type: integer
          format: int32
        hailCoverageRemovalConsent:
          type: boolean
        licenseBarcode:
          type: string
        licenseBarcodeFilename:
          type: string
        bnaClientInd:
          type: boolean
        mvrSaaqAuthorizationCd:
          type: string
          enum:
          - "YES"
          - "NO"
          - CONSENT_TO_FOLLOW
          - CONVERTED
        ltvBandCode:
          type: string
        ltvScore:
          type: number
          format: double
        isStudent:
          type: boolean
    ComDriverClaim:
      type: object
      properties:
        claimSequence:
          type: integer
          format: int32
        claimNatureYear:
          type: string
        claimNature:
          type: string
        claimNatureAmount:
          type: string
        claimDate:
          $ref: "#/components/schemas/ComDate"
        accidentAfterJuin1Ind:
          type: boolean
        personHurtInAccidentInd:
          type: boolean
        insurerPaidForDamageInd:
          type: boolean
        damageAmountExceeded2000Ind:
          type: boolean
        dateLoss:
          type: string
          format: date-time
          deprecated: true
    ComDriverConviction:
      type: object
      properties:
        convictionSequence:
          type: integer
          format: int32
        convictionCode:
          type: string
        convictionType:
          type: string
        convictionYear:
          type: string
    ComDriverOccupation:
      type: object
      properties:
        workSector:
          type: string
        domainDescription:
          type: string
        occupation:
          type: string
        occupationDescription:
          type: string
        insuredGroup:
          type: string
    ComEvent:
      type: object
      properties:
        eventCode:
          type: string
          enum:
          - NEW_QUOTE
          - RETRIEVE_QUOTE
          - RETRIEVE_ROADBLOCK_QUOTE
          - UPDATE_SAVINGS
          - ADD_VEHICLE
          - VIEW_VEHICLE
          - MODIFY_VEHICLE
          - DELETE_VEHICLE
          - ADD_DRIVER
          - VIEW_DRIVER
          - MODIFY_DRIVER
          - DELETE_DRIVER
          - VIEW_USAGE
          - SAVE_USAGE
          - VIEW_NOHIT
          - VIEW_OFFER
          - SAVE_OFFER
          - RECALCULATE
          - SECOND_CONSENT
          - SEND_EMAIL
          - VIEW_BIND
          - SAVE_BIND
          - VIEW_VEHICLE_DETAILS
          - VIEW_PAYMENT
          - VIEW_PROFILE
          - PURCHASE
          - ROADBLOCK
        comId:
          $ref: "#/components/schemas/ComId"
    ComFinancingCompany:
      type: object
      properties:
        companyName:
          type: string
        companyAddress:
          $ref: "#/components/schemas/ComAddress"
        financingCompany:
          type: string
    ComId:
      type: object
      properties:
        plpId:
          type: integer
          format: int64
        sequence:
          type: integer
          format: int32
    ComItemChoice:
      type: object
      properties:
        id:
          type: string
        value: {}
        coverageAmountType:
          type: string
          enum:
          - LIMIT_AMOUNT
          - DEDUCTIBLE_AMOUNT
    ComOffer:
      type: object
      properties:
        validationErrors:
          type: array
          items:
            $ref: "#/components/schemas/ComValidationError"
        annualPremium:
          type: number
          format: double
        monthlyPremium:
          type: number
          format: double
        optionalAccidentBenefits:
          type: string
        selected:
          type: boolean
        readyForRecalculation:
          type: boolean
        coverages:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ComCoverageItem"
        options:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ComCoverageItem"
        allCoverages:
          type: array
          items:
            $ref: "#/components/schemas/ComCoverageItem"
        surcharge:
          type: boolean
        offerType:
          type: string
    ComPartner:
      type: object
      properties:
        advisorNbrPresent:
          type: boolean
        advisorInd:
          type: boolean
        advisor:
          $ref: "#/components/schemas/ComAdvisor"
    ComPayment:
      type: object
      properties:
        comPaymentRequest:
          $ref: "#/components/schemas/ComPaymentRequest"
        comPaymentResponse:
          $ref: "#/components/schemas/ComPaymentResponse"
        comPaymentPlan:
          type: string
          enum:
          - ANNUAL
          - MONTHLY
          - MONTHLY_ADVANCED
        comPaymentMethod:
          type: string
          enum:
          - ELECTRONIC_FUNDS_TRANSFER
          - CREDIT_CARD
        hppttServiceUrl:
          type: string
        insuredGroupRated:
          type: string
        displayPaymentAtSource:
          type: boolean
        profileIdPlanB:
          type: string
        profileIdPlanE:
          type: string
        eligibleOnlyForPlanC:
          type: boolean
    ComPaymentAccountHolder:
      type: object
      properties:
        name:
          type: string
        authorization:
          type: boolean
    ComPaymentAtSource:
      type: object
      properties:
        membershipNumber:
          type: string
        badgeNumber:
          type: string
        employeeNumber:
          type: string
        socialInsuranceNumber:
          type: string
        ministryNumber:
          type: string
        employeeIndex:
          type: integer
          format: int32
    ComPaymentBank:
      type: object
      properties:
        bankAccountHolderName:
          type: string
        otherBankAccountHolder:
          $ref: "#/components/schemas/ComPaymentAccountHolder"
        transit:
          type: string
        institution:
          type: string
        accountNumber:
          type: string
        accountNumberConfirm:
          type: string
    ComPaymentCC:
      type: object
      properties:
        creditCardCompany:
          type: string
          enum:
          - MASTERCARD
          - VISA
        cardHolderName:
          type: string
        otherCreditCardAccountHolder:
          $ref: "#/components/schemas/ComPaymentAccountHolder"
        cardMaskedNumber:
          type: string
        cardExpiryMonth:
          type: string
        cardExpiryYear:
          type: string
        cardToken:
          type: string
        cardBinRange:
          type: string
        cardCVV:
          type: string
        tokenizationLang:
          type: string
    ComPaymentRequest:
      type: object
      properties:
        billingPlan:
          type: string
          enum:
          - PLAN_A
          - PLAN_B
          - PLAN_C
          - PLAN_E
        comPaymentBank:
          $ref: "#/components/schemas/ComPaymentBank"
        comPaymentCC:
          $ref: "#/components/schemas/ComPaymentCC"
        comPaymentAtSource:
          $ref: "#/components/schemas/ComPaymentAtSource"
    ComPaymentResponse:
      type: object
      properties:
        billingPlan:
          type: string
          enum:
          - PLAN_A
          - PLAN_B
          - PLAN_C
          - PLAN_E
    ComQuoteCalculationDetails:
      type: object
      properties:
        quotationTaxPercentage:
          type: number
          format: double
        policyTermInMonths:
          type: integer
          format: int32
        annualAmount:
          type: number
          format: double
        annualAmountTaxes:
          type: number
          format: double
        annualTaxableAmt:
          type: number
          format: double
        annualAmountWithTaxes:
          type: number
          format: double
        fullTermPremiumAmt:
          type: number
          format: double
        fullTermPremiumTaxableAmt:
          type: number
          format: double
        fullTermPremiumTaxes:
          type: number
          format: double
        fullTermPremiumWithTaxes:
          type: number
          format: double
        monthlyPayment:
          type: number
          format: double
        monthlyPaymentWithTaxes:
          type: number
          format: double
        firstMonthlyPayment:
          type: number
          format: double
        firstMonthlyPaymentWithTaxes:
          type: number
          format: double
        monthlyPaymentsEligible:
          type: boolean
        anyPaymentsEligible:
          type: boolean
        hasSurcharge:
          type: boolean
        surchargePercentageApplied:
          type: number
          format: double
        fullTermSurchargeAmount:
          type: number
          format: double
        fullTermPremiumWithTaxesAndSurcharge:
          type: number
          format: double
        monthlyPaymentWithTaxesAndSurcharge:
          type: number
          format: double
        firstMonthlyPaymentWithTaxesAndSurcharge:
          type: number
          format: double
        monthlySurchargeAmount:
          type: number
          format: double
    ComRelatedContractInfo:
      type: object
      properties:
        comRoadBlocks:
          type: array
          items:
            $ref: "#/components/schemas/ComRoadBlock"
        annualPremium:
          type: number
          format: double
        relatedQuoteNumber:
          type: string
        currentQuoteUuid:
          type: string
        dataSourceBackEndCode:
          type: string
    ComRoadBlock:
      type: object
      properties:
        roadBlockCode:
          type: string
        roadBlockType:
          type: string
          enum:
          - HARD
          - SOFT
    ComSavingItem:
      type: object
      properties:
        savingName:
          type: string
          enum:
          - DUOFLEX
          - MULTIVEHICLE
          - ANTITHEFT
          - UBI
          - YOUNG_DRIVERS
          - NO_INFRACTIONS
          - OCCASIONAL_DRIVER_LIVING_OUTSIDE
          - SYNCHRO
          - STOLEN_VEHICLE_TRACKING_SYSTEM
          - INTENSIVE_ANTITHEFT_MARKING
          - MY_HOME_AND_AUTO
          - LOYALTY
          - RETIREE
          - GRADUATED_LICENSING
          - OCCASIONAL_DRIVER_STUDYING_FULLTIME
          - GOOD_DRIVER
          - HYBRID_VEHICLE
          - EXPERIENCED_DRIVER
          - OCCASIONAL_DRIVER_CONVICTION_FREE
          - PRINCIPAL_DRIVER_CONVICTION_FREE
          - GOOD_DRIVING_RECORD
          - OCCUPATIONAL
          - POST_SECONDARY_STUDENT
        valueType:
          type: string
        editable:
          type: boolean
        choices:
          type: array
          items:
            $ref: "#/components/schemas/ComItemChoice"
        coverageValue:
          type: string
        eligible:
          type: boolean
        coverageInternalCode:
          type: string
    ComSegmentation:
      type: object
      properties:
        callUsPhone:
          type: string
        softRoadblockCallUsPhone:
          type: string
        hardRoadblockCallUsPhone:
          type: string
        specialRoadblockCallUsPhone:
          type: string
        displayChat:
          type: boolean
        displayAppForm:
          type: boolean
        targetSegment:
          type: string
        purchaseButtonLabel:
          type: string
        displayPromotion:
          type: boolean
        displayPartnershipConsent:
          type: boolean
        quickQuoteStep1CallUsPhone:
          type: string
        quickQuoteStep2CallUsPhone:
          type: string
        quickQuoteRoadblockCallUsPhone:
          type: string
        partnershipCode:
          type: string
        displayHomeQuoteLink:
          type: boolean
        bindCallUsPhone:
          type: string
    ComState:
      type: object
      properties:
        currentState:
          type: string
          enum:
          - INITIAL
          - VEHICLE
          - OFFER
          - BIND
          - DRIVER
          - VEHICLE_USAGE
          - ADDRESS_CHANGE
          - PURCHASE
          - INVALID_OFFER
        previousState:
          type: string
          enum:
          - INITIAL
          - VEHICLE
          - OFFER
          - BIND
          - DRIVER
          - VEHICLE_USAGE
          - ADDRESS_CHANGE
          - PURCHASE
          - INVALID_OFFER
        selectedOfferType:
          type: string
          enum:
          - C
          - P
          - V
          - O
          - M
          - "Y"
          - R
        canAddDriver:
          type: boolean
        canRemoveDriver:
          type: boolean
        canAddVehicle:
          type: boolean
        canRemoveVehicle:
          type: boolean
        canRate:
          type: boolean
        canBind:
          type: boolean
        canShowUsage:
          type: boolean
        policyEffectiveLesst30days:
          type: boolean
        showPreviousAddress:
          type: boolean
        showNoHit:
          type: boolean
        ignoreNoHit:
          type: boolean
        usageDisplayedInVehicle:
          type: boolean
        usageDisplayedInDriver:
          type: boolean
        dataChanged:
          type: boolean
        hasOffer:
          type: boolean
        monthlyPaymentsEligible:
          type: boolean
        canDisplayUBI:
          type: boolean
        useUBI20:
          type: boolean
        roadblockInd:
          type: boolean
        isRetrievable:
          type: boolean
        firstRate:
          type: boolean
        isInBind:
          type: boolean
        uploaded:
          type: boolean
        offerTypes:
          type: array
          items:
            type: string
            enum:
            - C
            - P
            - V
            - O
            - M
            - "Y"
            - R
        transactionStatus:
          type: string
          enum:
          - OK
          - ERROR
          - FOUND
          - NOT_FOUND
        agent:
          type: boolean
        goodDriver:
          type: boolean
    ComValidationError:
      type: object
      properties:
        errorCode:
          type: string
          enum:
          - FORMAT_ERROR
          - MANDATORY_FIELD
          - INVALID_FIELD
          - MUNICIPALITY_NOT_FOUND
          - MUNICIPALITY_OUTSIDE_PROVINCE
          - GROUP_CODE_INVALID
          - EXISTING_CLIENT
          - EXISTING_QUOTE
          - BIND_COMPLETE
          - SINGLE_ID_OTHER
          - CANCELLED_CLIENT
          - CANCELLED_CRITICAL_CLIENT
          - CANCELLED_NON_CRITICAL_CLIENT
          - VIN_INVALID
          - ASSIGN_DRIVER
          - DRIVER_LICENSE_NUMBER_MANDATORY
          - DRIVER_LICENSE_NUMBER_GENERIC
          - DRIVER_LICENSE_NUMBER_INVALID
          - CVD_MATCH
          - CVD_NOT_MATCH
          - INVALID_STATE
          - OFFER_WARNING_A_LIABILITY
          - OFFER_WARNING_B_AUTOCOMFORT_ADDED_WITHOUT_DEDUCTIBLES
          - OFFER_WARNING_C1_COLLISION_REMOVED_WITHOUT_AUTOCOMFORT
          - OFFER_WARNING_C2_COMPREHENSIVE_REMOVED_WITHOUT_AUTOCOMFORT
          - OFFER_WARNING_D_COMPREHENSIVE_REMOVED_WITHOUT_COLLISION
          - OFFER_WARNING_E_COMPREHENSIVE_REMOVED_WITHOUT_COLLISION_AND_AUTOCOMFORT
          - OFFER_WARNING_F_COLLISION_ADDED_WITHOUT_COMPREHENSIVE
          - NO_MATCH_FOUND
          - MULTIPLE_MATCH_FOUND
          - EXPIRED_QUOTE
          - BLOCKED_EMAIL
          - INVALID_PROVINCE
          - REPORT_NOT_FOUND
          - REPORT_IN_ERROR
          - REPORT_INVALID_INPUT
        errorField:
          type: string
          enum:
          - DRIVER_FIRST_NAME
          - DRIVER_LAST_NAME
          - DRIVER_GROUP_CODE
          - DRIVER_SINGLE_ID
          - MUNICIPALITY_INFO_POSTAL_CODE
          - DRIVER_EMAIL
          - VEHICLE_VIN
          - ASSIGN_DRIVER
          - DRIVER_LICENSE_NUMBER
          - SIN_NUMBER
          - RETRIEVE_QUOTE
        message:
          type: string
    ComVehicle:
      type: object
      properties:
        persisted:
          type: boolean
        plpId:
          type: integer
          format: int64
        vehicleId:
          type: integer
          format: int32
        webMsgId:
          type: integer
          format: int32
        year:
          type: string
        vehicleModel:
          $ref: "#/components/schemas/ComVehicleModel"
        conditionWhenBought:
          type: string
        trackingSystem:
          type: boolean
        otherAntiTheftDeviceIndicator:
          type: boolean
        trackingSystemCode:
          type: string
        intensiveEngravingCode:
          type: string
        leased:
          type: boolean
        odometerReading:
          type: string
        modified:
          type: boolean
        modifiedForPerformance:
          type: boolean
        modificationWorth:
          type: string
        parkingTypeCode:
          type: string
        currentOrSoonVehicleHolder:
          type: boolean
        winterTire:
          type: boolean
        intensiveEngraving:
          type: boolean
        engineImmobiliser:
          type: boolean
        electronicAlarmSystem:
          type: boolean
        unrepairedDamage:
          type: boolean
        riskType:
          type: string
        riskSubType:
          type: string
        identificationNumber:
          type: string
        kmUsedOutsideOfProvincePrivate:
          type: string
        kmUsedOutsideOfProvinceBusiness:
          type: string
        usedOutsideProvinceOrCountryInd:
          type: boolean
        annualKmDriven:
          type: string
        registerOwner:
          type: integer
          format: int32
        otherRegisteredOwnerIndicator:
          type: boolean
        secondRegisterOwner:
          type: integer
          format: int32
        principalDriver:
          type: integer
          format: int32
        purchaseDate:
          $ref: "#/components/schemas/ComDate"
        purchaseValue:
          type: string
        financedInd:
          type: boolean
        financingCompany:
          $ref: "#/components/schemas/ComFinancingCompany"
        primaryVehicleUsage:
          type: string
        drivenWorkOrSchoolInd:
          type: boolean
        workOrSchoolKm:
          type: string
        businessKm:
          type: string
        offerSet:
          type: array
          items:
            $ref: "#/components/schemas/ComOffer"
          uniqueItems: true
        currentOffer:
          $ref: "#/components/schemas/ComOffer"
        savings:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ComSavingItem"
        multiVehicleDiscountEligibilityInd:
          type: boolean
        modificationCount:
          type: string
        modificationType:
          type: string
        registeredInsideProvince:
          type: boolean
        rentedLeasedUsedCarryingPassengers:
          type: boolean
        rateGroups:
          type: string
        customerValueIndexInformations:
          type: string
        firstLinePolicyNbr:
          type: string
        normalRadiusKm:
          type: integer
          format: int32
        grossVehicleWeightQty:
          type: number
          format: double
        vehicleBusinessUse:
          type: string
        commercialUsageCategoryCd:
          type: string
        commercialUsageCd:
          type: string
        commercialUsageSpecificCd:
          type: string
        maximumRadiusKm:
          type: integer
          format: int32
        midHaulingDaysPerMonth:
          type: integer
          format: int32
    ComVehicleModel:
      type: object
      properties:
        code:
          type: string
        make:
          type: string
        makeModelAbbreviation:
          type: string
        model:
          type: string
    CommunicationObjectModel:
      type: object
      properties:
        comEvent:
          $ref: "#/components/schemas/ComEvent"
        policyVersionId:
          type: integer
          format: int64
        uuId:
          type: string
        quotationValidityExpiryDate:
          $ref: "#/components/schemas/ComDate"
        policyVersionXML:
          type: string
        agreementNumber:
          type: string
        context:
          $ref: "#/components/schemas/ComContext"
        state:
          $ref: "#/components/schemas/ComState"
        comPayment:
          $ref: "#/components/schemas/ComPayment"
        quoteCalculationDetails:
          $ref: "#/components/schemas/ComQuoteCalculationDetails"
        vehicles:
          type: array
          items:
            $ref: "#/components/schemas/ComVehicle"
        drivers:
          type: array
          items:
            $ref: "#/components/schemas/ComDriver"
        policyEffectiveDate:
          $ref: "#/components/schemas/ComDate"
        roadblock:
          type: array
          items:
            $ref: "#/components/schemas/ComRoadBlock"
        roadblockDebugInfos:
          type: string
        validationErrors:
          type: array
          items:
            $ref: "#/components/schemas/ComValidationError"
        cifClientId:
          type: integer
          format: int64
        sendDocumentsByMailInd:
          type: boolean
        segmentation:
          $ref: "#/components/schemas/ComSegmentation"
        comPartner:
          $ref: "#/components/schemas/ComPartner"
        policyAssignDate:
          type: string
        creditScoreStatus:
          type: string
        firstRatingDate:
          $ref: "#/components/schemas/ComDate"
        creditScoreDebugInformations:
          type: string
        onHolidayOpeningHours:
          type: boolean
        holidays:
          type: array
          items:
            type: string
            format: date-time
        comCallMe:
          $ref: "#/components/schemas/ComCallMe"
        policyDiscountCode:
          type: string
          enum:
          - HOMEOWNER
          - TENANT
          - CONDOMINIUM
          - DUOFLEX
          - SYE
        comRelatedContractInfo:
          $ref: "#/components/schemas/ComRelatedContractInfo"
    ModelDTO:
      type: object
      properties:
        code:
          type: string
        value:
          type: string
    MakeDTO:
      type: object
      properties:
        code:
          type: string
        value:
          type: string
