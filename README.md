# autoquote-backend [![Build and Release](https://githubifc.iad.ca.inet/lab-se/autoquote-backend/actions/workflows/build-main.yaml/badge.svg)](https://githubifc.iad.ca.inet/lab-se/autoquote-backend/actions/workflows/build-main.yaml)

### Grafana dashboard ([Prometheus & Grafana](https://confluence.tooling.intactfc.cloud/pages/viewpage.action?pageId=88245410))
- [JMX info for Springboot](https://isdatadisplay.iad.ca.inet/d/Car3K3RGk/template-springboot-by-pod?orgId=1&var-datasource=ocp-np-b&var-namespace=quoters-intg&var-pod=my-perfect-springboot-app-1-lhqxf&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All&var-datasourceappmon=ocp-np-b-appmon&from=now-1h&to=now)

## Util links
- [Installation](./Installation.md)
- [Config](./Config.md)
- [Dependencies](./Dependencies.md)
- [Contributing](./Contributing.md)
