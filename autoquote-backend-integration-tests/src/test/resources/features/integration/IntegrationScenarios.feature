@featureTag
Feature: Integration feature file

  These scenarios simply need to validate that the service is reachable in the given environment and that the service
  can communicate with its dependencies. It is important to note that the '@ZKey=' annotations have are linked to dummy
  test cases, for any real test cases an appropriate test case must be created in Zephyr for test reporting
  functionality to work.

  @ZKey=ACQ-T14111  @scenarioTag
  Scenario Outline: Greeting request with a right name
    Given the user's name is <name>
    When the user requests a greeting from the service
    Then the HTTP response code should be 200
    And the greeting message should be "Hello, <greetingVar>!"

    Examples:
      | name    | greetingVar |
      | Nick    | Nick        |
      | <PERSON> | <PERSON>     |


@ZKey=ACQ-T14112  @scenarioTag
  Scenario Outline: Greeting request with an invalid request returns a 206 status code.
    Given the user's name is <name>
    When the user requests a greeting from the service
    Then the HTTP response code should be 206

    Examples:
      | name    |
      | NULL34$   |