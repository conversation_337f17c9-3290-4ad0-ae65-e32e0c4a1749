package intact.test.template.integration.glue;

import intact.test.integration.glue.steps.AbstractValidationSteps;
import intact.test.template.integration.context.TestContext;
import intact.test.template.integration.models.ResponseDTO;
import io.cucumber.java.en.Then;

import static org.hamcrest.Matchers.equalTo;

public class AssertionSteps extends AbstractValidationSteps<TestContext> {

  private final TestContext context;

  public AssertionSteps(TestContext context) {
    super(context);
    this.context = context;
  }

  @Then("the greeting message should be {string}")
  public void verifyMessage(String expected) {
    assertWithMessage("Incorrect body in response", context.getLastResponseDTO(ResponseDTO.class).getData().getMessage(),
        equalTo(expected));
  }
}
