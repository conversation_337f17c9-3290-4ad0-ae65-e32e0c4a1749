package intact.test.template.integration;

import io.cucumber.picocontainer.PicoFactory;
import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
import org.testng.annotations.DataProvider;

@CucumberOptions(
  plugin = {
          "json:target/cucumber-report/cucumber.json",
          "junit:target/cucumber-report/cucumber.xml",
          "io.cucumber.core.plugin.PrettyParallelOutput:CONCISE",
          "io.cucumber.core.plugin.SummaryReport",
          "io.cucumber.core.plugin.ZephyrLink",
          "rerun:target/rerun/rerun.txt"
  },
  features = "classpath:features/integration",
  glue = {
          "intact.test.template.integration.glue",
          "intact.test.base.glue",
          "intact.test.integration.glue"
  },
  tags = "",
  objectFactory = PicoFactory.class)
public class RunCucumberIntegration extends AbstractTestNGCucumberTests {

  @Override
  @DataProvider(parallel = true)
  public Object[][] scenarios() {
    return super.scenarios();
  }
}
