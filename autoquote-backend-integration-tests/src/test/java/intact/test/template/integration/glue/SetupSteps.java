package intact.test.template.integration.glue;

import intact.test.integration.glue.steps.AbstractSteps;
import intact.test.template.integration.context.TestContext;
import intact.test.template.integration.models.RequestDTO;
import io.cucumber.java.en.Given;

public class SetupSteps extends AbstractSteps<TestContext> {

  public SetupSteps(TestContext context) {
    super(context);
  }

  @Given("the user's name is {nullWord}")
  public void setName(String name) {
    RequestDTO requestDTO = new RequestDTO();
    requestDTO.setData(name);
    context.getCurrentRequestWrapper().setRequestDTO(requestDTO);
  }

}
