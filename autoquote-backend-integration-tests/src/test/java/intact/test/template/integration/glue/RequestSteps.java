package intact.test.template.integration.glue;

import intact.test.integration.glue.steps.AbstractRequestSteps;
import intact.test.template.integration.api.GreetingAPI;
import intact.test.template.integration.context.TestContext;
import io.cucumber.java.en.When;

public class RequestSteps extends AbstractRequestSteps<TestContext> {
  private final GreetingAPI greetingAPI;

  public RequestSteps(TestContext context) {
    super(context);
    this.greetingAPI = new GreetingAPI(context);
  }

  @When("the user requests a greeting from the service")
  public void sendValidRequest() {
    greetingAPI.validRequest().execute();
  }

}
