package intact.test.template.integration;

import io.cucumber.testng.AbstractTestNGCucumberTests;
import io.cucumber.testng.CucumberOptions;
import org.testng.annotations.DataProvider;

@CucumberOptions(
  plugin = {"json:target/cucumber-report/cucumber.json", "io.cucumber.core.plugin.PrettyParallelOutput:CONCISE",
      "io.cucumber.core.plugin.SummaryReport", "io.cucumber.core.plugin.ZephyrLink", "rerun:target/rerun/rerun.txt"},
  features = "@target/rerun/rerun.txt",
    glue = {"intact.test.template.integration.glue", "intact.test.base.glue", "intact.test.integration.glue"})

public class ReRunCucumberIntegration extends AbstractTestNGCucumberTests {

  @Override
  @DataProvider(parallel = true)
  public Object[][] scenarios() {
    return super.scenarios();
  }
}
