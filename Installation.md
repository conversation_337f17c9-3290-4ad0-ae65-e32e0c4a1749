## Installation

### Pre-Requisite

The application requires JDK 21 and maven 3.6.3

IntelliJ users must install the `Lombok Plugin`

### Webquote Driver External Convictions API Installation

To generate the application use the following command.

`mvn install`

To run the application, you can start it from your IDE or use.

1- Go at the root of the module `autoquote-backend`

`mvn spring-boot:run -Dspring.profiles.active=local`

or

2- Go to `autoquote-backend-web/target`

`java -jar autoquote-backend-web-${version}.jar --spring.profiles.active=local`

or

3- `Run > Edit Configuration > Spring Boot > Application (default name)`

set VM options: `-Dspring.profiles.active=local`

run the spring-boot application
