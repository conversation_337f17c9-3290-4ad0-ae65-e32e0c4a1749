<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Integration UAT-CP" type="TestNG">
    <module name="autoquote-backend-integration-tests" />
    <shortenClasspath name="NONE" />
    <useClassPathOnly />
    <option name="SUITE_NAME" value="" />
    <option name="PACKAGE_NAME" value="intact.test.template.integration" />
    <option name="MAIN_CLASS_NAME" value="intact.test.template.integration.RunCucumberIntegration" />
    <option name="GROUP_NAME" value="" />
    <option name="TEST_OBJECT" value="CLASS" />
    <option name="VM_PARAMETERS" value="-ea -Dtest-e2e-environment=uat-cp -Ddataproviderthreadcount=5 -DPPP.OutputStyle=CONCISE" />
    <option name="PARAMETERS" value="" />
    <option name="OUTPUT_DIRECTORY" value="" />
    <option name="TEST_SEARCH_SCOPE">
      <value defaultName="moduleWithDependencies" />
    </option>
    <option name="PROPERTIES_FILE" value="" />
    <properties />
    <listeners />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>