<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ComponentTest" type="JUnit" factoryName="JUnit">
    <module name="autoquote-backend-web" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="ALTERNATIVE_JRE_PATH" value="temurin-17" />
    <option name="PACKAGE_NAME" value="intact.lab.autoquote.backend.component" />
    <option name="MAIN_CLASS_NAME" value="intact.lab.autoquote.backend.component.RunCucumberComponentTest" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea -DPPP.OutputStyle=CONCISE" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>