@featureTag
Feature: Component test feature file

  These scenarios test all possible functionalities in the service leaving only communication tests for the integration level.
  It is important to note that the '@ZKey=' annotations have are linked to dummy test cases, for any real test cases an
  appropriate test case must be created in Zephyr for test reporting functionality to work.

  @ZKey=SAN-T14
  Scenario Outline: Get greeting with name - <name>
    Given the user's name is <name>
    When the user sends a GET request to the endpoint
    Then the GreetingAPI response code should be 200
    And the greeting message should be "Hello, <name>!"

    Examples:
      | name    |
      | Benoit  |
      | Raymond |

  @ZKey=SAN-T15
  Scenario Outline: Get greeting with wrong name - <name>
   Given the user's name is <name>
   When the user sends a GET request to the endpoint
   Then the GreetingAPI response code should be 206

   Examples:
         | name    |
         | <PERSON>&@ |
         | John23  |
         | Jo2e    |
         | ^Jules  |

  @ZKey=SAN-T16
  Scenario: Endpoint doesn't exist
    When the user sends a POST request to the endpoint
    Then the GreetingAPI response code should be 405