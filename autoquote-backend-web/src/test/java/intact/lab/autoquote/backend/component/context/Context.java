package intact.lab.autoquote.backend.component.context;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import intact.test.component.context.ComponentTestContext;
import io.cucumber.spring.CucumberContextConfiguration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

@Data
@ActiveProfiles("local")
@SpringBootTest
@AutoConfigureMockMvc
@CucumberContextConfiguration
@EqualsAndHashCode(callSuper = true)
public class Context extends ComponentTestContext {

  @Autowired
  private MockMvc mockMvc;
  private ResultActions resultActions;
  private ResponseDTO responseDTO;

  @SneakyThrows
  public void callEndpoint(MockHttpServletRequestBuilder requestBuilder) {
    resultActions = this.mockMvc.perform(requestBuilder).andDo(print());
    String responseBody = resultActions.andReturn().getResponse().getContentAsString();
    if (!responseBody.isEmpty() && !responseBody.isBlank()) {
      ObjectMapper mapper = new ObjectMapper();
      responseDTO = mapper.readValue(responseBody, new TypeReference<>() {});
    }
  }

  @Override
  public int getResponseStatusCode() {
    return resultActions.andReturn().getResponse().getStatus();
  }
}


