package intact.lab.autoquote.backend.converter.com.impl;


import com.intact.com.offer.ComOffer;
import intact.lab.autoquote.backend.common.dto.OfferDTO;
import intact.lab.autoquote.backend.converter.impl.COMOfferConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class COMOfferConverterTest {

	@InjectMocks
	private COMOfferConverter comOfferConverter;

	@Test
	public void testToDTO() throws Exception {
		ComOffer comOffer = ConverterTestUtil.buildComOffer();
		OfferDTO offerDTO = this.comOfferConverter.toDTO(comOffer);
		assertNotNull(offerDTO);
        assertEquals("CUSTOM", offerDTO.getOfferCode());
	}

}
