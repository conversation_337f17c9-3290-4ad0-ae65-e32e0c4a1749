package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ModelValidationRuleTest {

	@InjectMocks
	private ModelValidationRule validationRule;

	@Mock
	private IVehicleFacade vehicleFacade;

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new VehicleDTO(), "vehicle");
	}

	@Test
	public void testValidate_ValidDomain_ShouldPass() {
		List<ModelDTO> validValues = buildValueDomain("accent");

		when(this.vehicleFacade.getVehicleModels(anyString(), anyString(), anyString(), anyString())).thenReturn(validValues);

		this.validationRule.validate(2020, "hyundai", "accent", null, "QC", "EN", this.errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_InvalidDomain_ShouldFail() {
		List<ModelDTO> validValues = buildValueDomain("elantra");

		when(this.vehicleFacade.getVehicleModels(anyString(), anyString(), anyString(), anyString())).thenReturn(validValues);

		this.validationRule.validate(2020, "hyundai", "accent", null, "QC", "EN", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "model", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode());
	}

	private List<ModelDTO> buildValueDomain(String value) {
		List<ModelDTO> validModels = new ArrayList<>();
		ModelDTO vv = new ModelDTO(value, value);
		validModels.add(vv);
		return validModels;
	}
}
