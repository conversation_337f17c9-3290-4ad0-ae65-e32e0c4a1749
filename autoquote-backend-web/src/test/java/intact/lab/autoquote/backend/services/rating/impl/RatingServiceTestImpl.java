/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.rating.IPremiumDerivationService;

/**
 * 
 * A test implementation of the autoquote/intact/rating/RatingService class.
 * Used to access the class' protected and private methods.
 * <AUTHOR>
 *
 */
public class RatingServiceTestImpl extends RatingService {
	
	/*********************************************************************************************
	 * Implementations needed for the parent IRatingService class. Will not be used by the tests.
	 *********************************************************************************************/
	@Override
	public PolicyVersion rateOffer(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected IPremiumDerivationService getPremiumDeviationService() {
		// TODO Auto-generated method stub
		return null;
	}

}
