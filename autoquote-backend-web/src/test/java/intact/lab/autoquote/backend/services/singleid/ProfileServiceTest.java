/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid;

import com.ing.canada.cif.domain.IBody;
import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.domain.IClientSpokenLanguage;
import com.ing.canada.cif.domain.IGeneral;
import com.ing.canada.cif.domain.IName;
import com.ing.canada.cif.domain.enums.ClientRelationTypeEnum;
import com.ing.canada.cif.domain.helpers.ClientHelper;
import com.ing.canada.cif.domain.impl.Addresses;
import com.ing.canada.cif.domain.impl.ClientRelationTypes;
import com.ing.canada.cif.domain.impl.ClientRelations;
import com.ing.canada.cif.domain.impl.IClientCreationParameters;
import com.ing.canada.cif.service.IClientRelationService;
import com.ing.canada.cif.service.IClientService;
import com.ing.canada.cif.util.CompanyUtils;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.AddressTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CountryCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LicenseJurisdictionCodeEnum;
import com.ing.canada.plp.domain.enums.MaritalStatusCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.RelationshipToNamedInsuredCodeEnum;
import com.ing.canada.plp.domain.enums.SexCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IConsentHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.domain.Client;
import com.ing.canada.singleid.accessmanager.domain.IState;
import com.ing.canada.singleid.accessmanager.domain.SecureDomain;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.ing.canada.singleid.accessmanager.service.IClientAccountService;
import com.ing.canada.singleid.accessmanager.service.IUserAccountService;
import com.ing.canada.singleid.accessmanager.service.MatchAccountParams;
import com.ing.canada.singleid.accessmanager.service.factory.AccessManagerServiceFactory;
import com.ing.canada.singleid.accessmanager.service.factory.IAccessManagerServiceFactory;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.common.utils.PartnerUtil;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.Configurator;
import intact.lab.autoquote.backend.services.singleid.impl.ProfileService;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;


import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static com.ing.canada.plp.domain.enums.LanguageCodeEnum.ENGLISH;
import static com.ing.canada.plp.domain.enums.LanguageCodeEnum.FRENCH;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * This class test {@link ProfileService ProfileService}
 *
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class ProfileServiceTest {

	/** Class to be tested.*/
	@InjectMocks
	private ProfileService profileService;

	/** Test objects **/
	private Addresses addressShort;

	private Addresses addressLong;

	private DriverComplementInfo currentTestDCI;

	private Party currentTestParty;

	private PolicyVersion currentTestPV;

	/** Mocks **/
	@Mock
	private IClientAccountService mockClientAccountService;

	@Mock
	private IClientService mockClientService;

	@Mock
	private IClientRelationService mockClientRelationService;

	@Mock
	private ICommonBusinessProcess mockCommonBusinessProcess;

	@Mock
	private Configurator mockConfigurator;

	@Mock
	private IConsentHelper mockConsentHelper;

	@Mock
	private PartnerUtil mockPartnerHelper;

	@Mock
	private IPartyHelper mockPartyHelper;

	@Mock
	private IPartyService mockPartyService;

	@Mock
	private ISingleIdService mockSingleIdService;

	@Mock
	private IUserAccountService mockUserAccountService;

	private static MockedStatic<ClientHelper> mockedClientHelper;
	private static MockedStatic<CompanyUtils> mockedCompanyUtils;
	private static MockedStatic<AccessManagerServiceFactory> mockedAccessManagerServiceFactory;

	@BeforeAll
	public static void init() {
		mockedClientHelper = mockStatic(ClientHelper.class);
		mockedCompanyUtils = mockStatic(CompanyUtils.class);

		mockedAccessManagerServiceFactory = mockStatic(AccessManagerServiceFactory.class);

		IAccessManagerServiceFactory mockFactory = mock(IAccessManagerServiceFactory.class);

		mockedAccessManagerServiceFactory.when(AccessManagerServiceFactory::getInstance)
				.thenReturn(mockFactory);
		when(mockFactory.getClientAccountService()).thenReturn(mock(IClientAccountService.class));
	}

	/**
	 * This method setup all the objects in order to execute the following tests
	 *
	 * @throws Exception if something goes wrong
	 */
	@BeforeEach
	public void setupTests() throws Exception {

		// Policy Version setup
		this.currentTestPV = new PolicyVersion(123L);
		this.currentTestPV.setLanguageOfCommunication(LanguageCodeEnum.ENGLISH);
		InsurancePolicy testInsurancePolicy = new InsurancePolicy();
		ManufacturingContext testContext = new ManufacturingContext();
		testContext.setProvince(ProvinceCodeEnum.BRITISH_COLUMBIA);
		testInsurancePolicy.setManufacturingContext(testContext);
		testInsurancePolicy.setAgreementNumber(""); // To avoid debug exception
		this.currentTestPV.setInsurancePolicy(testInsurancePolicy);

		// Party setup
		this.currentTestParty = new Party();
		this.currentTestParty.setFirstName("Test");
		this.currentTestParty.setLastName("Test");
		GregorianCalendar birthDate = new GregorianCalendar();
		birthDate.setTime(new Date());
		birthDate.add(Calendar.YEAR, -25);
		this.currentTestParty.setBirthDate(birthDate.getTime());
		this.currentTestParty.setSex(SexCodeEnum.FEMALE);
		this.currentTestParty.setPolicyVersion(this.currentTestPV);

		// Driver complement info setup
		this.currentTestDCI = new DriverComplementInfo();
		LicenseJurisdictionCodeEnum expectedLicenseCode = LicenseJurisdictionCodeEnum.BRITISH_COLUMBIA;
		String expectedLicenseNbr = "TestLicenseNumber";
		this.currentTestDCI.setLicenseNumber(expectedLicenseNbr);
		this.currentTestDCI.setLicenseJurisdiction(expectedLicenseCode);
		this.currentTestParty.setDriverComplementInfo(this.currentTestDCI);

		// Addresses setup
		this.addressShort = Addresses.createAddresse();
		this.addressShort.setStreetName("Sesame Street");
		this.addressShort.setCivicNo("123");

		this.addressLong = Addresses.createAddresse();
		this.addressLong.setStreetName("Sainte-Gabriele-De-Perpetue Rang Sud 4");
		this.addressLong.setCivicNo("123");
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressStreetEmpty() {
		this.addressShort.setStreetName("");

		assertThrows(IllegalArgumentException.class, () ->
				this.profileService.formatCifAddress(this.addressShort, FRENCH)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressStreetNull() {
		this.addressShort.setStreetName(null);

		assertThrows(IllegalArgumentException.class, () ->
				this.profileService.formatCifAddress(this.addressShort, FRENCH)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressCivicNoEmpty() {
		this.addressShort.setCivicNo("");

		assertThrows(IllegalArgumentException.class, () ->
				this.profileService.formatCifAddress(this.addressShort, FRENCH)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressCivicNoNull() {
		this.addressShort.setCivicNo(null);

		assertThrows(IllegalArgumentException.class, () ->
				this.profileService.formatCifAddress(this.addressShort, FRENCH)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddress() {
		this.profileService.formatCifAddress(this.addressShort, FRENCH);

		assertEquals("123 Sesame Street", this.addressShort.getAddrLine1());
		assertEquals(null, this.addressShort.getAddrLine2());

		assertEquals(null, this.addressShort.getUnitNbr());
		assertEquals(null, this.addressShort.getUnitType());

		assertEquals("Y", this.addressShort.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressWithAptFr() {
		this.addressShort.setUnitNbr("105");
		this.profileService.formatCifAddress(this.addressShort, FRENCH);

		assertEquals("123 Sesame Street APP. 105", this.addressShort.getAddrLine1());
		assertEquals(null, this.addressShort.getAddrLine2());

		assertEquals("105", this.addressShort.getUnitNbr());
		assertEquals("APT", this.addressShort.getUnitType());

		assertEquals("Y", this.addressShort.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressWithAptEn() {
		this.addressShort.setUnitNbr("105");
		this.profileService.formatCifAddress(this.addressShort, ENGLISH);

		assertEquals("123 Sesame Street APT. 105", this.addressShort.getAddrLine1());
		assertEquals(null, this.addressShort.getAddrLine2());

		assertEquals("105", this.addressShort.getUnitNbr());
		assertEquals("APT", this.addressShort.getUnitType());

		assertEquals("Y", this.addressShort.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressWithAptSecondLine() {
		this.addressShort.setStreetName(this.addressShort.getStreetName() + " a bit longer");
		this.addressShort.setUnitNbr("105");
		this.profileService.formatCifAddress(this.addressShort, FRENCH);

		assertEquals("123 Sesame Street a bit longer", this.addressShort.getAddrLine1());
		assertEquals("APP. 105", this.addressShort.getAddrLine2());

		assertEquals("105", this.addressShort.getUnitNbr());
		assertEquals("APT", this.addressShort.getUnitType());

		assertEquals("Y", this.addressShort.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressTruncate() {
		this.profileService.formatCifAddress(this.addressLong, FRENCH);

		assertEquals("123 Sainte-Gabriele-De-Perpetue ", this.addressLong.getAddrLine1());
		assertEquals(null, this.addressLong.getAddrLine2());

		assertEquals(null, this.addressLong.getUnitNbr());
		assertEquals(null, this.addressLong.getUnitType());

		assertEquals("Y", this.addressLong.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#formatCifAddress(com.ing.canada.cif.domain.IAddress, LanguageCodeEnum)}
	 */
	@Test
	public void testformatCifAddressTruncateWithAptSecondLine() {
		this.addressLong.setUnitNbr("105");
		this.profileService.formatCifAddress(this.addressLong, FRENCH);

		assertEquals("123 Sainte-Gabriele-De-Perpetue ", this.addressLong.getAddrLine1());
		assertEquals("APP. 105", this.addressLong.getAddrLine2());

		assertEquals("105", this.addressLong.getUnitNbr());
		assertEquals("APT", this.addressLong.getUnitType());

		assertEquals("Y", this.addressLong.getStructureInd());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateForCreateProfile(PolicyVersion,
	 * 			 ApplicationEnum, Long)}
	 * Case for the test party's marital status to be SINGLE.
	 */
	@Test
	public void testPopulateForCreateProfile_SingleParty() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.SINGLE, expectedNumber, expectedDetail,
				ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.SINGLE.getCode(), returnParams.getMaritalStatus());
		assertEquals(expectedNumber, returnParams.getAddress().getCivicNo());
		assertEquals(expectedDetail, returnParams.getAddress().getStreetName());
		assertEquals(expectedDetail, returnParams.getAddress().getCity());
		assertEquals(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode(), returnParams.getAddress().getAddressType());
		assertEquals(expectedDetail, returnParams.getCsioNumber());
		assertEquals(this.currentTestParty.getFirstName(), returnParams.getFirstName());
		assertEquals(this.currentTestParty.getLastName(), returnParams.getLastName());
		assertEquals(this.currentTestParty.getBirthDate(), returnParams.getBirthDate());
		assertEquals(this.currentTestParty.getSex().getCode(), returnParams.getGender());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateForCreateProfile(PolicyVersion,
	 * 			 ApplicationEnum, Long)}
	 * Case for the test party's marital status to be MARRIED.
	 */
	@Test
	public void testPopulateForCreateProfile_MarriedParty() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.MARRIED, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.MARRIED.getCode(), returnParams.getMaritalStatus());
		assertEquals(expectedNumber, returnParams.getAddress().getCivicNo());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getStreetName());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getCity());
		assertEquals(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode(), returnParams.getAddress().getAddressType());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateForCreateProfile(PolicyVersion,
	 * 			 ApplicationEnum, Long)}
	 * Case for the test party's marital status to be COMMON_LAW.
	 */
	@Test
	public void testPopulateForCreateProfile_CommonLawParty() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.COMMON_LAW.getCode(), returnParams.getMaritalStatus());
		assertEquals(expectedNumber, returnParams.getAddress().getCivicNo());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getStreetName());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getCity());
		assertEquals(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode(), returnParams.getAddress().getAddressType());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateForCreateProfile(PolicyVersion,
	 * 			 ApplicationEnum, Long)}
	 * Case for the test party's marital status to be other than SINGLE, MARRIED or COMMON_LAW.
	 */
	@Test
	public void testPopulateForCreateProfile_OtherMaritalStatus() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.DIVORCED, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.OTHER.getCode(), returnParams.getMaritalStatus());
		assertEquals(expectedNumber, returnParams.getAddress().getCivicNo());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getStreetName());
		assertEquals(expectedAddressDetail, returnParams.getAddress().getCity());
		assertEquals(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode(), returnParams.getAddress().getAddressType());
	}

	/**
	 * Case for the language of communication to be French and for the group repository entry
	 * to have a party group description.
	 * The method is accessed through the populateForCreateProfile method by setting the quote to be QuickQuote.
	 */
	@Test
	public void testGetOccupation_FrenchWithPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.SAME_SEX_PARTNER, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.QUICK_QUOTE);

		// Setting the language of communication to French for this test.
		this.currentTestPV.setLanguageOfCommunication(FRENCH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartyGroupDescriptionFrench(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the general method to access the tested method
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(expectedOccupation, returnParams.getOccupation());
	}

	/**
	 * Case for the language of communication to be French and for the group repository entry
	 * to not have a party group description, but a sub group description.
	 * The method is accessed through the populateForCreateProfile method by setting the quote to be QuickQuote.
	 */
	@Test
	public void testGetOccupation_FrenchNoPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.SAME_SEX_PARTNER, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.QUICK_QUOTE);

		// Setting the language of communication to French for this test.
		this.currentTestPV.setLanguageOfCommunication(FRENCH);

		// Setting the group repository entry with only a sub party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartySubGroupDescriptionFrench(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the general method to access the tested method
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(expectedOccupation, returnParams.getOccupation());
	}

	/**
	 * Case for the language of communication to be English and for the group repository entry
	 * to have a party group description.
	 * The method is accessed through the populateForCreateProfile method by setting the quote to be QuickQuote.
	 */
	@Test
	public void testGetOccupation_EnglishWithPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.SAME_SEX_PARTNER, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.QUICK_QUOTE);

		// Setting the language of communication to English for this test.
		this.currentTestPV.setLanguageOfCommunication(ENGLISH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartyGroupDescriptionEnglish(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the general method to access the tested method
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(expectedOccupation, returnParams.getOccupation());
	}

	/**
	 * Case for the language of communication to be English and for the group repository entry
	 * to not have a party group description, but a sub group description.
	 * The method is accessed through the populateForCreateProfile method by setting the quote to be QuickQuote.
	 */
	@Test
	public void testGetOccupation_EnglishNoPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedAddressDetail = "Test";
		this.setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum.SAME_SEX_PARTNER, expectedNumber, expectedAddressDetail,
				ApplicationModeEnum.QUICK_QUOTE);

		// Setting the language of communication to English for this test.
		this.currentTestPV.setLanguageOfCommunication(ENGLISH);

		// Setting the group repository entry with only a sub party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartySubGroupDescriptionEnglish(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the general method to access the tested method
		IClientCreationParameters returnParams = this.profileService.populateForCreateProfile(this.currentTestPV, ApplicationEnum.OTHER, 42L);
		assertEquals(expectedOccupation, returnParams.getOccupation());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 */
	@Test
	public void testPopulateCifForUpdateProfile() {
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.RELIGIOUS, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		IBody resultMockBody = ((List<IBody>)mockClient.getBodieses()).get(0);
		IName resultMockName = ((List<IName>)mockClient.getNameses()).get(0);
		verify(resultMockBody, times(2)).setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.OTHER.getCode());
		verify(resultMockBody, times(1)).setBirthDate(this.currentTestParty.getBirthDate());
		verify(resultMockBody, times(1)).setGender(this.currentTestParty.getSex().getCode());
		verify(resultMockName, times(1)).setFirstName(this.currentTestParty.getFirstName());
		verify(resultMockName, times(1)).setLastName(this.currentTestParty.getLastName());
		verify(ClientHelper.getClientSpokenLanguage(mockClient), times(1)).setLanguageEnglish();
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch for SINGLE marital status
	 */
	@Test
	public void testPopulateCifForUpdateProfile_MaritalStatusSingle() {
		// General setup for the test, with SINGLE as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.SINGLE, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		IBody resultMockBody = ((List<IBody>)mockClient.getBodieses()).get(0);
		verify(resultMockBody, times(2)).setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.SINGLE.getCode());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch for MARRIED marital status
	 */
	@Test
	public void testPopulateCifForUpdateProfile_MaritalStatusMarried() {
		// General setup for the test, with MARRIED as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.MARRIED, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		IBody resultMockBody = ((List<IBody>)mockClient.getBodieses()).get(0);
		verify(resultMockBody, times(2)).setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.MARRIED.getCode());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch for COMMON_LAW marital status
	 */
	@Test
	public void testPopulateCifForUpdateProfile_MaritalStatusCommonLaw() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		IBody resultMockBody = ((List<IBody>)mockClient.getBodieses()).get(0);
		verify(resultMockBody, times(2)).setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.COMMON_LAW.getCode());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch where a consent is found by the consent helper.
	 */
	@Test
	public void testPopulateCifForUpdateProfile_ConsentFound() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Setting the consent to be found
		Consent testConsent = new Consent();
		testConsent.setConsentIndicator(true);
		testConsent.setEffectiveDate(new Date());
		when(this.mockConsentHelper.getConsent(any(Party.class), any(ConsentTypeCodeEnum.class))).thenReturn(testConsent);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		verify(mockClient, times(1)).setCreditScoreConsentInd("Y");
		verify(mockClient, times(1)).setCreditScoreConsentDate(testConsent.getEffectiveDate());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Test paperless preference YES
	 */
	@Test
	public void testPopulateCifForUpdateProfile_Paperless() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// setup PLP paperless preference YES
		BusinessTransaction btransaction = new BusinessTransaction();
		btransaction.setTransactionalPaperIndicator(true);
		this.currentTestPV.setBusinessTransaction(btransaction);

		// Executing the tested method
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		// validate YES answer
		verify(mockClient, times(1)).setTransactionalPaperInd("Y");
	}

	@Test
	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Test paperless preference NO
	 */
	public void testPopulateCifForUpdateProfile_NotPaperless() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// setup PLP paperless preference NO
		BusinessTransaction btransaction = new BusinessTransaction();
		btransaction.setTransactionalPaperIndicator(false);
		this.currentTestPV.setBusinessTransaction(btransaction);

		// Executing the tested method
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		// validate NO answer
		verify(mockClient, times(1)).setTransactionalPaperInd("N");
	}

	@Test
	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Test without paperless preference
	 */
	public void testPopulateCifForUpdateProfile_NoPrefPaperless() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// setup PLP paperless without preference
		BusinessTransaction btransaction = new BusinessTransaction();
		btransaction.setTransactionalPaperIndicator(null);
		this.currentTestPV.setBusinessTransaction(btransaction);

		// Executing the tested method
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		// validate null answer
		verify(mockClient, times(1)).setTransactionalPaperInd(null);
	}

	@Test
	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Test without the Transaction object that normally contains the preference
	 */
	public void testPopulateCifForUpdateProfile_PrefPaperless_NullBT() {
		// General setup for the test, with COMMON_LAW as the marital status
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// setup PLP without business transaction
		this.currentTestPV.setBusinessTransaction(null);

		// Executing the tested method
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		// validate null answer
		verify(mockClient, times(1)).setTransactionalPaperInd(null);
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch where the language of communication is French and the group repository entry
	 * has a party group description.
	 */
	@Test
	public void testPopulateCifForUpdateProfile_OccupationFrenchWithPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Setting the mock cif general to be returned by the client helper and to be used for validation
		IGeneral mockCifGeneral = mock(IGeneral.class);
		when(ClientHelper.getCurrentGeneral(any(IClient.class))).thenReturn(mockCifGeneral);

		// Setting the language of communication to French for this test.
		this.currentTestPV.setLanguageOfCommunication(FRENCH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartyGroupDescriptionFrench(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		verify(mockCifGeneral, times(1)).setOccupation(testGRE.getPartyGroupDescriptionFrench());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch where the language of communication is French and the group repository entry
	 * doesn't have a party group description, but a sub group description.
	 */
	@Test
	public void testPopulateCifForUpdateProfile_OccupationFrenchNoPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Setting the mock cif general to be returned by the client helper and to be used for validation
		IGeneral mockCifGeneral = mock(IGeneral.class);
		when(ClientHelper.getCurrentGeneral(any(IClient.class))).thenReturn(mockCifGeneral);

		// Setting the language of communication to French for this test.
		this.currentTestPV.setLanguageOfCommunication(FRENCH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartySubGroupDescriptionFrench(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		verify(mockCifGeneral, times(1)).setOccupation(testGRE.getPartySubGroupDescriptionFrench());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch where the language of communication is English and the group repository entry
	 * has a party group description.
	 */
	@Test
	public void testPopulateCifForUpdateProfile_OccupationEnglishWithPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Setting the mock cif general to be returned by the client helper and to be used for validation
		IGeneral mockCifGeneral = mock(IGeneral.class);
		when(ClientHelper.getCurrentGeneral(any(IClient.class))).thenReturn(mockCifGeneral);

		// Setting the language of communication to English for this test.
		this.currentTestPV.setLanguageOfCommunication(ENGLISH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartyGroupDescriptionEnglish(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		verify(mockCifGeneral, times(1)).setOccupation(testGRE.getPartyGroupDescriptionEnglish());
	}

	/**
	 * Test for
	 * {@link ProfileService#populateCifForUpdateProfile(PolicyVersion, IClient, Long)}
	 * Case to test the branch where the language of communication is English and the group repository entry
	 * doesn't have a party group description, but a sub group description.
	 */
	@Test
	public void testPopulateCifForUpdateProfile_OccupationEnglishNoPartyGroupDescription() {
		// General setup for the test
		String expectedNumber = "42";
		String expectedDetail = "Test";
		IClient mockClient = this.setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum.COMMON_LAW, expectedNumber,
				expectedDetail, ApplicationModeEnum.REGULAR_QUOTE);

		// Setting the mock cif general to be returned by the client helper and to be used for validation
		IGeneral mockCifGeneral = mock(IGeneral.class);
		when(ClientHelper.getCurrentGeneral(any(IClient.class))).thenReturn(mockCifGeneral);

		// Setting the language of communication to English for this test.
		this.currentTestPV.setLanguageOfCommunication(ENGLISH);

		// Setting the group repository entry with a party group description
		String expectedOccupation = "TestOccupation";
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testGRE.setPartySubGroupDescriptionEnglish(expectedOccupation);
		when(this.mockPartyHelper.findGroupRepositoryEntry(any(Party.class), any(PartyGroupTypeCodeEnum.class))).thenReturn(testGRE);

		// Executing the tested method and validating the results
		this.profileService.populateCifForUpdateProfile(this.currentTestPV, mockClient, 42L);
		verify(mockCifGeneral, times(1)).setOccupation(testGRE.getPartySubGroupDescriptionEnglish());
	}

	/**
	 * Test for
	 * {@link ProfileService#verifyProductOnFirstValidation(SecureDomain, Long, String, RoadBlockExceptionContextEnum, boolean)}
	 * Case for a SingleIdActiveProductException to be raised for BR551.
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	@Test
	public void testVerifyProductOnFirstValidation_RaiseBR551() throws AccessManagerException, SingleIdActiveProductException {

		// Executing the tested method and validating the results
		assertThrows(SingleIdActiveProductException.class, () ->
				this.profileService.verifyProductOnFirstValidation(SecureDomain.BELAIRDIRECT, 42L, "1", RoadBlockExceptionContextEnum.ADD_DRIVER, true)
		);
	}

	/**
	 * Case for a SingleIdActiveProductException to be raised for BR551.
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	@Test
	public void testVerifyProductOnFirstValidation_RaiseBR550() throws AccessManagerException, SingleIdActiveProductException {

		// Executing the tested method and validating the results
		assertThrows(SingleIdActiveProductException.class, () ->
				this.profileService.verifyProductOnFirstValidation(SecureDomain.BELAIRDIRECT, 42L, "1", RoadBlockExceptionContextEnum.ADD_DRIVER, true)
		);
	}

	/**
	 * Test for
	 * Case for a SingleIdActiveProductException to be raised for BR732_AGENT.
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	@Test
	public void testVerifyProductOnFirstValidation_RaiseBR732_AGENT() throws AccessManagerException, SingleIdActiveProductException {

		IAccessManagerServiceFactory mockFactory = mock(IAccessManagerServiceFactory.class);
		when(mockFactory.getClientAccountService()).thenReturn(mockClientAccountService);

		mockedAccessManagerServiceFactory.when(AccessManagerServiceFactory::getInstance)
				.thenReturn(mockFactory);

		// Account services stubs setup to access the desired exception
		when(this.mockClientAccountService.getAutoPolicyState(any(Long.class))).thenReturn(IState.AUTOPOLICY_CANCELLED_CRITICAL_OLD);

		// Executing the tested method and validating the results
		assertThrows(SingleIdActiveProductException.class, () ->
				this.profileService.verifyProductOnFirstValidation(SecureDomain.BELAIRDIRECT, 42L, "1", RoadBlockExceptionContextEnum.ADD_DRIVER, true)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#verifyProductOnFirstValidation(SecureDomain, Long, String, RoadBlockExceptionContextEnum, boolean)}
	 * Case for a SingleIdActiveProductException to be raised for BR732_CLIENT.
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	@Test
	public void testVerifyProductOnFirstValidation_RaiseBR732_CLIENT() throws AccessManagerException, SingleIdActiveProductException {

		// Executing the tested method and validating the results
		assertThrows(SingleIdActiveProductException.class, () ->
				this.profileService.verifyProductOnFirstValidation(SecureDomain.BELAIRDIRECT, 42L, "1", RoadBlockExceptionContextEnum.ADD_DRIVER, false)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#verifyProductOnFirstValidation{(Long, String, RoadBlockExceptionContextEnum, boolean)}
	 * Case for a SingleIdActiveProductException to be raised for BR574.
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	@Test
	public void testVerifyProductOnFirstValidation_RaiseBR574() throws AccessManagerException, SingleIdActiveProductException {

		// Executing the tested method and validating the results
		assertThrows(SingleIdActiveProductException.class, () ->
				this.profileService.verifyProductOnFirstValidation(SecureDomain.BELAIRDIRECT, 42L, "1", RoadBlockExceptionContextEnum.ADD_DRIVER, true)
		);
	}

	/**
	 * Case for the quote to already be linked to a CIF file.
	 * @throws AccessManagerException
	 */
	@Test
	public void testRemoveRelationship_QuoteAlreadyLinked() throws AccessManagerException{
		// General setup for the test
		this.currentTestPV.getInsurancePolicy().setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		when(this.mockClientService.getClientById(any(Long.class))).thenReturn(mock(IClient.class));

		// Setting the current quote to be linked to a CIF file
		this.currentTestParty.setCifClientId(654L);

		ClientRelations mockRelation = mock(ClientRelations.class);
		when(mockClientRelationService.findByClientIdRelatedTo(654L, 654L)).thenReturn(mockRelation);

		// Stubs setup
		when(this.mockCommonBusinessProcess.loadPolicyVersion(any(Long.class))).thenReturn(this.currentTestPV);
		when(this.mockPartyHelper.getDriverBySequence(any(PolicyVersion.class), any(Integer.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getNamedInsured(any(PolicyVersion.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any(Party.class))).thenReturn(mock(Address.class));

		// Executing the tested method and validating the results
		this.profileService.removeRelationShip(this.currentTestPV.getId(), ApplicationEnum.OTHER, 42);
		verify(this.mockClientRelationService, times(1)).findByClientIdRelatedTo(any(Long.class), any(Long.class));
		verify(this.mockClientRelationService, times(1)).delete(any(ClientRelations.class));
	}

	/**
	 * Test for
	 * {@link ProfileService#manageProfile(Long, RoadBlockExceptionContextEnum, ApplicationEnum)}
	 * @throws AccessManagerException
	 */
	@Test
	public void testRemoveRelationship_RaiseAccessManagerException() throws AccessManagerException{

		IAccessManagerServiceFactory mockFactory = mock(IAccessManagerServiceFactory.class);
		when(mockFactory.getClientAccountService()).thenReturn(mockClientAccountService);

		mockedAccessManagerServiceFactory.when(AccessManagerServiceFactory::getInstance)
				.thenReturn(mockFactory);

		// General setup for the test
		this.currentTestPV.getInsurancePolicy().setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		when(this.mockClientService.getClientById(any())).thenReturn(mock(IClient.class));

		// Setting the exception to be thrown
		AccessManagerException testException
				= new AccessManagerException("Exception thrown for test \"testRemoveRelationship_RaiseAccessManagerException\"");

		when(this.mockClientAccountService.matchAccount(any(MatchAccountParams.class))).thenThrow(testException);

		// Stubs setup
		when(this.mockCommonBusinessProcess.loadPolicyVersion(any(Long.class))).thenReturn(this.currentTestPV);
		when(this.mockPartyHelper.getDriverBySequence(any(PolicyVersion.class), any(Integer.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getNamedInsured(any(PolicyVersion.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any(Party.class))).thenReturn(mock(Address.class));

		// Executing the tested method and validating the results
		assertThrows(AccessManagerException.class, () ->
				this.profileService.removeRelationShip(this.currentTestPV.getId(), ApplicationEnum.OTHER, 42)
		);
	}

	/**
	 * Test for
	 * {@link ProfileService#manageProfile(Long, RoadBlockExceptionContextEnum, ApplicationEnum)}
	 * Case for a SystemException to be raised - no cif client returned by the client service.
	 * @throws AccessManagerException
	 */
	@Test
	public void testUpdateCifClient_RaiseSystemException() throws AccessManagerException{
		this.currentTestPV.getInsurancePolicy().setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);

		// Executing the tested method and validating the results
		assertThrows(SystemException.class, () ->
				this.profileService.updateProfile(this.currentTestPV, 42L)
		);
	}

	/**
	 * Private method used to do the general setup for tests calling the PopulateForCreateProfile method.
	 * @param maritalStatus The marital status to be set in the current test party
	 * @param expectedNumber String used to set expected number values
	 * @param expectedDetail String used to set expected text values
	 * @param appMode The application mode to be set for the test (regular or QuickQuote)
	 */
	private void setupGeneralPopulateForCreateProfileTest(MaritalStatusCodeEnum maritalStatus, String expectedNumber, String expectedDetail, ApplicationModeEnum appMode){
		// Setting the marital status for the test party
		this.currentTestParty.setMaritalStatus(maritalStatus);

		// Address setup to avoid nullPointerException
		Address testAddress = new Address();
		testAddress.setCivicNumber(expectedNumber);
		testAddress.setStreetName(expectedDetail);
		testAddress.setMunicipality(expectedDetail);
		testAddress.setProvince(ProvinceCodeEnum.BRITISH_COLUMBIA);
		testAddress.setCountry(CountryCodeEnum.CANADA);

		// Setting the application mode for the test
		this.currentTestPV.getInsurancePolicy().setApplicationMode(appMode);

		// Stubs setup
		when(this.mockPartyHelper.getDriverBySequence(any(PolicyVersion.class), any(Integer.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any())).thenReturn(testAddress);
		when(CompanyUtils.getCsioNumber(anyString(), anyString())).thenReturn(expectedDetail);
	}

	/**
	 * Private method used to do the general setup for tests calling the populateCifForUpdateProfile method.
	 * @param maritalStatus The marital status to be set in the current test party
	 * @param expectedNumber String used to set expected number values
	 * @param expectedDetail String used to set expected text values
	 * @param appMode The application mode to be set for the test (regular or QuickQuote)
	 * @return The mock client created for the test
	 */
	private IClient setupGeneralPopulateCifForUpdateProfileTest(MaritalStatusCodeEnum maritalStatus, String expectedNumber, String expectedDetail, ApplicationModeEnum appMode){
		this.currentTestParty.setMaritalStatus(maritalStatus);
		this.currentTestPV.getInsurancePolicy().setApplicationMode(appMode);

		// Address setup
		Address testAddress = new Address();
		testAddress.setCivicNumber(expectedNumber);
		testAddress.setStreetName(expectedDetail);
		testAddress.setMunicipality(expectedDetail);
		testAddress.setProvince(ProvinceCodeEnum.BRITISH_COLUMBIA);
		testAddress.setCountry(CountryCodeEnum.CANADA);

		// Mock client with names and bodies setup
		IClient mockClient = mock(IClient.class);
		List<IName> listNames = new ArrayList<IName>();
		listNames.add(mock(IName.class));
		List<IBody> listBodies = new ArrayList<IBody>();
		listBodies.add(mock(IBody.class));
		when(mockClient.getNameses()).thenReturn(listNames);
		when(mockClient.getBodieses()).thenReturn(listBodies);

		// Stubs setup
		when(this.mockPartyHelper.getDriverBySequence(any(PolicyVersion.class), any(Integer.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any())).thenReturn(testAddress);
		when(ClientHelper.getClientSpokenLanguage(any(IClient.class))).thenReturn(mock(IClientSpokenLanguage.class));

		return mockClient;
	}

	/**
	 * Private method to do the general setup for tests calling the manageProfileForAdditionalDriver method.
	 * @param plpRelationShip The PLP relationship type
	 * @param cifRelationShipType The CIF relationship type
	 * @param currentMockClient The mock IClient for the current test
	 * @return The client relation type created for the test
	 * @throws Exception
	 */
	private ClientRelationTypes setupGeneralManageProfileForAdditionalDriverTest(IClient currentMockClient, RelationshipToNamedInsuredCodeEnum plpRelationShip,
			ClientRelationTypeEnum cifRelationShipType) throws Exception{
		// Setting the test address to be returned by the party helper
		Address testAddress = new Address();
		testAddress.setCivicNumber("42");
		testAddress.setStreetName("Test street");
		testAddress.setMunicipality("Test city");
		testAddress.setPostalCode("V2V2V2");
		testAddress.setProvince(ProvinceCodeEnum.BRITISH_COLUMBIA);
		testAddress.setCountry(CountryCodeEnum.CANADA);

		// Setting infos related to the client, notably the named insured
		Client testAccessManagerClient = new Client();
		testAccessManagerClient.setClientID("42");

//		when(this.mockClientAccountService.matchAccount(anyString(), anyString(), any(Date.class),
//				anyString(), any(AgreementTypeCodeEnum.class), any(String[].class)))
//				.thenReturn(testAccessManagerClient);

		when(this.mockClientAccountService.matchAccount(any(MatchAccountParams.class))).thenReturn(testAccessManagerClient);

		this.currentTestDCI.setRelationshipToNamedInsured(plpRelationShip);

		ClientRelationTypes testRelationshipType = new ClientRelationTypes();
		testRelationshipType.setRelationType(cifRelationShipType.getCode());
		when(this.mockClientRelationService.findClientRelationTypeByCode(any(ClientRelationTypeEnum.class))).thenReturn(testRelationshipType);

		// Other stubs setup to avoid NullPointerExceptions
		when(this.mockCommonBusinessProcess.loadPolicyVersion(any(Long.class))).thenReturn(this.currentTestPV);
		when(this.mockPartyHelper.getDriverBySequence(any(PolicyVersion.class), any(Integer.class))).thenReturn(this.currentTestParty);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any(Party.class))).thenReturn(testAddress);
		when(this.mockPartyHelper.getNamedInsured(any(PolicyVersion.class))).thenReturn(this.currentTestParty);
		when(this.mockClientService.getClientById(any(Long.class))).thenReturn(currentMockClient);

		return testRelationshipType;
	}

	@Test
	public void testGetCIFMaritalStatusCode(){

		assertEquals("",  ProfileService.getCIFMaritalStatusCode(null));
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.SINGLE.getCode(), ProfileService.getCIFMaritalStatusCode(MaritalStatusCodeEnum.SINGLE) );
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.MARRIED.getCode(), ProfileService.getCIFMaritalStatusCode(MaritalStatusCodeEnum.MARRIED) );
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.COMMON_LAW.getCode(), ProfileService.getCIFMaritalStatusCode(MaritalStatusCodeEnum.COMMON_LAW) );
		assertEquals(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.OTHER.getCode(), ProfileService.getCIFMaritalStatusCode(MaritalStatusCodeEnum.RELIGIOUS) );

	}

	public void testFindCSIONumbers(){

		// test BNA
		assertEquals("", ProfileService.findCSIONumbers(DistributorCodeEnum.BNA, ApplicationEnum.BELAIR)[0] );

		// test BEL
		assertEquals("BEL", ProfileService.findCSIONumbers(DistributorCodeEnum.BNA, ApplicationEnum.BELAIR)[0] );

		// test GP
		assertEquals("GP", ProfileService.findCSIONumbers(DistributorCodeEnum.BNA, ApplicationEnum.GREYPOWER)[0] );


	}

}
