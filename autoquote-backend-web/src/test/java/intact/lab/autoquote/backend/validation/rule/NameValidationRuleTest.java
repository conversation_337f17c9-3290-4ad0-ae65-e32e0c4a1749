package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class NameValidationRuleTest {

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "party");
	}

	@Test
	public void testValidate_Basic_ShouldPass() {
		NameValidationRule.validate("jean", "doe", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Accents_ShouldPass() {
		NameValidationRule.validate("ÀÁÂÄÇÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜ", "âàáäçèéêëìíîïòóôöùúûü", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_OtherChars_ShouldPass() {
		NameValidationRule.validate("jean-dit jean's ", "doe-dit doe's", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_FirstNameGarbage_ShouldFail() {
		NameValidationRule.validate("jean-dit!!!!$*&?*&? jean's ", "doe-dit doe's", errors);
		ValidationTestUtils.assertHasError(this.errors, "firstName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidate_LastNameGarbage_ShouldFail() {
		NameValidationRule.validate("jean-dit jean's ", "doe-dit !!!!$*&?*&? doe's", errors);
		ValidationTestUtils.assertHasError(this.errors, "lastName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidate_FirstNameEmpty_ShouldFail() {
		NameValidationRule.validate("", "doe", errors);
		ValidationTestUtils.assertHasError(this.errors, "firstName", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_LastNameEmpty_ShouldFail() {
		NameValidationRule.validate("jean", "", errors);
		ValidationTestUtils.assertHasError(this.errors, "lastName", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_BothEmpty_ShouldFail() {
		NameValidationRule.validate("", "", errors);
		assertEquals(2, errors.getErrorCount());
	}

	@Test
	public void testValidate_FirstNameLessThanTwo_ShouldFail() {
		NameValidationRule.validate("j", "doe", errors);
		ValidationTestUtils.assertHasError(this.errors, "firstName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidate_LastNameLessThanTwo_ShouldFail() {
		NameValidationRule.validate("jean", "d", errors);
		ValidationTestUtils.assertHasError(this.errors, "lastName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidate_BothLessThanTwo_ShouldFail() {
		NameValidationRule.validate("j", "d", errors);
		assertEquals(2, errors.getErrorCount());
	}

	@Test
	public void testValidate_FirstNameMoreThan30_ShouldFail() {
		NameValidationRule.validate(StringUtils.repeat('s', 31), "doe", errors);
		ValidationTestUtils.assertHasError(this.errors, "firstName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidate_LastNameMoreThan30_ShouldFail() {
		NameValidationRule.validate("jean", StringUtils.repeat('s', 31), errors);
		ValidationTestUtils.assertHasError(this.errors, "lastName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode());
	}

	@Test
	public void testValidateUnstructured_Basic_ShouldPass() {
		NameValidationRule.validateUnstructured("jean", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidateUnstructured_Accents_ShouldPass() {
		NameValidationRule.validateUnstructured("ÀÁÂÄÇÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜâàáäçèéêëìíîïòóôöùúûü", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidateUnstructured_Numbers_ShouldPass() {
		NameValidationRule.validateUnstructured("test-1234-test inc123", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidateUnstructured_ValidGarbage_ShouldPass() {
		NameValidationRule.validateUnstructured("test-1234-test inc123.(-.&()+)", errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidateUnstructured_Empty_ShouldFail() {
		NameValidationRule.validateUnstructured("", errors);
		ValidationTestUtils.assertHasError(this.errors, "unstructuredName", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidateUnstructured_LessThanTwo_ShouldFail() {
		NameValidationRule.validateUnstructured("t", errors);
		ValidationTestUtils.assertHasError(this.errors, "unstructuredName", "UNSTRUCT_LENGTH");
	}

	@Test
	public void testValidateUnstructured_MoreThan64_ShouldFail() {
		NameValidationRule.validateUnstructured(StringUtils.repeat('s', 65), errors);
		ValidationTestUtils.assertHasError(this.errors, "unstructuredName", "UNSTRUCT_LENGTH");
	}

	@Test
	public void testValidateUnstructured_InvalidGarbage_ShouldFail() {
		NameValidationRule.validateUnstructured("test-1234-test inc123.(-.&()$%#$%!!!+)", errors);
		ValidationTestUtils.assertHasError(this.errors, "unstructuredName", "UNSTRUCT_PATTERN");
	}
}
