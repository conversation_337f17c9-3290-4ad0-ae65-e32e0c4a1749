package intact.lab.autoquote.backend.common.exception;

import intact.lab.autoquote.backend.common.dto.ErrorDTO;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.MethodNotSupportedException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Optional;

import static intact.lab.autoquote.backend.common.data.DataConstant.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Test
    void HandleUncaughtException_Should_ReturnInternalError_When_AnExceptionIsThrown() {
        Exception exception = new RuntimeException(EXCEPTION_MESSAGE);
        ResponseEntity<ResponseDTO> response = this.globalExceptionHandler.handleUncaughtException(exception);

        Optional<ErrorDTO> error = response.getBody().getErrors().stream().findFirst();
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(ExceptionUtils.getStackTrace(exception), error.get().getDescription());
    }

    @Test
    void HandleNotSupportedMethod_Should_ReturnMethodNotAllowed_When_AWrongMethodIsRequested() {
        Exception exception = new MethodNotSupportedException(METHOD_NOT_ALLOWED_MESSAGE);
        ResponseEntity<ResponseDTO> response =
                this.globalExceptionHandler.handleNotSupportedMethod(exception);

        Optional<ErrorDTO> error = response.getBody().getErrors().stream().findFirst();
        assertEquals(HttpStatus.METHOD_NOT_ALLOWED, response.getStatusCode());
        assertEquals(ExceptionUtils.getStackTrace(exception), error.get().getDescription());
    }

    @Test
    void HandleAutoQuoteVehicleException_Should_ReturnBadRequest_When_AWrongParameterPassed() {

        String errorMessage = "The vehicle year passed as a parameter is null. Please provide a valid year parameter";
        Exception exception = new AutoQuoteVehicleException(AutoQuoteVehicleException.PARAM_VEHICLE_MAKE_NULL);

        ResponseEntity<ResponseDTO> response =
                this.globalExceptionHandler.handleAutoQuoteVehicleException(exception);

        Assertions.assertNotNull(response.getBody());
        Optional<ErrorDTO> error = response.getBody().getErrors().stream().findFirst();
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals(ExceptionUtils.getStackTrace(exception), error.get().getDescription());
    }
}
