package intact.lab.autoquote.backend.services.business.vehicle;

import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.*;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.common.utils.MediatorUtil;
import intact.lab.autoquote.backend.services.business.vehicle.impl.VehicleBusinessProcess;
import intact.lab.autoquote.backend.validation.impl.GeneralValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleBusinessProcessTest {

    @InjectMocks
    private VehicleBusinessProcess vehicleBusinessProcess;

    @Mock
    private VehicleModelsService vehicleModelsService;

    @Mock
    VehicleMakesByYearService vehicleMakesByYearService;

    @BeforeEach
    public void setUp() {
        try (MockedStatic<GeneralValidator> mockedValidator = mockStatic(GeneralValidator.class);
             MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class);
             MockedStatic<MediatorUtil> mockedMediatorUtil = mockStatic(MediatorUtil.class)) {

            mockedValidator.when(() -> GeneralValidator.validateApiParameter("en", "ON"))
                    .thenAnswer(invocationOnMock -> null);

            ManufacturingContext mockContext = new ManufacturingContext();

            mockedContextUtil.when(() -> ContextUtil.loadInitialComContext(null, "ON", null,
                            null, null, null))
                    .thenReturn(new ComContext());
            mockedMediatorUtil.when(() -> MediatorUtil.convertContext(any())).thenReturn(mockContext);
        }
    }

    @Test
    void GetVehicleMakeList_Should_Return_ListOfMakeDTO() {

        List<String> mockMakes = Arrays.asList("ACURA", "AUDI");

        when(vehicleMakesByYearService.getVehicleManufacturerListByYear(any(), any(), any(), any()))
                .thenReturn(mockMakes);

        List<String> result = vehicleBusinessProcess.getVehicleManufacturerListByYear("2023", DistributionChannelCodeEnum.THROUGH_BROKERS,
                null, ProvinceCodeEnum.ONTARIO);

        assertEquals(2, result.size());
        assertEquals(mockMakes.getFirst(), result.get(0));
        assertEquals(mockMakes.get(1), result.get(1));
        verify(vehicleMakesByYearService, times(1))
                .getVehicleManufacturerListByYear(any(), any(), any(), any());
    }

    @Test
    void GetVehicleModels_Should_Return_ListOfModelDTO() {

        VehicleModel model1 = new VehicleModel();
        model1.setCode("123");
        model1.setModel("ACURA");
        VehicleModel model2 = new VehicleModel();
        model2.setCode("456");
        model2.setModel("CAMRY");
        List<VehicleModel> mockModels = Arrays.asList(model1, model2);

        when(vehicleModelsService.getVehicleModelList(any(), anyString(), anyString(), any(), any(), any()))
                .thenReturn(mockModels);

        List<VehicleModel> result = vehicleBusinessProcess.getVehicleModelList(Language.ENGLISH, "ACURA", "2023",
                DistributionChannelCodeEnum.THROUGH_BROKERS, null, ProvinceCodeEnum.ONTARIO);

        assertEquals(2, result.size());
        assertEquals("123", result.get(0).getCode());
        assertEquals("ACURA", result.get(0).getModel());
        assertEquals("456", result.get(1).getCode());
        assertEquals("CAMRY", result.get(1).getModel());
        verify(vehicleModelsService, times(1))
                .getVehicleModelList(any(), anyString(), anyString(), any(), any(), any());
    }

    @Test
    void GetVehicleCount_ShouldReturnCorrectCount() {
        PolicyVersion mockPolicyVersion = mock(PolicyVersion.class);
        InsuranceRisk risk1 = mock(InsuranceRisk.class);
        InsuranceRisk risk2 = mock(InsuranceRisk.class);
        Set<InsuranceRisk> mockInsuranceRisks = Set.of(risk1, risk2);

        when(mockPolicyVersion.getInsuranceRisks()).thenReturn(mockInsuranceRisks);

        int result = vehicleBusinessProcess.getVehicleCount(mockPolicyVersion);

        assertEquals(2, result, "Expected vehicle count to be 2");
        verify(mockPolicyVersion, times(1)).getInsuranceRisks();
    }
}
