/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.moneyProvisionPremium.SubCoveragePremium;

import java.util.List;

/**
 * 
 * Mock class for a SOM Sub Coverage Premium. Created because of lack of an implementation that can be mocked and
 * because Mockito/Powermock can't mock the interface. Implements
 * {@link SubCoveragePremium}
 * 
 * <AUTHOR>
 *
 */
public class MockSubCoveragePremium implements SubCoveragePremium {

	String BasicCoverageCode;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public Double getAdditionalCoveragePercentageSystem() {
		return null;
	}

	@Override
	public void setAdditionalCoveragePercentageSystem(Double newAdditionalCoveragePercentageSystem) {
		// noop
	}

	@Override
	public Double getAdditionalCoveragePercentageModified() {
		return null;
	}

	@Override
	public void setAdditionalCoveragePercentageModified(Double newAdditionalCoveragePercentageModified) {
		// noop
	}

	@Override
	public Double getAdditionalCoveragePercentage() {
		return null;
	}

	@Override
	public void setAdditionalCoveragePercentage(Double newAdditionalCoveragePercentage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumSystem() {
		return null;
	}

	@Override
	public void setAnnualPremiumSystem(Double newAnnualPremiumSystem) {
		// noop
	}

	@Override
	public Double getAnnualPremiumModified() {
		return null;
	}

	@Override
	public void setAnnualPremiumModified(Double newAnnualPremiumModified) {
		// noop
	}

	@Override
	public Double getAnnualPremium() {
		return null;
	}

	@Override
	public void setAnnualPremium(Double newAnnualPremium) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoring() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoring(Double newAnnualPremiumScoring) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoringRai() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoringRai(Double newAnnualPremiumScoringRai) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFlex() {
		return null;
	}

	@Override
	public void setAnnualPremiumFlex(Double newAnnualPremiumFlex) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurcharged(Double newAnnualPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCapped(Double newAnnualPremiumDiscountedSurchargedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCappedPreapproved(Double newAnnualPremiumDiscountedSurchargedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumCapped(Double newAnnualPremiumCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAfterDeviation() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAfterDeviation(Double newAnnualPremiumCappedAfterDeviation) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidated() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidated(Double newAnnualPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCapped(Double newAnnualPremiumConsolidatedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCappedPreapproved(Double newAnnualPremiumConsolidatedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPool(Double newAnnualPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getFullTermPremium() {
		return null;
	}

	@Override
	public void setFullTermPremium(Double newFullTermPremium) {
		// noop
	}

	@Override
	public Double getFullTermPremiumGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumGrid(Double newFullTermPremiumGrid) {

	}

	@Override
	public Double getFullTermPremiumFlex() {
		return null;
	}

	@Override
	public void setFullTermPremiumFlex(Double newFullTermPremiumFlex) {
		// noop
	}

	@Override
	public Double getFullTermPremiumProRated() {
		return null;
	}

	@Override
	public void setFullTermPremiumProRated(Double newFullTermPremiumProRated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumProRatedGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumProRatedGrid(Double newFullTermPremiumProRatedGrid) {

	}

	@Override
	public Double getFullTermPremiumShortRated() {
		return null;
	}

	@Override
	public void setFullTermPremiumShortRated(Double newFullTermPremiumShortRated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumShortRatedGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumShortRatedGrid(Double newFullTermPremiumShortRatedGrid) {

	}

	@Override
	public Double getFullTermPremiumConsolidated() {
		return null;
	}

	@Override
	public void setFullTermPremiumConsolidated(Double newFullTermPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setFullTermPremiumDiscountedSurcharged(Double newFullTermPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPool(Double newFullTermPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getAdditionalPremium() {
		return null;
	}

	@Override
	public void setAdditionalPremium(Double newAdditionalPremium) {
		// noop
	}

	@Override
	public Double getAdditionalPremiumGrid() {
		return null;
	}

	@Override
	public void setAdditionalPremiumGrid(Double newAdditionalPremiumGrid) {

	}

	@Override
	public Double getAdditionalPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAdditionalPremiumDiscountedSurcharged(Double aDouble) {

	}

	@Override
	public Double getReturnPremium() {
		return null;
	}

	@Override
	public void setReturnPremium(Double newReturnPremium) {
		// noop
	}

	@Override
	public Double getReturnPremiumGrid() {
		return null;
	}

	@Override
	public void setReturnPremiumGrid(Double newReturnPremiumGrid) {

	}

	@Override
	public Double getReturnPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setReturnPremiumDiscountedSurcharged(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremium(Double newAdditionalReturnPremium) {
		// noop
	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPool(Double newAdditionalReturnPremiumFacilityRiskSharingPool) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolCeded(Double newAdditionalReturnPremiumFacilityRiskSharingPoolCeded) {

	}

	@Override
	public String getBasicCoverageCode() {
		return this.BasicCoverageCode;
	}

	@Override
	public void setBasicCoverageCode(String newBasicCoverageCode) {
		this.BasicCoverageCode = newBasicCoverageCode;
	}

	@Override
	public String getBreakdownBaseValue() {
		return null;
	}

	@Override
	public void setBreakdownBaseValue(String newBreakdownBaseValue) {

	}

	@Override
	public String getBreakdownReason() {
		return null;
	}

	@Override
	public void setBreakdownReason(String newBreakdownReason) {

	}

	@Override public String getBasicCoverageCodeApply() {
		return null;
	}

	@Override public void setBasicCoverageCodeApply(String s) {

	}

	@Override
	public Double getAdditionalReturnPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumDiscountedSurcharged(Double newAdditionalReturnPremiumDiscountedSurcharged) {

	}

	@Override
	public SubCoveragePremium getTheSubCoveragePremiumPriorTrans() {
		return null;
	}

	@Override
	public void setTheSubCoveragePremiumPriorTrans(SubCoveragePremium newTheSubCoveragePremiumPriorTrans) {
		// noop
	}

	@Override
	public SubCoveragePremium createTheSubCoveragePremiumPriorTrans() {
		return null;
	}

	@Override
	public SubCoveragePremium createTheSubCoveragePremiumPriorTrans(Class<? extends SubCoveragePremium> theInterface) {
		return null;
	}

	@Override
	public SubCoveragePremium getTheSubCoveragePremiumPriorTerm() {
		return null;
	}

	@Override
	public void setTheSubCoveragePremiumPriorTerm(SubCoveragePremium newTheSubCoveragePremiumPriorTerm) {
		// noop
	}

	@Override
	public SubCoveragePremium createTheSubCoveragePremiumPriorTerm() {
		return null;
	}

	@Override
	public SubCoveragePremium createTheSubCoveragePremiumPriorTerm(Class<? extends SubCoveragePremium> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFleetRated() {
		return null;
	}

	@Override
	public void setFullTermPremiumFleetRated(Double arg0) {
		// noop
	}

}
