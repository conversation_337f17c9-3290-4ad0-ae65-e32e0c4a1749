package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.validation.impl.AbstractQuoteDTOValidator;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;


public class QuoteDTOValidatorTest {

	private IQuoteDTOValidator quoteDTOValidator;

	@Mock
	private IPartyDTOValidator partyDTOValidator;

	@Mock
	private IPolicyHolderDTOValidator policyHolderDTOValidator;

	@Mock
	private IVehicleDTOValidator vehicleDTOValidator;

	@Mock
	private IDriverDTOValidator driverDTOValidator;

	private QuoteDTO quoteDTO;

	private DriverDTO driverDTO;

	private Errors errors;

	private ValidationContext context;

	@BeforeEach
	public void setUp() {
		quoteDTO = new QuoteDTO();
		quoteDTO.setParties(Collections.singletonList(new PartyDTO()));

		driverDTO = new DriverDTO();

		errors = new BeanPropertyBindingResult(quoteDTO, "quoteDTO");
		context = new ValidationContext("ON", "EN", AutoQuoteConstants.STR_INTACT, null);

		quoteDTOValidator = Mockito.mock(AbstractQuoteDTOValidator.class, Mockito.CALLS_REAL_METHODS);
		quoteDTOValidator.setContext(context);

		MockitoAnnotations.openMocks(this);

		quoteDTOValidator.setDriverDTOValidator(driverDTOValidator);
		quoteDTOValidator.setPartyDTOValidator(partyDTOValidator);
		quoteDTOValidator.setPolicyHolderDTOValidator(policyHolderDTOValidator);
		quoteDTOValidator.setVehicleDTOValidator(vehicleDTOValidator);
	}

	private static void assertHasError(Errors errors, String errorField, String BRuleException, String triggerValue) {
		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("Field %s should trigger an error", errorField));
		assertEquals(BRuleException, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleException));
	}

	@Test
	public void testValidateQuote_quoteDTOisNull_rejectedNotBlank() {
		quoteDTOValidator.validateQuote(null, errors);
		assertTrue(errors.hasErrors(), "Sending a null QuoteDTO should trigger an error");
	}

	@Test
	public void testValidateParties_partiesIsNull_rejectedNotBlank() {
		quoteDTO.setParties(null);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "parties", BRulesExceptionEnum.NotBlank.getErrorCode(), "parties is null");
	}

	@Test
	public void testValidateParties_partiesListIsEmpty_rejectedNotBlank() {
		quoteDTO.setParties(new ArrayList<>());
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "parties", BRulesExceptionEnum.NotBlank.getErrorCode(), "parties is null");
	}

	@Test
	public void testValidateParties_firstPartieIsNull_rejectedNotBlank() {
		List<PartyDTO> parties = Collections.singletonList(null);
		quoteDTO.setParties(parties);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "parties[0]", BRulesExceptionEnum.NotBlank.getErrorCode(), "party[0] is null");
	}

	@Test
	public void testValidateParties_secondPartieIsNull_rejectedNotBlank() {
		List<PartyDTO> parties = Arrays.asList(new PartyDTO(), null);
		quoteDTO.setParties(parties);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "parties[1]", BRulesExceptionEnum.NotBlank.getErrorCode(), "party[1] is null");
	}

	@Test
	public void testValidateParties_happyPath_acceptedValue() {
		List<PartyDTO> parties = Arrays.asList(new PartyDTO(), new PartyDTO());
		quoteDTO.setParties(parties);
		doNothing().when(partyDTOValidator).validate(any(PartyDTO.class), any(Errors.class), any(ValidationContext.class));
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertFalse(errors.hasErrors(), "The parties list has valid PartyDTO, no error should be present");
		verify(partyDTOValidator, times(parties.size())).validate(any(PartyDTO.class), any(Errors.class), any(ValidationContext.class));
	}

	@Test
	public void testValidatePolicyHolders_policyHolderIsNull_rejectedNotBlank() {
		quoteDTO.setPolicyHolders(null);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "policyHolders", BRulesExceptionEnum.NotBlank.getErrorCode(), "policyHolders list is null");
	}

	@Test
	public void testValidatePolicyHolders_firstHolderIsNull_rejectedNotBlank() {
		List<PolicyHolderDTO> policyHolders = Collections.singletonList(null);
		quoteDTO.setPolicyHolders(policyHolders);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "policyHolders[0]", BRulesExceptionEnum.NotBlank.getErrorCode(), "policyHolders[0] is null");
	}

	@Test
	public void testValidatePolicyHolders_secondHolderIsNull_rejectedNotBlank() {
		List<PolicyHolderDTO> policyHolders = Arrays.asList(new PolicyHolderDTO(), null);
		quoteDTO.setPolicyHolders(policyHolders);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "policyHolders[1]", BRulesExceptionEnum.NotBlank.getErrorCode(), "policyHolders[1] is null");
	}

	@Test
	public void testValidatePolicyHolder_happyPath_acceptedValue() {
		List<PolicyHolderDTO> policyHolders = Arrays.asList(new PolicyHolderDTO(), new PolicyHolderDTO());
		quoteDTO.setPolicyHolders(policyHolders);
		doNothing().when(policyHolderDTOValidator).validate(any(PolicyHolderDTO.class), any(Errors.class));
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertFalse(errors.hasErrors(), "Sending a list with valid PolicyHolderDTO should not trigger an error");
		verify(policyHolderDTOValidator, times(policyHolders.size())).validate(any(PolicyHolderDTO.class), any(Errors.class));
	}

	@Test
	public void testvalidateQuoteVehicles_riskIsNull_rejectedNotBlank() {
		quoteDTO.setRisks(null);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "risks", BRulesExceptionEnum.NotBlank.getErrorCode(), "risks is null");
	}

	@Test
	public void testValidateVehicles_firstRiskIsNull_rejectedNotBlank() {
		List<VehicleDTO> risks = Collections.singletonList(null);
		quoteDTO.setRisks(risks);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "risks[0]", BRulesExceptionEnum.NotBlank.getErrorCode(), "risks[0] is null");
	}

	@Test
	public void testValidateVehicles_secondRiskIsNull_rejectedNotBlank() {
		List<VehicleDTO> risks = Arrays.asList(new VehicleDTO(), null);
		quoteDTO.setRisks(risks);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "risks[1]", BRulesExceptionEnum.NotBlank.getErrorCode(), "risks[1] is null");
	}

	@Test
	public void testValidateVehicles_happyPath_acceptedValue() {
		List<VehicleDTO> risks = Arrays.asList(new VehicleDTO(), new VehicleDTO());
		quoteDTO.setRisks(risks);
		doNothing().when(vehicleDTOValidator).validate(any(VehicleDTO.class), any(Errors.class), any(ValidationContext.class));
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertFalse(errors.hasErrors(), "Sending a valid risks list should not trigger an error");
		verify(vehicleDTOValidator, times(risks.size())).validate(any(VehicleDTO.class), any(Errors.class), any(ValidationContext.class));
	}

	@Test
	public void testValidateDrivers_driversListIsNull_rejectedNotBlank() {
		quoteDTO.setDrivers(null);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "drivers", BRulesExceptionEnum.NotBlank.getErrorCode(), "drivers list is null");
	}

	@Test
	public void testValidateDrivers_firstDriverIsNull_rejectedNotBlank() {
		List<DriverDTO> drivers = Collections.singletonList(null);
		quoteDTO.setDrivers(drivers);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "drivers[0]", BRulesExceptionEnum.NotBlank.getErrorCode(), "drivers[0] in list is null");
	}

	@Test
	public void testValidateDrivers_secondDriverIsNull_rejectedNotBlank() {
		List<DriverDTO> drivers = Arrays.asList(new DriverDTO(), null);
		quoteDTO.setDrivers(drivers);
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertHasError(errors, "drivers[1]", BRulesExceptionEnum.NotBlank.getErrorCode(), "drivers[1] in list is null");
	}

	@Test
	public void testValidateDrivers_happyPath_acceptedValue() {
		LocalDate localDate = new LocalDate();
		driverDTO.setLicenseObtentionDate(localDate);
		List<DriverDTO> drivers = Arrays.asList(driverDTO);
		quoteDTO.setDrivers(drivers);
		doNothing().when(driverDTOValidator).validate(any(DriverDTO.class), any(Errors.class), any(ValidationContext.class));
		quoteDTOValidator.validateQuote(quoteDTO, errors);
		assertFalse(errors.hasErrors(), "Sending a valid risks list should not trigger an error");
		verify(driverDTOValidator, times(drivers.size())).validate(driverDTO, errors, context);
	}
}
