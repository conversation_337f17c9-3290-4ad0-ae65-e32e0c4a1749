package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.address.ComAddress;
import com.intact.com.driver.ComDriver;
import com.intact.com.util.ComDate;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.enums.ConsentTypeEnum;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.converter.impl.COMPartyConverter;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
public class COMPartyConverterTest {

	@InjectMocks
	public COMPartyConverter comPartyConverter;

	@Mock
	private ICOMConverter<AddressDTO, ComAddress> comAddressConverter;

	@Test
	public void testToDTOWithEmptyComDriver() throws Exception {
		ComDriver comDriver = new ComDriver();
		comDriver.setWebMsgId(1);
		PartyDTO party = this.comPartyConverter.toDTO(comDriver);
		assertNotNull(party);
	}

	@Test
	public void testToDTOForEnterprise() throws Exception {
		String enterpriseName = "Les entreprises qui fly";
		String emailAdress = "<EMAIL>";
		String phoneNumber = "5147777777";
		ComDriver comDriver = new ComDriver();
		comDriver.setPolicyHolderInd(Boolean.TRUE);
		comDriver.setEntreprise(enterpriseName);
		comDriver.setEmailAddress(emailAdress);
		comDriver.setHomePhoneNumber(phoneNumber);
		comDriver.setWebMsgId(1);
		comDriver.setMarketingConsent(Boolean.TRUE);

		PartyDTO partyDTO = this.comPartyConverter.toDTO(comDriver);
		assertNotNull(partyDTO);
        assertEquals(PartyTypeEnum.COMPANY, partyDTO.getPartyType());
        assertEquals(enterpriseName, partyDTO.getUnstructuredName());
        assertEquals(phoneNumber, partyDTO.getPhoneNumber());
        assertEquals(emailAdress, partyDTO.getEmailAddress());
		assertNull(partyDTO.getFirstName());
		assertNull(partyDTO.getLastName());
		assertNull(partyDTO.getDateOfBirth());
	}

	@Test
	public void testToDTOForPerson() throws Exception {
		LocalDate dob = new LocalDate();
		String firstName = "Patrick";
		String lastName = "Joly";
		ComDriver comDriver = new ComDriver();
		comDriver.setFirstName(firstName);
		comDriver.setLastName(lastName);
		comDriver.setPolicyHolderInd(Boolean.FALSE);
		comDriver.setWebMsgId(1);
		ComDate comDate = new ComDate();
		comDate.setDay("13");
		comDate.setMonth("10");
		comDate.setYear("1967");
		comDriver.setDateOfBirth(comDate);
		comDriver.setConsentInd(Boolean.TRUE);

		PartyDTO partyDTO = this.comPartyConverter.toDTO(comDriver);
		assertNotNull(partyDTO);
        assertEquals(PartyTypeEnum.PERSON, partyDTO.getPartyType());
        assertEquals(firstName, partyDTO.getFirstName());
        assertEquals(lastName, partyDTO.getLastName());
		assertNull(partyDTO.getUnstructuredName());
		assertNull(partyDTO.getEmailAddress());
        assertEquals(1, partyDTO.getConsents().size());
	}

	@Test
	public void testToCOMForEnterprise() throws Exception {
		String enterpriseName = "Les entreprises qui fly";
		String emailAddress = "<EMAIL>";
		String phoneNumber = "5147777777";
		String cityCode = "11111";
		String postalCode = "H7L2X9";

		ComDriver initialComDriver = new ComDriver();
		DriverDTO partyIn = new DriverDTO();
		partyIn.setPartyType(PartyTypeEnum.COMPANY);
		partyIn.setUnstructuredName(enterpriseName);
		partyIn.setEmailAddress(emailAddress);
		partyIn.setPhoneNumber(phoneNumber);
		this.addConsent(partyIn, ConsentTypeEnum.MARKETING);
		ComAddress mockComAddress = new ComAddress();
		mockComAddress.setCityCode(cityCode);
		mockComAddress.setPostalCode(postalCode);
		doReturn(mockComAddress).when(this.comAddressConverter).toCOM(any(), any());
		ComDriver driver = this.comPartyConverter.toCOM(partyIn, initialComDriver);
		assertNotNull(driver);
        assertEquals(enterpriseName, driver.getEntreprise());
        assertEquals(emailAddress, driver.getEmailAddress());
        assertEquals(phoneNumber, driver.getHomePhoneNumber());
		assertNull(driver.getFirstName());
		assertNull(driver.getLastName());
		assertNotNull(driver.getCurrentAddress());
        assertEquals(cityCode, driver.getCurrentAddress().getCityCode());
        assertEquals(postalCode, driver.getCurrentAddress().getPostalCode());
	}

	@Test
	public void testToCOMForPerson() throws Exception {
		final DateTimeFormatter dtf = DateTimeFormat.forPattern("yyyy-MM-dd");
		final LocalDate dt = dtf.parseLocalDate("1967-01-01");
		ComDate comDate = new ComDate();
		comDate.setDay("1");
		comDate.setMonth("1");
		comDate.setYear("1967");
		String firstName = "Patrick";
		String lastName = "Joly";
		ComDriver initialComDriver = new ComDriver();
		DriverDTO partyIn = new DriverDTO();
		partyIn.setPartyType(PartyTypeEnum.PERSON);
		partyIn.setFirstName(firstName);
		partyIn.setLastName(lastName);
		partyIn.setDateOfBirth(dt);
		this.addConsent(partyIn, ConsentTypeEnum.PROFILE);
		ComDriver driver = this.comPartyConverter.toCOM(partyIn, initialComDriver);

		assertNotNull(driver);
        assertEquals(firstName, driver.getFirstName());
        assertEquals(lastName, driver.getLastName());
        assertEquals(driver.getDateOfBirth().getDay(), comDate.getDay());
        assertEquals(driver.getDateOfBirth().getMonth(), comDate.getMonth());
        assertEquals(driver.getDateOfBirth().getYear(), comDate.getYear());
		assertNull(driver.getEntreprise());
		assertNull(driver.getEmailAddress());
		assertNull(driver.getHomePhoneNumber());
	}

	private void addConsent(PartyDTO partyIn, ConsentTypeEnum type) {
		ConsentDTO consentDTO = new ConsentDTO();
		consentDTO.setConsentInd(Boolean.TRUE);
		consentDTO.setConsentType(type);
		partyIn.setConsents(List.of(consentDTO));
	}


}
