package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.intactfc.bloom.mq.api.ApiException;
import com.intactfc.bloom.mq.api.MqEventServiceApi;
import com.intactfc.bloom.mq.model.MQMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
public class BloomMQHandlerServiceTest {

    @InjectMocks
    private BloomMQHandlerService bloomMQHandlerService;

    @Mock
    private MqEventServiceApi mqEventServiceApi;

    private boolean isEnabled;

    @Test
    public void test_sendMessageToMQ_with_null_parameters_should_do_nothing() throws ApiException {

        ReflectionTestUtils.setField(bloomMQHandlerService, "isEnabled", this.isEnabled);

        bloomMQHandlerService.sendMessageToMQ(null, null);

        verify(mqEventServiceApi, never()).convertAndSend(any(MQMessage.class));
    }

    @Test
    public void test_sendMessageToMQ_with_non_valid_region_should_do_nothing() throws ApiException {
        this.isEnabled = true;
        ReflectionTestUtils.setField(bloomMQHandlerService, "isEnabled", this.isEnabled);

        bloomMQHandlerService.sendMessageToMQ("B09A83F0-2D49-45C6-880C-51942DF24C17", ManufacturerCompanyCodeEnum.BELAIRDIRECT);

        verify(mqEventServiceApi, never()).convertAndSend(any(MQMessage.class));
    }

    @Test
    public void test_sendMessageToMQ_with_valid_region_should_send_message() throws ApiException {

        this.isEnabled = true;
        ReflectionTestUtils.setField(bloomMQHandlerService, "isEnabled", this.isEnabled);

        bloomMQHandlerService.sendMessageToMQ("B09A83F0-2D49-45C6-880C-51942DF24C17", ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION);

        verify(mqEventServiceApi, times(1)).convertAndSend(any(MQMessage.class));
    }
}
