package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.common.util.holiday.HolidayManager;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.driver.ComDriver;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComDistributor;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import com.intact.com.state.ComState;
import com.intact.com.state.enums.ComStateEnum;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.web.security.webattack.WebAttackAnalyser;
import com.intact.common.web.security.webattack.WebAttackException;
import com.intact.globaladmin.domain.enums.IPRestrictionTypeCodeEnum;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import intact.lab.autoquote.backend.services.business.webattack.IWebAttackBusinessProcess;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class BaseFacadeTest {

    @InjectMocks
    private BaseFacade baseFacade;

    @Mock
    private IBaseFacade baseFacadeMock;

    @Mock
    private IPolicyVersionService policyVersionService;

    @Mock
    private IQuoteStateManager quoteStateManager;

    @Mock
    private ICommonBusinessProcess mockCommonBusinessProcess;

    @Mock
    private IUsageBusinessProcess mockUsageBusinessProcess;

    @Mock
    private IMediatorComPlp mockMediatorCom;

    @Mock
    private ITransactionHistoryService mockTransactionHistoryService;

    @Mock
    private WebAttackAnalyser mockWebAttackAnalyser;

    @Mock
    private IWebAttackBusinessProcess mockWebAttackBusinessProcess;

    @Mock
    private IConfigurator mockConfigurator;

    @Mock
    private HolidayManager holiday;

    private CommunicationObjectModel currentTestCom;

    private PolicyVersion currentTestPV;

    private ComState currentTestState;

    @BeforeEach
    public void setUp() throws Exception {
        // Policy version setup
        this.currentTestPV = new PolicyVersion(123L);
        InsurancePolicy testInsPolicy = new InsurancePolicy();
        testInsPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);

        // Set up ManufacturingContext with matching province
        ManufacturingContext manufacturingContext = new ManufacturingContext();
        manufacturingContext.setProvince(ProvinceCodeEnum.ONTARIO);
        testInsPolicy.setManufacturingContext(manufacturingContext);
        testInsPolicy.setQuotationValidityExpiryDate(new Date());
        this.currentTestPV.setBusinessTransaction(new BusinessTransaction());
        this.currentTestPV.setInsurancePolicy(testInsPolicy);

        // Creation of the COM and its context
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);
        context.setProvince(ComProvinceCodeEnum.ONTARIO);
        context.setCompany(ComCompanyEnum.BELAIR);
        context.setTestDataInd(true);
        context.setDistributor(ComDistributor.BEL);

        this.currentTestCom = new CommunicationObjectModel();
        this.currentTestCom.setContext(context);
        this.currentTestCom.setPolicyVersionId(123L);
        this.currentTestState = new ComState();
        this.currentTestState.setCurrentState(ComStateEnum.INITIAL);
        this.currentTestState.setDataChanged(true);
        this.currentTestState.setHasOffer(true);
        this.currentTestCom.setState(this.currentTestState);
    }


    @Test
    void GetPolicyByUuid_Should_ReturnCOM() {
        String uuid = "test-uuid";
        CommunicationObjectModel comModel = spy(new CommunicationObjectModel());
        ComState state = new ComState();
        comModel.setState(state);

        BaseFacade spyFacade = spy(baseFacade);
        when(policyVersionService.findLatestQuoteByUUIDAndTransactionActivityCodes(anyString(), any(), any()))
                .thenReturn(currentTestPV);

        CommunicationObjectModel result = spyFacade.getPolicyByUuid(currentTestCom.getContext(), uuid);

        assertNotNull(result);
    }

//    @Test
    void GetPolicyByUuid_withValidationErrors() {
        String uuid = "test-uuid";
        currentTestPV.getInsurancePolicy().setQuotationValidityExpiryDate(new Date(System.currentTimeMillis() - 600000));

        when(policyVersionService.findLatestQuoteByUUIDAndTransactionActivityCodes(anyString(), any(), any()))
                .thenReturn(currentTestPV);

        CommunicationObjectModel result = baseFacade.getPolicyByUuid(currentTestCom.getContext(), uuid);

        assertNotNull(result);
    }

    @Test
    void GetPolicyByUuid_nullContext() {
        String uuid = "test-uuid";
        assertThrows(IllegalArgumentException.class, () -> baseFacade.getPolicyByUuid(null, uuid));
    }

    @Test
    void GetPolicyByUuid_nullUuid() {
        ComContext context = mock(ComContext.class);
        assertThrows(IllegalArgumentException.class, () -> baseFacade.getPolicyByUuid(context, null));
    }

    @Test
    public void testPersistRoadblock() {
        // Setting the roadblock exception list to be passed as parameter
        List<RoadBlockException> testRoadblockList = new ArrayList<>();
        RoadBlockException testRoadblockException = new RoadBlockException();
        testRoadblockException.setBusinessRuleInvolved("Test");
        testRoadblockList.add(testRoadblockException);

        // Executing the tested method and validating the results
        this.baseFacade.persistRoadblocks(this.currentTestPV, testRoadblockList, this.currentTestState);
        assertTrue("The current state's roadblockInd should've been set to true.", this.currentTestState.getRoadblockInd());
        verify(this.mockTransactionHistoryService, times(1))
                .updateTransHistoryOnRoadBlock(testRoadblockException, this.currentTestState.getCurrentState(), this.currentTestPV);
    }

    @Test
    void VerifyWebAttack_Should_CallWebAttackAnalyser_WhenValidInputProvided() throws Exception {
        // Setup for the test COM to avoid NullPointerExceptions
        this.currentTestCom.getContext().setClientXForwardIPNbr("123456789");
        ComDriver mockDriver = new ComDriver();
        mockDriver.setLicenseNumber("123456789");
        this.currentTestCom.setDrivers(List.of(mockDriver));
        this.currentTestCom.getContext().setApplication(ComApplicationEnum.QUICKQUOTE); // Set for better coverage

        // Executing the tested method and validating the results
        this.baseFacade.verifyWebAttack(this.currentTestCom, "", "");
        verify(this.mockWebAttackAnalyser, times(1)).verifyWebAttacks(any(ManufacturingContext.class),
                any(), any(), any(ApplicationModeEnum.class));
        verify(this.mockWebAttackBusinessProcess, times(1)).keepTraceOfThisCall(any(ManufacturingContext.class),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void VerifyWebAttack_Should_ThrowAutoquoteFacadeException_WhenWebAttackExceptionRaised() throws Exception {
        // Setup for the test COM to avoid NullPointerExceptions
        this.currentTestCom.getContext().setClientXForwardIPNbr("123456789");
        List<ComDriver> testDriversList = new ArrayList<>();
        testDriversList.add(mock(ComDriver.class));
        this.currentTestCom.setDrivers(testDriversList);

        // Setting the exception to be raised
        WebAttackException testException = new WebAttackException(IPRestrictionTypeCodeEnum.RDBK1);
        doThrow(testException).when(this.mockWebAttackAnalyser).verifyWebAttacks(any(ManufacturingContext.class),
                anyString(), anyString(), any(ApplicationModeEnum.class));

        // Executing the tested method and validating the results
        assertThrows(intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException.class, () -> {
            this.baseFacade.verifyWebAttack(this.currentTestCom, "", "");
        });
    }
}
