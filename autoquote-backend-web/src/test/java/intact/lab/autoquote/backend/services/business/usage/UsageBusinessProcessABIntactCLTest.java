package intact.lab.autoquote.backend.services.business.usage;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import intact.lab.autoquote.backend.services.business.common.IAssignClaimsService;
import intact.lab.autoquote.backend.services.business.common.IAssignDriverService;
import intact.lab.autoquote.backend.services.business.usage.impl.UsageBusinessProcessABIntactCL;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StopWatch;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.internal.verification.VerificationModeFactory.times;


@ExtendWith(MockitoExtension.class)
public class UsageBusinessProcessABIntactCLTest {

	@Mock
	IAssignClaimsService mockAssignClaimService;
	@Mock
	IAssignDriverService mockAssignDriverService;
	@InjectMocks
	private UsageBusinessProcessABIntactCL usageBusinessProcess;

	@Test
	public void testCallAssignmentServices() {
		StopWatch stopWatch = new StopWatch();
		PolicyVersion policyVersion = mock(PolicyVersion.class);
		ManufacturingContext context = new ManufacturingContext();

		when(this.mockAssignDriverService.assignDrivers(policyVersion, context)).thenReturn(policyVersion);
		when(this.mockAssignClaimService.assignClaims(policyVersion, context)).thenReturn(policyVersion);

		PolicyVersion result = usageBusinessProcess.callAssignmentServices(stopWatch, policyVersion, context);

		verify(this.mockAssignDriverService, times(1)).assignDrivers(policyVersion, context);
		verify(this.mockAssignClaimService, times(1)).assignClaims(policyVersion, context);

		assertSame(policyVersion, result);
	}

}
