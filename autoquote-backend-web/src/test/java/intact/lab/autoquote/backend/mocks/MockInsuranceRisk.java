/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.activity.MarketingMessage;
import com.ing.canada.som.interfaces.agreement.CommercialWebQualityScoreAggregate;
import com.ing.canada.som.interfaces.agreement.CommercialWebQualityScoreGroup;
import com.ing.canada.som.interfaces.agreement.DiagnosticAutomatedAdvice;
import com.ing.canada.som.interfaces.agreement.InsurancePolicy;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.agreement.RatingCommercialWebQualityScore;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.claim.ClaimDerivedInfo;
import com.ing.canada.som.interfaces.contactPoint.Address;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.intermediary.Program;
import com.ing.canada.som.interfaces.note.Note;
import com.ing.canada.som.interfaces.party.CreditScore;
import com.ing.canada.som.interfaces.partyRoleInRisk.AdditionalInterest;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.partyRoleInRisk.Insured;
import com.ing.canada.som.interfaces.partyRoleInRisk.LocationOtherOccupant;
import com.ing.canada.som.interfaces.partyRoleInRisk.Owner;
import com.ing.canada.som.interfaces.partyRoleInRisk.SubscriptionAgreement;
import com.ing.canada.som.interfaces.physicalObject.Boat;
import com.ing.canada.som.interfaces.physicalObject.Building;
import com.ing.canada.som.interfaces.physicalObject.Trailer;
import com.ing.canada.som.interfaces.physicalObject.Vehicle;
import com.ing.canada.som.interfaces.product.InsuranceRiskProductVersion;
import com.ing.canada.som.interfaces.registration.Conviction;
import com.ing.canada.som.interfaces.risk.BusinessActivity;
import com.ing.canada.som.interfaces.risk.CommercialUsage;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.som.interfaces.risk.CoverageOption;
import com.ing.canada.som.interfaces.risk.FireRateWorksheet;
import com.ing.canada.som.interfaces.risk.FormComponent;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.InsuranceRiskCampaignEligibility;
import com.ing.canada.som.interfaces.risk.InsuranceRiskDerivedInd;
import com.ing.canada.som.interfaces.risk.LegacyRatingInfoByPostalCode;
import com.ing.canada.som.interfaces.risk.OccupancyRepositoryEntry;
import com.ing.canada.som.interfaces.risk.OtherOccupancy;
import com.ing.canada.som.interfaces.risk.PremiumBase;
import com.ing.canada.som.interfaces.risk.RatingRisk;
import com.ing.canada.som.interfaces.risk.ReinsuranceComponentGroup;
import com.ing.canada.som.interfaces.risk.ScheduledArticle;
import com.ing.canada.som.interfaces.risk.SubscriptionComponentGroup;
import com.ing.canada.som.interfaces.risk.UnderlyingSchedule;
import com.ing.canada.som.interfaces.service.MlAttribute;
import com.ing.canada.som.interfaces.service.MlModel;
import com.ing.canada.som.interfaces.service.UnderwritingNetworkInformation;
import com.ing.canada.som.interfaces.service.UnderwritingQuestion;

import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

/**
 *
 * Mock class for a SOM Insurance Risk. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link InsuranceRisk}
 *
 * <AUTHOR>
 *
 */
public class MockInsuranceRisk implements InsuranceRisk {

	private MockVehicle testVehicle;

	private MockCoverage testCoverage;

	private MockRatingRisk testRatingRisk;

	private List<Claim> testClaimList;

	private List<Coverage> testCoverageList;

	private List<Driver> testDriverList;

	private String goodDriverInd;

	private String offerType;

	private String actionTaken;

	private String riskSelectedInd;

	private String uniqueId;

	private Integer insuranceRiskSequence;

	private Integer numberOfMinorConvictionsOccasionalDriver3Years;

	@Override
	public String getObjectType() {
		return null;
	}

	@Override
	public void setObjectType(String newObjectType) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiable6YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable6YearsUnderwriting(Integer newNumberOfClaimsNonLiable6YearsUnderwriting) {

	}

	@Override
	public Integer getHighestNumberOfClaimsNonLiableCollision3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsNonLiableCollision3Years(Integer newHighestNumberOfClaimsNonLiableCollision3Years) {

	}

	@Override
	public Integer getHighestNumberOfSuspensions6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfSuspensions6Years(Integer newHighestNumberOfSuspensions6Years) {

	}

	@Override
	public Integer getNumberOfDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfDriversUnder25Years(Integer newNumberOfDriversUnder25Years) {

	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsUnderwriting(Integer newNumberOfMinorConvictions3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfSevereConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3YearsUnderwriting(Integer newNumberOfSevereConvictions3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsUnderwriting(Integer newNumberOfMajorConvictions3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfDistractedDrivingConvictions3years() {
		return null;
	}

	@Override
	public void setNumberOfDistractedDrivingConvictions3years(Integer newNumberOfDistractedDrivingConvictions3years) {

	}

	@Override
	public Double getTotalReceiptsAmountCovered() {
		return null;
	}

	@Override
	public void setTotalReceiptsAmountCovered(Double newTotalReceiptsAmountCovered) {

	}

	@Override
	public Double getGrossProfitAmountEstimated() {
		return null;
	}

	@Override
	public void setGrossProfitAmountEstimated(Double aDouble) {

	}

	@Override
	public String getFloodCoverageOnPriorTerm() {
		return null;
	}

	@Override
	public void setFloodCoverageOnPriorTerm(String s) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public GregorianCalendar getFacilityRiskSharingPoolPremiumCalculationDate() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolPremiumCalculationDate(GregorianCalendar gregorianCalendar) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getLtvScore() {
		return null;
	}

	@Override
	public void setLtvScore(Double aDouble) {

	}

	@Override
	public Integer getMaximumConsecutiveDaysBusinessClosed() {
		return null;
	}

	@Override
	public void setMaximumConsecutiveDaysBusinessClosed(Integer integer) {

	}

	@Override
	public String getFullCommercialCookingFacilitiesOnPremisesInd() {
		return null;
	}

	@Override
	public void setFullCommercialCookingFacilitiesOnPremisesInd(String s) {

	}

	@Override
	public String getPrivateWaterSourceOnPremisesInd() {
		return null;
	}

	@Override
	public void setPrivateWaterSourceOnPremisesInd(String s) {

	}

	@Override
	public String getLtvScoreVaiFeatureVector() {
		return null;
	}

	@Override
	public void setLtvScoreVaiFeatureVector(String s) {

	}

	@Override
	public Double getMaximizationAdjustmentPercentageVai() {
		return null;
	}

	@Override
	public void setMaximizationAdjustmentPercentageVai(Double newMaximizationAdjustmentPercentageVai) {

	}

	@Override
	public String getMaximizationAdjustmentPercentageVaiFeatureVector() {
		return null;
	}

	@Override
	public void setMaximizationAdjustmentPercentageVaiFeatureVector(String newMaximizationAdjustmentPercentageVaiFeatureVector) {

	}

	@Override
	public Integer getInspectionPriority() {
		return null;
	}

	@Override
	public void setInspectionPriority(Integer newInspectionPriority) {

	}

	@Override
	public Integer getNumberOfDaysPerYearBusinessIsOpen() {
		return null;
	}

	@Override
	public void setNumberOfDaysPerYearBusinessIsOpen(Integer newNumberOfDaysPerYearBusinessIsOpen) {

	}

	@Override
	public String getIntermediaryDataTrace() {
		return null;
	}

	@Override
	public void setIntermediaryDataTrace(String newIntermediaryDataTrace) {

	}

	@Override
	public String getRiskInsuranceBusiness() {
		return null;
	}

	@Override
	public void setRiskInsuranceBusiness(String newRiskInsuranceBusiness) {

	}

	@Override
	public String getRiskInsuranceBusinessPeriod() {
		return null;
	}

	@Override
	public void setRiskInsuranceBusinessPeriod(String s) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastSuspensionPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSuspensionPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastSuspensionPrincAndNonRatedDrvr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastSuspensionOccasionalDriver() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSuspensionOccasionalDriver(Integer newNumberOfMonthsSinceLastSuspensionOccasionalDriver) {

	}

	@Override
	public Integer getNumberOfUninsuredConvictionsPrincAndNonRatedDrvr3Years() {
		return null;
	}

	@Override
	public void setNumberOfUninsuredConvictionsPrincAndNonRatedDrvr3Years(Integer newNumberOfUninsuredConvictionsPrincAndNonRatedDrvr3Years) {

	}

	@Override
	public Integer getNumberOfUninsuredConvictionsOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfUninsuredConvictionsOccasionalDriver3Years(Integer newNumberOfUninsuredConvictionsOccasionalDriver3Years) {

	}

	@Override
	public Integer getNumberOfForgivenClaimsPrincAndNonRatedDrvr10Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaimsPrincAndNonRatedDrvr10Years(Integer newNumberOfForgivenClaimsPrincAndNonRatedDrvr10Years) {

	}

	@Override
	public Double getRenewalPercentageToApply() {
		return null;
	}

	@Override
	public void setRenewalPercentageToApply(Double newRenewalPercentageToApply) {

	}

	@Override
	public Double getProfitabilityScore() {
		return null;
	}

	@Override
	public void setProfitabilityScore(Double newProfitabilityScore) {

	}

	@Override
	public String getRoadsideAssistanceInd() {
		return null;
	}

	@Override
	public void setRoadsideAssistanceInd(String newRoadsideAssistanceInd) {

	}

	@Override
	public Integer getNumberOfClaimsLiable1YearUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable1YearUnderwriting(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsLiable6YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable6YearsUnderwriting(Integer integer) {

	}

	@Override
	public Integer getNumberOfDriversWithDrivingExperienceUnder3Years() {
		return null;
	}

	@Override
	public void setNumberOfDriversWithDrivingExperienceUnder3Years(Integer integer) {

	}

	@Override
	public Double getNewBusinessAdjustmentVai() {
		return null;
	}

	@Override
	public void setNewBusinessAdjustmentVai(Double aDouble) {

	}

	@Override
	public String getNewBusinessAdjustmentVaiFeatureVector() {
		return null;
	}

	@Override
	public void setNewBusinessAdjustmentVaiFeatureVector(String s) {

	}

	@Override
	public String getVaiEligibleInd() {
		return null;
	}

	@Override
	public void setVaiEligibleInd(String s) {

	}

	@Override
	public Double getAnnualCededPremium() {
		return null;
	}

	@Override
	public void setAnnualCededPremium(Double aDouble) {

	}

	@Override
	public Double getFullTermCededPremium() {
		return null;
	}

	@Override
	public void setFullTermCededPremium(Double aDouble) {

	}

	@Override
	public Integer getNumberOfClaimsConsidered1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsConsidered1Year(Integer integer) {

	}

	@Override public String getMachineryExposure() {
		return null;
	}

	@Override public void setMachineryExposure(String s) {

	}

	@Override public Double getTotalEstimatedMaximumLossInTheArea() {
		return null;
	}

	@Override public void setTotalEstimatedMaximumLossInTheArea(Double aDouble) {

	}

	@Override public Double getTotalEstimatedMaximumLoss() {
		return null;
	}

	@Override public void setTotalEstimatedMaximumLoss(Double aDouble) {

	}

	@Override public Double getValuationAmountDepreciated() {
		return null;
	}

	@Override public void setValuationAmountDepreciated(Double aDouble) {

	}

	@Override public Double getAnnualPremiumDiscountedSurchargedForGridComparison() {
		return null;
	}

	@Override public void setAnnualPremiumDiscountedSurchargedForGridComparison(Double aDouble) {

	}

	@Override public Double getAnnualSubscribedPremium() {
		return null;
	}

	@Override public void setAnnualSubscribedPremium(Double aDouble) {

	}

	@Override public Double getFullTermSubscribedPremium() {
		return null;
	}

	@Override public void setFullTermSubscribedPremium(Double aDouble) {

	}

	@Override public Double getAnnualRetainedPremium() {
		return null;
	}

	@Override public void setAnnualRetainedPremium(Double aDouble) {

	}

	@Override public Double getFullTermRetainedPremium() {
		return null;
	}

	@Override public void setFullTermRetainedPremium(Double aDouble) {

	}

	@Override public Double getImputationScore() {
		return null;
	}

	@Override public void setImputationScore(Double aDouble) {

	}

	@Override public String getImputationScoreFeatureVector() {
		return null;
	}

	@Override public void setImputationScoreFeatureVector(String s) {

	}

	@Override public Double getAnnualPremiumAddedAtRenewalGroundWater() {
		return null;
	}

	@Override public void setAnnualPremiumAddedAtRenewalGroundWater(Double aDouble) {

	}

	@Override public Double getAnnualPremiumAddedAtRenewalOvrlndWater() {
		return null;
	}

	@Override public void setAnnualPremiumAddedAtRenewalOvrlndWater(Double aDouble) {

	}

	@Override public Double getAnnualSubtotalPremiumCappedForCvi() {
		return null;
	}

	@Override public void setAnnualSubtotalPremiumCappedForCvi(Double aDouble) {

	}

	@Override
	public void clearTheMlModelCompleted() {

	}

	@Override
	public List<MlModel> getTheMlModelCompleted() {
		return null;
	}

	@Override
	public MlModel getTheMlModelCompleted(String uniqueId) {
		return null;
	}

	@Override
	public MlModel getTheMlModelCompleted(int index) {
		return null;
	}

	@Override
	public MlModel addTheMlModelCompleted() {
		return null;
	}

	@Override
	public MlModel addTheMlModelCompleted(Class<? extends MlModel> theInterface) {
		return null;
	}

	@Override
	public void addTheMlModelCompleted(MlModel newTheMlModelCompleted) {

	}

	@Override
	public void addTheMlModelCompleted(int index, MlModel newTheMlModelCompleted) {

	}

	@Override
	public void setTheMlModelCompleted(int index, MlModel newTheMlModelCompleted) {

	}

	@Override
	public void setTheMlModelCompleted(List<MlModel> objList) {

	}

	@Override
	public void removeTheMlModelCompleted(String uniqueId) {

	}

	@Override
	public void removeTheMlModelCompleted(int index) {

	}

	private Integer numberOfMinorConvictionsPrincipalDriver3Years;

	private Integer numberOfAdditionalInterests;

	private double annualPremium;

	private Integer numberOfClaims3Years;

	private Integer numberOfClaims5Years;

	private Integer numberOfClaims6Years;

	private Integer numberOfClaims10Years;

	private Integer numberOfCLaimsLiable1Year;

	private Integer numberOfCLaimsLiable2Years;

	private Integer numberOfCLaimsLiable3Years;

	private Integer numberOfCLaimsLiable4Years;

	private Integer numberOfCLaimsLiable5Years;

	private Integer numberOfCLaimsLiable6Years;

	private Integer numberOfCLaimsLiable10Years;

	private Integer numberOfClaimsExcludingGlassBreakage3Years;

	private Integer numberOfClaimsLiablePrincipalAndNonRatedDriver6Years;

	private Integer numberOfClaimsLiableOccasionalDriver6Years;

	private Integer customerValueIndexPureWithoutCredit;

	private Integer customerValueIndexPureWithCredit;

	private Integer retentionScorePureWithoutCredit;

	private Integer retentionScorePureWithCredit;

	private Integer competitivityScoreWithoutCredit;

	private Integer competitivityScoreWithCredit;

	private Integer cappingPercentage;

	private Integer cappingPercentageSystem;

	private Integer competitivityAdjustmentPercentage;

	private Integer customerValueIndexAdjustedWithCredit;

	private Integer flexMaximumPercentage;

	private Integer flexPercentage;

	private Integer flexPercentageSystem;

	private Integer scoringAdjustmentPercentage;

	@Override
	public String getActionTaken() {
		return this.actionTaken;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		this.actionTaken = newActionTaken;
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String _uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String _uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String _uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return this.uniqueId;
	}

	@Override
	public void setUniqueId(String string) {
		this.uniqueId = string;
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		// For test purposes, will be equal if it has the same unique id
		return this.uniqueId.equals(_baseObject.getUniqueId());
	}

	@Override
	public String getRiskType() {
		return null;
	}

	@Override
	public void setRiskType(String newRiskType) {
		// noop
	}

	@Override
	public String getRiskSubtype() {
		return null;
	}

	@Override
	public void setRiskSubtype(String newRiskSubtype) {
		// noop
	}

	@Override
	public String getRiskSubtypeClass() {
		return null;
	}

	@Override
	public void setRiskSubtypeClass(String newRiskSubtypeClass) {
		// noop
	}

	@Override
	public String getLineOfInsurance() {
		return null;
	}

	@Override
	public void setLineOfInsurance(String newLineOfInsurance) {
		// noop
	}

	@Override
	public String getLiabilityCode() {
		return null;
	}

	@Override
	public void setLiabilityCode(String newLiabilityCode) {
		// noop
	}

	@Override
	public String getRiskTypeLegacyCode() {
		return null;
	}

	@Override
	public void setRiskTypeLegacyCode(String newRiskTypeLegacyCode) {
		// noop
	}

	@Override
	public String getNonStandardRiskInd() {
		return null;
	}

	@Override
	public void setNonStandardRiskInd(String newNonStandardRiskInd) {
		// noop
	}

	@Override
	public String getEndorsementPackage() {
		return null;
	}

	@Override
	public void setEndorsementPackage(String newEndorsementPackage) {
		// noop
	}

	@Override
	public Integer getInsuranceRiskSequence() {
		// For test purposes, return by default a false sequence id (will always be 42)
		return this.insuranceRiskSequence == null ? new Integer(42) : this.insuranceRiskSequence;
	}

	@Override
	public void setInsuranceRiskSequence(Integer newInsuranceRiskSequence) {
		this.insuranceRiskSequence = newInsuranceRiskSequence;
	}

	@Override
	public String getInsuranceRiskNumberPrinted() {
		return null;
	}

	@Override
	public void setInsuranceRiskNumberPrinted(String newInsuranceRiskNumberPrinted) {
		// noop
	}

	@Override
	public String getIndicatorProofDrivingRecord5System() {
		return null;
	}

	@Override
	public void setIndicatorProofDrivingRecord5System(String newIndicatorProofDrivingRecord5System) {
		// noop
	}

	@Override
	public String getIndicatorProofDrivingRecord5Modified() {
		return null;
	}

	@Override
	public void setIndicatorProofDrivingRecord5Modified(String newIndicatorProofDrivingRecord5Modified) {
		// noop
	}

	@Override
	public String getIndicatorProofDrivingRecord5() {
		return null;
	}

	@Override
	public void setIndicatorProofDrivingRecord5(String newIndicatorProofDrivingRecord5) {
		// noop
	}

	@Override
	public String getIndicatorProofDrivingRecord5Occasional() {
		return null;
	}

	@Override
	public void setIndicatorProofDrivingRecord5Occasional(String newIndicatorProofDrivingRecord5Occasional) {
		// noop
	}

	@Override
	public Integer getDriverSequenceGridPrincipal() {
		return null;
	}

	@Override
	public void setDriverSequenceGridPrincipal(Integer newDriverSequenceGridPrincipal) {
		// noop
	}

	@Override
	public Integer getDriverSequenceGridOccasional() {
		return null;
	}

	@Override
	public void setDriverSequenceGridOccasional(Integer newDriverSequenceGridOccasional) {
		// noop
	}

	@Override
	public String getGoodDriverIndSystem() {
		return null;
	}

	@Override
	public void setGoodDriverIndSystem(String newGoodDriverIndSystem) {
		// noop
	}

	@Override
	public String getGoodDriverIndModified() {
		return null;
	}

	@Override
	public void setGoodDriverIndModified(String newGoodDriverIndModified) {
		// noop
	}

	@Override
	public String getGoodDriverInd() {
		return this.goodDriverInd;
	}

	@Override
	public void setGoodDriverInd(String newGoodDriverInd) {
		this.goodDriverInd = newGoodDriverInd;
	}

	@Override
	public GregorianCalendar getGoodDriverSinceDate() {
		return null;
	}

	@Override
	public void setGoodDriverSinceDate(GregorianCalendar newGoodDriverSinceDate) {
		// noop
	}

	@Override
	public String getLicenseSuspensionInd() {
		return null;
	}

	@Override
	public void setLicenseSuspensionInd(String newLicenseSuspensionInd) {
		// noop
	}

	@Override
	public String getLicenseSuspensionIndPrincipal() {
		return null;
	}

	@Override
	public void setLicenseSuspensionIndPrincipal(String newLicenseSuspensionIndPrincipal) {
		// noop
	}

	@Override
	public String getLicenseSuspensionIndOccasional() {
		return null;
	}

	@Override
	public void setLicenseSuspensionIndOccasional(String newLicenseSuspensionIndOccasional) {
		// noop
	}

	@Override
	public String getLicenseSuspensionIndNonRated() {
		return null;
	}

	@Override
	public void setLicenseSuspensionIndNonRated(String newLicenseSuspensionIndNonRated) {
		// noop
	}

	@Override
	public String getClaimSurchargeInd() {
		return null;
	}

	@Override
	public void setClaimSurchargeInd(String newClaimSurchargeInd) {
		// noop
	}

	@Override
	public String getRentalSurchargeCode() {
		return null;
	}

	@Override
	public void setRentalSurchargeCode(String newRentalSurchargeCode) {
		// noop
	}

	@Override
	public GregorianCalendar getCreationDate() {
		return null;
	}

	@Override
	public void setCreationDate(GregorianCalendar newCreationDate) {
		// noop
	}

	@Override
	public GregorianCalendar getRiskAttachedDate() {
		return null;
	}

	@Override
	public void setRiskAttachedDate(GregorianCalendar newRiskAttachedDate) {
		// noop
	}

	@Override
	public GregorianCalendar getEffectiveDate() {
		return null;
	}

	@Override
	public void setEffectiveDate(GregorianCalendar newEffectiveDate) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDate() {
		return null;
	}

	@Override
	public void setExpiryDate(GregorianCalendar newExpiryDate) {
		// noop
	}

	@Override
	public Integer getRiskDaysLeft() {
		return null;
	}

	@Override
	public void setRiskDaysLeft(Integer newRiskDaysLeft) {
		// noop
	}

	@Override
	public Integer getRiskTermInDays() {
		return null;
	}

	@Override
	public void setRiskTermInDays(Integer newRiskTermInDays) {
		// noop
	}

	@Override
	public Integer getMaximumGvpDuration() {
		return null;
	}

	@Override
	public void setMaximumGvpDuration(Integer newMaximumGvpDuration) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingDateSystem() {
		return null;
	}

	@Override
	public void setRatingDateSystem(GregorianCalendar newRatingDateSystem) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingDateModified() {
		return null;
	}

	@Override
	public void setRatingDateModified(GregorianCalendar newRatingDateModified) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingDate() {
		return null;
	}

	@Override
	public void setRatingDate(GregorianCalendar newRatingDate) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingDateSandbox() {
		return null;
	}

	@Override
	public void setRatingDateSandbox(GregorianCalendar newRatingDateSandbox) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingExternalizationActivationDate() {
		return null;
	}

	@Override
	public void setRatingExternalizationActivationDate(GregorianCalendar newRatingExternalizationActivationDate) {
		// noop
	}

	@Override
	public String getRateType() {
		return null;
	}

	@Override
	public void setRateType(String newRateType) {
		// noop
	}

	@Override
	public String getRatingProgram() {
		return null;
	}

	@Override
	public void setRatingProgram(String newRatingProgram) {
		// noop
	}

	@Override
	public String getRateBasedOnkindOfLossInd() {
		return null;
	}

	@Override
	public void setRateBasedOnkindOfLossInd(String newRateBasedOnkindOfLossInd) {
		// noop
	}

	@Override
	public String getRatingTableIdentification() {
		return null;
	}

	@Override
	public void setRatingTableIdentification(String newRatingTableIdentification) {
		// noop
	}

	@Override
	public GregorianCalendar getOriginalDateRated() {
		return null;
	}

	@Override
	public void setOriginalDateRated(GregorianCalendar newOriginalDateRated) {
		// noop
	}

	@Override
	public String getFutureDatedRiskInd() {
		return null;
	}

	@Override
	public void setFutureDatedRiskInd(String newFutureDatedRiskInd) {
		// noop
	}

	@Override
	public String getWelcomeDiscountInd() {
		return null;
	}

	@Override
	public void setWelcomeDiscountInd(String newWelcomeDiscountInd) {
		// noop
	}

	@Override
	public String getMatureDiscountEligibiIityIndSystem() {
		return null;
	}

	@Override
	public void setMatureDiscountEligibiIityIndSystem(String newMatureDiscountEligibiIityIndSystem) {
		// noop
	}

	@Override
	public String getMatureDiscountEligibilityIndModified() {
		return null;
	}

	@Override
	public void setMatureDiscountEligibilityIndModified(String newMatureDiscountEligibilityIndModified) {
		// noop
	}

	@Override
	public String getMatureDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setMatureDiscountEligibilityInd(String newMatureDiscountEligibilityInd) {
		// noop
	}

	@Override
	public String getMatureInd() {
		return null;
	}

	@Override
	public void setMatureInd(String newMatureInd) {
		// noop
	}

	@Override
	public Integer getMatureIndAjustedAge() {
		return null;
	}

	@Override
	public void setMatureIndAjustedAge(Integer newMatureIndAjustedAge) {
		// noop
	}

	@Override
	public String getPipDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setPipDiscountEligibilityInd(String newPipDiscountEligibilityInd) {
		// noop
	}

	@Override
	public String getFleetDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setFleetDiscountEligibilityInd(String newFleetDiscountEligibilityInd) {
		// noop
	}

	@Override
	public Double getValuationAmount() {
		return null;
	}

	@Override
	public void setValuationAmount(Double newValuationAmount) {
		// noop
	}

	@Override
	public Integer getRentalValue() {
		return null;
	}

	@Override
	public void setRentalValue(Integer newRentalValue) {
		// noop
	}

	@Override
	public Integer getFlexMaximumPercentage() {
		return this.flexMaximumPercentage;
	}

	@Override
	public void setFlexMaximumPercentage(Integer newFlexMaximumPercentage) {
		this.flexMaximumPercentage = newFlexMaximumPercentage;
	}

	@Override
	public Integer getFlexPercentageSystem() {
		return this.flexPercentageSystem;
	}

	@Override
	public void setFlexPercentageSystem(Integer newFlexPercentageSystem) {
		this.flexPercentageSystem = newFlexPercentageSystem;
	}

	@Override
	public Integer getFlexPercentageModified() {
		return null;
	}

	@Override
	public void setFlexPercentageModified(Integer newFlexPercentageModified) {
		// noop
	}

	@Override
	public Integer getFlexPercentage() {
		return this.flexPercentage;
	}

	@Override
	public void setFlexPercentage(Integer newFlexPercentage) {
		this.flexPercentage = newFlexPercentage;
	}

	@Override
	public Integer getFlexPercentageFirstRating() {
		return null;
	}

	@Override
	public void setFlexPercentageFirstRating(Integer newFlexPercentageFirstRating) {
		// noop
	}

	@Override
	public String getFlexPercentageAdjustedInd() {
		return null;
	}

	@Override
	public void setFlexPercentageAdjustedInd(String newFlexPercentageAdjustedInd) {
		// noop
	}

	@Override
	public String getRatingPlanSystem() {
		return null;
	}

	@Override
	public void setRatingPlanSystem(String newRatingPlanSystem) {
		// noop
	}

	@Override
	public String getRatingPlanModified() {
		return null;
	}

	@Override
	public void setRatingPlanModified(String newRatingPlanModified) {
		// noop
	}

	@Override
	public String getRatingPlan() {
		return null;
	}

	@Override
	public void setRatingPlan(String newRatingPlan) {
		// noop
	}

	@Override
	public String getPrincipalLocationInd() {
		return null;
	}

	@Override
	public void setPrincipalLocationInd(String newPrincipalLocationInd) {
		// noop
	}

	@Override
	public Integer getNumberOfStabilityMonthsSystem() {
		return null;
	}

	@Override
	public void setNumberOfStabilityMonthsSystem(Integer newNumberOfStabilityMonthsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfStabilityMonthsModified() {
		return null;
	}

	@Override
	public void setNumberOfStabilityMonthsModified(Integer newNumberOfStabilityMonthsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfStabilityMonths() {
		return null;
	}

	@Override
	public void setNumberOfStabilityMonths(Integer newNumberOfStabilityMonths) {
		// noop
	}

	@Override
	public String getStabilityDiscountIndSystem() {
		return null;
	}

	@Override
	public void setStabilityDiscountIndSystem(String newStabilityDiscountIndSystem) {
		// noop
	}

	@Override
	public String getStabilityDiscountIndModified() {
		return null;
	}

	@Override
	public void setStabilityDiscountIndModified(String newStabilityDiscountIndModified) {
		// noop
	}

	@Override
	public String getStabilityDiscountInd() {
		return null;
	}

	@Override
	public void setStabilityDiscountInd(String newStabilityDiscountInd) {
		// noop
	}

	@Override
	public String getExternalProviderUbiDiscountCode() {
		return null;
	}

	@Override
	public void setExternalProviderUbiDiscountCode(String newExternalProviderUbiDiscountCode) {
		// noop
	}

	@Override
	public String getTargetRiskInd() {
		return null;
	}

	@Override
	public void setTargetRiskInd(String newTargetRiskInd) {
		// noop
	}

	@Override
	public String getMultiVehicleDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setMultiVehicleDiscountEligibilityInd(String newMultiVehicleDiscountEligibilityInd) {
		// noop
	}

	@Override
	public String getMultiVehicleDiscountType() {
		return null;
	}

	@Override
	public void setMultiVehicleDiscountType(String newMultiVehicleDiscountType) {
		// noop
	}

	@Override
	public String getTerritoryStatSystem() {
		return null;
	}

	@Override
	public void setTerritoryStatSystem(String newTerritoryStatSystem) {
		// noop
	}

	@Override
	public String getTerritoryStatModified() {
		return null;
	}

	@Override
	public void setTerritoryStatModified(String newTerritoryStatModified) {
		// noop
	}

	@Override
	public String getTerritoryStat() {
		return null;
	}

	@Override
	public void setTerritoryStat(String newTerritoryStat) {
		// noop
	}

	@Override
	public String getRatingTerritorySystem() {
		return null;
	}

	@Override
	public void setRatingTerritorySystem(String newRatingTerritorySystem) {
		// noop
	}

	@Override
	public String getRatingTerritoryModified() {
		return null;
	}

	@Override
	public void setRatingTerritoryModified(String newRatingTerritoryModified) {
		// noop
	}

	@Override
	public String getRatingTerritory() {
		return null;
	}

	@Override
	public void setRatingTerritory(String newRatingTerritory) {
		// noop
	}

	@Override
	public String getRatingTerritoryDescriptionEng() {
		return null;
	}

	@Override
	public void setRatingTerritoryDescriptionEng(String newRatingTerritoryDescriptionEng) {
		// noop
	}

	@Override
	public String getRatingTerritoryDescriptionFre() {
		return null;
	}

	@Override
	public void setRatingTerritoryDescriptionFre(String newRatingTerritoryDescriptionFre) {
		// noop
	}

	@Override
	public String getTerritoryCategory() {
		return null;
	}

	@Override
	public void setTerritoryCategory(String newTerritoryCategory) {
		// noop
	}

	@Override
	public String getGridTerritory() {
		return null;
	}

	@Override
	public void setGridTerritory(String newGridTerritory) {
		// noop
	}

	@Override
	public String getGridPremiumChargedInd() {
		return null;
	}

	@Override
	public void setGridPremiumChargedInd(String newGridPremiumChargedInd) {
		// noop
	}

	@Override
	public String getGridPremiumForcedInd() {
		return null;
	}

	@Override
	public void setGridPremiumForcedInd(String newGridPremiumForcedInd) {
		// noop
	}

	@Override
	public String getMunicipalAdjustmentSystem() {
		return null;
	}

	@Override
	public void setMunicipalAdjustmentSystem(String newMunicipalAdjustmentSystem) {
		// noop
	}

	@Override
	public String getMunicipalAdjustmentModified() {
		return null;
	}

	@Override
	public void setMunicipalAdjustmentModified(String newMunicipalAdjustmentModified) {
		// noop
	}

	@Override
	public String getMunicipalAdjustment() {
		return null;
	}

	@Override
	public void setMunicipalAdjustment(String newMunicipalAdjustment) {
		// noop
	}

	@Override
	public Integer getDeductibleAmountSystem() {
		return null;
	}

	@Override
	public void setDeductibleAmountSystem(Integer newDeductibleAmountSystem) {
		// noop
	}

	@Override
	public Integer getDeductibleAmountModified() {
		return null;
	}

	@Override
	public void setDeductibleAmountModified(Integer newDeductibleAmountModified) {
		// noop
	}

	@Override
	public Integer getDeductibleAmount() {
		return null;
	}

	@Override
	public void setDeductibleAmount(Integer newDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getDeductibleDiscountAmountSystem() {
		return null;
	}

	@Override
	public void setDeductibleDiscountAmountSystem(Integer newDeductibleDiscountAmountSystem) {
		// noop
	}

	@Override
	public Integer getDeductibleDiscountAmountModified() {
		return null;
	}

	@Override
	public void setDeductibleDiscountAmountModified(Integer newDeductibleDiscountAmountModified) {
		// noop
	}

	@Override
	public Integer getDeductibleDiscountAmount() {
		return null;
	}

	@Override
	public void setDeductibleDiscountAmount(Integer newDeductibleDiscountAmount) {
		// noop
	}

	@Override
	public Double getMarketingDiscountPercentage() {
		return null;
	}

	@Override
	public void setMarketingDiscountPercentage(Double newMarketingDiscountPercentage) {
		// noop
	}

	@Override
	public Double getMarketingDiscountPercentageScoring() {
		return null;
	}

	@Override
	public void setMarketingDiscountPercentageScoring(Double newMarketingDiscountPercentageScoring) {
		// noop
	}

	@Override
	public String getStaffFamilyDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setStaffFamilyDiscountEligibilityInd(String newStaffFamilyDiscountEligibilityInd) {
		// noop
	}

	@Override
	public Integer getBuildingCoverageLimitOfInsurance() {
		return null;
	}

	@Override
	public void setBuildingCoverageLimitOfInsurance(Integer newBuildingCoverageLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getContentsCoverageLimitOfInsurance() {
		return null;
	}

	@Override
	public void setContentsCoverageLimitOfInsurance(Integer newContentsCoverageLimitOfInsurance) {
		// noop
	}

	@Override
	public Double getContentReplacementCostLimit() {
		return null;
	}

	@Override
	public void setContentReplacementCostLimit(Double newContentReplacementCostLimit) {

	}

	@Override
	public Integer getLiabilityCoverageLimitOfInsurance() {
		return null;
	}

	@Override
	public void setLiabilityCoverageLimitOfInsurance(Integer newLiabilityCoverageLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getFoodFreezerLimitOfInsurance() {
		return null;
	}

	@Override
	public void setFoodFreezerLimitOfInsurance(Integer newFoodFreezerLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getCreditCardLimitOfInsurance() {
		return null;
	}

	@Override
	public void setCreditCardLimitOfInsurance(Integer newCreditCardLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getVoluntaryPropertyDamageLimitOfInsurance() {
		return null;
	}

	@Override
	public void setVoluntaryPropertyDamageLimitOfInsurance(Integer newVoluntaryPropertyDamageLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getCondominiumUnitLimitOfInsurance() {
		return null;
	}

	@Override
	public void setCondominiumUnitLimitOfInsurance(Integer newCondominiumUnitLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getLossAssessmentsLimitOfInsurance() {
		return null;
	}

	@Override
	public void setLossAssessmentsLimitOfInsurance(Integer newLossAssessmentsLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getUnitImprovementsLimitOfInsurance() {
		return null;
	}

	@Override
	public void setUnitImprovementsLimitOfInsurance(Integer newUnitImprovementsLimitOfInsurance) {
		// noop
	}

	@Override
	public Double getLimitMedicalExpensesPerPerson() {
		return null;
	}

	@Override
	public void setLimitMedicalExpensesPerPerson(Double newLimitMedicalExpensesPerPerson) {
		// noop
	}

	@Override
	public String getCaaEligibilityInd() {
		return null;
	}

	@Override
	public void setCaaEligibilityInd(String newCaaEligibilityInd) {
		// noop
	}

	@Override
	public String getCappingEligibilityIndSystem() {
		return null;
	}

	@Override
	public void setCappingEligibilityIndSystem(String newCappingEligibilityIndSystem) {
		// noop
	}

	@Override
	public String getCappingEligibilityIndModified() {
		return null;
	}

	@Override
	public void setCappingEligibilityIndModified(String newCappingEligibilityIndModified) {
		// noop
	}

	@Override
	public String getCappingEligibilityInd() {
		return null;
	}

	@Override
	public void setCappingEligibilityInd(String newCappingEligibilityInd) {
		// noop
	}

	@Override
	public Integer getCappingPercentageSystem() {
		return this.cappingPercentageSystem;
	}

	@Override
	public void setCappingPercentageSystem(Integer newCappingPercentageSystem) {
		this.cappingPercentageSystem = newCappingPercentageSystem;
	}

	@Override
	public Integer getCappingPercentageModified() {
		return null;
	}

	@Override
	public void setCappingPercentageModified(Integer newCappingPercentageModified) {
		// noop
	}

	@Override
	public Integer getCappingPercentage() {
		return this.cappingPercentage;
	}

	@Override
	public void setCappingPercentage(Integer newCappingPercentage) {
		this.cappingPercentage = newCappingPercentage;
	}

	@Override
	public Integer getCappingPercentageFirstRating() {
		return null;
	}

	@Override
	public void setCappingPercentageFirstRating(Integer newCappingPercentageFirstRating) {
		// noop
	}

	@Override
	public String getCappingPercentageAdjustedInd() {
		return null;
	}

	@Override
	public void setCappingPercentageAdjustedInd(String newCappingPercentageAdjustedInd) {
		// noop
	}

	@Override
	public Integer getCompetitivityAdjustmentPercentage() {
		return this.competitivityAdjustmentPercentage;
	}

	@Override
	public void setCompetitivityAdjustmentPercentage(Integer newCompetitivityAdjustmentPercentage) {
		this.competitivityAdjustmentPercentage = newCompetitivityAdjustmentPercentage;
	}

	@Override
	public Integer getCompetitivityScoreWithCredit() {
		return this.competitivityScoreWithCredit;
	}

	@Override
	public void setCompetitivityScoreWithCredit(Integer newCompetitivityScoreWithCredit) {
		this.competitivityScoreWithCredit = newCompetitivityScoreWithCredit;
	}

	@Override
	public Integer getCompetitivityScoreWithCreditRegular() {
		return null;
	}

	@Override
	public void setCompetitivityScoreWithCreditRegular(Integer newCompetitivityScoreWithCreditRegular) {
		// noop
	}

	@Override
	public Integer getCompetitivityScoreWithCreditPreferred() {
		return null;
	}

	@Override
	public void setCompetitivityScoreWithCreditPreferred(Integer newCompetitivityScoreWithCreditPreferred) {
		// noop
	}

	@Override
	public Integer getCompetitivityScoreWithoutCredit() {
		return this.competitivityScoreWithoutCredit;
	}

	@Override
	public void setCompetitivityScoreWithoutCredit(Integer newCompetitivityScoreWithoutCredit) {
		this.competitivityScoreWithoutCredit = newCompetitivityScoreWithoutCredit;
	}

	@Override
	public Integer getCompetitivityScoreWithoutCreditRegular() {
		return null;
	}

	@Override
	public void setCompetitivityScoreWithoutCreditRegular(Integer newCompetitivityScoreWithoutCreditRegular) {
		// noop
	}

	@Override
	public Integer getCompetitivityScoreWithoutCreditPreferred() {
		return null;
	}

	@Override
	public void setCompetitivityScoreWithoutCreditPreferred(Integer newCompetitivityScoreWithoutCreditPreferred) {
		// noop
	}

	@Override
	public Integer getNumberOfNamedInsured() {
		return null;
	}

	@Override
	public void setNumberOfNamedInsured(Integer newNumberOfNamedInsured) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalNamedInsured() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalNamedInsured(Integer newNumberOfAdditionalNamedInsured) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalInterestsOnQuote() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalInterestsOnQuote(Integer newNumberOfAdditionalInterestsOnQuote) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalInterestsSystem() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalInterestsSystem(Integer newNumberOfAdditionalInterestsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalInterestsModified() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalInterestsModified(Integer newNumberOfAdditionalInterestsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalInterests() {
		return this.numberOfAdditionalInterests;
	}

	@Override
	public void setNumberOfAdditionalInterests(Integer newNumberOfAdditionalInterests) {
		this.numberOfAdditionalInterests = newNumberOfAdditionalInterests;
	}

	@Override
	public String getTypeOfAdditionalInterestsOnQuote() {
		return null;
	}

	@Override
	public void setTypeOfAdditionalInterestsOnQuote(String newTypeOfAdditionalInterestsOnQuote) {
		// noop
	}

	@Override
	public Double getInflationGuardPercentage() {
		return null;
	}

	@Override
	public void setInflationGuardPercentage(Double newInflationGuardPercentage) {
		// noop
	}

	@Override
	public Integer getRiskScore() {
		return null;
	}

	@Override
	public void setRiskScore(Integer newRiskScore) {
		// noop
	}

	@Override
	public Double getElasticityScore() {
		return null;
	}

	@Override
	public void setElasticityScore(Double newElasticityScore) {
		// noop
	}

	@Override
	public Integer getRetentionScorePureWithCredit() {
		return this.retentionScorePureWithCredit;
	}

	@Override
	public void setRetentionScorePureWithCredit(Integer newRetentionScorePureWithCredit) {
		this.retentionScorePureWithCredit = newRetentionScorePureWithCredit;
	}

	@Override
	public Integer getRetentionScorePureWithCreditRegular() {
		return null;
	}

	@Override
	public void setRetentionScorePureWithCreditRegular(Integer newRetentionScorePureWithCreditRegular) {
		// noop
	}

	@Override
	public Integer getRetentionScorePureWithCreditPreferred() {
		return null;
	}

	@Override
	public void setRetentionScorePureWithCreditPreferred(Integer newRetentionScorePureWithCreditPreferred) {
		// noop
	}

	@Override
	public Integer getRetentionScorePureWithoutCredit() {
		return this.retentionScorePureWithoutCredit;
	}

	@Override
	public void setRetentionScorePureWithoutCredit(Integer newRetentionScorePureWithoutCredit) {
		this.retentionScorePureWithoutCredit = newRetentionScorePureWithoutCredit;
	}

	@Override
	public Integer getRetentionScorePureWithoutCreditRegular() {
		return null;
	}

	@Override
	public void setRetentionScorePureWithoutCreditRegular(Integer newRetentionScorePureWithoutCreditRegular) {
		// noop
	}

	@Override
	public Integer getRetentionScorePureWithoutCreditPreferred() {
		return null;
	}

	@Override
	public void setRetentionScorePureWithoutCreditPreferred(Integer newRetentionScorePureWithoutCreditPreferred) {
		// noop
	}

	@Override
	public Integer getRetentionScoreAdjustedWithCredit() {
		return null;
	}

	@Override
	public void setRetentionScoreAdjustedWithCredit(Integer newRetentionScoreAdjustedWithCredit) {
		// noop
	}

	@Override
	public Integer getRetentionScoreAdjustedWithoutCredit() {
		return null;
	}

	@Override
	public void setRetentionScoreAdjustedWithoutCredit(Integer newRetentionScoreAdjustedWithoutCredit) {
		// noop
	}

	@Override
	public Double getRetentionScore0Vai() {
		return null;
	}

	@Override
	public void setRetentionScore0Vai(Double newRetentionScore0Vai) {

	}

	@Override
	public String getRetentionScore0VaiFeatureVector() {
		return null;
	}

	@Override
	public void setRetentionScore0VaiFeatureVector(String newRetentionScore0VaiFeatureVector) {

	}

	@Override
	public Double getRetentionScoreEVai() {
		return null;
	}

	@Override
	public void setRetentionScoreEVai(Double newRetentionScoreEVai) {

	}

	@Override
	public String getRetentionScoreEVaiFeatureVector() {
		return null;
	}

	@Override
	public void setRetentionScoreEVaiFeatureVector(String newRetentionScoreEVaiFeatureVector) {

	}

	@Override
	public String getQualityRiskLevel() {
		return null;
	}

	@Override
	public void setQualityRiskLevel(String newQualityRiskLevel) {
		// noop
	}

	@Override
	public String getQualityRiskLevelRegular() {
		return null;
	}

	@Override
	public void setQualityRiskLevelRegular(String newQualityRiskLevelRegular) {
		// noop
	}

	@Override
	public String getQualityRiskLevelPreferred() {
		return null;
	}

	@Override
	public void setQualityRiskLevelPreferred(String newQualityRiskLevelPreferred) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureWithCredit() {
		return this.customerValueIndexPureWithCredit;
	}

	@Override
	public void setCustomerValueIndexPureWithCredit(Integer newCustomerValueIndexPureWithCredit) {
		this.customerValueIndexPureWithCredit = newCustomerValueIndexPureWithCredit;
	}

	@Override
	public Integer getCustomerValueIndexPureWithCreditRegular() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureWithCreditRegular(Integer newCustomerValueIndexPureWithCreditRegular) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureWithCreditPreferred() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureWithCreditPreferred(Integer newCustomerValueIndexPureWithCreditPreferred) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureWithoutCredit() {
		return this.customerValueIndexPureWithoutCredit;
	}

	@Override
	public void setCustomerValueIndexPureWithoutCredit(Integer newCustomerValueIndexPureWithoutCredit) {
		this.customerValueIndexPureWithoutCredit = newCustomerValueIndexPureWithoutCredit;
	}

	@Override
	public Integer getCustomerValueIndexPureWithoutCreditRegular() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureWithoutCreditRegular(Integer newCustomerValueIndexPureWithoutCreditRegular) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureWithoutCreditPreferred() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureWithoutCreditPreferred(Integer newCustomerValueIndexPureWithoutCreditPreferred) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureForUnderwriting() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureForUnderwriting(Integer newCustomerValueIndexPureForUnderwriting) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureForUnderwritingRegular() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureForUnderwritingRegular(Integer newCustomerValueIndexPureForUnderwritingRegular) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexPureForUnderwritingPreferred() {
		return null;
	}

	@Override
	public void setCustomerValueIndexPureForUnderwritingPreferred(Integer newCustomerValueIndexPureForUnderwritingPreferred) {
		// noop
	}

	@Override
	public Integer getCustomerValueIndexAdjustedWithCredit() {
		return this.customerValueIndexAdjustedWithCredit;
	}

	@Override
	public void setCustomerValueIndexAdjustedWithCredit(Integer newCustomerValueIndexAdjustedWithCredit) {
		this.customerValueIndexAdjustedWithCredit = newCustomerValueIndexAdjustedWithCredit;
	}

	@Override
	public Integer getCustomerValueIndexAdjustedWithoutCredit() {
		return null;
	}

	@Override
	public void setCustomerValueIndexAdjustedWithoutCredit(Integer newCustomerValueIndexAdjustedWithoutCredit) {
		// noop
	}

	@Override
	public String getCustomerValueIndexBand() {
		return null;
	}

	@Override
	public void setCustomerValueIndexBand(String newCustomerValueIndexBand) {
		// noop
	}

	@Override
	public Integer getUnderwritingIndexScore() {
		return null;
	}

	@Override
	public void setUnderwritingIndexScore(Integer newUnderwritingIndexScore) {
		// noop
	}

	@Override
	public String getUnderwritingIndexBand() {
		return null;
	}

	@Override
	public void setUnderwritingIndexBand(String newUnderwritingIndexBand) {
		// noop
	}

	@Override
	public String getRetentionBand() {
		return null;
	}

	@Override
	public void setRetentionBand(String newRetentionBand) {
		// noop
	}

	@Override
	public String getPrincipalDriverHighestRatedVehicleInd() {
		return null;
	}

	@Override
	public void setPrincipalDriverHighestRatedVehicleInd(String newPrincipalDriverHighestRatedVehicleInd) {
		// noop
	}

	@Override
	public Integer getScoringAdjustmentPercentage() {
		return this.scoringAdjustmentPercentage;
	}

	@Override
	public void setScoringAdjustmentPercentage(Integer newScoringAdjustmentPercentage) {
		this.scoringAdjustmentPercentage = newScoringAdjustmentPercentage;
	}

	@Override
	public String getPotentialFacilityRiskSharingPoolCode() {
		return null;
	}

	@Override
	public void setPotentialFacilityRiskSharingPoolCode(String newPotentialFacilityRiskSharingPoolCode) {
		// noop
	}

	@Override
	public String getEligibilityToFacilityRiskSharingPoolInd() {
		return null;
	}

	@Override
	public void setEligibilityToFacilityRiskSharingPoolInd(String newEligibilityToFacilityRiskSharingPoolInd) {
		// noop
	}

	@Override
	public String getQualifiedToFacilityRiskSharingPoolInd() {
		return null;
	}

	@Override
	public void setQualifiedToFacilityRiskSharingPoolInd(String newQualifiedToFacilityRiskSharingPoolInd) {
		// noop
	}

	@Override
	public Integer getFacilityRiskSharingPoolScoringRegular() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolScoringRegular(Integer newFacilityRiskSharingPoolScoringRegular) {
		// noop
	}

	@Override
	public Integer getFacilityRiskSharingPoolScoringPreferred() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolScoringPreferred(Integer newFacilityRiskSharingPoolScoringPreferred) {
		// noop
	}

	@Override
	public Integer getFacilityRiskSharingPoolScoring() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolScoring(Integer newFacilityRiskSharingPoolScoring) {
		// noop
	}

	@Override
	public Double getFacilityRiskSharingPoolScoringDecimal() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolScoringDecimal(Double newFacilityRiskSharingPoolScoringDecimal) {

	}

	@Override
	public Integer getFacilityRiskSharingPoolCessionScoringRegular() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolCessionScoringRegular(Integer newFacilityRiskSharingPoolCessionScoringRegular) {
		// noop
	}

	@Override
	public Integer getFacilityRiskSharingPoolCessionScoringPreferred() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolCessionScoringPreferred(Integer newFacilityRiskSharingPoolCessionScoringPreferred) {
		// noop
	}

	@Override
	public String getFacilityRiskSharingPoolCessionActuaryDecision() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolCessionActuaryDecision(String newFacilityRiskSharingPoolCessionActuaryDecision) {

	}

	@Override
	public String getRspCessionDecisionIndicatorRegular() {
		return null;
	}

	@Override
	public void setRspCessionDecisionIndicatorRegular(String newRspCessionDecisionIndicatorRegular) {
		// noop
	}

	@Override
	public String getRspCessionDecisionIndicatorPreferred() {
		return null;
	}

	@Override
	public void setRspCessionDecisionIndicatorPreferred(String newRspCessionDecisionIndicatorPreferred) {
		// noop
	}

	@Override
	public String getRspTransfertType() {
		return null;
	}

	@Override
	public void setRspTransfertType(String newRspTransfertType) {
		// noop
	}

	@Override
	public GregorianCalendar getRspTransferDate() {
		return null;
	}

	@Override
	public void setRspTransferDate(GregorianCalendar newRspTransferDate) {

	}

	@Override
	public String getRspDecisionOrigin() {
		return null;
	}

	@Override
	public void setRspDecisionOrigin(String newRspDecisionOrigin) {
		// noop
	}

	@Override
	public String getRspMentionAtRenewal() {
		return null;
	}

	@Override
	public void setRspMentionAtRenewal(String newRspMentionAtRenewal) {
		// noop
	}

	@Override
	public String getRspReasonCode() {
		return null;
	}

	@Override
	public void setRspReasonCode(String newRspReasonCode) {
		// noop
	}

	@Override
	public String getRspReason() {
		return null;
	}

	@Override
	public void setRspReason(String newRspReason) {
		// noop
	}

	@Override
	public String getRspRestrictedChangeInd() {
		return null;
	}

	@Override
	public void setRspRestrictedChangeInd(String newRspRestrictedChangeInd) {

	}

	@Override
	public String getUnderwritingReviewRequiredInd() {
		return null;
	}

	@Override
	public void setUnderwritingReviewRequiredInd(String newUnderwritingReviewRequiredInd) {

	}

	@Override
	public String getRiskOutOfProvinceInd() {
		return null;
	}

	@Override
	public void setRiskOutOfProvinceInd(String newRiskOutOfProvinceInd) {
		// noop
	}

	@Override
	public Integer getInsurancePercentage() {
		return null;
	}

	@Override
	public void setInsurancePercentage(Integer newInsurancePercentage) {
		// noop
	}

	@Override
	public String getMinimumAnnualPremiumAppliesInd() {
		return null;
	}

	@Override
	public void setMinimumAnnualPremiumAppliesInd(String newMinimumAnnualPremiumAppliesInd) {
		// noop
	}

	@Override
	public Double getMinimumAnnualPremiumAdjustmentAmount() {
		return null;
	}

	@Override
	public void setMinimumAnnualPremiumAdjustmentAmount(Double newMinimumAnnualPremiumAdjustmentAmount) {
		// noop
	}

	@Override
	public Double getAnnualPremium() {
		return this.annualPremium;
	}

	@Override
	public void setAnnualPremium(Double newAnnualPremium) {
		this.annualPremium = newAnnualPremium;
	}

	@Override
	public Double getAnnualPremiumScoringRai() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoringRai(Double newAnnualPremiumScoringRai) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFlex() {
		return null;
	}

	@Override
	public void setAnnualPremiumFlex(Double newAnnualPremiumFlex) {
		// noop
	}

	@Override
	public Double getAnnualPremiumNonAdjustable() {
		return null;
	}

	@Override
	public void setAnnualPremiumNonAdjustable(Double newAnnualPremiumNonAdjustable) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAdjustedAddedAtRenewal() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAdjustedAddedAtRenewal(Double newAnnualPremiumCappedAdjustedAddedAtRenewal) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAdjustedDeletedAtRenewalGroundWater() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAdjustedDeletedAtRenewalGroundWater(Double newAnnualPremiumCappedAdjustedDeletedAtRenewalGroundWater) {

	}

	@Override
	public Double getAnnualPremiumCappedAdjustedDeletedAtRenewalOvrlndWater() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAdjustedDeletedAtRenewalOvrlndWater(Double newAnnualPremiumCappedAdjustedDeletedAtRenewalOvrlndWater) {

	}

	@Override
	public Double getAnnualPremiumCappedForBasicCoverage() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForBasicCoverage(Double newAnnualPremiumCappedForBasicCoverage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedWithoutCaa() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedWithoutCaa(Double newAnnualPremiumCappedWithoutCaa) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedWithoutCaaFirstRating() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedWithoutCaaFirstRating(Double newAnnualPremiumCappedWithoutCaaFirstRating) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedWithoutExcludedEndorsements() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedWithoutExcludedEndorsements(Double newAnnualPremiumCappedWithoutExcludedEndorsements) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedWithoutExcludedEndorsementsGrid() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedWithoutExcludedEndorsementsGrid(Double aDouble) {

	}

	@Override
	public Double getAnnualPremiumCappedForReplacementCost() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForReplacementCost(Double newAnnualPremiumCappedForReplacementCost) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedForReplacementCostFirstRating() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForReplacementCostFirstRating(Double newAnnualPremiumCappedForReplacementCostFirstRating) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedForVehicleStorage() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForVehicleStorage(Double newAnnualPremiumCappedForVehicleStorage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedForUbiDiscount() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForUbiDiscount(Double newAnnualPremiumCappedForUbiDiscount) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedForUbiSurcharge() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedForUbiSurcharge(Double newAnnualPremiumCappedForUbiSurcharge) {

	}

	@Override
	public Double getAnnualPremiumCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumCapped(Double newAnnualPremiumCapped) {
		// noop
	}

	@Override
	public Double getDeviationPercentage() {
		return null;
	}

	@Override
	public void setDeviationPercentage(Double newDeviationPercentage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedBeforeDeviation() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedBeforeDeviation(Double aDouble) {

	}

	@Override
	public Double getAnnualPremiumCappedAfterDeviation() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAfterDeviation(Double newAnnualPremiumCappedAfterDeviation) {
		// noop
	}

	@Override
	public Double getAnnualPremiumGrid() {
		return null;
	}

	@Override
	public void setAnnualPremiumGrid(Double newAnnualPremiumGrid) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoring() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoring(Double newAnnualPremiumScoring) {
		// noop
	}

	@Override
	public Double getAnnualPremiumPricing() {
		return null;
	}

	@Override
	public void setAnnualPremiumPricing(Double newAnnualPremiumPricing) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCosting() {
		return null;
	}

	@Override
	public void setAnnualPremiumCosting(Double newAnnualPremiumCosting) {
		// noop
	}

	@Override
	public Double getAnnualSubtotalPremiumForCvi() {
		return null;
	}

	@Override
	public void setAnnualSubtotalPremiumForCvi(Double newAnnualSubtotalPremiumForCvi) {
		// noop
	}

	@Override
	public Double getAnnualSubtotalPremiumForCviScoring() {
		return null;
	}

	@Override
	public void setAnnualSubtotalPremiumForCviScoring(Double newAnnualSubtotalPremiumForCviScoring) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidated() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidated(Double newAnnualPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCapped(Double newAnnualPremiumConsolidatedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCappedPreapproved(Double newAnnualPremiumConsolidatedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurcharged(Double newAnnualPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCapped(Double newAnnualPremiumDiscountedSurchargedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCappedPreapproved(Double newAnnualPremiumDiscountedSurchargedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumOfExcludedEndorsements() {
		return null;
	}

	@Override
	public void setAnnualPremiumOfExcludedEndorsements(Double newAnnualPremiumOfExcludedEndorsements) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPool(Double newAnnualPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPoolFloor(Double newAnnualPremiumFacilityRiskSharingPoolFloor) {
		// noop
	}

	@Override
	public Double getFacilityRiskSharingPoolFloorPercentage() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolFloorPercentage(Double newFacilityRiskSharingPoolFloorPercentage) {
		// noop
	}

	@Override
	public String getMinimumFullTermPremiumAppliesInd() {
		return null;
	}

	@Override
	public void setMinimumFullTermPremiumAppliesInd(String newMinimumFullTermPremiumAppliesInd) {
		// noop
	}

	@Override
	public Double getMinimumFullTermPremiumAdjustmentAmount() {
		return null;
	}

	@Override
	public void setMinimumFullTermPremiumAdjustmentAmount(Double newMinimumFullTermPremiumAdjustmentAmount) {
		// noop
	}

	@Override
	public Double getFullTermPremiumConversionExpectedDifference() {
		return null;
	}

	@Override
	public void setFullTermPremiumConversionExpectedDifference(Double newFullTermPremiumConversionExpectedDifference) {

	}

	@Override
	public Double getFullTermPremium() {
		return null;
	}

	@Override
	public void setFullTermPremium(Double newFullTermPremium) {
		// noop
	}

	@Override
	public Double getFullTermPremiumWithTax() {
		return null;
	}

	@Override
	public void setFullTermPremiumWithTax(Double newFullTermPremiumWithTax) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFlex() {
		return null;
	}

	@Override
	public void setFullTermPremiumFlex(Double newFullTermPremiumFlex) {
		// noop
	}

	@Override
	public Double getFullTermPremiumGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumGrid(Double newFullTermPremiumGrid) {
		// noop
	}

	@Override
	public Double getFullTermPremiumProRatedGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumProRatedGrid(Double newFullTermPremiumProRatedGrid) {
		// noop
	}

	@Override
	public Double getFullTermPremiumShortRatedGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumShortRatedGrid(Double newFullTermPremiumShortRatedGrid) {
		// noop
	}

	@Override
	public Double getFullTermPremiumForBasicCoverage() {
		return null;
	}

	@Override
	public void setFullTermPremiumForBasicCoverage(Double newFullTermPremiumForBasicCoverage) {
		// noop
	}

	@Override
	public Double getFullTermPremiumBuilding() {
		return null;
	}

	@Override
	public void setFullTermPremiumBuilding(Double newFullTermPremiumBuilding) {
		// noop
	}

	@Override
	public Double getFullTermPremiumContent() {
		return null;
	}

	@Override
	public void setFullTermPremiumContent(Double newFullTermPremiumContent) {
		// noop
	}

	@Override
	public Double getFullTermPremiumConsolidated() {
		return null;
	}

	@Override
	public void setFullTermPremiumConsolidated(Double newFullTermPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setFullTermPremiumDiscountedSurcharged(Double newFullTermPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDifferenceWithPriorTrans() {
		return null;
	}

	@Override
	public void setFullTermPremiumDifferenceWithPriorTrans(Double newFullTermPremiumDifferenceWithPriorTrans) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDifferencePercentageWithPriorTrans() {
		return null;
	}

	@Override
	public void setFullTermPremiumDifferencePercentageWithPriorTrans(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPool(Double newFullTermPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getRecommendedPremium() {
		return null;
	}

	@Override
	public void setRecommendedPremium(Double newRecommendedPremium) {

	}

	@Override
	public Double getAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremium(Double newAdditionalReturnPremium) {
		// noop
	}

	@Override
	public Double getAdditionalReturnPremiumGrid() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumGrid(Double newAdditionalReturnPremiumGrid) {
		// noop
	}

	@Override
	public Double getCumulatedAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setCumulatedAdditionalReturnPremium(Double newCumulatedAdditionalReturnPremium) {
		// noop
	}

	@Override
	public String getAdjustmentReason1() {
		return null;
	}

	@Override
	public void setAdjustmentReason1(String newAdjustmentReason1) {
		// noop
	}

	@Override
	public String getAdjustmentReason2() {
		return null;
	}

	@Override
	public void setAdjustmentReason2(String newAdjustmentReason2) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage1System() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage1System(Integer newAdjustmentPercentage1System) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage1Modified() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage1Modified(Integer newAdjustmentPercentage1Modified) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage1() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage1(Integer newAdjustmentPercentage1) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage2System() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage2System(Integer newAdjustmentPercentage2System) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage2Modified() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage2Modified(Integer newAdjustmentPercentage2Modified) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentage2() {
		return null;
	}

	@Override
	public void setAdjustmentPercentage2(Integer newAdjustmentPercentage2) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther1System() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther1System(Integer newAdjustmentPercentageOther1System) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther1Modified() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther1Modified(Integer newAdjustmentPercentageOther1Modified) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther1() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther1(Integer newAdjustmentPercentageOther1) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther2System() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther2System(Integer newAdjustmentPercentageOther2System) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther2Modified() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther2Modified(Integer newAdjustmentPercentageOther2Modified) {
		// noop
	}

	@Override
	public Integer getAdjustmentPercentageOther2() {
		return null;
	}

	@Override
	public void setAdjustmentPercentageOther2(Integer newAdjustmentPercentageOther2) {
		// noop
	}

	@Override
	public String getReasonForAbsenceOfThirdPartyLiabilityCoverage() {
		return null;
	}

	@Override
	public void setReasonForAbsenceOfThirdPartyLiabilityCoverage(String newReasonForAbsenceOfThirdPartyLiabilityCoverage) {
		// noop
	}

	@Override
	public String getVehicleClassSystem() {
		return null;
	}

	@Override
	public void setVehicleClassSystem(String newVehicleClassSystem) {
		// noop
	}

	@Override
	public String getVehicleClassModified() {
		return null;
	}

	@Override
	public void setVehicleClassModified(String newVehicleClassModified) {
		// noop
	}

	@Override
	public String getVehicleClass() {
		return null;
	}

	@Override
	public void setVehicleClass(String newVehicleClass) {
		// noop
	}

	@Override
	public String getVehicleClassOccasionalSystem() {
		return null;
	}

	@Override
	public void setVehicleClassOccasionalSystem(String newVehicleClassOccasionalSystem) {
		// noop
	}

	@Override
	public String getVehicleClassOccasionalModified() {
		return null;
	}

	@Override
	public void setVehicleClassOccasionalModified(String newVehicleClassOccasionalModified) {
		// noop
	}

	@Override
	public String getVehicleClassOccasional() {
		return null;
	}

	@Override
	public void setVehicleClassOccasional(String newVehicleClassOccasional) {
		// noop
	}

	@Override
	public String getVehicleSubclass() {
		return null;
	}

	@Override
	public void setVehicleSubclass(String newVehicleSubclass) {
		// noop
	}

	@Override
	public String getVehicleSubclassOccasional() {
		return null;
	}

	@Override
	public void setVehicleSubclassOccasional(String newVehicleSubclassOccasional) {
		// noop
	}

	@Override
	public String getCodingClassSystem() {
		return null;
	}

	@Override
	public void setCodingClassSystem(String newCodingClassSystem) {
		// noop
	}

	@Override
	public String getCodingClassModified() {
		return null;
	}

	@Override
	public void setCodingClassModified(String newCodingClassModified) {
		// noop
	}

	@Override
	public String getCodingClass() {
		return null;
	}

	@Override
	public void setCodingClass(String newCodingClass) {
		// noop
	}

	@Override
	public String getSafeBurglaryClass() {
		return null;
	}

	@Override
	public void setSafeBurglaryClass(String newSafeBurglaryClass) {
		// noop
	}

	@Override
	public String getNewOfferRequestInd() {
		return null;
	}

	@Override
	public void setNewOfferRequestInd(String newNewOfferRequestInd) {
		// noop
	}

	@Override
	public String getOfferType() {
		// CUSTOM will be the default value for tests
		this.offerType = this.offerType == null ? OfferTypeCodeEnum.CUSTOM.getCode() : this.offerType;
		return this.offerType;
	}

	@Override
	public void setOfferType(String newOfferType) {
		this.offerType = newOfferType;
	}

	@Override
	public String getInternalTechnicalOfferType() {
		return null;
	}

	@Override
	public void setInternalTechnicalOfferType(String newInternalTechnicalOfferType) {
		// noop
	}

	@Override
	public String getCoverageLevelOnRisk() {
		return null;
	}

	@Override
	public void setCoverageLevelOnRisk(String newCoverageLevelOnRisk) {
		// noop
	}

	@Override
	public String getRiskSelectedInd() {
		return this.riskSelectedInd;
	}

	@Override
	public void setRiskSelectedInd(String newRiskSelectedInd) {
		this.riskSelectedInd = newRiskSelectedInd;
	}

	@Override
	public Integer getPeriodLengthForClaimsHistory() {
		return null;
	}

	@Override
	public void setPeriodLengthForClaimsHistory(Integer newPeriodLengthForClaimsHistory) {
		// noop
	}

	@Override
	public Integer getNumberOfClaims() {
		return null;
	}

	@Override
	public void setNumberOfClaims(Integer newNumberOfClaims) {

	}

	@Override
	public Integer getNumberOfClaims1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaims1Year(Integer newNumberOfClaims1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaims2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaims2Years(Integer newNumberOfClaims2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaims3Years() {
		return this.numberOfClaims3Years;
	}

	@Override
	public void setNumberOfClaims3Years(Integer newNumberOfClaims3Years) {
		this.numberOfClaims3Years = newNumberOfClaims3Years;
	}

	@Override
	public Integer getNumberOfClaims5Years() {
		return this.numberOfClaims5Years;
	}

	@Override
	public void setNumberOfClaims5Years(Integer newNumberOfClaims5Years) {
		this.numberOfClaims5Years = newNumberOfClaims5Years;
	}

	@Override
	public Integer getNumberOfClaims6Years() {
		return this.numberOfClaims6Years;
	}

	@Override
	public void setNumberOfClaims6Years(Integer newNumberOfClaims6Years) {
		this.numberOfClaims6Years = newNumberOfClaims6Years;
	}

	@Override
	public Integer getNumberOfClaims10Years() {
		return this.numberOfClaims10Years;
	}

	@Override
	public void setNumberOfClaims10Years(Integer newNumberOfClaims10Years) {
		this.numberOfClaims10Years = newNumberOfClaims10Years;
	}

	@Override
	public Integer getNumberOfClaims5YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaims5YearsUnderwriting(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaims6YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaims6YearsUnderwriting(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaims10YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaims10YearsUnderwriting(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsConsidered3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsConsidered3Years(Integer newNumberOfClaimsConsidered3Years) {

	}

	@Override
	public Integer getNumberOfClaimsConsidered5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsConsidered5Years(Integer newNumberOfClaimsConsidered5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsCollision5Years(Integer newNumberOfClaimsCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsCollisionAndComprehensive3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsCollisionAndComprehensive3Years(Integer newNumberOfClaimsCollisionAndComprehensive3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerilsSmallAmt5Yrs() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerilsSmallAmt5Yrs(Integer newNumberOfClaimsComprehensiveSpecifiedPerilsSmallAmt5Yrs) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerilsLargeAmt5Yrs() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerilsLargeAmt5Yrs(Integer newNumberOfClaimsComprehensiveSpecifiedPerilsLargeAmt5Yrs) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerils1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerils1Year(Integer newNumberOfClaimsComprehensiveSpecifiedPerils1Year) {

	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerils2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerils2Years(Integer newNumberOfClaimsComprehensiveSpecifiedPerils2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerils3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerils3Years(Integer newNumberOfClaimsComprehensiveSpecifiedPerils3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerils5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerils5Years(Integer newNumberOfClaimsComprehensiveSpecifiedPerils5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsComprehensiveSpecifiedPerils6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsComprehensiveSpecifiedPerils6Years(Integer newNumberOfClaimsComprehensiveSpecifiedPerils6Years) {

	}

	@Override
	public Integer getNumberOfClaimsDisregarded3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded3Years(Integer newNumberOfClaimsDisregarded3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfClaimsDisregarded3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsDisregarded3Years(Integer newHighestNumberOfClaimsDisregarded3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsDisregarded5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded5Years(Integer newNumberOfClaimsDisregarded5Years) {

	}

	@Override
	public Integer getHighestNumberOfClaimsDisregarded5Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsDisregarded5Years(Integer newHighestNumberOfClaimsDisregarded5Years) {

	}

	@Override
	public Integer getNumberOfClaimsDisregarded6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded6Years(Integer newNumberOfClaimsDisregarded6Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfClaimsDisregarded6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsDisregarded6Years(Integer newHighestNumberOfClaimsDisregarded6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsEarthquake1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEarthquake1Year(Integer newNumberOfClaimsEarthquake1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsEarthquake5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEarthquake5Years(Integer newNumberOfClaimsEarthquake5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsEarthquake10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEarthquake10Years(Integer newNumberOfClaimsEarthquake10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsExcludingGlassBreakage3Years() {
		return this.numberOfClaimsExcludingGlassBreakage3Years;
	}

	@Override
	public void setNumberOfClaimsExcludingGlassBreakage3Years(Integer newNumberOfClaimsExcludingGlassBreakage3Years) {
		this.numberOfClaimsExcludingGlassBreakage3Years = newNumberOfClaimsExcludingGlassBreakage3Years;
	}

	@Override
	public Integer getNumberOfClaimsFire1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsFire1Year(Integer newNumberOfClaimsFire1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsFire3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsFire3Years(Integer newNumberOfClaimsFire3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsFire5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsFire5Years(Integer newNumberOfClaimsFire5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsFire10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsFire10Years(Integer newNumberOfClaimsFire10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsGlassRepair3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassRepair3Years(Integer newNumberOfClaimsGlassRepair3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsGlassReplacement1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassReplacement1Year(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsGlassReplacement2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassReplacement2Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsGlassReplacement3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassReplacement3Years(Integer newNumberOfClaimsGlassReplacement3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsGlassReplacement3YearsUw() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassReplacement3YearsUw(Integer newNumberOfClaimsGlassReplacement3YearsUw) {

	}

	@Override
	public Integer getNumberOfClaimsLiability1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiability1Year(Integer newNumberOfClaimsLiability1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiability5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiability5Years(Integer newNumberOfClaimsLiability5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiability10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiability10Years(Integer newNumberOfClaimsLiability10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOther1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOther1Year(Integer newNumberOfClaimsOther1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOther5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOther5Years(Integer newNumberOfClaimsOther5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOther10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOther10Years(Integer newNumberOfClaimsOther10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsSewerBackup1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsSewerBackup1Year(Integer newNumberOfClaimsSewerBackup1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsSewerBackup5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsSewerBackup5Years(Integer newNumberOfClaimsSewerBackup5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsSewerBackup10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsSewerBackup10Years(Integer newNumberOfClaimsSewerBackup10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsTheft1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft1Year(Integer newNumberOfClaimsTheft1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsTheft3YearsWithAmountMin10000() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft3YearsWithAmountMin10000(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsTheft5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft5Years(Integer newNumberOfClaimsTheft5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsTheft6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft6Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsTheft10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft10Years(Integer newNumberOfClaimsTheft10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWaterDamage1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWaterDamage1Year(Integer newNumberOfClaimsWaterDamage1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWaterDamage5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWaterDamage5Years(Integer newNumberOfClaimsWaterDamage5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWaterDamage10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWaterDamage10Years(Integer newNumberOfClaimsWaterDamage10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWindAndHail1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWindAndHail1Year(Integer newNumberOfClaimsWindAndHail1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWindAndHail5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWindAndHail5Years(Integer newNumberOfClaimsWindAndHail5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWindAndHail10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWindAndHail10Years(Integer newNumberOfClaimsWindAndHail10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsHail10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsHail10Years(Integer newNumberOfClaimsHail10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsWind10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWind10Years(Integer newNumberOfClaimsWind10Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfClaimsLiable3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsLiable3Years(Integer newHighestNumberOfClaimsLiable3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfClaimsLiable5Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsLiable5Years(Integer newHighestNumberOfClaimsLiable5Years) {

	}

	@Override
	public Integer getHighestNumberOfClaimsLiable6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsLiable6Years(Integer newHighestNumberOfClaimsLiable6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiable1Year() {
		return this.numberOfCLaimsLiable1Year;
	}

	@Override
	public void setNumberOfClaimsLiable1Year(Integer newNumberOfClaimsLiable1Year) {
		this.numberOfCLaimsLiable1Year = newNumberOfClaimsLiable1Year;
	}

	@Override
	public Integer getNumberOfClaimsLiable2Years() {
		return this.numberOfCLaimsLiable2Years;
	}

	@Override
	public void setNumberOfClaimsLiable2Years(Integer newNumberOfClaimsLiable2Years) {
		this.numberOfCLaimsLiable2Years = newNumberOfClaimsLiable2Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable3Years() {
		return this.numberOfCLaimsLiable3Years;
	}

	@Override
	public void setNumberOfClaimsLiable3Years(Integer newNumberOfClaimsLiable3Years) {
		this.numberOfCLaimsLiable3Years = newNumberOfClaimsLiable3Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable4Years() {
		return this.numberOfCLaimsLiable4Years;
	}

	@Override
	public void setNumberOfClaimsLiable4Years(Integer newNumberOfClaimsLiable4Years) {
		this.numberOfCLaimsLiable4Years = newNumberOfClaimsLiable4Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable5Years() {
		return this.numberOfCLaimsLiable5Years;
	}

	@Override
	public void setNumberOfClaimsLiable5Years(Integer newNumberOfClaimsLiable5Years) {
		this.numberOfCLaimsLiable5Years = newNumberOfClaimsLiable5Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable6Years() {
		return this.numberOfCLaimsLiable6Years;
	}

	@Override
	public void setNumberOfClaimsLiable6Years(Integer newNumberOfClaimsLiable6Years) {
		this.numberOfCLaimsLiable6Years = newNumberOfClaimsLiable6Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable7Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable7Years(Integer newNumberOfClaimsLiable7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiable9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable9Years(Integer newNumberOfClaimsLiable9Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiable10Years() {
		return this.numberOfCLaimsLiable10Years;
	}

	@Override
	public void setNumberOfClaimsLiable10Years(Integer newNumberOfClaimsLiable10Years) {
		this.numberOfCLaimsLiable10Years = newNumberOfClaimsLiable10Years;
	}

	@Override
	public Integer getNumberOfClaimsLiable15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable15Years(Integer newNumberOfClaimsLiable15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableInternal6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableInternal6Years(Integer newNumberOfClaimsLiableInternal6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableInternal9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableInternal9Years(Integer newNumberOfClaimsLiableInternal9Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableInternal15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableInternal15Years(Integer newNumberOfClaimsLiableInternal15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableCollision1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision1Year(Integer newNumberOfClaimsLiableCollision1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision5Years(Integer newNumberOfClaimsLiableCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableCollision6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision6Years(Integer newNumberOfClaimsLiableCollision6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableDirectCompensation6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableDirectCompensation6Years(Integer newNumberOfClaimsLiableDirectCompensation6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableLiability6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableLiability6Years(Integer newNumberOfClaimsLiableLiability6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableMotorcycle6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableMotorcycle6Years(Integer newNumberOfClaimsLiableMotorcycle6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOtherThanCollision1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOtherThanCollision1Year(Integer newNumberOfClaimsLiableOtherThanCollision1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOtherThanCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOtherThanCollision5Years(Integer newNumberOfClaimsLiableOtherThanCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalDriverDuringPriorTerm() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalDriverDuringPriorTerm(Integer newNumberOfClaimsLiablePrincipalDriverDuringPriorTerm) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriverDuringPriorTerm() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriverDuringPriorTerm(Integer newNumberOfClaimsLiableOccasionalDriverDuringPriorTerm) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalDriver3Years(Integer newNumberOfClaimsLiablePrincipalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalDriver5Years(Integer newNumberOfClaimsLiablePrincipalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalDriver6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalDriver6Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver1Year(Integer newNumberOfClaimsLiableOccasionalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver2Years(Integer newNumberOfClaimsLiableOccasionalDriver2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver3Years(Integer newNumberOfClaimsLiableOccasionalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver4Years(Integer newNumberOfClaimsLiableOccasionalDriver4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver5Years(Integer newNumberOfClaimsLiableOccasionalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver6Years() {
		return this.numberOfClaimsLiableOccasionalDriver6Years;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver6Years(Integer newNumberOfClaimsLiableOccasionalDriver6Years) {
		this.numberOfClaimsLiableOccasionalDriver6Years = newNumberOfClaimsLiableOccasionalDriver6Years;
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver7Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver7Years(Integer newNumberOfClaimsLiableOccasionalDriver7Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver10Years(Integer newNumberOfClaimsLiableOccasionalDriver10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver15Years(Integer newNumberOfClaimsLiableOccasionalDriver15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver1Year(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver2Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver3Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver4Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver5Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years() {
		return this.numberOfClaimsLiablePrincipalAndNonRatedDriver6Years;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years) {
		this.numberOfClaimsLiablePrincipalAndNonRatedDriver6Years = newNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years;
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver7Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver7Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver9Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver9Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver10Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiablePrincipalAndNonRatedDriver15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincipalAndNonRatedDriver15Years(Integer newNumberOfClaimsLiablePrincipalAndNonRatedDriver15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableNonRatedDriver5Years(Integer newNumberOfClaimsLiableNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiable1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable1Year(Integer newNumberOfClaimsNonLiable1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiable3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable3Years(Integer newNumberOfClaimsNonLiable3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiable5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable5Years(Integer newNumberOfClaimsNonLiable5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiable6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable6Years(Integer newNumberOfClaimsNonLiable6Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfClaimsNonLiable6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsNonLiable6Years(Integer newHighestNumberOfClaimsNonLiable6Years) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision1Year(Integer newNumberOfClaimsNonLiableCollision1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision2Years(Integer newNumberOfClaimsNonLiableCollision2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision3Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision3Year(Integer newNumberOfClaimsNonLiableCollision3Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision4Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision4Year(Integer newNumberOfClaimsNonLiableCollision4Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision5Years(Integer newNumberOfClaimsNonLiableCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision6Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision6Year(Integer newNumberOfClaimsNonLiableCollision6Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableComprehensiveSpecifiedPerils2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableComprehensiveSpecifiedPerils2Yr(Integer newNumberOfClaimsNonLiableComprehensiveSpecifiedPerils2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableComprehensiveSpecifiedPerils3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableComprehensiveSpecifiedPerils3Yr(Integer newNumberOfClaimsNonLiableComprehensiveSpecifiedPerils3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableDcpd2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableDcpd2Years(Integer newNumberOfClaimsNonLiableDcpd2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableDcpd3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableDcpd3Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalDriver5Years(Integer newNumberOfClaimsNonLiablePrincipalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriver5Years(Integer newNumberOfClaimsNonLiableOccasionalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriver6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriver6Years(Integer newNumberOfClaimsNonLiableOccasionalDriver6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDriver3Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDriver6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDriver6Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDriver6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl1Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl2Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl3Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl4Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl5Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl6Yr(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDrvrColl6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision1Year(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision2Years(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision3Years(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision4Years(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision5Years(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalDriverCollision6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalDriverCollision6Years(Integer newNumberOfClaimsNonLiableOccasionalDriverCollision6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd1Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd2Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd3Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd4Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd5Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOtherThanCollisionAndWindshd6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOtherThanCollisionAndWindshd6Yr(Integer newNumberOfClaimsNonLiableOtherThanCollisionAndWindshd6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind1Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind2Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind3Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind4Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind5Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind6Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatOthCollAndWind6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind1Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind2Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind3Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind4Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind5Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalOthCollAndWind6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalOthCollAndWind6Yr(Integer newNumberOfClaimsNonLiableOccasionalOthCollAndWind6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind1Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind2Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind3Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind4Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind5Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincAndNonRatWind6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincAndNonRatWind6Yr(Integer newNumberOfClaimsNonLiablePrincAndNonRatWind6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind1Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind1Yr(Integer newNumberOfClaimsNonLiableOccasionalWind1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind2Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind2Yr(Integer newNumberOfClaimsNonLiableOccasionalWind2Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind3Yr(Integer newNumberOfClaimsNonLiableOccasionalWind3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind4Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind4Yr(Integer newNumberOfClaimsNonLiableOccasionalWind4Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind5Yr(Integer newNumberOfClaimsNonLiableOccasionalWind5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableOccasionalWind6Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableOccasionalWind6Yr(Integer newNumberOfClaimsNonLiableOccasionalWind6Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableNonRatedDriver5Years(Integer newNumberOfClaimsNonLiableNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiablePrincipalAndNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiablePrincipalAndNonRatedDriver5Years(Integer newNumberOfClaimsNonLiablePrincipalAndNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableThirdPartyLiabAllPerilsColl3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableThirdPartyLiabAllPerilsColl3Yr(Integer newNumberOfClaimsNonLiableThirdPartyLiabAllPerilsColl3Yr) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiaThrdPartyLiaAllPerlColPrNonRtDr3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiaThrdPartyLiaAllPerlColPrNonRtDr3Yr(Integer newNumberOfClaimsNonLiaThrdPartyLiaAllPerlColPrNonRtDr3Yr) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiableThirdPartyLiabAllPerlColOcDr3Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableThirdPartyLiabAllPerlColOcDr3Yr(Integer newNumberOfClaimsNonLiableThirdPartyLiabAllPerlColOcDr3Yr) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd1Year(Integer newNumberOfClaimsNonLiableWindshd1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd2Years(Integer newNumberOfClaimsNonLiableWindshd2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd3Years(Integer newNumberOfClaimsNonLiableWindshd3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd4Years(Integer newNumberOfClaimsNonLiableWindshd4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd5Years(Integer newNumberOfClaimsNonLiableWindshd5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableWindshd6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableWindshd6Years(Integer newNumberOfClaimsNonLiableWindshd6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiableIncludingMidterm10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableIncludingMidterm10Years(Integer newNumberOfClaimsNonLiableIncludingMidterm10Years) {

	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver1Year(Integer newNumberOfClaimsPrincipalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver2Years(Integer newNumberOfClaimsPrincipalDriver2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver3Years(Integer newNumberOfClaimsPrincipalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver4Years(Integer newNumberOfClaimsPrincipalDriver4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver5Years(Integer newNumberOfClaimsPrincipalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriver6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriver6Years(Integer newNumberOfClaimsPrincipalDriver6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalAndNonRatedDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalAndNonRatedDriver1Year(Integer newNumberOfClaimsPrincipalAndNonRatedDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalAndNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalAndNonRatedDriver3Years(Integer newNumberOfClaimsPrincipalAndNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalAndNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalAndNonRatedDriver5Years(Integer newNumberOfClaimsPrincipalAndNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver1Year(Integer newNumberOfClaimsOccasionalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver2Years(Integer newNumberOfClaimsOccasionalDriver2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver3Years(Integer newNumberOfClaimsOccasionalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver4Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver4Years(Integer newNumberOfClaimsOccasionalDriver4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver5Years(Integer newNumberOfClaimsOccasionalDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriver6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriver6Years(Integer newNumberOfClaimsOccasionalDriver6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonRatedDriver5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonRatedDriver5Years(Integer newNumberOfClaimsNonRatedDriver5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincipalDriverDuringPriorTerm() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincipalDriverDuringPriorTerm(Integer newNumberOfClaimsPrincipalDriverDuringPriorTerm) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverDuringPriorTerm() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverDuringPriorTerm(Integer newNumberOfClaimsOccasionalDriverDuringPriorTerm) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsTheft3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsTheft3Years(Integer newNumberOfClaimsTheft3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaimsPrincAndNonRatedDrvr6Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaimsPrincAndNonRatedDrvr6Years(Integer newNumberOfForgivenClaimsPrincAndNonRatedDrvr6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaimsPrincAndNonRatedDrvr7Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaimsPrincAndNonRatedDrvr7Years(Integer newNumberOfForgivenClaimsPrincAndNonRatedDrvr7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedLiability5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedLiability5Years(Integer newNumberOfClaimsPrincAndNonRatedLiability5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverLiability5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverLiability5Years(Integer newNumberOfClaimsOccasionalDriverLiability5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedCollision5Years(Integer newNumberOfClaimsPrincAndNonRatedCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverCollision5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverCollision5Years(Integer newNumberOfClaimsOccasionalDriverCollision5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedSpSmallAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedSpSmallAmt5Yr(Integer newNumberOfClaimsPrincAndNonRatedSpSmallAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverSpSmallAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverSpSmallAmt5Yr(Integer newNumberOfClaimsOccasionalDriverSpSmallAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedSpLargeAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedSpLargeAmt5Yr(Integer newNumberOfClaimsPrincAndNonRatedSpLargeAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverSpLargeAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverSpLargeAmt5Yr(Integer newNumberOfClaimsOccasionalDriverSpLargeAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatLiability5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatLiability5Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatLiability5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatCollision5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatCollision5Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatCollision5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt5Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt5Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt5Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt5Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedLiability10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedLiability10Years(Integer newNumberOfClaimsPrincAndNonRatedLiability10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverLiability10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverLiability10Years(Integer newNumberOfClaimsOccasionalDriverLiability10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedCollision10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedCollision10Years(Integer newNumberOfClaimsPrincAndNonRatedCollision10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverCollision10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverCollision10Years(Integer newNumberOfClaimsOccasionalDriverCollision10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedSpSmallAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedSpSmallAmt10Yr(Integer newNumberOfClaimsPrincAndNonRatedSpSmallAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverSpSmallAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverSpSmallAmt10Yr(Integer newNumberOfClaimsOccasionalDriverSpSmallAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsPrincAndNonRatedSpLargeAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsPrincAndNonRatedSpLargeAmt10Yr(Integer newNumberOfClaimsPrincAndNonRatedSpLargeAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsOccasionalDriverSpLargeAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsOccasionalDriverSpLargeAmt10Yr(Integer newNumberOfClaimsOccasionalDriverSpLargeAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatLiability10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatLiability10Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatLiability10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatCollision10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatCollision10Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatCollision10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt10Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatSpSmallAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt10Yr() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt10Yr(Integer newNumberOfClaimsNonForgPrincAndNonRatSpLargeAmt10Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsHitAndRun2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsHitAndRun2Years(Integer newNumberOfClaimsHitAndRun2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsHitAndRun3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsHitAndRun3Years(Integer newNumberOfClaimsHitAndRun3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfDrivers() {
		return null;
	}

	@Override
	public void setNumberOfDrivers(Integer newNumberOfDrivers) {
		// noop
	}

	@Override
	public Integer getNumberOfConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfConvictions3Years(Integer newNumberOfConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfConvictionsPrincipalAndNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfConvictionsPrincipalAndNonRatedDriver3Years(Integer newNumberOfConvictionsPrincipalAndNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfConvictionsOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfConvictionsOccasionalDriver3Years(Integer newNumberOfConvictionsOccasionalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3Years(Integer newNumberOfMinorConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions10Years(Integer newNumberOfMinorConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3Years(Integer newNumberOfMajorConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions10Years(Integer newNumberOfMajorConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsPrincipalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsPrincipalDriver1Year(Integer newNumberOfMajorConvictionsPrincipalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsOccasionalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsOccasionalDriver1Year(Integer newNumberOfMajorConvictionsOccasionalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsPrincipalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsPrincipalDriver3Years(Integer newNumberOfMajorConvictionsPrincipalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsNonRatedDriver3Years(Integer newNumberOfMajorConvictionsNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsPrincipalAndNonRatedDriver1Yr() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsPrincipalAndNonRatedDriver1Yr(Integer newNumberOfMajorConvictionsPrincipalAndNonRatedDriver1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsPrincipalAndNonRatedDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsPrincipalAndNonRatedDriver3Yr(Integer newNumberOfMajorConvictionsPrincipalAndNonRatedDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsPrincipalAndOccDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsPrincipalAndOccDriver3Yr(Integer newNumberOfMajorConvictionsPrincipalAndOccDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictionsOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictionsOccasionalDriver3Years(Integer newNumberOfMajorConvictionsOccasionalDriver3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfConvictions3Years(Integer newHighestNumberOfConvictions3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfDistractedDrivingConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfDistractedDrivingConvictions3Years(Integer newHighestNumberOfDistractedDrivingConvictions3Years) {

	}

	@Override
	public Integer getHighestNumberOfMajorConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfMajorConvictions3Years(Integer newHighestNumberOfMajorConvictions3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfMajorConvictions6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfMajorConvictions6Years(Integer newHighestNumberOfMajorConvictions6Years) {

	}

	@Override
	public Integer getHighestNumberOfAlcoholConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfAlcoholConvictions3Years(Integer newHighestNumberOfAlcoholConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsExcludingAlcohol3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsExcludingAlcohol3Years(Integer newNumberOfSevereConvictionsExcludingAlcohol3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3Years(Integer newNumberOfSevereConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfAlcoholConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfAlcoholConvictions3Years(Integer newNumberOfAlcoholConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfAlcoholConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfAlcoholConvictions10Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfSevereConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions10Years(Integer newNumberOfSevereConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsPrincipalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsPrincipalDriver1Year(Integer newNumberOfSevereConvictionsPrincipalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsOccasionalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsOccasionalDriver1Year(Integer newNumberOfSevereConvictionsOccasionalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsPrincipalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsPrincipalDriver3Years(Integer newNumberOfSevereConvictionsPrincipalDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsNonRatedDriver3Years(Integer newNumberOfSevereConvictionsNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsPrincipalAndNonRatedDriver1Yr() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsPrincipalAndNonRatedDriver1Yr(Integer newNumberOfSevereConvictionsPrincipalAndNonRatedDriver1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsPrincipalAndNonRatedDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsPrincipalAndNonRatedDriver3Yr(Integer newNumberOfSevereConvictionsPrincipalAndNonRatedDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsPrincipalAndOccDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsPrincipalAndOccDriver3Yr(Integer newNumberOfSevereConvictionsPrincipalAndOccDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsOccasionalDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsOccasionalDriver3Years(Integer newNumberOfSevereConvictionsOccasionalDriver3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfSevereConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfSevereConvictions3Years(Integer newHighestNumberOfSevereConvictions3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfSevereConvictions6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfSevereConvictions6Years(Integer newHighestNumberOfSevereConvictions6Years) {

	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalDriver1Year(Integer newNumberOfMinorConvictionsPrincipalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsOccasionalDriver1Year() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsOccasionalDriver1Year(Integer newNumberOfMinorConvictionsOccasionalDriver1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalDriver3Years() {
		return this.numberOfMinorConvictionsPrincipalDriver3Years;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalDriver3Years(Integer newNumberOfMinorConvictionsPrincipalDriver3Years) {
		this.numberOfMinorConvictionsPrincipalDriver3Years = newNumberOfMinorConvictionsPrincipalDriver3Years;
	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalDriver3YearsUw() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalDriver3YearsUw(Integer integer) {

	}

	@Override
	public Integer getNumberOfMinorConvictionsNonRatedDriver3Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsNonRatedDriver3Years(Integer newNumberOfMinorConvictionsNonRatedDriver3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalAndNonRatedDriver1Yr() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalAndNonRatedDriver1Yr(Integer newNumberOfMinorConvictionsPrincipalAndNonRatedDriver1Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalAndNonRatedDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalAndNonRatedDriver3Yr(Integer newNumberOfMinorConvictionsPrincipalAndNonRatedDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsPrincipalAndOccDriver3Yr() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictionsPrincipalAndOccDriver3Yr(Integer newNumberOfMinorConvictionsPrincipalAndOccDriver3Yr) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictionsOccasionalDriver3Years() {
		return this.numberOfMinorConvictionsOccasionalDriver3Years;
	}

	@Override
	public void setNumberOfMinorConvictionsOccasionalDriver3Years(Integer newNumberOfMinorConvictionsOccasionalDriver3Years) {
		this.numberOfMinorConvictionsOccasionalDriver3Years = newNumberOfMinorConvictionsOccasionalDriver3Years;
	}

	@Override
	public Integer getHighestNumberOfMinorConvictions3Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfMinorConvictions3Years(Integer newHighestNumberOfMinorConvictions3Years) {
		// noop
	}

	@Override
	public Integer getHighestNumberOfMinorConvictions6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfMinorConvictions6Years(Integer newHighestNumberOfMinorConvictions6Years) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastForgLiableClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastForgLiableClaimPrincAndNonRatDr(Integer newNumberOfMonthsSinceLastForgLiableClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaimPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaimPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastLiableClaimPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaimOccDrvr(Integer newNumberOfMonthsSinceLastLiableClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConvictionPrincDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConvictionPrincDrvr(Integer newNumberOfMonthsSinceLastMinorConvictionPrincDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConvictionOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConvictionOccDrvr(Integer newNumberOfMonthsSinceLastMinorConvictionOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastConvPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastConvPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastConvPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastSevereConvPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSevereConvPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastSevereConvPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastSevereConvOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSevereConvOccDrvr(Integer newNumberOfMonthsSinceLastSevereConvOccDrvr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConvPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConvPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastMajorConvPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConvOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConvOccDrvr(Integer newNumberOfMonthsSinceLastMajorConvOccDrvr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConvPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConvPrincAndNonRatedDrvr(Integer newNumberOfMonthsSinceLastMinorConvPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastClaim(Integer newNumberOfYearsSinceLastClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableClaim(Integer newNumberOfYearsSinceLastLiableClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastConsideredClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastConsideredClaim(Integer newNumberOfYearsSinceLastConsideredClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableClaimPrincAndNonRatedDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableClaimPrincAndNonRatedDrvr(Integer newNumberOfYearsSinceLastLiableClaimPrincAndNonRatedDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastForgLiableClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastForgLiableClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastForgLiableClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastNonLiableClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonLiableClaimPrincAndNonRatedDr(Integer newNumberOfYearsSinceLastNonLiableClaimPrincAndNonRatedDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableClaimOccDrvr(Integer newNumberOfYearsSinceLastLiableClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonLiableClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonLiableClaimOccDrvr(Integer newNumberOfYearsSinceLastNonLiableClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastEarthquakeClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastEarthquakeClaim(Integer newNumberOfYearsSinceLastEarthquakeClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastFireClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastFireClaim(Integer newNumberOfYearsSinceLastFireClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiabilityClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiabilityClaim(Integer newNumberOfYearsSinceLastLiabilityClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastOtherClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastOtherClaim(Integer newNumberOfYearsSinceLastOtherClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSewerBackupClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSewerBackupClaim(Integer newNumberOfYearsSinceLastSewerBackupClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastTheftClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastTheftClaim(Integer newNumberOfYearsSinceLastTheftClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastWaterDamageClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastWaterDamageClaim(Integer newNumberOfYearsSinceLastWaterDamageClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastWindAndHailClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastWindAndHailClaim(Integer newNumberOfYearsSinceLastWindAndHailClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastHailClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastHailClaim(Integer newNumberOfYearsSinceLastHailClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastWindClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastWindClaim(Integer newNumberOfYearsSinceLastWindClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiabilityClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiabilityClaimOccDrvr(Integer newNumberOfYearsSinceLastLiabilityClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiabilityClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiabilityClaimPrincAndNonRatedDr(Integer newNumberOfYearsSinceLastLiabilityClaimPrincAndNonRatedDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastCollisionClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastCollisionClaimPrincAndNonRatedDr(Integer newNumberOfYearsSinceLastCollisionClaimPrincAndNonRatedDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastCollisionClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastCollisionClaimOccDrvr(Integer newNumberOfYearsSinceLastCollisionClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpClaim(Integer newNumberOfYearsSinceLastSpClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpClaimOccDrvr(Integer newNumberOfYearsSinceLastSpClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpClaimPrincAndNonRatedDr(Integer newNumberOfYearsSinceLastSpClaimPrincAndNonRatedDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpLargeAmtClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpLargeAmtClaimOccDrvr(Integer newNumberOfYearsSinceLastSpLargeAmtClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpLargeAmtClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpLargeAmtClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastSpLargeAmtClaimPrincAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpSmallAmtClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpSmallAmtClaimOccDrvr(Integer newNumberOfYearsSinceLastSpSmallAmtClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastSpSmallAmtClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastSpSmallAmtClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastSpSmallAmtClaimPrincAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfNonPrincipalDriversUnder25YearsWithoutRegLic() {
		return null;
	}

	@Override
	public void setNumberOfNonPrincipalDriversUnder25YearsWithoutRegLic(Integer newNumberOfNonPrincipalDriversUnder25YearsWithoutRegLic) {
		// noop
	}

	@Override
	public Integer getNumberOfNonPrincipalDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfNonPrincipalDriversUnder25Years(Integer newNumberOfNonPrincipalDriversUnder25Years) {
		// noop
	}

	@Override
	public Integer getNumberOfNonRatedDriversOver24Years() {
		return null;
	}

	@Override
	public void setNumberOfNonRatedDriversOver24Years(Integer newNumberOfNonRatedDriversOver24Years) {
		// noop
	}

	@Override
	public Integer getNumberOfNonRatedDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfNonRatedDriversUnder25Years(Integer newNumberOfNonRatedDriversUnder25Years) {
		// noop
	}

	@Override
	public Integer getNumberOfNonRatedFemaleDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfNonRatedFemaleDriversUnder25Years(Integer newNumberOfNonRatedFemaleDriversUnder25Years) {
		// noop
	}

	@Override
	public Integer getNumberOfNonRatedSingleFemaleDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfNonRatedSingleFemaleDriversUnder25Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfOccasionalDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfOccasionalDriversUnder25Years(Integer newNumberOfOccasionalDriversUnder25Years) {
		// noop
	}

	@Override
	public Integer getNumberOfNonRatedDriversUnder25YearsWithoutRegLic() {
		return null;
	}

	@Override
	public void setNumberOfNonRatedDriversUnder25YearsWithoutRegLic(Integer newNumberOfNonRatedDriversUnder25YearsWithoutRegLic) {
		// noop
	}

	@Override
	public Integer getNumberOfOccasionalDriversUnder25YearsWithoutRegLic() {
		return null;
	}

	@Override
	public void setNumberOfOccasionalDriversUnder25YearsWithoutRegLic(Integer newNumberOfOccasionalDriversUnder25YearsWithoutRegLic) {
		// noop
	}

	@Override
	public Integer getNumberOfPrincipalDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfPrincipalDriversUnder25Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfDisregardedDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfDisregardedDriversUnder25Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfDisregardedFemaleDriversUnder25Years() {
		return null;
	}

	@Override
	public void setNumberOfDisregardedFemaleDriversUnder25Years(Integer integer) {

	}

	@Override
	public Integer getMaximumNumberOfNonPaymentCancellations3Years() {
		return null;
	}

	@Override
	public void setMaximumNumberOfNonPaymentCancellations3Years(Integer newMaximumNumberOfNonPaymentCancellations3Years) {
		// noop
	}

	@Override
	public Integer getMaximumNumberOfCancellationsForNonDisclosure3Years() {
		return null;
	}

	@Override
	public void setMaximumNumberOfCancellationsForNonDisclosure3Years(Integer newMaximumNumberOfCancellationsForNonDisclosure3Years) {
		// noop
	}

	@Override
	public Integer getMaximumNumberOfInsuranceFrauds10Years() {
		return null;
	}

	@Override
	public void setMaximumNumberOfInsuranceFrauds10Years(Integer newMaximumNumberOfInsuranceFrauds10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfFamiliesInDwelling() {
		return null;
	}

	@Override
	public void setNumberOfFamiliesInDwelling(Integer newNumberOfFamiliesInDwelling) {
		// noop
	}

	@Override
	public Integer getNumberOfUnrelatedOccupants() {
		return null;
	}

	@Override
	public void setNumberOfUnrelatedOccupants(Integer newNumberOfUnrelatedOccupants) {
		// noop
	}

	@Override
	public GregorianCalendar getCircumstanceVersionDateTime() {
		return null;
	}

	@Override
	public void setCircumstanceVersionDateTime(GregorianCalendar newCircumstanceVersionDateTime) {
		// noop
	}

	@Override
	public GregorianCalendar getCircDateTime() {
		return null;
	}

	@Override
	public void setCircDateTime(GregorianCalendar newCircDateTime) {
		// noop
	}

	@Override
	public GregorianCalendar getCircCommercialDateTime() {
		return null;
	}

	@Override
	public void setCircCommercialDateTime(GregorianCalendar newCircCommercialDateTime) {
		// noop
	}

	@Override
	public String getQualityOfNeighbourhood() {
		return null;
	}

	@Override
	public void setQualityOfNeighbourhood(String newQualityOfNeighbourhood) {
		// noop
	}

	@Override
	public String getTheftCoverageInd() {
		return null;
	}

	@Override
	public void setTheftCoverageInd(String newTheftCoverageInd) {
		// noop
	}

	@Override
	public String getAboveGroundPoolCoverageInd() {
		return null;
	}

	@Override
	public void setAboveGroundPoolCoverageInd(String newAboveGroundPoolCoverageInd) {
		// noop
	}

	@Override
	public String getEarthquakeCoverageInd() {
		return null;
	}

	@Override
	public void setEarthquakeCoverageInd(String newEarthquakeCoverageInd) {
		// noop
	}

	@Override
	public String getSewerBackUpCoverageInd() {
		return null;
	}

	@Override
	public void setSewerBackUpCoverageInd(String newSewerBackUpCoverageInd) {
		// noop
	}

	@Override
	public String getRewardProgramProtectionCoverageInd() {
		return null;
	}

	@Override
	public void setRewardProgramProtectionCoverageInd(String newRewardProgramProtectionCoverageInd) {
		// noop
	}

	@Override
	public String getFloodExposureInd() {
		return null;
	}

	@Override
	public void setFloodExposureInd(String newFloodExposureInd) {
		// noop
	}

	@Override
	public String getWaterDamageWithoutSewerBackUpCoverageInd() {
		return null;
	}

	@Override
	public void setWaterDamageWithoutSewerBackUpCoverageInd(String newWaterDamageWithoutSewerBackUpCoverageInd) {
		// noop
	}

	@Override
	public String getWaterDamageGroundWaterAndSewerBackUpCoverageInd() {
		return null;
	}

	@Override
	public void setWaterDamageGroundWaterAndSewerBackUpCoverageInd(String newWaterDamageGroundWaterAndSewerBackUpCoverageInd) {
		// noop
	}

	@Override
	public String getWaterDamageAboveGroundWaterCoverageInd() {
		return null;
	}

	@Override
	public void setWaterDamageAboveGroundWaterCoverageInd(String newWaterDamageAboveGroundWaterCoverageInd) {
		// noop
	}

	@Override
	public String getWaterDamageCoverageAndLimitChangeInd() {
		return null;
	}

	@Override
	public void setWaterDamageCoverageAndLimitChangeInd(String newWaterDamageCoverageAndLimitChangeInd) {
		// noop
	}

	@Override
	public String getBoatCoverageInd() {
		return null;
	}

	@Override
	public void setBoatCoverageInd(String newBoatCoverageInd) {
		// noop
	}

	@Override
	public String getSeniorTenantCoverageInd() {
		return null;
	}

	@Override
	public void setSeniorTenantCoverageInd(String newSeniorTenantCoverageInd) {
		// noop
	}

	@Override
	public String getWinterTireCoverageInd() {
		return null;
	}

	@Override
	public void setWinterTireCoverageInd(String newWinterTireCoverageInd) {
		// noop
	}

	@Override
	public String getGlassRemovalCoverageInd() {
		return null;
	}

	@Override
	public void setGlassRemovalCoverageInd(String newGlassRemovalCoverageInd) {
		// noop
	}

	@Override
	public String getCrashProofCoverageInd() {
		return null;
	}

	@Override
	public void setCrashProofCoverageInd(String newCrashProofCoverageInd) {
		// noop
	}

	@Override
	public String getNewAccidentBenefitCoverageInd() {
		return null;
	}

	@Override
	public void setNewAccidentBenefitCoverageInd(String newNewAccidentBenefitCoverageInd) {
		// noop
	}

	@Override
	public String getFarmCoverageInd() {
		return null;
	}

	@Override
	public void setFarmCoverageInd(String newFarmCoverageInd) {
		// noop
	}

	@Override
	public String getFinancialResponsabilityCertificateCoverageInd() {
		return null;
	}

	@Override
	public void setFinancialResponsabilityCertificateCoverageInd(String newFinancialResponsabilityCertificateCoverageInd) {
		// noop
	}

	@Override
	public String getMultiVehicleCoverageInd() {
		return null;
	}

	@Override
	public void setMultiVehicleCoverageInd(String newMultiVehicleCoverageInd) {
		// noop
	}

	@Override
	public String getAntiTheftCoverageInd() {
		return null;
	}

	@Override
	public void setAntiTheftCoverageInd(String newAntiTheftCoverageInd) {
		// noop
	}

	@Override
	public String getLimitedGlassCoverageInd() {
		return null;
	}

	@Override
	public void setLimitedGlassCoverageInd(String newLimitedGlassCoverageInd) {
		// noop
	}

	@Override
	public String getVehicleRecreatifCoverageCode() {
		return null;
	}

	@Override
	public void setVehicleRecreatifCoverageCode(String newVehicleRecreatifCoverageCode) {
		// noop
	}

	@Override
	public String getMyAutoWithMeCoverageCode() {
		return null;
	}

	@Override
	public void setMyAutoWithMeCoverageCode(String newMyAutoWithMeCoverageCode) {
		// noop
	}

	@Override
	public String getModifiedVehicleCoverageCode() {
		return null;
	}

	@Override
	public void setModifiedVehicleCoverageCode(String newModifiedVehicleCoverageCode) {
		// noop
	}

	@Override
	public String getDistinctionPlusCoverageCode() {
		return null;
	}

	@Override
	public void setDistinctionPlusCoverageCode(String newDistinctionPlusCoverageCode) {
		// noop
	}

	@Override
	public String getVehicleUseOutsideProvCoverageCode() {
		return null;
	}

	@Override
	public void setVehicleUseOutsideProvCoverageCode(String newVehicleUseOutsideProvCoverageCode) {
		// noop
	}

	@Override
	public String getParkingCoverageCode() {
		return null;
	}

	@Override
	public void setParkingCoverageCode(String newParkingCoverageCode) {
		// noop
	}

	@Override
	public String getNewBusinessCoverageCode() {
		return null;
	}

	@Override
	public void setNewBusinessCoverageCode(String newNewBusinessCoverageCode) {
		// noop
	}

	@Override
	public String getCaaDiscountCoverageCode() {
		return null;
	}

	@Override
	public void setCaaDiscountCoverageCode(String newCaaDiscountCoverageCode) {
		// noop
	}

	@Override
	public String getHailCoverageCode() {
		return null;
	}

	@Override
	public void setHailCoverageCode(String newHailCoverageCode) {
		// noop
	}

	@Override
	public String getAccommodationSurchargeInd() {
		return null;
	}

	@Override
	public void setAccommodationSurchargeInd(String newAccommodationSurchargeInd) {
		// noop
	}

	@Override
	public String getAgeOfBuildingSurchargeInd() {
		return null;
	}

	@Override
	public void setAgeOfBuildingSurchargeInd(String newAgeOfBuildingSurchargeInd) {
		// noop
	}

	@Override
	public String getDeductibleDiscountRemovalInd() {
		return null;
	}

	@Override
	public void setDeductibleDiscountRemovalInd(String newDeductibleDiscountRemovalInd) {
		// noop
	}

	@Override
	public String getSeasonalShortTermRentalSurchargeInd() {
		return null;
	}

	@Override
	public void setSeasonalShortTermRentalSurchargeInd(String newSeasonalShortTermRentalSurchargeInd) {
		// noop
	}

	@Override
	public String getSeasonalLongTermRentalSurchargeInd() {
		return null;
	}

	@Override
	public void setSeasonalLongTermRentalSurchargeInd(String newSeasonalLongTermRentalSurchargeInd) {
		// noop
	}

	@Override
	public String getReplacementCost() {
		return null;
	}

	@Override
	public void setReplacementCost(String newReplacementCost) {
		// noop
	}

	@Override
	public Integer getAgeOfPrincipalInsuredForRating() {
		return null;
	}

	@Override
	public void setAgeOfPrincipalInsuredForRating(Integer newAgeOfPrincipalInsuredForRating) {
		// noop
	}

	@Override
	public Double getBrokerCommissionPercentage() {
		return null;
	}

	@Override
	public void setBrokerCommissionPercentage(Double newBrokerCommissionPercentage) {
		// noop
	}

	@Override
	public String getReinsuranceMethod() {
		return null;
	}

	@Override
	public void setReinsuranceMethod(String newReinsuranceMethod) {
		// noop
	}

	@Override
	public String getStandalonePackageInd() {
		return null;
	}

	@Override
	public void setStandalonePackageInd(String newStandalonePackageInd) {
		// noop
	}

	@Override
	public String getPowerSquadDiscountInd() {
		return null;
	}

	@Override
	public void setPowerSquadDiscountInd(String newPowerSquadDiscountInd) {
		// noop
	}

	@Override
	public Integer getGroupTravelAgeBand() {
		return null;
	}

	@Override
	public void setGroupTravelAgeBand(Integer newGroupTravelAgeBand) {
		// noop
	}

	@Override
	public String getGroupTravelPlanTypeLegacy() {
		return null;
	}

	@Override
	public void setGroupTravelPlanTypeLegacy(String newGroupTravelPlanTypeLegacy) {
		// noop
	}

	@Override
	public String getFlexEligibilityInd() {
		return null;
	}

	@Override
	public void setFlexEligibilityInd(String newFlexEligibilityInd) {

	}

	@Override
	public Integer getFlexPercentagePreapproved() {
		return null;
	}

	@Override
	public void setFlexPercentagePreapproved(Integer newFlexPercentagePreapproved) {
		// noop
	}

	@Override
	public String getCommercialActivityInResidenceInd() {
		return null;
	}

	@Override
	public void setCommercialActivityInResidenceInd(String newCommercialActivityInResidenceInd) {
		// noop
	}

	@Override
	public Integer getAreaPercentageForCommercialActivityInResidence() {
		return null;
	}

	@Override
	public void setAreaPercentageForCommercialActivityInResidence(Integer newAreaPercentageForCommercialActivityInResidence) {
		// noop
	}

	@Override
	public String getTypeOfCommercialActivityInResidence() {
		return null;
	}

	@Override
	public void setTypeOfCommercialActivityInResidence(String newTypeOfCommercialActivityInResidence) {
		// noop
	}

	@Override
	public Integer getNumberOfSalespersons() {
		return null;
	}

	@Override
	public void setNumberOfSalespersons(Integer newNumberOfSalespersons) {
		// noop
	}

	@Override
	public String getDuplicateAccountingRecordOffsiteInd() {
		return null;
	}

	@Override
	public void setDuplicateAccountingRecordOffsiteInd(String newDuplicateAccountingRecordOffsiteInd) {
		// noop
	}

	@Override
	public Integer getYoungestPrincipalOrNonRatedDriverAge() {
		return null;
	}

	@Override
	public void setYoungestPrincipalOrNonRatedDriverAge(Integer newYoungestPrincipalOrNonRatedDriverAge) {
		// noop
	}

	@Override
	public String getRatingExecutionStepControlCode() {
		return null;
	}

	@Override
	public void setRatingExecutionStepControlCode(String newRatingExecutionStepControlCode) {
		// noop
	}

	@Override
	public String getAgreeToUseRelyNetworkInd() {
		return null;
	}

	@Override
	public void setAgreeToUseRelyNetworkInd(String newAgreeToUseRelyNetworkInd) {
		// noop
	}

	@Override
	public Integer getLossFactorPropertySystem() {
		return null;
	}

	@Override
	public void setLossFactorPropertySystem(Integer newLossFactorPropertySystem) {
		// noop
	}

	@Override
	public Integer getLossFactorPropertyModified() {
		return null;
	}

	@Override
	public void setLossFactorPropertyModified(Integer newLossFactorPropertyModified) {
		// noop
	}

	@Override
	public Integer getLossFactorProperty() {
		return null;
	}

	@Override
	public void setLossFactorProperty(Integer newLossFactorProperty) {
		// noop
	}

	@Override
	public Integer getLossFactorMiscellaneousSystem() {
		return null;
	}

	@Override
	public void setLossFactorMiscellaneousSystem(Integer newLossFactorMiscellaneousSystem) {
		// noop
	}

	@Override
	public Integer getLossFactorMiscellaneousModified() {
		return null;
	}

	@Override
	public void setLossFactorMiscellaneousModified(Integer newLossFactorMiscellaneousModified) {
		// noop
	}

	@Override
	public Integer getLossFactorMiscellaneous() {
		return null;
	}

	@Override
	public void setLossFactorMiscellaneous(Integer newLossFactorMiscellaneous) {
		// noop
	}

	@Override
	public String getHighestOccupancyHazardGrade() {
		return null;
	}

	@Override
	public void setHighestOccupancyHazardGrade(String newHighestOccupancyHazardGrade) {
		// noop
	}

	@Override
	public String getOccupancyHazardGrade() {
		return null;
	}

	@Override
	public void setOccupancyHazardGrade(String newOccupancyHazardGrade) {
		// noop
	}

	@Override
	public Double getScoringRai() {
		return null;
	}

	@Override
	public void setScoringRai(Double newScoringRai) {
		// noop
	}

	@Override
	public Double getScoringRaiAdjusted() {
		return null;
	}

	@Override
	public void setScoringRaiAdjusted(Double newScoringRaiAdjusted) {
		// noop
	}

	@Override
	public String getUnapprovedPaintingRoomSurchargeInd() {
		return null;
	}

	@Override
	public void setUnapprovedPaintingRoomSurchargeInd(String newUnapprovedPaintingRoomSurchargeInd) {
		// noop
	}

	@Override
	public String getNoDustCollectorsSurchargeInd() {
		return null;
	}

	@Override
	public void setNoDustCollectorsSurchargeInd(String newNoDustCollectorsSurchargeInd) {
		// noop
	}

	@Override
	public String getPoorBuildingConditionSurchargeInd() {
		return null;
	}

	@Override
	public void setPoorBuildingConditionSurchargeInd(String newPoorBuildingConditionSurchargeInd) {
		// noop
	}

	@Override
	public String getPremisesMaintenanceSurchargeInd() {
		return null;
	}

	@Override
	public void setPremisesMaintenanceSurchargeInd(String newPremisesMaintenanceSurchargeInd) {
		// noop
	}

	@Override
	public String getPresenceShedSurchargeInd() {
		return null;
	}

	@Override
	public void setPresenceShedSurchargeInd(String newPresenceShedSurchargeInd) {
		// noop
	}

	@Override
	public String getSeasonalRiskSurchargeInd() {
		return null;
	}

	@Override
	public void setSeasonalRiskSurchargeInd(String newSeasonalRiskSurchargeInd) {
		// noop
	}

	@Override
	public String getSprinklersPowderOrNoneSurchargeInd() {
		return null;
	}

	@Override
	public void setSprinklersPowderOrNoneSurchargeInd(String newSprinklersPowderOrNoneSurchargeInd) {
		// noop
	}

	@Override
	public String getSprinklersTestedMoreThan10YearsSurchargeInd() {
		return null;
	}

	@Override
	public void setSprinklersTestedMoreThan10YearsSurchargeInd(String newSprinklersTestedMoreThan10YearsSurchargeInd) {
		// noop
	}

	@Override
	public String getClassOfTenantSurchargeInd() {
		return null;
	}

	@Override
	public void setClassOfTenantSurchargeInd(String newClassOfTenantSurchargeInd) {
		// noop
	}

	@Override
	public Double getRenewalImpactPercentageForCapping() {
		return null;
	}

	@Override
	public void setRenewalImpactPercentageForCapping(Double newRenewalImpactPercentageForCapping) {
		// noop
	}

	@Override
	public Double getRenewalImpactPremiumCostingForCapping() {
		return null;
	}

	@Override
	public void setRenewalImpactPremiumCostingForCapping(Double newRenewalImpactPremiumCostingForCapping) {
		// noop
	}

	@Override
	public Double getRenewalImpactPremiumPricingForCapping() {
		return null;
	}

	@Override
	public void setRenewalImpactPremiumPricingForCapping(Double newRenewalImpactPremiumPricingForCapping) {
		// noop
	}

	@Override
	public Double getRaiPercentageForCapping() {
		return null;
	}

	@Override
	public void setRaiPercentageForCapping(Double newRaiPercentageForCapping) {
		// noop
	}

	@Override
	public Double getRaiPremiumCostingForCapping() {
		return null;
	}

	@Override
	public void setRaiPremiumCostingForCapping(Double newRaiPremiumCostingForCapping) {
		// noop
	}

	@Override
	public Double getRaiPremiumPricingForCapping() {
		return null;
	}

	@Override
	public void setRaiPremiumPricingForCapping(Double newRaiPremiumPricingForCapping) {
		// noop
	}

	@Override
	public Integer getExcessValue() {
		return null;
	}

	@Override
	public void setExcessValue(Integer newExcessValue) {
		// noop
	}

	@Override
	public String getFleetRatedInd() {
		return null;
	}

	@Override
	public void setFleetRatedInd(String newFleetRatedInd) {
		// noop
	}

	@Override
	public GregorianCalendar getRetroactiveDate() {
		return null;
	}

	@Override
	public void setRetroactiveDate(GregorianCalendar newRetroactiveDate) {
		// noop
	}

	@Override
	public Double getPremiumRetainedAmount() {
		return null;
	}

	@Override
	public void setPremiumRetainedAmount(Double newPremiumRetainedAmount) {
		// noop
	}

	@Override
	public Double getLowCostDiscountPercentage() {
		return null;
	}

	@Override
	public void setLowCostDiscountPercentage(Double newLowCostDiscountPercentage) {
		// noop
	}

	@Override
	public Integer getAgeOfYoungestFemaleDriverUnder25Years() {
		return null;
	}

	@Override
	public void setAgeOfYoungestFemaleDriverUnder25Years(Integer newAgeOfYoungestFemaleDriverUnder25Years) {
		// noop
	}

	@Override
	public Double getRatingPremiumFactorTrailerLiability() {
		return null;
	}

	@Override
	public void setRatingPremiumFactorTrailerLiability(Double newRatingPremiumFactorTrailerLiability) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableCollision3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision3Years(Integer newNumberOfClaimsLiableCollision3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableLiability3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableLiability3Years(Integer newNumberOfClaimsLiableLiability3Years) {
		// noop
	}

	@Override
	public String getVehicleAddOrSubstitutionDiscountSurchargeInd() {
		return null;
	}

	@Override
	public void setVehicleAddOrSubstitutionDiscountSurchargeInd(String newVehicleAddOrSubstitutionDiscountSurchargeInd) {
		// noop
	}

	@Override
	public Double getVehicleAddOrSubstitutionDiscountSurchargePercentage() {
		return null;
	}

	@Override
	public void setVehicleAddOrSubstitutionDiscountSurchargePercentage(Double newVehicleAddOrSubstitutionDiscountSurchargePercentage) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgLiability10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgLiability10Years(Integer newNumberOfClaimsNonForgLiability10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsCollision10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsCollision10Years(Integer newNumberOfClaimsCollision10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgCollision10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgCollision10Years(Integer newNumberOfClaimsNonForgCollision10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsSpLargeAmt10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsSpLargeAmt10Years(Integer newNumberOfClaimsSpLargeAmt10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgSpLargeAmt10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgSpLargeAmt10Years(Integer newNumberOfClaimsNonForgSpLargeAmt10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsSpSmallAmt10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsSpSmallAmt10Years(Integer newNumberOfClaimsSpSmallAmt10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonForgSpSmallAmt10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonForgSpSmallAmt10Years(Integer newNumberOfClaimsNonForgSpSmallAmt10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgLiabilityClmPrAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgLiabilityClmPrAndNonRatDr(Integer newNumberOfYearsSinceLastNonForgLiabilityClmPrAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgLiabilityClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgLiabilityClaimOccDrvr(Integer newNumberOfYearsSinceLastNonForgLiabilityClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgCollClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgCollClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastNonForgCollClaimPrincAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgCollisionClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgCollisionClaimOccDrvr(Integer newNumberOfYearsSinceLastNonForgCollisionClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgSpLargAmtClmPrAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgSpLargAmtClmPrAndNonRatDr(Integer newNumberOfYearsSinceLastNonForgSpLargAmtClmPrAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgSpLargeAmtClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgSpLargeAmtClaimOccDrvr(Integer newNumberOfYearsSinceLastNonForgSpLargeAmtClaimOccDrvr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgSpSmalAmtClmPrAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgSpSmalAmtClmPrAndNonRatDr(Integer newNumberOfYearsSinceLastNonForgSpSmalAmtClmPrAndNonRatDr) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastNonForgSpSmallAmtClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastNonForgSpSmallAmtClaimOccDrvr(Integer newNumberOfYearsSinceLastNonForgSpSmallAmtClaimOccDrvr) {
		// noop
	}

	@Override
	public Double getTermBalancePercentageProRateForSuspensionCoverage() {
		return null;
	}

	@Override
	public void setTermBalancePercentageProRateForSuspensionCoverage(Double newTermBalancePercentageProRateForSuspensionCoverage) {
		// noop
	}

	@Override
	public String getRatingBasis() {
		return null;
	}

	@Override
	public void setRatingBasis(String newRatingBasis) {
		// noop
	}

	@Override
	public PolicyVersion getThePolicyVersion() {
		return null;
	}

	@Override
	public void setThePolicyVersion(PolicyVersion newThePolicyVersion) {
		// noop
	}

	@Override
	public PolicyVersion createThePolicyVersion() {
		return null;
	}

	@Override
	public PolicyVersion createThePolicyVersion(Class<? extends PolicyVersion> theInterface) {
		return null;
	}

	@Override
	public void clearTheDiagnosticAutomatedAdvice() {
		// noop
	}

	@Override
	public List<DiagnosticAutomatedAdvice> getTheDiagnosticAutomatedAdvice() {
		return null;
	}

	@Override
	public DiagnosticAutomatedAdvice getTheDiagnosticAutomatedAdvice(String _uniqueId) {
		return null;
	}

	@Override
	public DiagnosticAutomatedAdvice getTheDiagnosticAutomatedAdvice(int index) {
		return null;
	}

	@Override
	public DiagnosticAutomatedAdvice addTheDiagnosticAutomatedAdvice() {
		return null;
	}

	@Override
	public DiagnosticAutomatedAdvice addTheDiagnosticAutomatedAdvice(Class<? extends DiagnosticAutomatedAdvice> theInterface) {
		return null;
	}

	@Override
	public void addTheDiagnosticAutomatedAdvice(DiagnosticAutomatedAdvice newTheDiagnosticAutomatedAdvice) {
		// noop
	}

	@Override
	public void addTheDiagnosticAutomatedAdvice(int index, DiagnosticAutomatedAdvice newTheDiagnosticAutomatedAdvice) {
		// noop
	}

	@Override
	public void setTheDiagnosticAutomatedAdvice(int index, DiagnosticAutomatedAdvice newTheDiagnosticAutomatedAdvice) {
		// noop
	}

	@Override
	public void setTheDiagnosticAutomatedAdvice(List<DiagnosticAutomatedAdvice> objList) {
		// noop
	}

	@Override
	public void removeTheDiagnosticAutomatedAdvice(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDiagnosticAutomatedAdvice(int index) {
		// noop
	}

	@Override
	public void clearTheClaim() {
		// noop
	}

	@Override
	public List<Claim> getTheClaim() {
		this.testClaimList = this.testClaimList == null ? new ArrayList<Claim>() : this.testClaimList;
		return this.testClaimList;
	}

	@Override
	public Claim getTheClaim(String _uniqueId) {
		return null;
	}

	@Override
	public Claim getTheClaim(int index) {
		return null;
	}

	@Override
	public Claim addTheClaim() {
		return null;
	}

	@Override
	public Claim addTheClaim(Class<? extends Claim> theInterface) {
		return null;
	}

	@Override
	public void addTheClaim(Claim newTheClaim) {
		// noop
	}

	@Override
	public void addTheClaim(int index, Claim newTheClaim) {
		// noop
	}

	@Override
	public void setTheClaim(int index, Claim newTheClaim) {
		// noop
	}

	@Override
	public void setTheClaim(List<Claim> objList) {
		// noop
	}

	@Override
	public void removeTheClaim(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheClaim(int index) {
		// noop
	}

	@Override
	public void clearTheNote() {
		// noop
	}

	@Override
	public List<Note> getTheNote() {
		return null;
	}

	@Override
	public Note getTheNote(String _uniqueId) {
		return null;
	}

	@Override
	public Note getTheNote(int index) {
		return null;
	}

	@Override
	public Note addTheNote() {
		return null;
	}

	@Override
	public Note addTheNote(Class<? extends Note> theInterface) {
		return null;
	}

	@Override
	public void addTheNote(Note newTheNote) {
		// noop
	}

	@Override
	public void addTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(List<Note> objList) {
		// noop
	}

	@Override
	public void removeTheNote(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheNote(int index) {
		// noop
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskPriorTerm() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskPriorTerm(InsuranceRisk newTheInsuranceRiskPriorTerm) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPriorTerm() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPriorTerm(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheDriver() {
		// noop
	}

	@Override
	public List<Driver> getTheDriver() {
		this.testDriverList = this.testDriverList == null ? new ArrayList<Driver>() : this.testDriverList;
		return this.testDriverList;
	}

	@Override
	public Driver getTheDriver(String _uniqueId) {
		return null;
	}

	@Override
	public Driver getTheDriver(int index) {
		return null;
	}

	@Override
	public Driver addTheDriver() {
		return null;
	}

	@Override
	public Driver addTheDriver(Class<? extends Driver> theInterface) {
		return null;
	}

	@Override
	public void addTheDriver(Driver newTheDriver) {
		this.testDriverList = this.testDriverList == null ? new ArrayList<>() : this.testDriverList;
		this.testDriverList.add(newTheDriver);
	}

	@Override
	public void addTheDriver(int index, Driver newTheDriver) {
		// noop
	}

	@Override
	public void setTheDriver(int index, Driver newTheDriver) {
		// noop
	}

	@Override
	public void setTheDriver(List<Driver> objList) {
		// noop
	}

	@Override
	public void removeTheDriver(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDriver(int index) {
		// noop
	}

	@Override
	public void clearTheOwner() {
		// noop
	}

	@Override
	public List<Owner> getTheOwner() {
		return null;
	}

	@Override
	public Owner getTheOwner(String _uniqueId) {
		return null;
	}

	@Override
	public Owner getTheOwner(int index) {
		return null;
	}

	@Override
	public Owner addTheOwner() {
		return null;
	}

	@Override
	public Owner addTheOwner(Class<? extends Owner> theInterface) {
		return null;
	}

	@Override
	public void addTheOwner(Owner newTheOwner) {
		// noop
	}

	@Override
	public void addTheOwner(int index, Owner newTheOwner) {
		// noop
	}

	@Override
	public void setTheOwner(int index, Owner newTheOwner) {
		// noop
	}

	@Override
	public void setTheOwner(List<Owner> objList) {
		// noop
	}

	@Override
	public void removeTheOwner(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheOwner(int index) {
		// noop
	}

	@Override
	public void clearTheInsured() {
		// noop
	}

	@Override
	public List<Insured> getTheInsured() {
		return null;
	}

	@Override
	public Insured getTheInsured(String _uniqueId) {
		return null;
	}

	@Override
	public Insured getTheInsured(int index) {
		return null;
	}

	@Override
	public Insured addTheInsured() {
		return null;
	}

	@Override
	public Insured addTheInsured(Class<? extends Insured> theInterface) {
		return null;
	}

	@Override
	public void addTheInsured(Insured newTheInsured) {
		// noop
	}

	@Override
	public void addTheInsured(int index, Insured newTheInsured) {
		// noop
	}

	@Override
	public void setTheInsured(int index, Insured newTheInsured) {
		// noop
	}

	@Override
	public void setTheInsured(List<Insured> objList) {
		// noop
	}

	@Override
	public void removeTheInsured(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheInsured(int index) {
		// noop
	}

	@Override
	public CreditScore getTheCreditScoreRiskPostalCode() {
		return null;
	}

	@Override
	public void setTheCreditScoreRiskPostalCode(CreditScore newTheCreditScoreRiskPostalCode) {
		// noop
	}

	@Override
	public CreditScore createTheCreditScoreRiskPostalCode() {
		return null;
	}

	@Override
	public CreditScore createTheCreditScoreRiskPostalCode(Class<? extends CreditScore> theInterface) {
		return null;
	}

	@Override
	public void clearTheAdditionalInterestCommercial() {
		// noop
	}

	@Override
	public List<AdditionalInterest> getTheAdditionalInterestCommercial() {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterestCommercial(String _uniqueId) {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterestCommercial(int index) {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterestCommercial() {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterestCommercial(Class<? extends AdditionalInterest> theInterface) {
		return null;
	}

	@Override
	public void addTheAdditionalInterestCommercial(AdditionalInterest newTheAdditionalInterestCommercial) {
		// noop
	}

	@Override
	public void addTheAdditionalInterestCommercial(int index, AdditionalInterest newTheAdditionalInterestCommercial) {
		// noop
	}

	@Override
	public void setTheAdditionalInterestCommercial(int index, AdditionalInterest newTheAdditionalInterestCommercial) {
		// noop
	}

	@Override
	public void setTheAdditionalInterestCommercial(List<AdditionalInterest> objList) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterestCommercial(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterestCommercial(int index) {
		// noop
	}

	@Override
	public void clearTheLocationOtherOccupant() {

	}

	@Override
	public List<LocationOtherOccupant> getTheLocationOtherOccupant() {
		return null;
	}

	@Override
	public LocationOtherOccupant getTheLocationOtherOccupant(String uniqueId) {
		return null;
	}

	@Override
	public LocationOtherOccupant getTheLocationOtherOccupant(int index) {
		return null;
	}

	@Override
	public LocationOtherOccupant addTheLocationOtherOccupant() {
		return null;
	}

	@Override
	public LocationOtherOccupant addTheLocationOtherOccupant(Class<? extends LocationOtherOccupant> theInterface) {
		return null;
	}

	@Override
	public void addTheLocationOtherOccupant(LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void addTheLocationOtherOccupant(int index, LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void setTheLocationOtherOccupant(int index, LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void setTheLocationOtherOccupant(List<LocationOtherOccupant> objList) {

	}

	@Override
	public void removeTheLocationOtherOccupant(String uniqueId) {

	}

	@Override
	public void removeTheLocationOtherOccupant(int index) {

	}

	@Override
	public Vehicle getTheVehicle() {
		/**
		 * For test purposes, returns the existing vehicle or a new one if none exists.
		 */
		this.testVehicle = this.testVehicle == null ? new MockVehicle() : this.testVehicle;
		return this.testVehicle;
	}

	@Override
	public void setTheVehicle(Vehicle newTheVehicle) {
		// noop
	}

	@Override
	public Vehicle createTheVehicle() {
		return null;
	}

	@Override
	public Vehicle createTheVehicle(Class<? extends Vehicle> theInterface) {
		return null;
	}

	@Override
	public Trailer getTheTrailer() {
		return null;
	}

	@Override
	public void setTheTrailer(Trailer newTheTrailer) {
		// noop
	}

	@Override
	public Trailer createTheTrailer() {
		return null;
	}

	@Override
	public Trailer createTheTrailer(Class<? extends Trailer> theInterface) {
		return null;
	}

	@Override
	public Building getTheBuilding() {
		return null;
	}

	@Override
	public void setTheBuilding(Building newTheBuilding) {
		// noop
	}

	@Override
	public Building createTheBuilding() {
		return null;
	}

	@Override
	public Building createTheBuilding(Class<? extends Building> theInterface) {
		return null;
	}

	@Override
	public void clearTheTrailerLegacy() {
		// noop
	}

	@Override
	public List<Trailer> getTheTrailerLegacy() {
		return null;
	}

	@Override
	public Trailer getTheTrailerLegacy(String _uniqueId) {
		return null;
	}

	@Override
	public Trailer getTheTrailerLegacy(int index) {
		return null;
	}

	@Override
	public Trailer addTheTrailerLegacy() {
		return null;
	}

	@Override
	public Trailer addTheTrailerLegacy(Class<? extends Trailer> theInterface) {
		return null;
	}

	@Override
	public void addTheTrailerLegacy(Trailer newTheTrailerLegacy) {
		// noop
	}

	@Override
	public void addTheTrailerLegacy(int index, Trailer newTheTrailerLegacy) {
		// noop
	}

	@Override
	public void setTheTrailerLegacy(int index, Trailer newTheTrailerLegacy) {
		// noop
	}

	@Override
	public void setTheTrailerLegacy(List<Trailer> objList) {
		// noop
	}

	@Override
	public void removeTheTrailerLegacy(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheTrailerLegacy(int index) {
		// noop
	}

	@Override
	public void clearTheVehicleLegacy() {
		// noop
	}

	@Override
	public List<Vehicle> getTheVehicleLegacy() {
		return null;
	}

	@Override
	public Vehicle getTheVehicleLegacy(String _uniqueId) {
		return null;
	}

	@Override
	public Vehicle getTheVehicleLegacy(int index) {
		return null;
	}

	@Override
	public Vehicle addTheVehicleLegacy() {
		return null;
	}

	@Override
	public Vehicle addTheVehicleLegacy(Class<? extends Vehicle> theInterface) {
		return null;
	}

	@Override
	public void addTheVehicleLegacy(Vehicle newTheVehicleLegacy) {
		// noop
	}

	@Override
	public void addTheVehicleLegacy(int index, Vehicle newTheVehicleLegacy) {
		// noop
	}

	@Override
	public void setTheVehicleLegacy(int index, Vehicle newTheVehicleLegacy) {
		// noop
	}

	@Override
	public void setTheVehicleLegacy(List<Vehicle> objList) {
		// noop
	}

	@Override
	public void removeTheVehicleLegacy(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheVehicleLegacy(int index) {
		// noop
	}

	@Override
	public Boat getTheBoat() {
		return null;
	}

	@Override
	public void setTheBoat(Boat newTheBoat) {
		// noop
	}

	@Override
	public Boat createTheBoat() {
		return null;
	}

	@Override
	public Boat createTheBoat(Class<? extends Boat> theInterface) {
		return null;
	}

	@Override
	public void clearTheBoatLegacy() {
		// noop
	}

	@Override
	public List<Boat> getTheBoatLegacy() {
		return null;
	}

	@Override
	public Boat getTheBoatLegacy(String _uniqueId) {
		return null;
	}

	@Override
	public Boat getTheBoatLegacy(int index) {
		return null;
	}

	@Override
	public Boat addTheBoatLegacy() {
		return null;
	}

	@Override
	public Boat addTheBoatLegacy(Class<? extends Boat> theInterface) {
		return null;
	}

	@Override
	public void addTheBoatLegacy(Boat newTheBoatLegacy) {
		// noop
	}

	@Override
	public void addTheBoatLegacy(int index, Boat newTheBoatLegacy) {
		// noop
	}

	@Override
	public void setTheBoatLegacy(int index, Boat newTheBoatLegacy) {
		// noop
	}

	@Override
	public void setTheBoatLegacy(List<Boat> objList) {
		// noop
	}

	@Override
	public void removeTheBoatLegacy(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheBoatLegacy(int index) {
		// noop
	}

	@Override
	public InsuranceRiskProductVersion getTheInsuranceRiskProductVersion() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskProductVersion(InsuranceRiskProductVersion newTheInsuranceRiskProductVersion) {
		// noop
	}

	@Override
	public InsuranceRiskProductVersion createTheInsuranceRiskProductVersion() {
		return null;
	}

	@Override
	public InsuranceRiskProductVersion createTheInsuranceRiskProductVersion(Class<? extends InsuranceRiskProductVersion> theInterface) {
		return null;
	}

	@Override
	public void clearTheCoverage() {
		// noop
	}

	@Override
	public List<Coverage> getTheCoverage() {
		/**
		 * For test purposes, if the list is null, create it and add a single test coverage before returning the list.
		 */
		if (this.testCoverageList == null) {
			this.testCoverageList = new ArrayList<>();
			MockCoverage mockCoverage = new MockCoverage();
			this.testCoverageList.add(mockCoverage);
		}
		return this.testCoverageList;
	}

	@Override
	public Coverage getTheCoverage(String _uniqueId) {
		return null;
	}

	@Override
	public Coverage getTheCoverage(int index) {
		return null;
	}

	@Override
	public Coverage addTheCoverage() {
		this.testCoverage = this.testCoverage == null ? new MockCoverage() : this.testCoverage;
		return this.testCoverage;
	}

	@Override
	public Coverage addTheCoverage(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public void addTheCoverage(Coverage newTheCoverage) {
		// noop
	}

	@Override
	public void addTheCoverage(int index, Coverage newTheCoverage) {
		// noop
	}

	@Override
	public void setTheCoverage(int index, Coverage newTheCoverage) {
		// noop
	}

	@Override
	public void setTheCoverage(List<Coverage> objList) {
		// noop
	}

	@Override
	public void removeTheCoverage(String _uniqueId) {
		for (Coverage coverage : this.testCoverageList) {
			if (coverage.getUniqueId().equals(this.uniqueId)) {
				this.testCoverageList.remove(coverage);
				break;
			}
		}
	}

	@Override
	public void removeTheCoverage(int index) {
		// noop
	}

	@Override
	public Address getTheAddress() {
		return null;
	}

	@Override
	public void setTheAddress(Address newTheAddress) {
		// noop
	}

	@Override
	public Address createTheAddress() {
		return null;
	}

	@Override
	public Address createTheAddress(Class<? extends Address> theInterface) {
		return null;
	}

	@Override
	public void clearTheInsuranceRiskCampaignEligibility() {
		// noop
	}

	@Override
	public List<InsuranceRiskCampaignEligibility> getTheInsuranceRiskCampaignEligibility() {
		return null;
	}

	@Override
	public InsuranceRiskCampaignEligibility getTheInsuranceRiskCampaignEligibility(String _uniqueId) {
		return null;
	}

	@Override
	public InsuranceRiskCampaignEligibility getTheInsuranceRiskCampaignEligibility(int index) {
		return null;
	}

	@Override
	public InsuranceRiskCampaignEligibility addTheInsuranceRiskCampaignEligibility() {
		return null;
	}

	@Override
	public InsuranceRiskCampaignEligibility addTheInsuranceRiskCampaignEligibility(Class<? extends InsuranceRiskCampaignEligibility> theInterface) {
		return null;
	}

	@Override
	public void addTheInsuranceRiskCampaignEligibility(InsuranceRiskCampaignEligibility newTheInsuranceRiskCampaignEligibility) {
		// noop
	}

	@Override
	public void addTheInsuranceRiskCampaignEligibility(int index, InsuranceRiskCampaignEligibility newTheInsuranceRiskCampaignEligibility) {
		// noop
	}

	@Override
	public void setTheInsuranceRiskCampaignEligibility(int index, InsuranceRiskCampaignEligibility newTheInsuranceRiskCampaignEligibility) {
		// noop
	}

	@Override
	public void setTheInsuranceRiskCampaignEligibility(List<InsuranceRiskCampaignEligibility> objList) {
		// noop
	}

	@Override
	public void removeTheInsuranceRiskCampaignEligibility(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheInsuranceRiskCampaignEligibility(int index) {
		// noop
	}

	@Override
	public CreditScore getTheCreditScorePostalCode() {
		return null;
	}

	@Override
	public void setTheCreditScorePostalCode(CreditScore newTheCreditScorePostalCode) {
		// noop
	}

	@Override
	public CreditScore createTheCreditScorePostalCode() {
		return null;
	}

	@Override
	public CreditScore createTheCreditScorePostalCode(Class<? extends CreditScore> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskPriorTrans() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskPriorTrans(InsuranceRisk newTheInsuranceRiskPriorTrans) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPriorTrans() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPriorTrans(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public RatingRisk getTheRatingRiskPrincipal() {
		// Create a new risk if none exist (for test purposes)
		this.testRatingRisk = this.testRatingRisk == null ? new MockRatingRisk() : this.testRatingRisk;
		return this.testRatingRisk;
	}

	@Override
	public void setTheRatingRiskPrincipal(RatingRisk newTheRatingRiskPrincipal) {
		// noop
	}

	@Override
	public RatingRisk createTheRatingRiskPrincipal() {
		return null;
	}

	@Override
	public RatingRisk createTheRatingRiskPrincipal(Class<? extends RatingRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheConviction() {
		// noop
	}

	@Override
	public List<Conviction> getTheConviction() {
		return null;
	}

	@Override
	public Conviction getTheConviction(String _uniqueId) {
		return null;
	}

	@Override
	public Conviction getTheConviction(int index) {
		return null;
	}

	@Override
	public Conviction addTheConviction() {
		return null;
	}

	@Override
	public Conviction addTheConviction(Class<? extends Conviction> theInterface) {
		return null;
	}

	@Override
	public void addTheConviction(Conviction newTheConviction) {
		// noop
	}

	@Override
	public void addTheConviction(int index, Conviction newTheConviction) {
		// noop
	}

	@Override
	public void setTheConviction(int index, Conviction newTheConviction) {
		// noop
	}

	@Override
	public void setTheConviction(List<Conviction> objList) {
		// noop
	}

	@Override
	public void removeTheConviction(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheConviction(int index) {
		// noop
	}

	@Override
	public InsuranceRiskDerivedInd getTheInsuranceRiskDerivedInd() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskDerivedInd(InsuranceRiskDerivedInd newTheInsuranceRiskDerivedInd) {
		// noop
	}

	@Override
	public InsuranceRiskDerivedInd createTheInsuranceRiskDerivedInd() {
		return null;
	}

	@Override
	public InsuranceRiskDerivedInd createTheInsuranceRiskDerivedInd(Class<? extends InsuranceRiskDerivedInd> theInterface) {
		return null;
	}

	@Override
	public OccupancyRepositoryEntry getTheOccupancyRepositoryEntryPrincipal() {
		return null;
	}

	@Override
	public void setTheOccupancyRepositoryEntryPrincipal(OccupancyRepositoryEntry newTheOccupancyRepositoryEntryPrincipal) {
		// noop
	}

	@Override
	public OccupancyRepositoryEntry createTheOccupancyRepositoryEntryPrincipal() {
		return null;
	}

	@Override
	public OccupancyRepositoryEntry createTheOccupancyRepositoryEntryPrincipal(Class<? extends OccupancyRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public void clearTheOtherOccupancy() {
		// noop
	}

	@Override
	public List<OtherOccupancy> getTheOtherOccupancy() {
		return null;
	}

	@Override
	public OtherOccupancy getTheOtherOccupancy(String _uniqueId) {
		return null;
	}

	@Override
	public OtherOccupancy getTheOtherOccupancy(int index) {
		return null;
	}

	@Override
	public OtherOccupancy addTheOtherOccupancy() {
		return null;
	}

	@Override
	public OtherOccupancy addTheOtherOccupancy(Class<? extends OtherOccupancy> theInterface) {
		return null;
	}

	@Override
	public void addTheOtherOccupancy(OtherOccupancy newTheOtherOccupancy) {
		// noop
	}

	@Override
	public void addTheOtherOccupancy(int index, OtherOccupancy newTheOtherOccupancy) {
		// noop
	}

	@Override
	public void setTheOtherOccupancy(int index, OtherOccupancy newTheOtherOccupancy) {
		// noop
	}

	@Override
	public void setTheOtherOccupancy(List<OtherOccupancy> objList) {
		// noop
	}

	@Override
	public void removeTheOtherOccupancy(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheOtherOccupancy(int index) {
		// noop
	}

	@Override
	public void clearTheScheduledArticle() {
		// noop
	}

	@Override
	public List<ScheduledArticle> getTheScheduledArticle() {
		return null;
	}

	@Override
	public ScheduledArticle getTheScheduledArticle(String _uniqueId) {
		return null;
	}

	@Override
	public ScheduledArticle getTheScheduledArticle(int index) {
		return null;
	}

	@Override
	public ScheduledArticle addTheScheduledArticle() {
		return null;
	}

	@Override
	public ScheduledArticle addTheScheduledArticle(Class<? extends ScheduledArticle> theInterface) {
		return null;
	}

	@Override
	public void addTheScheduledArticle(ScheduledArticle newTheScheduledArticle) {
		// noop
	}

	@Override
	public void addTheScheduledArticle(int index, ScheduledArticle newTheScheduledArticle) {
		// noop
	}

	@Override
	public void setTheScheduledArticle(int index, ScheduledArticle newTheScheduledArticle) {
		// noop
	}

	@Override
	public void setTheScheduledArticle(List<ScheduledArticle> objList) {
		// noop
	}

	@Override
	public void removeTheScheduledArticle(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheScheduledArticle(int index) {
		// noop
	}

	@Override
	public FireRateWorksheet getTheFireRateWorksheet() {
		return null;
	}

	@Override
	public void setTheFireRateWorksheet(FireRateWorksheet newTheFireRateWorksheet) {
		// noop
	}

	@Override
	public FireRateWorksheet createTheFireRateWorksheet() {
		return null;
	}

	@Override
	public FireRateWorksheet createTheFireRateWorksheet(Class<? extends FireRateWorksheet> theInterface) {
		return null;
	}

	@Override
	public void clearThePremiumBase() {
		// noop
	}

	@Override
	public List<PremiumBase> getThePremiumBase() {
		return null;
	}

	@Override
	public PremiumBase getThePremiumBase(String _uniqueId) {
		return null;
	}

	@Override
	public PremiumBase getThePremiumBase(int index) {
		return null;
	}

	@Override
	public PremiumBase addThePremiumBase() {
		return null;
	}

	@Override
	public PremiumBase addThePremiumBase(Class<? extends PremiumBase> theInterface) {
		return null;
	}

	@Override
	public void addThePremiumBase(PremiumBase newThePremiumBase) {
		// noop
	}

	@Override
	public void addThePremiumBase(int index, PremiumBase newThePremiumBase) {
		// noop
	}

	@Override
	public void setThePremiumBase(int index, PremiumBase newThePremiumBase) {
		// noop
	}

	@Override
	public void setThePremiumBase(List<PremiumBase> objList) {
		// noop
	}

	@Override
	public void removeThePremiumBase(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePremiumBase(int index) {
		// noop
	}

	@Override
	public void clearTheRatingRiskCommercial() {
		// noop
	}

	@Override
	public List<RatingRisk> getTheRatingRiskCommercial() {
		return null;
	}

	@Override
	public RatingRisk getTheRatingRiskCommercial(String _uniqueId) {
		return null;
	}

	@Override
	public RatingRisk getTheRatingRiskCommercial(int index) {
		return null;
	}

	@Override
	public RatingRisk addTheRatingRiskCommercial() {
		return null;
	}

	@Override
	public RatingRisk addTheRatingRiskCommercial(Class<? extends RatingRisk> theInterface) {
		return null;
	}

	@Override
	public void addTheRatingRiskCommercial(RatingRisk newTheRatingRiskCommercial) {
		// noop
	}

	@Override
	public void addTheRatingRiskCommercial(int index, RatingRisk newTheRatingRiskCommercial) {
		// noop
	}

	@Override
	public void setTheRatingRiskCommercial(int index, RatingRisk newTheRatingRiskCommercial) {
		// noop
	}

	@Override
	public void setTheRatingRiskCommercial(List<RatingRisk> objList) {
		// noop
	}

	@Override
	public void removeTheRatingRiskCommercial(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheRatingRiskCommercial(int index) {
		// noop
	}

	@Override
	public LegacyRatingInfoByPostalCode getTheLegacyRatingInfoByPostalCode() {
		return null;
	}

	@Override
	public void setTheLegacyRatingInfoByPostalCode(LegacyRatingInfoByPostalCode newTheLegacyRatingInfoByPostalCode) {
		// noop
	}

	@Override
	public LegacyRatingInfoByPostalCode createTheLegacyRatingInfoByPostalCode() {
		return null;
	}

	@Override
	public LegacyRatingInfoByPostalCode createTheLegacyRatingInfoByPostalCode(Class<? extends LegacyRatingInfoByPostalCode> theInterface) {
		return null;
	}

	@Override
	public InsurancePolicy getTheInsurancePolicyMultiVehicleDiscount() {
		return null;
	}

	@Override
	public void setTheInsurancePolicyMultiVehicleDiscount(InsurancePolicy newTheInsurancePolicyMultiVehicleDiscount) {
		// noop
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyMultiVehicleDiscount() {
		return null;
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyMultiVehicleDiscount(Class<? extends InsurancePolicy> theInterface) {
		return null;
	}

	@Override
	public void clearTheUnderlyingSchedule() {
		// noop
	}

	@Override
	public List<UnderlyingSchedule> getTheUnderlyingSchedule() {
		return null;
	}

	@Override
	public UnderlyingSchedule getTheUnderlyingSchedule(String _uniqueId) {
		return null;
	}

	@Override
	public UnderlyingSchedule getTheUnderlyingSchedule(int index) {
		return null;
	}

	@Override
	public UnderlyingSchedule addTheUnderlyingSchedule() {
		return null;
	}

	@Override
	public UnderlyingSchedule addTheUnderlyingSchedule(Class<? extends UnderlyingSchedule> theInterface) {
		return null;
	}

	@Override
	public void addTheUnderlyingSchedule(UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void addTheUnderlyingSchedule(int index, UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void setTheUnderlyingSchedule(int index, UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void setTheUnderlyingSchedule(List<UnderlyingSchedule> objList) {
		// noop
	}

	@Override
	public void removeTheUnderlyingSchedule(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheUnderlyingSchedule(int index) {
		// noop
	}

	@Override
	public void clearTheCoverageOption() {
		// noop
	}

	@Override
	public List<CoverageOption> getTheCoverageOption() {
		return null;
	}

	@Override
	public CoverageOption getTheCoverageOption(String _uniqueId) {
		return null;
	}

	@Override
	public CoverageOption getTheCoverageOption(int index) {
		return null;
	}

	@Override
	public CoverageOption addTheCoverageOption() {
		return null;
	}

	@Override
	public CoverageOption addTheCoverageOption(Class<? extends CoverageOption> theInterface) {
		return null;
	}

	@Override
	public void addTheCoverageOption(CoverageOption newTheCoverageOption) {
		// noop
	}

	@Override
	public void addTheCoverageOption(int index, CoverageOption newTheCoverageOption) {
		// noop
	}

	@Override
	public void setTheCoverageOption(int index, CoverageOption newTheCoverageOption) {
		// noop
	}

	@Override
	public void setTheCoverageOption(List<CoverageOption> objList) {
		// noop
	}

	@Override
	public void removeTheCoverageOption(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheCoverageOption(int index) {
		// noop
	}

	@Override
	public void clearTheCoveragePackage() {
		// noop
	}

	@Override
	public List<Coverage> getTheCoveragePackage() {
		return null;
	}

	@Override
	public Coverage getTheCoveragePackage(String _uniqueId) {
		return null;
	}

	@Override
	public Coverage getTheCoveragePackage(int index) {
		return null;
	}

	@Override
	public Coverage addTheCoveragePackage() {
		return null;
	}

	@Override
	public Coverage addTheCoveragePackage(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public void addTheCoveragePackage(Coverage newTheCoveragePackage) {
		// noop
	}

	@Override
	public void addTheCoveragePackage(int index, Coverage newTheCoveragePackage) {
		// noop
	}

	@Override
	public void setTheCoveragePackage(int index, Coverage newTheCoveragePackage) {
		// noop
	}

	@Override
	public void setTheCoveragePackage(List<Coverage> objList) {
		// noop
	}

	@Override
	public void removeTheCoveragePackage(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheCoveragePackage(int index) {
		// noop
	}

	@Override
	public void clearTheFormComponent() {
		// noop
	}

	@Override
	public List<FormComponent> getTheFormComponent() {
		return null;
	}

	@Override
	public FormComponent getTheFormComponent(String _uniqueId) {
		return null;
	}

	@Override
	public FormComponent getTheFormComponent(int index) {
		return null;
	}

	@Override
	public FormComponent addTheFormComponent() {
		return null;
	}

	@Override
	public FormComponent addTheFormComponent(Class<? extends FormComponent> theInterface) {
		return null;
	}

	@Override
	public void addTheFormComponent(FormComponent newTheFormComponent) {
		// noop
	}

	@Override
	public void addTheFormComponent(int index, FormComponent newTheFormComponent) {
		// noop
	}

	@Override
	public void setTheFormComponent(int index, FormComponent newTheFormComponent) {
		// noop
	}

	@Override
	public void setTheFormComponent(List<FormComponent> objList) {
		// noop
	}

	@Override
	public void removeTheFormComponent(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheFormComponent(int index) {
		// noop
	}

	@Override
	public void clearTheSubscriptionAgreement() {
		// noop
	}

	@Override
	public List<SubscriptionAgreement> getTheSubscriptionAgreement() {
		return null;
	}

	@Override
	public SubscriptionAgreement getTheSubscriptionAgreement(String _uniqueId) {
		return null;
	}

	@Override
	public SubscriptionAgreement getTheSubscriptionAgreement(int index) {
		return null;
	}

	@Override
	public SubscriptionAgreement addTheSubscriptionAgreement() {
		return null;
	}

	@Override
	public SubscriptionAgreement addTheSubscriptionAgreement(Class<? extends SubscriptionAgreement> theInterface) {
		return null;
	}

	@Override
	public void addTheSubscriptionAgreement(SubscriptionAgreement newTheSubscriptionAgreement) {
		// noop
	}

	@Override
	public void addTheSubscriptionAgreement(int index, SubscriptionAgreement newTheSubscriptionAgreement) {
		// noop
	}

	@Override
	public void setTheSubscriptionAgreement(int index, SubscriptionAgreement newTheSubscriptionAgreement) {
		// noop
	}

	@Override
	public void setTheSubscriptionAgreement(List<SubscriptionAgreement> objList) {
		// noop
	}

	@Override
	public void removeTheSubscriptionAgreement(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheSubscriptionAgreement(int index) {
		// noop
	}

	@Override
	public RatingRisk getTheRatingRiskOccasional() {
		/**
		 * Return a new test rating risk for better test coverage
		 */
		return new MockRatingRisk();
	}

	@Override
	public void setTheRatingRiskOccasional(RatingRisk newTheRatingRiskOccasional) {
		// noop
	}

	@Override
	public RatingRisk createTheRatingRiskOccasional() {
		return null;
	}

	@Override
	public RatingRisk createTheRatingRiskOccasional(Class<? extends RatingRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheMarketingMessage() {
		// noop
	}

	@Override
	public List<MarketingMessage> getTheMarketingMessage() {
		return null;
	}

	@Override
	public MarketingMessage getTheMarketingMessage(String _uniqueId) {
		return null;
	}

	@Override
	public MarketingMessage getTheMarketingMessage(int index) {
		return null;
	}

	@Override
	public MarketingMessage addTheMarketingMessage() {
		return null;
	}

	@Override
	public MarketingMessage addTheMarketingMessage(Class<? extends MarketingMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheMarketingMessage(MarketingMessage newTheMarketingMessage) {
		// noop
	}

	@Override
	public void addTheMarketingMessage(int index, MarketingMessage newTheMarketingMessage) {
		// noop
	}

	@Override
	public void setTheMarketingMessage(int index, MarketingMessage newTheMarketingMessage) {
		// noop
	}

	@Override
	public void setTheMarketingMessage(List<MarketingMessage> objList) {
		// noop
	}

	@Override
	public void removeTheMarketingMessage(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheMarketingMessage(int index) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsFlood10Years() {
		return null;
	}

	@Override
	public Integer getNumberOfClaimsLiableDirectCompensation5Years() {
		return null;
	}

	@Override
	public Integer getNumberOfClaimsLiableLiability5Years() {
		return null;
	}

	@Override
	public Integer getNumberOfYearsSinceLastFloodClaim() {
		return null;
	}

	@Override
	public void setNumberOfClaimsFlood10Years(Integer arg0) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsGlassRepair1Year() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassRepair1Year(Integer newNumberOfClaimsGlassRepair1Year) {

	}

	@Override
	public Integer getNumberOfClaimsGlassRepair2Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsGlassRepair2Years(Integer integer) {

	}

	@Override
	public void setNumberOfClaimsLiableDirectCompensation5Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfClaimsLiableLiability5Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfYearsSinceLastFloodClaim(Integer arg0) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableAccidentBenefit5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableAccidentBenefit5Years(Integer newNumberOfClaimsLiableAccidentBenefit5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiableAccidentBenefit6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableAccidentBenefit6Years(Integer newNumberOfClaimsLiableAccidentBenefit6Years) {
		// noop
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public CommercialUsage addTheCommercialUsage() {
		return null;
	}

	@Override
	public CommercialUsage addTheCommercialUsage(Class<? extends CommercialUsage> arg0) {
		return null;
	}

	@Override
	public void addTheCommercialUsage(CommercialUsage arg0) {
		// noop
	}

	@Override
	public void addTheCommercialUsage(int arg0, CommercialUsage arg1) {
		// noop
	}

	@Override
	public MlModel addTheMlModel() {
		return null;
	}

	@Override
	public MlModel addTheMlModel(Class<? extends MlModel> arg0) {
		return null;
	}

	@Override
	public void addTheMlModel(MlModel arg0) {
		// noop
	}

	@Override
	public void addTheMlModel(int arg0, MlModel arg1) {
		// noop
	}

	@Override
	public void clearTheCommercialUsage() {
		// noop
	}

	@Override
	public void clearTheMlModel() {
		// noop
	}

	@Override
	public String getConfirmationOfUsFilingInd() {
		return null;
	}

	@Override
	public Double getDangerousGoodsPercentage() {
		return null;
	}

	@Override
	public String getGoodRecordInd() {
		return null;
	}

	@Override
	public Integer getGroupTravelAge() {
		return null;
	}

	@Override
	public GregorianCalendar getGroupTravelDateOfBirth() {
		return null;
	}

	@Override
	public Integer getHighestNumberOfClaimsLiableWithAccommodation9Years() {
		return null;
	}

	@Override
	public String getMinorConvictionProtectorOccDrvrCovEligInd() {
		return null;
	}

	@Override
	public String getMinorConvictionProtectorPrincOrNonRatedDrvrCovEligInd() {
		return null;
	}

	@Override
	public Integer getNumberOfClaimsLiableWithAccommodation9Years() {
		return null;
	}

	@Override
	public Integer getNumberOfClaimsWaterDamage3Years() {
		return null;
	}

	@Override
	public Integer getNumberOfForgivenClaims6Years() {
		return null;
	}

	@Override
	public Integer getNumberOfPropertyNamedInsured() {
		return null;
	}

	@Override
	public Double getReceiptsForHaulingGoodForOthers() {
		return null;
	}

	@Override
	public String getSelectedForProcessingInd() {
		return null;
	}

	@Override
	public List<CommercialUsage> getTheCommercialUsage() {
		return null;
	}

	@Override
	public CommercialUsage getTheCommercialUsage(String arg0) {
		return null;
	}

	@Override
	public CommercialUsage getTheCommercialUsage(int arg0) {
		return null;
	}

	@Override
	public List<MlModel> getTheMlModel() {
		return null;
	}

	@Override
	public MlModel getTheMlModel(String arg0) {
		return null;
	}

	@Override
	public MlModel getTheMlModel(int arg0) {
		return null;
	}

	@Override
	public GregorianCalendar getValuationDate() {
		return null;
	}

	@Override
	public void removeTheCommercialUsage(String arg0) {
		// noop
	}

	@Override
	public void removeTheCommercialUsage(int arg0) {
		// noop
	}

	@Override
	public void clearTheBusinessActivity() {

	}

	@Override
	public List<BusinessActivity> getTheBusinessActivity() {
		return null;
	}

	@Override
	public BusinessActivity getTheBusinessActivity(String s) {
		return null;
	}

	@Override
	public BusinessActivity getTheBusinessActivity(int i) {
		return null;
	}

	@Override
	public BusinessActivity addTheBusinessActivity() {
		return null;
	}

	@Override
	public BusinessActivity addTheBusinessActivity(Class<? extends BusinessActivity> aClass) {
		return null;
	}

	@Override
	public void addTheBusinessActivity(BusinessActivity businessActivity) {

	}

	@Override
	public void addTheBusinessActivity(int i, BusinessActivity businessActivity) {

	}

	@Override
	public void setTheBusinessActivity(int i, BusinessActivity businessActivity) {

	}

	@Override
	public void setTheBusinessActivity(List<BusinessActivity> list) {

	}

	@Override
	public void removeTheBusinessActivity(String s) {

	}

	@Override
	public void removeTheBusinessActivity(int i) {

	}

	@Override
	public void clearTheInsurancePolicyOther() {

	}

	@Override
	public List<InsurancePolicy> getTheInsurancePolicyOther() {
		return null;
	}

	@Override
	public InsurancePolicy getTheInsurancePolicyOther(String uniqueId) {
		return null;
	}

	@Override
	public InsurancePolicy getTheInsurancePolicyOther(int index) {
		return null;
	}

	@Override
	public InsurancePolicy addTheInsurancePolicyOther() {
		return null;
	}

	@Override
	public InsurancePolicy addTheInsurancePolicyOther(Class<? extends InsurancePolicy> theInterface) {
		return null;
	}

	@Override
	public void addTheInsurancePolicyOther(InsurancePolicy newTheInsurancePolicyOther) {

	}

	@Override
	public void addTheInsurancePolicyOther(int index, InsurancePolicy newTheInsurancePolicyOther) {

	}

	@Override
	public void setTheInsurancePolicyOther(int index, InsurancePolicy newTheInsurancePolicyOther) {

	}

	@Override
	public void setTheInsurancePolicyOther(List<InsurancePolicy> objList) {

	}

	@Override
	public void removeTheInsurancePolicyOther(String uniqueId) {

	}

	@Override
	public void removeTheInsurancePolicyOther(int index) {

	}

	@Override
	public void clearTheReinsuranceComponentGroup() {

	}

	@Override
	public List<ReinsuranceComponentGroup> getTheReinsuranceComponentGroup() {
		return null;
	}

	@Override
	public ReinsuranceComponentGroup getTheReinsuranceComponentGroup(String s) {
		return null;
	}

	@Override
	public ReinsuranceComponentGroup getTheReinsuranceComponentGroup(int i) {
		return null;
	}

	@Override
	public ReinsuranceComponentGroup addTheReinsuranceComponentGroup() {
		return null;
	}

	@Override
	public ReinsuranceComponentGroup addTheReinsuranceComponentGroup(Class<? extends ReinsuranceComponentGroup> aClass) {
		return null;
	}

	@Override
	public void addTheReinsuranceComponentGroup(ReinsuranceComponentGroup reinsuranceComponentGroup) {

	}

	@Override
	public void addTheReinsuranceComponentGroup(int i, ReinsuranceComponentGroup reinsuranceComponentGroup) {

	}

	@Override
	public void setTheReinsuranceComponentGroup(int i, ReinsuranceComponentGroup reinsuranceComponentGroup) {

	}

	@Override
	public void setTheReinsuranceComponentGroup(List<ReinsuranceComponentGroup> list) {

	}

	@Override
	public void removeTheReinsuranceComponentGroup(String s) {

	}

	@Override
	public void removeTheReinsuranceComponentGroup(int i) {

	}

	@Override public void clearTheRatingCommercialWebQualityScore() {

	}

	@Override public List<RatingCommercialWebQualityScore> getTheRatingCommercialWebQualityScore() {
		return null;
	}

	@Override public RatingCommercialWebQualityScore getTheRatingCommercialWebQualityScore(String s) {
		return null;
	}

	@Override public RatingCommercialWebQualityScore getTheRatingCommercialWebQualityScore(int i) {
		return null;
	}

	@Override public RatingCommercialWebQualityScore addTheRatingCommercialWebQualityScore() {
		return null;
	}

	@Override
	public RatingCommercialWebQualityScore addTheRatingCommercialWebQualityScore(
		Class<? extends RatingCommercialWebQualityScore> aClass) {
		return null;
	}

	@Override
	public void addTheRatingCommercialWebQualityScore(RatingCommercialWebQualityScore ratingCommercialWebQualityScore) {

	}

	@Override
	public void addTheRatingCommercialWebQualityScore(int i,
		RatingCommercialWebQualityScore ratingCommercialWebQualityScore) {

	}

	@Override
	public void setTheRatingCommercialWebQualityScore(int i,
		RatingCommercialWebQualityScore ratingCommercialWebQualityScore) {

	}

	@Override public void setTheRatingCommercialWebQualityScore(List<RatingCommercialWebQualityScore> list) {

	}

	@Override public void removeTheRatingCommercialWebQualityScore(String s) {

	}

	@Override public void removeTheRatingCommercialWebQualityScore(int i) {

	}

	@Override public CommercialWebQualityScoreAggregate getTheCommercialWebQualityScoreAggregate() {
		return null;
	}

	@Override
	public void setTheCommercialWebQualityScoreAggregate(
		CommercialWebQualityScoreAggregate commercialWebQualityScoreAggregate) {

	}

	@Override public CommercialWebQualityScoreAggregate createTheCommercialWebQualityScoreAggregate() {
		return null;
	}

	@Override
	public CommercialWebQualityScoreAggregate createTheCommercialWebQualityScoreAggregate(
		Class<? extends CommercialWebQualityScoreAggregate> aClass) {
		return null;
	}

	@Override public void clearTheCommercialWebQualityScoreGroup() {

	}

	@Override public List<CommercialWebQualityScoreGroup> getTheCommercialWebQualityScoreGroup() {
		return null;
	}

	@Override public CommercialWebQualityScoreGroup getTheCommercialWebQualityScoreGroup(String s) {
		return null;
	}

	@Override public CommercialWebQualityScoreGroup getTheCommercialWebQualityScoreGroup(int i) {
		return null;
	}

	@Override public CommercialWebQualityScoreGroup addTheCommercialWebQualityScoreGroup() {
		return null;
	}

	@Override
	public CommercialWebQualityScoreGroup addTheCommercialWebQualityScoreGroup(
		Class<? extends CommercialWebQualityScoreGroup> aClass) {
		return null;
	}

	@Override
	public void addTheCommercialWebQualityScoreGroup(CommercialWebQualityScoreGroup commercialWebQualityScoreGroup) {

	}

	@Override
	public void addTheCommercialWebQualityScoreGroup(int i,
		CommercialWebQualityScoreGroup commercialWebQualityScoreGroup) {

	}

	@Override
	public void setTheCommercialWebQualityScoreGroup(int i,
		CommercialWebQualityScoreGroup commercialWebQualityScoreGroup) {

	}

	@Override public void setTheCommercialWebQualityScoreGroup(List<CommercialWebQualityScoreGroup> list) {

	}

	@Override public void removeTheCommercialWebQualityScoreGroup(String s) {

	}

	@Override public void removeTheCommercialWebQualityScoreGroup(int i) {

	}

	@Override public void clearTheSubscriptionComponentGroup() {

	}

	@Override public List<SubscriptionComponentGroup> getTheSubscriptionComponentGroup() {
		return null;
	}

	@Override public SubscriptionComponentGroup getTheSubscriptionComponentGroup(String s) {
		return null;
	}

	@Override public SubscriptionComponentGroup getTheSubscriptionComponentGroup(int i) {
		return null;
	}

	@Override public SubscriptionComponentGroup addTheSubscriptionComponentGroup() {
		return null;
	}

	@Override
	public SubscriptionComponentGroup addTheSubscriptionComponentGroup(
		Class<? extends SubscriptionComponentGroup> aClass) {
		return null;
	}

	@Override public void addTheSubscriptionComponentGroup(SubscriptionComponentGroup subscriptionComponentGroup) {

	}

	@Override public void addTheSubscriptionComponentGroup(int i, SubscriptionComponentGroup subscriptionComponentGroup) {

	}

	@Override public void setTheSubscriptionComponentGroup(int i, SubscriptionComponentGroup subscriptionComponentGroup) {

	}

	@Override public void setTheSubscriptionComponentGroup(List<SubscriptionComponentGroup> list) {

	}

	@Override public void removeTheSubscriptionComponentGroup(String s) {

	}

	@Override public void removeTheSubscriptionComponentGroup(int i) {

	}

	@Override
	public void removeTheMlModel(String arg0) {
		// noop
	}

	@Override
	public void removeTheMlModel(int arg0) {
		// noop
	}

	@Override
	public void clearTheMlAttribute() {

	}

	@Override
	public List<MlAttribute> getTheMlAttribute() {
		return null;
	}

	@Override
	public MlAttribute getTheMlAttribute(String uniqueId) {
		return null;
	}

	@Override
	public MlAttribute getTheMlAttribute(int index) {
		return null;
	}

	@Override
	public MlAttribute addTheMlAttribute() {
		return null;
	}

	@Override
	public MlAttribute addTheMlAttribute(Class<? extends MlAttribute> theInterface) {
		return null;
	}

	@Override
	public void addTheMlAttribute(MlAttribute newTheMlAttribute) {

	}

	@Override
	public void addTheMlAttribute(int index, MlAttribute newTheMlAttribute) {

	}

	@Override
	public void setTheMlAttribute(int index, MlAttribute newTheMlAttribute) {

	}

	@Override
	public void setTheMlAttribute(List<MlAttribute> objList) {

	}

	@Override
	public void removeTheMlAttribute(String uniqueId) {

	}

	@Override
	public void removeTheMlAttribute(int index) {

	}

	@Override
	public void setConfirmationOfUsFilingInd(String arg0) {
		// noop
	}

	@Override
	public void setDangerousGoodsPercentage(Double arg0) {
		// noop
	}

	@Override
	public void setGoodRecordInd(String arg0) {
		// noop
	}

	@Override
	public void setGroupTravelAge(Integer arg0) {
		// noop
	}

	@Override
	public void setGroupTravelDateOfBirth(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public void setHighestNumberOfClaimsLiableWithAccommodation9Years(Integer arg0) {
		// noop
	}

	@Override
	public void setMinorConvictionProtectorOccDrvrCovEligInd(String arg0) {
		// noop
	}

	@Override
	public String getPrimaryScheduledArticleCategory() {
		return null;
	}

	@Override
	public void setPrimaryScheduledArticleCategory(String newPrimaryScheduledArticleCategory) {
		// null
	}


	@Override
	public Double getScheduledArticleReplacementCostLimit() {
		return null;
	}

	@Override
	public void setScheduledArticleReplacementCostLimit(Double newScheduledArticleReplacementCostLimit) {

	}

	@Override
	public void setMinorConvictionProtectorPrincOrNonRatedDrvrCovEligInd(String arg0) {
		// noop
	}

	@Override
	public void setNumberOfClaimsLiableWithAccommodation9Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfClaimsWaterDamage3Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfForgivenClaims6Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfPropertyNamedInsured(Integer arg0) {
		// noop
	}

	@Override
	public void setReceiptsForHaulingGoodForOthers(Double arg0) {
		// noop
	}

	@Override
	public void setSelectedForProcessingInd(String arg0) {
		// noop
	}

	@Override
	public void setTheCommercialUsage(List<CommercialUsage> arg0) {
		// noop
	}

	@Override
	public void setTheCommercialUsage(int arg0, CommercialUsage arg1) {
		// noop
	}

	@Override
	public void setTheMlModel(List<MlModel> arg0) {
		// noop
	}

	@Override
	public void setTheMlModel(int arg0, MlModel arg1) {
		// noop
	}

	@Override
	public void setValuationDate(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public ClaimDerivedInfo getTheClaimDerivedInfo() {
		return null;
	}

	@Override
	public void setTheClaimDerivedInfo(ClaimDerivedInfo newTheClaimDerivedInfo) {
		// noop
	}

	@Override
	public ClaimDerivedInfo createTheClaimDerivedInfo() {
		return null;
	}

	@Override
	public ClaimDerivedInfo createTheClaimDerivedInfo(Class<? extends ClaimDerivedInfo> theInterface) {
		return null;
	}

	@Override public void clearTheProgram() {

	}

	@Override public List<Program> getTheProgram() {
		return null;
	}

	@Override public Program getTheProgram(String s) {
		return null;
	}

	@Override public Program getTheProgram(int i) {
		return null;
	}

	@Override public Program addTheProgram() {
		return null;
	}

	@Override public Program addTheProgram(Class<? extends Program> aClass) {
		return null;
	}

	@Override public void addTheProgram(Program program) {

	}

	@Override public void addTheProgram(int i, Program program) {

	}

	@Override public void setTheProgram(int i, Program program) {

	}

	@Override public void setTheProgram(List<Program> list) {

	}

	@Override public void removeTheProgram(String s) {

	}

	@Override public void removeTheProgram(int i) {

	}

	@Override public Program getTheProgramBestDiscount() {
		return null;
	}

	@Override public void setTheProgramBestDiscount(Program program) {

	}

	@Override public Program createTheProgramBestDiscount() {
		return null;
	}

	@Override public Program createTheProgramBestDiscount(Class<? extends Program> aClass) {
		return null;
	}

	@Override
	public Integer getAgeOfYoungestNonRatedDriverUnder25Years() {
		return null;
	}

	@Override
	public void setAgeOfYoungestNonRatedDriverUnder25Years(Integer newAgeOfYoungestNonRatedDriverUnder25Years) {
		// noop
	}

	@Override
	public String getElasticityScoreFeatureVector() {
		return null;
	}

	@Override
	public void setElasticityScoreFeatureVector(String newElasticityScoreFeatureVector) {

	}

	@Override
	public Integer getNumberOfClaimsWaterDamage3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWaterDamage3YearsUnderwriting(Integer newNumberOfClaimsWaterDamage3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfClaimsWaterDamage5YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsWaterDamage5YearsUnderwriting(Integer newNumberOfClaimsWaterDamage5YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfClaimsLiableOccasionalDriver9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableOccasionalDriver9Years(Integer newNumberOfClaimsLiableOccasionalDriver9Years) {
		// null
	}

	@Override
	public String getForcedSubstitutionInd() {
		return null;
	}

	@Override
	public void setForcedSubstitutionInd(String newForcedSubstitutionInd) {

	}

	@Override
	public Integer getNumberOfClaimsEnhancedWaterDamagePackage5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEnhancedWaterDamagePackage5Years(Integer newNumberOfClaimsEnhancedWaterDamagePackage5Years) {

	}

	@Override
	public Integer getNumberOfClaimsEnhancedWaterDamagePackage9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEnhancedWaterDamagePackage9Years(Integer newNumberOfClaimsEnhancedWaterDamagePackage9Years) {

	}

	@Override
	public Integer getNumberOfClaimsEnhancedWaterDamagePackage10Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsEnhancedWaterDamagePackage10Years(Integer newNumberOfClaimsEnhancedWaterDamagePackage10Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiablePrincAndNonRatedCollision9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincAndNonRatedCollision9Years(Integer newNumberOfClaimsLiablePrincAndNonRatedCollision9Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiablePrincAndNonRatedCollision15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincAndNonRatedCollision15Years(Integer newNumberOfClaimsLiablePrincAndNonRatedCollision15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiablePrincAndNonRatedLiability9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincAndNonRatedLiability9Years(Integer newNumberOfClaimsLiablePrincAndNonRatedLiability9Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiablePrincAndNonRatedLiability15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiablePrincAndNonRatedLiability15Years(Integer newNumberOfClaimsLiablePrincAndNonRatedLiability15Years) {

	}

	@Override
	public Integer getHighestNumberOfClaimsLiableWithAccommodation6Years() {
		return null;
	}

	@Override
	public void setHighestNumberOfClaimsLiableWithAccommodation6Years(Integer newHighestNumberOfClaimsLiableWithAccommodation6Years) {

	}

	@Override
	public Integer getNumberOfForgivenClaimsPrincAndNonRatedDrvr9Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaimsPrincAndNonRatedDrvr9Years(Integer newNumberOfForgivenClaimsPrincAndNonRatedDrvr9Years) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableCollClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableCollClaimPrincAndNonRatDr(Integer newNumberOfMonthsSinceLastLiableCollClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableLiabClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableLiabClaimPrincAndNonRatDr(Integer newNumberOfMonthsSinceLastLiableLiabClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableCollClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableCollClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastLiableCollClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableLiabClaimPrincAndNonRatDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableLiabClaimPrincAndNonRatDr(Integer newNumberOfYearsSinceLastLiableLiabClaimPrincAndNonRatDr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastNonLiableClaimPrincAndNonRateDr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastNonLiableClaimPrincAndNonRateDr(Integer newNumberOfMonthsSinceLastNonLiableClaimPrincAndNonRateDr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastNonLiableClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastNonLiableClaimOccDrvr(Integer newNumberOfMonthsSinceLastNonLiableClaimOccDrvr) {

	}

	@Override
	public String getScoringRaiBand() {
		return null;
	}

	@Override
	public void setScoringRaiBand(String newScoringRaiBand) {

	}

	@Override
	public Integer getScoringRaiRsp() {
		return null;
	}

	@Override
	public void setScoringRaiRsp(Integer newScoringRaiRsp) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastForgivenClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastForgivenClaimPrincAndNonRatedDr(Integer newNumberOfYearsSinceLastForgivenClaimPrincAndNonRatedDr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastForgivenClaimPrincAndNonRatedDr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastForgivenClaimPrincAndNonRatedDr(Integer newNumberOfMonthsSinceLastForgivenClaimPrincAndNonRatedDr) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastForgivenClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastForgivenClaimOccDrvr(Integer newNumberOfYearsSinceLastForgivenClaimOccDrvr) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastForgivenClaimOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastForgivenClaimOccDrvr(Integer newNumberOfMonthsSinceLastForgivenClaimOccDrvr) {

	}

	@Override
	public Integer getMaximumRatingPriority() {
		return null;
	}

	@Override
	public void setMaximumRatingPriority(Integer newMaximumRatingPriority) {

	}

	@Override
	public String getAcceptableSewerBackUpWaterMitigationMeasureInd() {
		return null;
	}

	@Override
	public void setAcceptableSewerBackUpWaterMitigationMeasureInd(String newAcceptableSewerBackUpWaterMitigationMeasureInd) {

	}

	@Override
	public Integer getUnderwritingAssessmentStatusLevel() {
		return null;
	}

	@Override
	public void setUnderwritingAssessmentStatusLevel(Integer newUnderwritingAssessmentStatusLevel) {

	}

	@Override
	public Integer getNumberOfMortgagees() {
		return null;
	}

	@Override
	public void setNumberOfMortgagees(Integer newNumberOfMortgagees) {

	}

	@Override
	public Double getTotalInsuredValue() {
		return null;
	}

	@Override
	public void setTotalInsuredValue(Double newTotalInsuredValue) {

	}

	@Override
	public Double getTotalInsuredValueInTheArea() {
		return null;
	}

	@Override
	public void setTotalInsuredValueInTheArea(Double newTotalInsuredValueInTheArea) {

	}

	@Override
	public void clearTheUnderwritingQuestion() {

	}

	@Override
	public List<UnderwritingQuestion> getTheUnderwritingQuestion() {
		return null;
	}

	@Override
	public UnderwritingQuestion getTheUnderwritingQuestion(String uniqueId) {
		return null;
	}

	@Override
	public UnderwritingQuestion getTheUnderwritingQuestion(int index) {
		return null;
	}

	@Override
	public UnderwritingQuestion addTheUnderwritingQuestion() {
		return null;
	}

	@Override
	public UnderwritingQuestion addTheUnderwritingQuestion(Class<? extends UnderwritingQuestion> theInterface) {
		return null;
	}

	@Override
	public void addTheUnderwritingQuestion(UnderwritingQuestion newTheUnderwritingQuestion) {

	}

	@Override
	public void addTheUnderwritingQuestion(int index, UnderwritingQuestion newTheUnderwritingQuestion) {

	}

	@Override
	public void setTheUnderwritingQuestion(int index, UnderwritingQuestion newTheUnderwritingQuestion) {

	}

	@Override
	public void setTheUnderwritingQuestion(List<UnderwritingQuestion> objList) {

	}

	@Override
	public void removeTheUnderwritingQuestion(String uniqueId) {

	}

	@Override
	public void removeTheUnderwritingQuestion(int index) {

	}

	@Override
	public void clearTheUnderwritingNetworkInformation() {

	}

	@Override
	public List<UnderwritingNetworkInformation> getTheUnderwritingNetworkInformation() {
		return null;
	}

	@Override
	public UnderwritingNetworkInformation getTheUnderwritingNetworkInformation(String uniqueId) {
		return null;
	}

	@Override
	public UnderwritingNetworkInformation getTheUnderwritingNetworkInformation(int index) {
		return null;
	}

	@Override
	public UnderwritingNetworkInformation addTheUnderwritingNetworkInformation() {
		return null;
	}

	@Override
	public UnderwritingNetworkInformation addTheUnderwritingNetworkInformation(Class<? extends UnderwritingNetworkInformation> theInterface) {
		return null;
	}

	@Override
	public void addTheUnderwritingNetworkInformation(UnderwritingNetworkInformation newTheUnderwritingNetworkInformation) {

	}

	@Override
	public void addTheUnderwritingNetworkInformation(int index, UnderwritingNetworkInformation newTheUnderwritingNetworkInformation) {

	}

	@Override
	public void setTheUnderwritingNetworkInformation(int index, UnderwritingNetworkInformation newTheUnderwritingNetworkInformation) {

	}

	@Override
	public void setTheUnderwritingNetworkInformation(List<UnderwritingNetworkInformation> objList) {

	}

	@Override
	public void removeTheUnderwritingNetworkInformation(String uniqueId) {

	}

	@Override
	public void removeTheUnderwritingNetworkInformation(int index) {

	}
}
