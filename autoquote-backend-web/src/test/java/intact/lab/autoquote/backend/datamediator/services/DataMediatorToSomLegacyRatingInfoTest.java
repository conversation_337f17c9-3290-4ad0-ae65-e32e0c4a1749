package intact.lab.autoquote.backend.datamediator.services;

import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMLegacyRatingInfo;

import com.ing.canada.som.impl.agreement.PolicyVersionImpl;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.LegacyRatingInfoByPostalCode;
import com.ing.canada.som.rootclasses.GenericRootObject;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class DataMediatorToSomLegacyRatingInfoTest {

    @Test
    public void convertToSomWithNullPlInsuranceRisk() {
        DataMediatorToSOMLegacyRatingInfo mediator = new DataMediatorToSOMLegacyRatingInfo();
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk somInsuranceRisk =  somPolicyVersion.addTheInsuranceRisk();
        mediator.convertToSom(null, somInsuranceRisk);
    }

    @Test
    public void convertToSomWithNullSomInsuranceRisk() {
        DataMediatorToSOMLegacyRatingInfo mediator = new DataMediatorToSOMLegacyRatingInfo();
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = new com.ing.canada.plp.domain.insurancerisk.InsuranceRisk();
        mediator.convertToSom(plInsuranceRisk, null);
    }

    @Test
    public void convertToSomWithNullLegacyRatingInfo() {
        DataMediatorToSOMLegacyRatingInfo mediator = new DataMediatorToSOMLegacyRatingInfo();
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk somInsuranceRisk =  somPolicyVersion.addTheInsuranceRisk();
        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = new com.ing.canada.plp.domain.insurancerisk.InsuranceRisk();
        plInsuranceRisk.setId(1L);
        plInsuranceRisk.setAutoMobileTerritoryCollision("00");
        plInsuranceRisk.setAutoMobileTerritoryComprehensive("01");
        plInsuranceRisk.setAutoMobileTerritoryGlassBreak("02");
        plInsuranceRisk.setAutoMobileTerritoryLiability("03");
        plInsuranceRisk.setAutoMobileTerritoryRating("04");

        mediator.convertToSom(plInsuranceRisk, somInsuranceRisk);
        LegacyRatingInfoByPostalCode legacyRatingInfoByPostalCode = somInsuranceRisk.getTheLegacyRatingInfoByPostalCode();
        assertNotNull(legacyRatingInfoByPostalCode);
        assertEquals("00", legacyRatingInfoByPostalCode.getAutomobileTerritoryCollision());
        assertEquals("01", legacyRatingInfoByPostalCode.getAutomobileTerritoryComprehensive());
        assertEquals("02", legacyRatingInfoByPostalCode.getAutomobileTerritoryGlassBreakage());
        assertEquals("03", legacyRatingInfoByPostalCode.getAutomobileTerritoryLiability());
        assertEquals("04", legacyRatingInfoByPostalCode.getAutomobileTerritoryRating());
    }

    @Test
    public void convertToSomWithLegacyRatingInfoPresent() {
        DataMediatorToSOMLegacyRatingInfo mediator = new DataMediatorToSOMLegacyRatingInfo();
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk somInsuranceRisk =  somPolicyVersion.addTheInsuranceRisk();
        LegacyRatingInfoByPostalCode initialRatingInfoByPostalCode = somInsuranceRisk.createTheLegacyRatingInfoByPostalCode();
        initialRatingInfoByPostalCode.setAutomobileTerritoryCollision("0");
        initialRatingInfoByPostalCode.setAutomobileTerritoryComprehensive("1");
        initialRatingInfoByPostalCode.setAutomobileTerritoryGlassBreakage("2");
        initialRatingInfoByPostalCode.setAutomobileTerritoryLiability("3");
        initialRatingInfoByPostalCode.setAutomobileTerritoryRating("4");

        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = new com.ing.canada.plp.domain.insurancerisk.InsuranceRisk();
        plInsuranceRisk.setId(1L);
        plInsuranceRisk.setAutoMobileTerritoryCollision("00");
        plInsuranceRisk.setAutoMobileTerritoryComprehensive("01");
        plInsuranceRisk.setAutoMobileTerritoryGlassBreak("02");
        plInsuranceRisk.setAutoMobileTerritoryLiability("03");
        plInsuranceRisk.setAutoMobileTerritoryRating("04");

        mediator.convertToSom(plInsuranceRisk, somInsuranceRisk);
        LegacyRatingInfoByPostalCode legacyRatingInfoByPostalCode = somInsuranceRisk.getTheLegacyRatingInfoByPostalCode();
        assertNotNull(legacyRatingInfoByPostalCode);
        assertEquals("00", legacyRatingInfoByPostalCode.getAutomobileTerritoryCollision());
        assertEquals("01", legacyRatingInfoByPostalCode.getAutomobileTerritoryComprehensive());
        assertEquals("02", legacyRatingInfoByPostalCode.getAutomobileTerritoryGlassBreakage());
        assertEquals("03", legacyRatingInfoByPostalCode.getAutomobileTerritoryLiability());
        assertEquals("04", legacyRatingInfoByPostalCode.getAutomobileTerritoryRating());
    }
}
