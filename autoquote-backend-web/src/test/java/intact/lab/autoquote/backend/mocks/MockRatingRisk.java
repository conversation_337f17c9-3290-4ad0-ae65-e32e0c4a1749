/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.moneyProvisionPremium.MultRatingFactorFromBasicCoverage;
import com.ing.canada.som.interfaces.moneyProvisionPremium.MultRatingFactorFromNonBasicCoverage;
import com.ing.canada.som.interfaces.party.GroupRepositoryEntry;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.RatingRisk;

import java.util.List;

/**
 * 
 * Mock class for a SOM RatingRisk. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link RatingRisk}
 * 
 * <AUTHOR>
 *
 */
public class MockRatingRisk implements RatingRisk {

	private MockGroupRepositoryEntry testGroupRepositoryEntry;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public String getRatingRiskType() {
		return null;
	}

	@Override
	public void setRatingRiskType(String newRatingRiskType) {
		// noop
	}

	@Override
	public String getDrivingRecordSystem() {
		return null;
	}

	@Override
	public void setDrivingRecordSystem(String newDrivingRecordSystem) {
		// noop
	}

	@Override
	public String getDrivingRecordModified() {
		return null;
	}

	@Override
	public void setDrivingRecordModified(String newDrivingRecordModified) {
		// noop
	}

	@Override
	public String getDrivingRecord() {
		return null;
	}

	@Override
	public void setDrivingRecord(String newDrivingRecord) {
		// noop
	}

	@Override
	public String getDrivingRecordAccidentBenefit() {
		return null;
	}

	@Override
	public void setDrivingRecordAccidentBenefit(String newDrivingRecordAccidentBenefit) {
		// noop
	}

	@Override
	public String getDrivingRecordAccidentBenefitActual() {
		return null;
	}

	@Override
	public void setDrivingRecordAccidentBenefitActual(String newDrivingRecordAccidentBenefitActual) {
		// noop
	}

	@Override
	public String getDrivingRecordCollision() {
		return null;
	}

	@Override
	public void setDrivingRecordCollision(String newDrivingRecordCollision) {
		// noop
	}

	@Override
	public String getDrivingRecordCollisionActual() {
		return null;
	}

	@Override
	public void setDrivingRecordCollisionActual(String newDrivingRecordCollisionActual) {
		// noop
	}

	@Override
	public String getDrivingRecordLiabilitySystem() {
		return null;
	}

	@Override
	public void setDrivingRecordLiabilitySystem(String newDrivingRecordLiabilitySystem) {
		// noop
	}

	@Override
	public String getDrivingRecordLiabilityModified() {
		return null;
	}

	@Override
	public void setDrivingRecordLiabilityModified(String newDrivingRecordLiabilityModified) {
		// noop
	}

	@Override
	public String getDrivingRecordLiabilityModifiedReason() {
		return null;
	}

	@Override
	public void setDrivingRecordLiabilityModifiedReason(String newDrivingRecordLiabilityModifiedReason) {

	}

	@Override
	public String getDrivingRecordLiability() {
		return null;
	}

	@Override
	public void setDrivingRecordLiability(String newDrivingRecordLiability) {
		// noop
	}

	@Override
	public String getDrivingRecordLiabilityActual() {
		return null;
	}

	@Override
	public void setDrivingRecordLiabilityActual(String newDrivingRecordLiabilityActual) {
		// noop
	}

	@Override
	public String getDrivingRecordDcpd() {
		return null;
	}

	@Override
	public void setDrivingRecordDcpd(String newDrivingRecordDcpd) {
		// noop
	}

	@Override
	public String getDrivingRecordDcpdActual() {
		return null;
	}

	@Override
	public void setDrivingRecordDcpdActual(String newDrivingRecordDcpdActual) {
		// noop
	}

	@Override
	public String getRatingClassSystem() {
		return null;
	}

	@Override
	public void setRatingClassSystem(String newRatingClassSystem) {
		// noop
	}

	@Override
	public String getRatingClassModified() {
		return null;
	}

	@Override
	public void setRatingClassModified(String newRatingClassModified) {
		// noop
	}

	@Override
	public String getRatingClass() {
		return null;
	}

	@Override
	public void setRatingClass(String newRatingClass) {
		// noop
	}

	@Override
	public Double getConvictionSurchargePercentage() {
		return null;
	}

	@Override
	public void setConvictionSurchargePercentage(Double newConvictionSurchargePercentage) {
		// noop
	}

	@Override
	public String getRiskDiscountEligibilityInd() {
		return null;
	}

	@Override
	public void setRiskDiscountEligibilityInd(String newRiskDiscountEligibilityInd) {
		// noop
	}

	@Override
	public String getClaimsFreeDiscountSystem() {
		return null;
	}

	@Override
	public void setClaimsFreeDiscountSystem(String newClaimsFreeDiscountSystem) {
		// noop
	}

	@Override
	public String getClaimsFreeDiscountModified() {
		return null;
	}

	@Override
	public void setClaimsFreeDiscountModified(String newClaimsFreeDiscountModified) {
		// noop
	}

	@Override
	public String getClaimsFreeDiscount() {
		return null;
	}

	@Override
	public void setClaimsFreeDiscount(String newClaimsFreeDiscount) {
		// noop
	}

	@Override
	public Double getCappedChangeAmountAnnual() {
		return null;
	}

	@Override
	public void setCappedChangeAmountAnnual(Double newCappedChangeAmountAnnual) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedMaximum() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedMaximum(Double newAnnualPremiumCappedMaximum) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedMinimum() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedMinimum(Double newAnnualPremiumCappedMinimum) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumCapped(Double newAnnualPremiumCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAdjusted() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAdjusted(Double newAnnualPremiumCappedAdjusted) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAfterDeviation() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAfterDeviation(Double newAnnualPremiumCappedAfterDeviation) {
		// noop
	}

	@Override
	public Double getAnnualPremiumSystem() {
		return null;
	}

	@Override
	public void setAnnualPremiumSystem(Double newAnnualPremiumSystem) {
		// noop
	}

	@Override
	public Double getAnnualPremiumModified() {
		return null;
	}

	@Override
	public void setAnnualPremiumModified(Double newAnnualPremiumModified) {
		// noop
	}

	@Override
	public Double getAnnualPremium() {
		return null;
	}

	@Override
	public void setAnnualPremium(Double newAnnualPremium) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoringRai() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoringRai(Double newAnnualPremiumScoringRai) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurcharged(Double newAnnualPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedBasicCoverage() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedBasicCoverage(Double newAnnualPremiumDiscountedSurchargedBasicCoverage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedNonBasicCoverage() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedNonBasicCoverage(Double newAnnualPremiumDiscountedSurchargedNonBasicCoverage) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCapped(Double newAnnualPremiumDiscountedSurchargedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCappedPreapproved(Double newAnnualPremiumDiscountedSurchargedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedInclEndtFloater() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedInclEndtFloater(Double aDouble) {

	}

	@Override
	public Double getAnnualPremiumConsolidated() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidated(Double newAnnualPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCapped(Double newAnnualPremiumConsolidatedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCappedPreapproved(Double newAnnualPremiumConsolidatedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPool(Double newAnnualPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPoolFloor(Double newAnnualPremiumFacilityRiskSharingPoolFloor) {
		// noop
	}

	@Override
	public Double getFullTermPremium() {
		return null;
	}

	@Override
	public void setFullTermPremium(Double newFullTermPremium) {
		// noop
	}

	@Override
	public Double getFullTermPremiumGrid() {
		return null;
	}

	@Override
	public void setFullTermPremiumGrid(Double newFullTermPremiumGrid) {
		// noop
	}

	@Override
	public Double getFullTermPremiumConsolidated() {
		return null;
	}

	@Override
	public void setFullTermPremiumConsolidated(Double newFullTermPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setFullTermPremiumDiscountedSurcharged(Double newFullTermPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPool(Double newFullTermPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremium(Double newAdditionalReturnPremium) {
		// noop
	}

	@Override
	public Double getAdditionalReturnPremiumGrid() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumGrid(Double newAdditionalReturnPremiumGrid) {
		// noop
	}

	@Override
	public Double getCumulatedAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setCumulatedAdditionalReturnPremium(Double newCumulatedAdditionalReturnPremium) {
		// noop
	}

	@Override
	public String getDriverRoleHasChangedInd() {
		return null;
	}

	@Override
	public void setDriverRoleHasChangedInd(String newDriverRoleHasChangedInd) {
		// noop
	}

	@Override
	public String getCappingEligibilityInd() {
		return null;
	}

	@Override
	public void setCappingEligibilityInd(String newCappingEligibilityInd) {
		// noop
	}

	@Override
	public String getCappingApplicabilityInd() {
		return null;
	}

	@Override
	public void setCappingApplicabilityInd(String newCappingApplicabilityInd) {
		// noop
	}

	@Override
	public Double getGeneralCappingPercentageMinimum() {
		return null;
	}

	@Override
	public void setGeneralCappingPercentageMinimum(Double newGeneralCappingPercentageMinimum) {
		// noop
	}

	@Override
	public Double getGeneralCappingPercentageMaximum() {
		return null;
	}

	@Override
	public void setGeneralCappingPercentageMaximum(Double newGeneralCappingPercentageMaximum) {
		// noop
	}

	@Override
	public String getCappingLevel() {
		return null;
	}

	@Override
	public void setCappingLevel(String newCappingLevel) {
		// noop
	}

	@Override
	public Double getCappingPercentageSystem() {
		return null;
	}

	@Override
	public void setCappingPercentageSystem(Double newCappingPercentageSystem) {
		// noop
	}

	@Override
	public Double getCappingPercentageModified() {
		return null;
	}

	@Override
	public void setCappingPercentageModified(Double newCappingPercentageModified) {
		// noop
	}

	@Override
	public Double getCappingPercentage() {
		return null;
	}

	@Override
	public void setCappingPercentage(Double newCappingPercentage) {
		// noop
	}

	@Override
	public Double getCappedMaximumPremiumBasicCoverage() {
		return null;
	}

	@Override
	public void setCappedMaximumPremiumBasicCoverage(Double newCappedMaximumPremiumBasicCoverage) {
		// noop
	}

	@Override
	public Integer getPricingIndexScore() {
		return null;
	}

	@Override
	public void setPricingIndexScore(Integer newPricingIndexScore) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public String getIntermediaryDataTrace() {
		return null;
	}

	@Override
	public void setIntermediaryDataTrace(String newIntermediaryDataTrace) {

	}

	@Override public Double getRenewalImpactPremiumPricingForCapping() {
		return null;
	}

	@Override public void setRenewalImpactPremiumPricingForCapping(Double aDouble) {

	}

	@Override
	public RatingRisk getTheRatingRiskPriorTrans() {
		return null;
	}

	@Override
	public void setTheRatingRiskPriorTrans(RatingRisk newTheRatingRiskPriorTrans) {
		// noop
	}

	@Override
	public RatingRisk createTheRatingRiskPriorTrans() {
		return null;
	}

	@Override
	public RatingRisk createTheRatingRiskPriorTrans(Class<? extends RatingRisk> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskPrincipal() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskPrincipal(InsuranceRisk newTheInsuranceRiskPrincipal) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPrincipal() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPrincipal(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public GroupRepositoryEntry getTheGroupRepositoryEntry() {
		// Create a new repository entry if none exist (for test purposes)
		this.testGroupRepositoryEntry = this.testGroupRepositoryEntry == null ? new MockGroupRepositoryEntry() : this.testGroupRepositoryEntry;
		return this.testGroupRepositoryEntry;
	}

	@Override
	public void setTheGroupRepositoryEntry(GroupRepositoryEntry newTheGroupRepositoryEntry) {
		// noop
	}

	@Override
	public GroupRepositoryEntry createTheGroupRepositoryEntry() {
		return null;
	}

	@Override
	public GroupRepositoryEntry createTheGroupRepositoryEntry(Class<? extends GroupRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public RatingRisk getTheRatingRiskPriorTerm() {
		return null;
	}

	@Override
	public void setTheRatingRiskPriorTerm(RatingRisk newTheRatingRiskPriorTerm) {
		// noop
	}

	@Override
	public RatingRisk createTheRatingRiskPriorTerm() {
		return null;
	}

	@Override
	public RatingRisk createTheRatingRiskPriorTerm(Class<? extends RatingRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheMultRatingFactorFromBasicCoverage() {
		// noop
	}

	@Override
	public List<MultRatingFactorFromBasicCoverage> getTheMultRatingFactorFromBasicCoverage() {
		return null;
	}

	@Override
	public MultRatingFactorFromBasicCoverage getTheMultRatingFactorFromBasicCoverage(String uniqueId) {
		return null;
	}

	@Override
	public MultRatingFactorFromBasicCoverage getTheMultRatingFactorFromBasicCoverage(int index) {
		return null;
	}

	@Override
	public MultRatingFactorFromBasicCoverage addTheMultRatingFactorFromBasicCoverage() {
		return null;
	}

	@Override
	public MultRatingFactorFromBasicCoverage addTheMultRatingFactorFromBasicCoverage(Class<? extends MultRatingFactorFromBasicCoverage> theInterface) {
		return null;
	}

	@Override
	public void addTheMultRatingFactorFromBasicCoverage(MultRatingFactorFromBasicCoverage newTheMultRatingFactorFromBasicCoverage) {
		// noop
	}

	@Override
	public void addTheMultRatingFactorFromBasicCoverage(int index, MultRatingFactorFromBasicCoverage newTheMultRatingFactorFromBasicCoverage) {
		// noop
	}

	@Override
	public void setTheMultRatingFactorFromBasicCoverage(int index, MultRatingFactorFromBasicCoverage newTheMultRatingFactorFromBasicCoverage) {
		// noop
	}

	@Override
	public void setTheMultRatingFactorFromBasicCoverage(List<MultRatingFactorFromBasicCoverage> objList) {
		// noop
	}

	@Override
	public void removeTheMultRatingFactorFromBasicCoverage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheMultRatingFactorFromBasicCoverage(int index) {
		// noop
	}

	@Override
	public void clearTheMultRatingFactorFromNonBasicCoverage() {
		// noop
	}

	@Override
	public List<MultRatingFactorFromNonBasicCoverage> getTheMultRatingFactorFromNonBasicCoverage() {
		return null;
	}

	@Override
	public MultRatingFactorFromNonBasicCoverage getTheMultRatingFactorFromNonBasicCoverage(String uniqueId) {
		return null;
	}

	@Override
	public MultRatingFactorFromNonBasicCoverage getTheMultRatingFactorFromNonBasicCoverage(int index) {
		return null;
	}

	@Override
	public MultRatingFactorFromNonBasicCoverage addTheMultRatingFactorFromNonBasicCoverage() {
		return null;
	}

	@Override
	public MultRatingFactorFromNonBasicCoverage addTheMultRatingFactorFromNonBasicCoverage(Class<? extends MultRatingFactorFromNonBasicCoverage> theInterface) {
		return null;
	}

	@Override
	public void addTheMultRatingFactorFromNonBasicCoverage(MultRatingFactorFromNonBasicCoverage newTheMultRatingFactorFromNonBasicCoverage) {
		// noop
	}

	@Override
	public void addTheMultRatingFactorFromNonBasicCoverage(int index, MultRatingFactorFromNonBasicCoverage newTheMultRatingFactorFromNonBasicCoverage) {
		// noop
	}

	@Override
	public void setTheMultRatingFactorFromNonBasicCoverage(int index, MultRatingFactorFromNonBasicCoverage newTheMultRatingFactorFromNonBasicCoverage) {
		// noop
	}

	@Override
	public void setTheMultRatingFactorFromNonBasicCoverage(List<MultRatingFactorFromNonBasicCoverage> objList) {
		// noop
	}

	@Override
	public void removeTheMultRatingFactorFromNonBasicCoverage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheMultRatingFactorFromNonBasicCoverage(int index) {
		// noop
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskOccasional() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskOccasional(InsuranceRisk newTheInsuranceRiskOccasional) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskOccasional() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskOccasional(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public Double getMajorConvictionSurchargePercentage() {
		return null;
	}

	@Override
	public Double getMinorConvictionSurchargePercentage() {
		return null;
	}

	@Override
	public Double getSevereConvictionSurchargePercentage() {
		return null;
	}

	@Override
	public void setMajorConvictionSurchargePercentage(Double arg0) {
		// noop
	}

	@Override
	public void setMinorConvictionSurchargePercentage(Double arg0) {
		// noop
	}

	@Override
	public void setSevereConvictionSurchargePercentage(Double arg0) {
		// noop
	}

}
