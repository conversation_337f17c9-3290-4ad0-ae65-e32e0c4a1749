package intact.lab.autoquote.backend.facade.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ing.canada.cif.service.exception.SubBrokerServiceException;
import com.ing.canada.common.util.holiday.HolidayManager;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.newquote.BR3162_CheckInvalidSubBroker;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import intact.lab.autoquote.backend.services.INewQuoteBusinessProcessService;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class AutoQuoteCommonFacadeTest extends FacadeTestUtil {

    @InjectMocks
    private AutoQuoteCommonFacade commonFacade;

    @Mock
    private IMediatorComPlp mediatorComPlp;

    @Mock
    private INewQuoteBusinessProcessService newQuoteBusinessProcessService;

    @Mock
    private IConfigurator configurator;

    @Mock
    private HolidayManager holidayManager;

    @Mock
    private IQuoteStateManager quoteStateManager;

    @Mock
    private IBaseFacade baseFacade;

    @Mock
    private ICommonBusinessProcess commonBusinessProcess;

    private CommunicationObjectModel expectedCom;

    @Test
    void testCreateQuote() {
        try (MockedStatic<BR3162_CheckInvalidSubBroker> mockedStatic = mockStatic(BR3162_CheckInvalidSubBroker.class)) {
            prepareGeneralForBuildCom(this.context_IN_QC_FR, this.context_IN_QC_FR);

            ComBrokerInfo testBrokerInfo = new ComBrokerInfo();
            testBrokerInfo.setCompanyNumber("testCompanyNumber");
            testBrokerInfo.setPostalCode("H2H2H2");
            this.context_IN_QC_FR.setBrokerInfo(testBrokerInfo);

            when(newQuoteBusinessProcessService.newQuote(any(), any(), anyString(), any(), any(), any(), any()))
                    .thenReturn(initMockPolicyVersion("QUOT_NUM_12345", 123456L,
                            MediatorUtils.convertContext(this.context_IN_QC_FR), "fr"));
            when(baseFacade.getOfferTypes(any())).thenReturn(List.of(OfferTypeCodeEnum.CUSTOM));
            when(mediatorComPlp.convertPLPtoCOM(any(), any(), eq(List.of(OfferTypeCodeEnum.CUSTOM)))).thenReturn(expectedCom);
            doNothing().when(quoteStateManager).updateState(any(), any());
            when(configurator.isUbiVersion2(anyString(), anyString())).thenReturn(true);
            when(holidayManager.isOnHolidayOpeningHours(any())).thenReturn(true);
            when(holidayManager.getHolidays(any())).thenReturn(List.of());

            CommunicationObjectModel resultCom = commonFacade.createQuote(this.context_IN_QC_FR);
            assertEquals(this.expectedCom.getContext(), resultCom.getContext());
        }
    }

    @Test
    void testCreateQuote_MissingBrokerInfo() throws AutoQuoteQuoteServiceException, SubBrokerServiceException {
        prepareGeneralForBuildCom(this.context_IN_QC_FR, this.context_IN_QC_FR);

        try {
            this.commonFacade.createQuote(this.context_IN_QC_FR);
            fail("An AutoquoteFacadeException should've been raised. - Missing broker info.");
        } catch (AutoQuoteQuoteServiceException e) {
            // Expected exception
        }
    }

    @Test
    void RetrievePolicyByUUID_Should_Call_GetPolicyByUuid() throws Exception {
        // Arrange
        ComContext mockContext = new ComContext();
        String testUuid = "A33F04DC-7C75-4713-8254-0F51D4AF0F07";
        CommunicationObjectModel expectedCom = new CommunicationObjectModel();
        expectedCom.setAgreementNumber("QUOT_NUM_12345");
        expectedCom.setPolicyVersionId(123456L);

        // Use spy to mock specific method in the class under test
        AutoQuoteCommonFacade spyFacade = spy(commonFacade);
        doReturn(expectedCom).when(spyFacade).getPolicyByUuid(mockContext, testUuid);

        // Act
        CommunicationObjectModel resultCom = spyFacade.retrievePolicyByUUID(mockContext, testUuid);

        // Assert
        assertNotNull(resultCom);
        assertEquals(expectedCom.getAgreementNumber(), resultCom.getAgreementNumber());
        assertEquals(expectedCom.getPolicyVersionId(), resultCom.getPolicyVersionId());
        verify(spyFacade, times(1)).getPolicyByUuid(mockContext, testUuid);
    }

    @Test
    void RetrievePolicyByUUID_Should_ThrowsException() {
        ComContext mockContext = new ComContext();
        String testUuid = "INVALID_UUID";

        AutoQuoteCommonFacade spyFacade = spy(commonFacade);
        doThrow(new RuntimeException("Facade error")).when(spyFacade).getPolicyByUuid(mockContext, testUuid);

        AutoQuoteQuoteServiceException thrown = assertThrows(
                AutoQuoteQuoteServiceException.class,
                () -> spyFacade.retrievePolicyByUUID(mockContext, testUuid)
        );

        assertTrue(thrown.getCode().contains(AutoQuoteQuoteServiceException.EXEC_RETRIEVE_QUOTE_ERROR_CODE));
        verify(spyFacade, times(1)).getPolicyByUuid(mockContext, testUuid);
    }



    private CommunicationObjectModel prepareGeneralForBuildCom(ComContext context, ComContext expectedContext) {
        context.setPartnershipId(222L);
        ManufacturingContext currentContext = MediatorUtils.convertContext(context);
        long MOCK_POLICYVERSION_ID = 123456;
        String MOCK_QUOTATION_NUMBER = "QUOT_NUM_12345";
        PolicyVersion currentMockPv = initMockPolicyVersion(MOCK_QUOTATION_NUMBER, MOCK_POLICYVERSION_ID,
                currentContext, context.getLanguage().getCode());

        this.expectedCom = new CommunicationObjectModel();
        this.expectedCom.setContext(expectedContext);
        this.expectedCom.setAgreementNumber(MOCK_QUOTATION_NUMBER);
        this.expectedCom.setPolicyVersionId(MOCK_POLICYVERSION_ID);

        CommunicationObjectModel com = new CommunicationObjectModel();
        com.setContext(context);
        com.setPolicyVersionId(111L);
        return com;
    }

    private static PolicyVersion initMockPolicyVersion(String agreementNbr, Long policyVersionId,
                                                       ManufacturingContext manufacturingContext, String lang) {
        PolicyVersion policyVersion = new PolicyVersion();
        policyVersion.setId(policyVersionId);
        return policyVersion;
    }
}
