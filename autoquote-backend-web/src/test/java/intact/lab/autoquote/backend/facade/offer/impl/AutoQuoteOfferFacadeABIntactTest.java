/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.offer.impl;

import com.ing.canada.common.util.holiday.HolidayManager;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.AgreementTypeCodeEnum;
import com.ing.canada.plp.domain.enums.InitialTransactionCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.enums.RatingBasisCodeEnum;
import com.ing.canada.plp.domain.enums.SpfCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.impl.PolicyVersionService;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.driver.ComDriver;
import com.intact.com.offer.ComOffer;
import com.intact.com.state.ComState;
import com.intact.com.vehicle.ComVehicle;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import com.intact.common.web.security.dos.IDenyOfService;
import com.intact.common.web.security.webattack.WebAttackConfigurationService;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import intact.lab.autoquote.backend.facade.impl.FacadeTestUtil;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.offer.IOfferBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
public class AutoQuoteOfferFacadeABIntactTest extends FacadeTestUtil {

	/** Class to be tested. */
	@InjectMocks
	private AutoQuoteOfferFacadeABIntactCL offerFacade;

	/** Test objects **/
	private PolicyVersion currentMockPv;

	private ComContext currentComContext;

	private CommunicationObjectModel builtCom;

	/** Mocks */
	@Mock
	private IBaseFacade mockBaseFacade;

	@Mock
	private IConfigurator mockConfigurator;

	@Mock
	private ICommonBusinessProcess mockCommonBusinessProcess;

	@Mock
	private ICoverageHelper mockCoverageHelper;

	@Mock
	private IDenyOfService mockDenyOfService;

	@Mock
	private IDriverBusinessProcess mockDriverBusinessProcess;

	@Mock
	private IMediatorComPlp mockMediatorCom;

	@Mock
	private IOfferBusinessProcess mockOfferBusinessProcess;

	@Mock
	private IPolicyVersionHelper mockPolicyVersionHelper;

	@Mock
	private PolicyVersionService mockPolicyVersionService;

	@Mock
	private IQuoteStateManager mockQuoteStateManager;

	@Mock
	private ITransactionHistoryService mockTransactionHistoryService;

	@Mock
	private WebAttackConfigurationService mockWebAttackConfigurationService;

	@Mock
	private BloomMQHandlerService bloomMQHandlerService;

	@Mock
	private HolidayManager mockHolidayManager;

	/** Constants */
	private final long MOCK_POLICYVERSION_ID = 123456;

	private final String MOCK_QUOTATION_NUMBER = "QUOT_NUM_12345";

	/**
	 * Initialization
	 */
	@Override
	@BeforeEach
	public void setUp() {
		super.setUp();
	}


	/**
	 * Test method for
	 * {@link AutoQuoteOfferFacadeABIntactCL#retrieveOffer(CommunicationObjectModel)}
	 * @throws AutoquoteFacadeException
	 *
	 */
	@Test
	@Disabled
	public void testGetOffer() throws AutoquoteFacadeException {
		// General setup for the test
		CommunicationObjectModel testCom = this.prepareGeneralForBuildCom(this.context_IN_ON_EN, this.context_IN_ON_EN);

		// Executing the tested method and validating the results
		CommunicationObjectModel resultCom = this.offerFacade.retrieveOffer(testCom);
		Assertions.assertEquals(this.builtCom.getContext(), resultCom.getContext());
	}

	/**
	 * Test method for
	 * {@link AutoQuoteOfferFacadeABIntactCL#retrieveOffer(CommunicationObjectModel)}
	 * Case for a general exception to be raised during the method's execution.
	 * @throws Exception
	 *
	 */
	@Test
	void RetrieveOffer_Should_ThrowAutoquoteFacadeException_WhenGeneralExceptionRaised() throws Exception {
		// General setup for the test
		CommunicationObjectModel testCom = this.prepareGeneralForBuildCom(this.context_IN_ON_EN, this.context_IN_ON_EN);

		// Setting the exception to be raised
		RuntimeException testException = new RuntimeException("Exception thrown for the test \"testGetOffer_RaiseGeneralException\"");
		doThrow(testException).when(this.mockOfferBusinessProcess).selectOffer(any(PolicyVersion.class), any(Boolean.class));
		when(mockBaseFacade.getOfferTypes(any())).thenReturn(List.of(OfferTypeCodeEnum.CUSTOM));

		// Executing the tested method and validating the results
		assertThrows(AutoquoteFacadeException.class, () -> {
			this.offerFacade.retrieveOffer(testCom);
		});
	}

	/**
	 * General method used to create the necessary mocks, stubs and objects so the BaseFacade.buildCom method
	 * can be called without causing an error Receives the desired context as param as well as the expected
	 * context.
	 * @param context desired context.
	 * @param expectedContext expected context.
	 * @return COM to be passed to the tested method
	 */
	private CommunicationObjectModel prepareGeneralForBuildCom (ComContext context, ComContext expectedContext) {
		// create ComContext and PolicyVersion with that context
		this.currentComContext = context;
		this.currentComContext.setPartnershipId(222L);
		ManufacturingContext currentContext = MediatorUtils.convertContext(this.currentComContext);
		this.currentMockPv = initMockPolicyVersion(this.MOCK_QUOTATION_NUMBER, this.MOCK_POLICYVERSION_ID,
					currentContext, this.currentComContext.getLanguage().getCode());

		// Setup for the COM object that will be built by the buildCom method
		this.builtCom = new CommunicationObjectModel();
		this.builtCom.setContext(expectedContext); // Needs to be a different context to validate correctly
		this.builtCom.setAgreementNumber(this.MOCK_QUOTATION_NUMBER);
		this.builtCom.setPolicyVersionId(this.MOCK_POLICYVERSION_ID);
		when(this.mockMediatorCom.convertPLPtoCOM(any(PolicyVersion.class), any(ComContext.class), anyList())).thenReturn(this.builtCom);
		ReflectionTestUtils.setField(this.offerFacade, "mediatorCom", this.mockMediatorCom, IMediatorComPlp.class);

		// Adding a comVehicle with offer to the built com
		ComVehicle testComVehicle = new ComVehicle();
		testComVehicle.setCurrentOffer(new ComOffer());
		this.builtCom.getVehicles().add(testComVehicle);

		ComDriver testComDriver = new ComDriver();
		testComDriver.setLicenseNumber("*********");
		this.builtCom.getDrivers().add(testComDriver);
		doNothing().when(this.bloomMQHandlerService).sendMessageToMQ(anyString(), any(ManufacturerCompanyCodeEnum.class));

		// Creation of the COM to pass to the tested method
		CommunicationObjectModel com = new CommunicationObjectModel();
		com.setContext(this.currentComContext);
		com.setPolicyVersionId(111L);
		ComState testState = new ComState();
		testState.setDataChanged(true);
		testState.setHasOffer(true);
		com.setState(testState);

		return com;
	}

	/**
	 * Private method to initialize a mock policy version, not made for persistance.
	 *
	 * @param agreementNbr - {@link String}
	 * @param manufacturingContext - {@link ManufacturingContext}
	 * @param lang - the language
	 * @return {@link PolicyVersion}
	 */
	private static PolicyVersion initMockPolicyVersion(final String agreementNbr, final Long policyVersionId,
													   final ManufacturingContext manufacturingContext, final String lang) {
		// set insurancePolicy part
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		insurancePolicy.setAgreementType(AgreementTypeCodeEnum.QUOTATION);
		insurancePolicy.setSpfCode(SpfCodeEnum.STANDARD_OWNERS_AUTOMOBILE_POLICY.getCode());
		insurancePolicy.setRatingBasis(RatingBasisCodeEnum.INDIVIDUALLY_RATED);
		insurancePolicy.setLineOfBusinessCode(LineOfBusinessCodeEnum.PERSONAL_LINES);
		insurancePolicy.setManufacturingContext(manufacturingContext);
		insurancePolicy.setAgreementNumber(agreementNbr);
		insurancePolicy.setCreditScoreInfoClientConfirmationInd(Boolean.FALSE);
		insurancePolicy.setTestDataIndicator(Boolean.FALSE);
		insurancePolicy.setManufacturerCompany(manufacturingContext.getManufacturerCompany());

		// set policyVersion part
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setId(policyVersionId);
		policyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL);

		if (lang.equalsIgnoreCase("fr")) {
			policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
		} else {
			policyVersion.setLanguageOfCommunication(LanguageCodeEnum.ENGLISH);
		}
		policyVersion.setInitialTransactionCode(InitialTransactionCodeEnum.NEW_BUSINESS);
		policyVersion.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);

		// Populate the insurance policy
		insurancePolicy.addPolicyVersion(policyVersion);
		policyVersion.setInsurancePolicy(insurancePolicy);

		// An insurance risk needs to be added to the policy version
		policyVersion.addInsuranceRisk(new InsuranceRisk());
		return policyVersion;
	}
}
