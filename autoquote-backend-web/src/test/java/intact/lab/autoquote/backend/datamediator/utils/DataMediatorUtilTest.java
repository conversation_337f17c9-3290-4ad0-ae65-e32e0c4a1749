package intact.lab.autoquote.backend.datamediator.utils;

import com.ing.canada.plp.domain.coverage.CoverageOption;
import com.ing.canada.som.impl.agreement.PolicyVersionImpl;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.rootclasses.GenericRootObject;
import com.ing.canada.som.sdo.risk.CoverageOptionBO;
import com.ing.canada.sombase.IGenericRootObject;
import com.ing.canada.sombase.ModelFactory;
import commonj.sdo.ChangeSummary;
import commonj.sdo.DataGraph;
import commonj.sdo.DataObject;
import intact.lab.autoquote.backend.datamediator.DMConstants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class DataMediatorUtilTest {

    @Test
    public void testCoverageOptionsConversion() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();
        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        com.ing.canada.som.interfaces.risk.CoverageOption coverageOption = insuranceRisk.addTheCoverageOption();
        coverageOption.setDeductibleAmount(1000);
        coverageOption.setLimitOfInsurance(2000000);
        coverageOption.setProfileBuyingPercentage(20.55);
        coverageOption.setProfileMostPopularInd("Y");
        coverageOption.setRecommendedInd("N");
        coverageOption.setRecommendationCode("REC-CODE");

        com.ing.canada.plp.domain.coverage.CoverageOption plCoverageOption = new CoverageOption();

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject dataObject = changedDataObjectsList.stream()
                .filter(changedDataObject -> changedDataObject instanceof CoverageOptionBO)
                .findFirst()
                .orElse(null);

        DataMediatorUtils.setAttributesByReflectionFromSOMtoPL(
                DMConstants.plCoverageOptionAttributeNames,
                DMConstants.somCoverageOptionAttributeNames,
                plCoverageOption,
                dataObject
        );

        assertNotNull(plCoverageOption);
        assertEquals(1000, plCoverageOption.getDeductibleAmount().intValue());
        assertEquals(2000000, plCoverageOption.getLimitOfInsurance().intValue());
        assertEquals("20.55", plCoverageOption.getBuyingPercentage().toString());
        assertTrue(plCoverageOption.getMostPopularInd());
        assertFalse(plCoverageOption.getRecommendedInd());
        assertEquals("REC-CODE", plCoverageOption.getRecommendationCode());
    }
}
