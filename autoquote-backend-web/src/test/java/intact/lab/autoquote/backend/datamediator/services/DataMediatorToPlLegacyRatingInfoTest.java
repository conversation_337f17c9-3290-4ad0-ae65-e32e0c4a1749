package intact.lab.autoquote.backend.datamediator.services;

import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToPlLegacyRatingInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.som.impl.agreement.PolicyVersionImpl;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.LegacyRatingInfoByPostalCode;
import com.ing.canada.som.rootclasses.GenericRootObject;
import com.ing.canada.som.sdo.risk.LegacyRatingInfoByPostalCodeBO;
import com.ing.canada.sombase.IGenericRootObject;
import com.ing.canada.sombase.ModelFactory;

import commonj.sdo.ChangeSummary;
import commonj.sdo.DataGraph;
import commonj.sdo.DataObject;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DataMediatorToPlLegacyRatingInfoTest {

    @Mock
    private IBaseEntityDAO baseEntityDAO;

    @InjectMocks
    private DataMediatorToPlLegacyRatingInfo mediator;


    @Test
    public void processAdditionWithSomPersistentIdNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        insuranceRisk.setInsuranceRiskSequence(1);
        insuranceRisk.createTheLegacyRatingInfoByPostalCode();

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream().filter(dataObject ->
                dataObject instanceof LegacyRatingInfoByPostalCodeBO).findFirst().orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        assertThrows(Exception.class, () -> mediator.processLegacyRatingInfoByPostalCodeBO(plPolicyVersion, changedDataObject));
    }

    @Test
    public void processAdditionWithPolicyVersionIdNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        insuranceRisk.createTheLegacyRatingInfoByPostalCode();

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream().filter(dataObject -> dataObject instanceof LegacyRatingInfoByPostalCodeBO).findFirst().orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        plPolicyVersion.setId(null);
        assertThrows(Exception.class, () -> mediator.processLegacyRatingInfoByPostalCodeBO(plPolicyVersion, changedDataObject));
    }

    @Test
    public void processAdditions() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();

        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        insuranceRisk.setPersistenceUniqueId("1");

        LegacyRatingInfoByPostalCode legacyRatingInfoByPostalCodeBo = insuranceRisk.createTheLegacyRatingInfoByPostalCode();
        legacyRatingInfoByPostalCodeBo.setAutomobileTerritoryCollision("00");
        legacyRatingInfoByPostalCodeBo.setAutomobileTerritoryComprehensive("01");
        legacyRatingInfoByPostalCodeBo.setAutomobileTerritoryGlassBreakage("02");
        legacyRatingInfoByPostalCodeBo.setAutomobileTerritoryLiability("03");
        legacyRatingInfoByPostalCodeBo.setAutomobileTerritoryRating("04");

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = null;

        for (DataObject dataObject : changedDataObjectsList) {
            if (dataObject instanceof LegacyRatingInfoByPostalCodeBO) {
                changedDataObject = dataObject;
                break;
            }
        }

        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = new com.ing.canada.plp.domain.insurancerisk.InsuranceRisk();
        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        plPolicyVersion.addInsuranceRisk(plInsuranceRisk);

        when(baseEntityDAO.findEntityById(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, 1L)).thenReturn(plInsuranceRisk);

        ReflectionTestUtils.setField(mediator, "baseEntityDAO", baseEntityDAO, IBaseEntityDAO.class);
        mediator.processLegacyRatingInfoByPostalCodeBO(plPolicyVersion, changedDataObject);

        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk actualInsuranceRisk = plPolicyVersion.getInsuranceRisks().iterator().next();
        assertNotNull(actualInsuranceRisk);
        assertEquals("00", actualInsuranceRisk.getAutoMobileTerritoryCollision());
        assertEquals("01", actualInsuranceRisk.getAutoMobileTerritoryComprehensive());
        assertEquals("02", actualInsuranceRisk.getAutoMobileTerritoryGlassBreak());
        assertEquals("03", actualInsuranceRisk.getAutoMobileTerritoryLiability());
        assertEquals("04", actualInsuranceRisk.getAutoMobileTerritoryRating());
    }
}
