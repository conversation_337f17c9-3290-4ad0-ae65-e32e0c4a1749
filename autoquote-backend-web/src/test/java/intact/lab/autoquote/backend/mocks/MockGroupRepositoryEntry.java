/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessModel.ManufacturingContext;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;

import java.util.GregorianCalendar;
import java.util.List;

/**
 * 
 * Mock class for a SOM GroupRepositoryEntry. Created because of lack of an implementation that can be mocked and
 * because Mockito/Powermock can't mock the interface. Implements
 * {@link com.ing.canada.som.interfaces.party.GroupRepositoryEntry}
 * 
 * <AUTHOR>
 *
 */
public class MockGroupRepositoryEntry implements com.ing.canada.som.interfaces.party.GroupRepositoryEntry {

	private String insuredGroup = "test";

	@Override
	public String getLanguage() {
		return null;
	}

	@Override
	public void setLanguage(String newLanguage) {
		// noop
	}

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public String getPartyGroupType() {
		return null;
	}

	@Override
	public void setPartyGroupType(String newPartyGroupType) {
		// noop
	}

	@Override
	public String getPartyGroupCode() {
		return null;
	}

	@Override
	public void setPartyGroupCode(String newPartyGroupCode) {
		// noop
	}

	@Override
	public String getPartyGroupDescription() {
		return null;
	}

	@Override
	public void setPartyGroupDescription(String newPartyGroupDescription) {
		// noop
	}

	@Override
	public String getPartySubGroupType() {
		return null;
	}

	@Override
	public void setPartySubGroupType(String newPartySubGroupType) {
		// noop
	}

	@Override
	public String getPartySubGroupCode() {
		return null;
	}

	@Override
	public void setPartySubGroupCode(String newPartySubGroupCode) {
		// noop
	}

	@Override
	public String getPartySubGroupDescription() {
		return null;
	}

	@Override
	public void setPartySubGroupDescription(String newPartySubGroupDescription) {
		// noop
	}

	@Override
	public String getInsuredGroupType() {
		return null;
	}

	@Override
	public void setInsuredGroupType(String newInsuredGroupType) {
		// noop
	}

	@Override
	public String getInsuredGroup() {
		return this.insuredGroup;
	}

	@Override
	public void setInsuredGroup(String newInsuredGroup) {
		this.insuredGroup = newInsuredGroup;
	}

	@Override
	public String getInsuredGroupLegacy() {
		return null;
	}

	@Override
	public void setInsuredGroupLegacy(String newInsuredGroupLegacy) {
		// noop
	}

	@Override
	public Double getInsuredGroupDiscount() {
		return null;
	}

	@Override
	public void setInsuredGroupDiscount(Double newInsuredGroupDiscount) {
		// noop
	}

	@Override
	public String getEligibleToPayrollDeductionInd() {
		return null;
	}

	@Override
	public void setEligibleToPayrollDeductionInd(String newEligibleToPayrollDeductionInd) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDate() {
		return null;
	}

	@Override
	public void setExpiryDate(GregorianCalendar newExpiryDate) {
		// noop
	}

	@Override public String getRiskTypeAndSubTypeEligible() {
		return null;
	}

	@Override public void setRiskTypeAndSubTypeEligible(String s) {

	}

	@Override public String getPartyGroupSector() {
		return null;
	}

	@Override public void setPartyGroupSector(String s) {

	}

	@Override
	public ManufacturingContext getTheManufacturingContext() {
		return null;
	}

	@Override
	public void setTheManufacturingContext(ManufacturingContext newTheManufacturingContext) {
		// noop
	}

	@Override
	public ManufacturingContext createTheManufacturingContext() {
		return null;
	}

	@Override
	public ManufacturingContext createTheManufacturingContext(Class<? extends ManufacturingContext> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

}
