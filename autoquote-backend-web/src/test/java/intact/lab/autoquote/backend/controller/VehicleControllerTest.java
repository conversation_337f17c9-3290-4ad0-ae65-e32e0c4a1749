package intact.lab.autoquote.backend.controller;

import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class VehicleControllerTest {

    @Mock
    private IVehicleFacade vehicleFacade;

    @InjectMocks
    private VehicleController classeToTest;

    MockMvc mockMvc;

    @BeforeEach
    public void setUp() {

        this.mockMvc = MockMvcBuilders.standaloneSetup(this.classeToTest).build();
    }

    @Test
    void testGetVehicleMakeList() throws Exception {

        when(vehicleFacade.getVehicleMakeList("2023", "ON", "en"))
                .thenReturn(List.of(new MakeDTO("ACURA", "ACURA"), new MakeDTO("AUDI", "AUDI")));

        mockMvc.perform(get("/irca/v2/vehicles/2023/makes")
                        .param("province", "ON")
                        .param("language", "en"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].code").value("ACURA"))
                .andExpect(jsonPath("$[1].code").value("AUDI"));
    }

    @Test
    void testGetVehicleModelList() throws Exception {

        ModelDTO model1 = new ModelDTO("851800", "GIULIA QUADRIFOGLIO 4DR");
        ModelDTO model2 = new ModelDTO("851702", "GIULIA SPRINT");

        when(vehicleFacade.getVehicleModels("2024", "ALFA ROMEO", "ON", "en"))
                .thenReturn(List.of(model1, model2));

        mockMvc.perform(get("/irca/v2/vehicles/2024/ALFA ROMEO/models")
                        .param("province", "ON")
                        .param("language", "en"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].code").value(model1.getCode()))
                .andExpect(jsonPath("$[1].code").value(model2.getCode()));
    }
}
