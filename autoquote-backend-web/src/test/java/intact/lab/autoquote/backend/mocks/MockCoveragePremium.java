/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.moneyProvisionPremium.KindOfLossPremium;
import com.ing.canada.som.interfaces.moneyProvisionPremium.SubCoveragePremium;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.som.interfaces.risk.CoverageDiscountSurcharge;

import java.util.ArrayList;
import java.util.List;

/**
 * Mock class for a SOM Coverage Premium. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements
 * {@link com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium}
 *
 * <AUTHOR>
 */
public class MockCoveragePremium implements com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium {

    private List<SubCoveragePremium> testSubCoveragePremiumList;

    @Override
    public String getActionTaken() {
        return null;
    }

    @Override
    public void setActionTaken(String newActionTaken) {
        // noop
    }

    @Override
    public String getPersistenceUniqueId() {
        return null;
    }

    @Override
    public void setPersistenceUniqueId(String newPersistenceUniqueId) {
        // noop
    }

    @Override
    public String getTestDataTrace() {
        return null;
    }

    @Override
    public void setTestDataTrace(String newTestDataTrace) {
        // noop
    }

    @Override public String getTestDataTraceFormatted() {
        return null;
    }

    @Override public void setTestDataTraceFormatted(String s) {

    }

    @Override
    public void clearTheDocument() {
        // noop
    }

    @Override
    public List<Document> getTheDocument() {
        return null;
    }

    @Override
    public Document getTheDocument(String uniqueId) {
        return null;
    }

    @Override
    public Document getTheDocument(int index) {
        return null;
    }

    @Override
    public Document addTheDocument() {
        return null;
    }

    @Override
    public Document addTheDocument(Class<? extends Document> theInterface) {
        return null;
    }

    @Override
    public void addTheDocument(Document newTheDocument) {
        // noop
    }

    @Override
    public void addTheDocument(int index, Document newTheDocument) {
        // noop
    }

    @Override
    public void setTheDocument(int index, Document newTheDocument) {
        // noop
    }

    @Override
    public void setTheDocument(List<Document> objList) {
        // noop
    }

    @Override
    public void removeTheDocument(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheDocument(int index) {
        // noop
    }

    @Override
    public void clearTheAssessmentResult() {
        // noop
    }

    @Override
    public List<AssessmentResult> getTheAssessmentResult() {
        return null;
    }

    @Override
    public AssessmentResult getTheAssessmentResult(String uniqueId) {
        return null;
    }

    @Override
    public AssessmentResult getTheAssessmentResult(int index) {
        return null;
    }

    @Override
    public AssessmentResult addTheAssessmentResult() {
        return null;
    }

    @Override
    public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
        return null;
    }

    @Override
    public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
        // noop
    }

    @Override
    public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
        // noop
    }

    @Override
    public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
        // noop
    }

    @Override
    public void setTheAssessmentResult(List<AssessmentResult> objList) {
        // noop
    }

    @Override
    public void removeTheAssessmentResult(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheAssessmentResult(int index) {
        // noop
    }

    @Override
    public void clearTheTransactionalMessage() {
        // noop
    }

    @Override
    public List<TransactionalMessage> getTheTransactionalMessage() {
        return null;
    }

    @Override
    public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
        return null;
    }

    @Override
    public TransactionalMessage getTheTransactionalMessage(int index) {
        return null;
    }

    @Override
    public TransactionalMessage addTheTransactionalMessage() {
        return null;
    }

    @Override
    public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
        return null;
    }

    @Override
    public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
        // noop
    }

    @Override
    public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
        // noop
    }

    @Override
    public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
        // noop
    }

    @Override
    public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
        // noop
    }

    @Override
    public void removeTheTransactionalMessage(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheTransactionalMessage(int index) {
        // noop
    }

    @Override
    public String getUniqueId() {
        return null;
    }

    @Override
    public void setUniqueId(String string) {
        // noop
    }

    @Override
    public boolean equals(SOMBaseObjectInterface _baseObject) {
        return false;
    }

    @Override
    public Double getAnnualPremiumSystem() {
        return null;
    }

    @Override
    public void setAnnualPremiumSystem(Double newAnnualPremiumSystem) {
        // noop
    }

    @Override
    public Double getAnnualPremiumModified() {
        return null;
    }

    @Override
    public void setAnnualPremiumModified(Double newAnnualPremiumModified) {
        // noop
    }

    @Override
    public Double getAnnualPremium() {
        return null;
    }

    @Override
    public void setAnnualPremium(Double newAnnualPremium) {
        // noop
    }

    @Override
    public Double getAnnualPremiumVai() {
        return null;
    }

    @Override
    public void setAnnualPremiumVai(Double newAnnualPremiumVai) {

    }

    @Override
    public Double getAnnualPremiumBasicCoverageScoring() {
        return null;
    }

    @Override
    public void setAnnualPremiumBasicCoverageScoring(Double newAnnualPremiumBasicCoverageScoring) {
        // noop
    }

    @Override
    public Double getAnnualPremiumScoringRai() {
        return null;
    }

    @Override
    public void setAnnualPremiumScoringRai(Double newAnnualPremiumScoringRai) {
        // noop
    }

    @Override
    public Double getAnnualPremiumTarget() {
        return null;
    }

    @Override
    public void setAnnualPremiumTarget(Double newAnnualPremiumTarget) {
        // noop
    }

    @Override
    public Double getAnnualPremiumCostOfCapital() {
        return null;
    }

    @Override
    public void setAnnualPremiumCostOfCapital(Double newAnnualPremiumCostOfCapital) {
        // noop
    }

    @Override
    public Double getAnnualPremiumFloor() {
        return null;
    }

    @Override
    public void setAnnualPremiumFloor(Double newAnnualPremiumFloor) {
        // noop
    }

    @Override
    public Double getAnnualPremiumFlex() {
        return null;
    }

    @Override
    public void setAnnualPremiumFlex(Double newAnnualPremiumFlex) {
        // noop
    }

    @Override
    public Double getAnnualPremiumScoring() {
        return null;
    }

    @Override
    public void setAnnualPremiumScoring(Double newAnnualPremiumScoring) {
        // noop
    }

    @Override
    public Double getAnnualPremiumScoringVai() {
        return null;
    }

    @Override
    public void setAnnualPremiumScoringVai(Double newAnnualPremiumScoringVai) {

    }

    @Override
    public Double getAnnualPremiumMaximum() {
        return null;
    }

    @Override
    public void setAnnualPremiumMaximum(Double newAnnualPremiumMaximum) {
        // noop
    }

    @Override
    public Double getAnnualPremiumDiscountedSurcharged() {
        return null;
    }

    @Override
    public void setAnnualPremiumDiscountedSurcharged(Double newAnnualPremiumDiscountedSurcharged) {
        // noop
    }

    @Override
    public Double getAnnualPremiumDiscountedSurchargedCapped() {
        return null;
    }

    @Override
    public void setAnnualPremiumDiscountedSurchargedCapped(Double newAnnualPremiumDiscountedSurchargedCapped) {
        // noop
    }

    @Override
    public Double getAnnualPremiumKindOfLoss() {
        return null;
    }

    @Override
    public void setAnnualPremiumKindOfLoss(Double newAnnualPremiumKindOfLoss) {
        // noop
    }

    @Override
    public Double getAnnualPremiumIntermediary() {
        return null;
    }

    @Override
    public void setAnnualPremiumIntermediary(Double newAnnualPremiumIntermediary) {
        // noop
    }

    @Override
    public Double getCappedChangeAmountAnnual() {
        return null;
    }

    @Override
    public void setCappedChangeAmountAnnual(Double newCappedChangeAmountAnnual) {
        // noop
    }

    @Override
    public Double getCappingPercentageSystem() {
        return null;
    }

    @Override
    public void setCappingPercentageSystem(Double newCappingPercentageSystem) {
        // noop
    }

    @Override
    public Double getCappingPercentageModified() {
        return null;
    }

    @Override
    public void setCappingPercentageModified(Double aDouble) {

    }

    @Override
    public Double getCappingPercentage() {
        return null;
    }

    @Override
    public void setCappingPercentage(Double aDouble) {

    }

    @Override
    public Double getAnnualPremiumCapped() {
        return null;
    }

    @Override
    public void setAnnualPremiumCapped(Double newAnnualPremiumCapped) {
        // noop
    }

    @Override
    public Double getAnnualPremiumConsolidated() {
        return null;
    }

    @Override
    public void setAnnualPremiumConsolidated(Double newAnnualPremiumConsolidated) {
        // noop
    }

    @Override
    public Double getAnnualPremiumConsolidatedCapped() {
        return null;
    }

    @Override
    public void setAnnualPremiumConsolidatedCapped(Double newAnnualPremiumConsolidatedCapped) {
        // noop
    }

    @Override
    public Double getAnnualPremiumBeforeMarketingDiscount() {
        return null;
    }

    @Override
    public void setAnnualPremiumBeforeMarketingDiscount(Double newAnnualPremiumBeforeMarketingDiscount) {
        // noop
    }

    @Override
    public Double getAnnualPremiumBeforeMarketingDiscountScoring() {
        return null;
    }

    @Override
    public void setAnnualPremiumBeforeMarketingDiscountScoring(Double newAnnualPremiumBeforeMarketingDiscountScoring) {
        // noop
    }

    @Override
    public Double getAnnualPremiumOfExcludedEndorsements() {
        return null;
    }

    @Override
    public void setAnnualPremiumOfExcludedEndorsements(Double newAnnualPremiumOfExcludedEndorsements) {
        // noop
    }

    @Override
    public Double getAnnualPremiumFacilityRiskSharingPool() {
        return null;
    }

    @Override
    public void setAnnualPremiumFacilityRiskSharingPool(Double newAnnualPremiumFacilityRiskSharingPool) {
        // noop
    }

    @Override
    public Double getAnnualPremiumFacilityRiskSharingPoolFloor() {
        return null;
    }

    @Override
    public void setAnnualPremiumFacilityRiskSharingPoolFloor(Double newAnnualPremiumFacilityRiskSharingPoolFloor) {
        // noop
    }

    @Override
    public Double getAdditionalReturnPremiumSystem() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumSystem(Double newAdditionalReturnPremiumSystem) {
        // noop
    }

    @Override
    public Double getAdditionalReturnPremiumModified() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumModified(Double newAdditionalReturnPremiumModified) {
        // noop
    }

    @Override
    public Double getAdditionalReturnPremium() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremium(Double newAdditionalReturnPremium) {
        // noop
    }

    @Override
    public String getAdditionalReturnPremiumText() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumText(String newAdditionalReturnPremiumText) {
        // noop
    }

    @Override
    public Double getAdditionalReturnPremiumAdjustmentForMinimumRetained() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumAdjustmentForMinimumRetained(Double newAdditionalReturnPremiumAdjustmentForMinimumRetained) {
        // noop
    }

    @Override
    public Double getAdditionalReturnPremiumAdjustmentForWaived() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumAdjustmentForWaived(Double newAdditionalReturnPremiumAdjustmentForWaived) {
        // noop
    }

    @Override
    public Double getAdditionalPremium() {
        return null;
    }

    @Override
    public void setAdditionalPremium(Double newAdditionalPremium) {
        // noop
    }

    @Override
    public Double getAdditionalPremiumGrid() {
        return null;
    }

    @Override
    public void setAdditionalPremiumGrid(Double newAdditionalPremiumGrid) {
        // noop
    }

    @Override
    public Double getReturnPremium() {
        return null;
    }

    @Override
    public void setReturnPremium(Double newReturnPremium) {
        // noop
    }

    @Override
    public Double getReturnPremiumGrid() {
        return null;
    }

    @Override
    public void setReturnPremiumGrid(Double newReturnPremiumGrid) {
        // noop
    }

    @Override
    public Double getRegularPremium() {
        return null;
    }

    @Override
    public void setRegularPremium(Double newRegularPremium) {
        // noop
    }

    @Override
    public Double getFullTermPremiumRegular() {
        return null;
    }

    @Override
    public void setFullTermPremiumRegular(Double newFullTermPremiumRegular) {
        // noop
    }

    @Override
    public Double getFullTermPremiumRegularTarget() {
        return null;
    }

    @Override
    public void setFullTermPremiumRegularTarget(Double newFullTermPremiumRegularTarget) {
        // noop
    }

    @Override
    public Double getFullTermPremiumRegularCostOfCapital() {
        return null;
    }

    @Override
    public void setFullTermPremiumRegularCostOfCapital(Double newFullTermPremiumRegularCostOfCapital) {
        // noop
    }

    @Override
    public Double getFullTermPremiumRegularFloor() {
        return null;
    }

    @Override
    public void setFullTermPremiumRegularFloor(Double newFullTermPremiumRegularFloor) {
        // noop
    }

    @Override
    public Double getFullTermPremium() {
        return null;
    }

    @Override
    public void setFullTermPremium(Double newFullTermPremium) {
        // noop
    }

    @Override
    public String getFullTermPremiumText() {
        return null;
    }

    @Override
    public void setFullTermPremiumText(String newFullTermPremiumText) {
        // noop
    }

    @Override
    public Double getFullTermPremiumTarget() {
        return null;
    }

    @Override
    public void setFullTermPremiumTarget(Double newFullTermPremiumTarget) {
        // noop
    }

    @Override
    public Double getFullTermPremiumCostOfCapital() {
        return null;
    }

    @Override
    public void setFullTermPremiumCostOfCapital(Double newFullTermPremiumCostOfCapital) {
        // noop
    }

    @Override
    public Double getFullTermPremiumFloor() {
        return null;
    }

    @Override
    public void setFullTermPremiumFloor(Double newFullTermPremiumFloor) {
        // noop
    }

    @Override
    public Double getFullTermPremiumGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumGrid(Double newFullTermPremiumGrid) {
        // noop
    }

    @Override
    public Double getFullTermPremiumFlex() {
        return null;
    }

    @Override
    public void setFullTermPremiumFlex(Double newFullTermPremiumFlex) {
        // noop
    }

    @Override
    public Double getFullTermPremiumProRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumProRated(Double newFullTermPremiumProRated) {
        // noop
    }

    @Override
    public Double getFullTermPremiumProRatedGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumProRatedGrid(Double newFullTermPremiumProRatedGrid) {
        // noop
    }

    @Override
    public Double getFullTermPremiumShortRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumShortRated(Double newFullTermPremiumShortRated) {
        // noop
    }

    @Override
    public Double getFullTermPremiumShortRatedGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumShortRatedGrid(Double newFullTermPremiumShortRatedGrid) {
        // noop
    }

    @Override
    public Double getFullTermPremiumConsolidated() {
        return null;
    }

    @Override
    public void setFullTermPremiumConsolidated(Double newFullTermPremiumConsolidated) {
        // noop
    }

    @Override
    public Double getFullTermPremiumDiscountedSurcharged() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurcharged(Double newFullTermPremiumDiscountedSurcharged) {
        // noop
    }

    @Override
    public Double getFullTermPremiumDiscountedSurchargedGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurchargedGrid(Double newFullTermPremiumDiscountedSurchargedGrid) {

    }


    @Override
    public Double getFullTermPremiumDifferenceWithPriorTrans() {
        return null;
    }

    @Override
    public void setFullTermPremiumDifferenceWithPriorTrans(Double newFullTermPremiumDifferenceWithPriorTrans) {
        // noop
    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPool() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPool(Double newFullTermPremiumFacilityRiskSharingPool) {
        // noop
    }

    @Override
    public Double getFullTermPremiumCostingInitial() {
        return null;
    }

    @Override
    public void setFullTermPremiumCostingInitial(Double newFullTermPremiumCostingInitial) {
        // noop
    }

    @Override
    public Double getFullTermPremiumTrended() {
        return null;
    }

    @Override
    public void setFullTermPremiumTrended(Double newFullTermPremiumTrended) {
        // noop
    }

    @Override
    public Double getCumulatedFullTermPremium() {
        return null;
    }

    @Override
    public void setCumulatedFullTermPremium(Double newCumulatedFullTermPremium) {
        // noop
    }

    @Override
    public Double getCumulatedFullTermPremiumWithTax() {
        return null;
    }

    @Override
    public void setCumulatedFullTermPremiumWithTax(Double newCumulatedFullTermPremiumWithTax) {
        // noop
    }

    @Override
    public Double getAnnualAmountOfAdjustment() {
        return null;
    }

    @Override
    public void setAnnualAmountOfAdjustment(Double newAnnualAmountOfAdjustment) {
        // noop
    }

    @Override
    public String getAdjustmentReason() {
        return null;
    }

    @Override
    public void setAdjustmentReason(String newAdjustmentReason) {
        // noop
    }

    @Override
    public Double getAdjustmentAmountForAdditionalReturnPremium() {
        return null;
    }

    @Override
    public void setAdjustmentAmountForAdditionalReturnPremium(Double newAdjustmentAmountForAdditionalReturnPremium) {
        // noop
    }

    @Override
    public Double getAdjustmentPercentageSystem() {
        return null;
    }

    @Override
    public void setAdjustmentPercentageSystem(Double newAdjustmentPercentageSystem) {
        // noop
    }

    @Override
    public Double getAdjustmentPercentageModified() {
        return null;
    }

    @Override
    public void setAdjustmentPercentageModified(Double newAdjustmentPercentageModified) {
        // noop
    }

    @Override
    public Double getAdjustmentPercentage() {
        return null;
    }

    @Override
    public void setAdjustmentPercentage(Double newAdjustmentPercentage) {
        // noop
    }

    @Override
    public Double getDeviationPercentageSystem() {
        return null;
    }

    @Override
    public void setDeviationPercentageSystem(Double newDeviationPercentageSystem) {
        // noop
    }

    @Override
    public Double getDeviationPercentageModified() {
        return null;
    }

    @Override
    public void setDeviationPercentageModified(Double newDeviationPercentageModified) {
        // noop
    }

    @Override
    public Double getDeviationPercentage() {
        return null;
    }

    @Override
    public void setDeviationPercentage(Double newDeviationPercentage) {
        // noop
    }

    @Override
    public Double getDeviationAmount() {
        return null;
    }

    @Override
    public void setDeviationAmount(Double newDeviationAmount) {
        // noop
    }

    @Override
    public Double getAnnualPremiumCappedAfterDeviation() {
        return null;
    }

    @Override
    public void setAnnualPremiumCappedAfterDeviation(Double newAnnualPremiumCappedAfterDeviation) {
        // noop
    }

    @Override
    public Double getRatePerThousand() {
        return null;
    }

    @Override
    public void setRatePerThousand(Double newRatePerThousand) {
        // noop
    }

    @Override
    public Double getDiscountSurchargePercentage() {
        return null;
    }

    @Override
    public void setDiscountSurchargePercentage(Double newDiscountSurchargePercentage) {
        // noop
    }

    @Override
    public Double getDeductibleDiscountSurchargePercentage() {
        return null;
    }

    @Override
    public void setDeductibleDiscountSurchargePercentage(Double newDeductibleDiscountSurchargePercentage) {
        // noop
    }

    @Override
    public Double getDeductibleDiscountSurchargePercentageScoring() {
        return null;
    }

    @Override
    public void setDeductibleDiscountSurchargePercentageScoring(Double newDeductibleDiscountSurchargePercentageScoring) {
        // noop
    }

    @Override
    public String getMultiVehicleDiscountType() {
        return null;
    }

    @Override
    public void setMultiVehicleDiscountType(String newMultiVehicleDiscountType) {
        // noop
    }

    @Override
    public Double getConvictionSurchargePercentage() {
        return null;
    }

    @Override
    public void setConvictionSurchargePercentage(Double newConvictionSurchargePercentage) {
        // noop
    }

    @Override
    public Double getCoverageRateRegular() {
        return null;
    }

    @Override
    public void setCoverageRateRegular(Double newCoverageRateRegular) {
        // noop
    }

    @Override
    public Double getCoverageRateRegularTarget() {
        return null;
    }

    @Override
    public void setCoverageRateRegularTarget(Double newCoverageRateRegularTarget) {
        // noop
    }

    @Override
    public Double getCoverageRateRegularCostOfCapital() {
        return null;
    }

    @Override
    public void setCoverageRateRegularCostOfCapital(Double newCoverageRateRegularCostOfCapital) {
        // noop
    }

    @Override
    public Double getCoverageRateRegularFloor() {
        return null;
    }

    @Override
    public void setCoverageRateRegularFloor(Double newCoverageRateRegularFloor) {
        // noop
    }

    @Override
    public Double getCoverageRateRegularContent() {
        return null;
    }

    @Override
    public void setCoverageRateRegularContent(Double newCoverageRateRegularContent) {
        // noop
    }

    @Override
    public Double getCoverageRate() {
        return null;
    }

    @Override
    public void setCoverageRate(Double newCoverageRate) {
        // noop
    }

    @Override
    public Double getCoverageRateTarget() {
        return null;
    }

    @Override
    public void setCoverageRateTarget(Double newCoverageRateTarget) {
        // noop
    }

    @Override
    public Double getCoverageRateCostOfCapital() {
        return null;
    }

    @Override
    public void setCoverageRateCostOfCapital(Double newCoverageRateCostOfCapital) {
        // noop
    }

    @Override
    public Double getCoverageRateFloor() {
        return null;
    }

    @Override
    public void setCoverageRateFloor(Double newCoverageRateFloor) {
        // noop
    }

    @Override
    public Double getRegularLoadingRate() {
        return null;
    }

    @Override
    public void setRegularLoadingRate(Double newRegularLoadingRate) {
        // noop
    }

    @Override
    public Double getRegularPureRate() {
        return null;
    }

    @Override
    public void setRegularPureRate(Double newRegularPureRate) {
        // noop
    }

    @Override
    public Double getCommercialFinalRate() {
        return null;
    }

    @Override
    public void setCommercialFinalRate(Double newCommercialFinalRate) {
        // noop
    }

    @Override
    public Double getAnnualPremiumPreliminary() {
        return null;
    }

    @Override
    public void setAnnualPremiumPreliminary(Double newAnnualPremiumPreliminary) {
        // noop
    }

    @Override
    public Double getAnnualPremiumPreliminaryPricing() {
        return null;
    }

    @Override
    public void setAnnualPremiumPreliminaryPricing(Double newAnnualPremiumPreliminaryPricing) {
        // noop
    }

    @Override
    public Double getAnnualPremiumPricing() {
        return null;
    }

    @Override
    public void setAnnualPremiumPricing(Double newAnnualPremiumPricing) {
        // noop
    }

    @Override
    public Double getCoverageRatePricing() {
        return null;
    }

    @Override
    public void setCoverageRatePricing(Double newCoverageRatePricing) {
        // noop
    }

    @Override
    public Double getFullTermPremiumPricing() {
        return null;
    }

    @Override
    public void setFullTermPremiumPricing(Double newFullTermPremiumPricing) {
        // noop
    }

    @Override public Double getAnnualPremiumPreliminaryBuildingCostingVai() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryBuildingCostingVai(Double aDouble) {

    }

    @Override
    public Double getAnnualPremiumPreliminaryCosting() {
        return null;
    }

    @Override
    public void setAnnualPremiumPreliminaryCosting(Double newAnnualPremiumPreliminaryCosting) {
        // noop
    }

    @Override public String getAnnualPremiumPreliminaryCostingVaiFeatureVector() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryCostingVaiFeatureVector(String s) {

    }

    @Override public String getAnnualPremiumPreliminaryBuildingCostingVaiFeatureVector() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryBuildingCostingVaiFeatureVector(String s) {

    }

    @Override public String getAnnualPremiumPreliminaryContentCostingVaiFeatureVector() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryContentCostingVaiFeatureVector(String s) {

    }

    @Override public Double getRenewalImpactPremiumPricingForCapping() {
        return null;
    }

    @Override public void setRenewalImpactPremiumPricingForCapping(Double aDouble) {

    }

    @Override public Double getAnnualSubscribedPremium() {
        return null;
    }

    @Override public void setAnnualSubscribedPremium(Double aDouble) {

    }

    @Override public Double getFullTermSubscribedPremium() {
        return null;
    }

    @Override public void setFullTermSubscribedPremium(Double aDouble) {

    }

    @Override public Double getAnnualRetainedPremium() {
        return null;
    }

    @Override public void setAnnualRetainedPremium(Double aDouble) {

    }

    @Override public Double getFullTermRetainedPremium() {
        return null;
    }

    @Override public void setFullTermRetainedPremium(Double aDouble) {

    }

    @Override public Double getFullTermRetainedPremiumModified() {
        return null;
    }

    @Override public void setFullTermRetainedPremiumModified(Double aDouble) {

    }

    @Override public Double getAdditionalReturnRetainedPremium() {
        return null;
    }

    @Override public void setAdditionalReturnRetainedPremium(Double aDouble) {

    }

    @Override public Double getAdditionalReturnRetainedPremiumModified() {
        return null;
    }

    @Override public void setAdditionalReturnRetainedPremiumModified(Double aDouble) {

    }

    @Override
    public Double getAnnualPremiumCosting() {
        return null;
    }

    @Override
    public void setAnnualPremiumCosting(Double newAnnualPremiumCosting) {
        // noop
    }

    @Override
    public Double getCoverageRateCosting() {
        return null;
    }

    @Override
    public void setCoverageRateCosting(Double newCoverageRateCosting) {
        // noop
    }

    @Override
    public Double getFullTermPremiumCosting() {
        return null;
    }

    @Override
    public void setFullTermPremiumCosting(Double newFullTermPremiumCosting) {
        // noop
    }

    @Override
    public Double getRecommendedPremium() {
        return null;
    }

    @Override
    public void setRecommendedPremium(Double newRecommendedPremium) {
        // noop
    }

    @Override
    public Double getRecommendedPremiumInitial() {
        return null;
    }

    @Override
    public void setRecommendedPremiumInitial(Double newRecommendedPremiumInitial) {
        // noop
    }

    @Override
    public Double getRecommendedDeviationPercentage() {
        return null;
    }

    @Override
    public void setRecommendedDeviationPercentage(Double newRecommendedDeviationPercentage) {
        // noop
    }

    @Override
    public Double getWalkawayPremium() {
        return null;
    }

    @Override
    public void setWalkawayPremium(Double newWalkawayPremium) {
        // noop
    }

    @Override
    public Double getRatePerThousandScoring() {
        return null;
    }

    @Override
    public void setRatePerThousandScoring(Double newRatePerThousandScoring) {
        // noop
    }

    @Override
    public Double getOptimalChangeRatePercentage() {
        return null;
    }

    @Override
    public void setOptimalChangeRatePercentage(Double newOptimalChangeRatePercentage) {
        // noop
    }

    @Override
    public Double getSpeedToFullTermPremiumCosting() {
        return null;
    }

    @Override
    public void setSpeedToFullTermPremiumCosting(Double newSpeedToFullTermPremiumCosting) {
        // noop
    }

    @Override
    public Double getAdditionalPremiumDiscountedSurcharged() {
        return null;
    }

    @Override
    public void setAdditionalPremiumDiscountedSurcharged(Double aDouble) {

    }

    @Override
    public Double getAdditionalPremiumDiscountedSurchargedGrid() {
        return null;
    }

    @Override
    public void setAdditionalPremiumDiscountedSurchargedGrid(Double newAdditionalPremiumDiscountedSurchargedGrid) {

    }

    @Override
    public Double getReturnPremiumDiscountedSurcharged() {
        return null;
    }

    @Override
    public void setReturnPremiumDiscountedSurcharged(Double aDouble) {

    }

    @Override
    public Double getReturnPremiumDiscountedSurchargedGrid() {
        return null;
    }

    @Override
    public void setReturnPremiumDiscountedSurchargedGrid(Double newReturnPremiumDiscountedSurchargedGrid) {

    }

    @Override
    public Double getAdditionalReturnPremiumDiscountedSurcharged() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumDiscountedSurcharged(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumDiscountedSurchargedProRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurchargedProRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumDiscountedSurchargedProRatedGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurchargedProRatedGrid(Double newFullTermPremiumDiscountedSurchargedProRatedGrid) {

    }

    @Override
    public Double getFullTermPremiumDiscountedSurchargedShortRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurchargedShortRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumDiscountedSurchargedShortRatedGrid() {
        return null;
    }

    @Override
    public void setFullTermPremiumDiscountedSurchargedShortRatedGrid(Double newFullTermPremiumDiscountedSurchargedShortRatedGrid) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolCeded() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

    }

    @Override
    public Double getAdditionalPremiumFacilityRiskSharingPoolCeded() {
        return null;
    }

    @Override
    public void setAdditionalPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

    }

    @Override
    public Double getReturnPremiumFacilityRiskSharingPoolCeded() {
        return null;
    }

    @Override
    public void setReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

    }

    @Override
    public Double getAdditionalReturnPremiumFacilityRiskSharingPool() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumFacilityRiskSharingPool(Double newAdditionalReturnPremiumFacilityRiskSharingPool) {

    }

    @Override
    public Double getAdditionalReturnPremiumFacilityRiskSharingPoolCeded() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolCededShortRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolCededShortRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolCededProRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolCededProRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolStd() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolStd(Double aDouble) {

    }

    @Override
    public Double getAdditionalPremiumFacilityRiskSharingPoolStd() {
        return null;
    }

    @Override
    public void setAdditionalPremiumFacilityRiskSharingPoolStd(Double aDouble) {

    }

    @Override
    public Double getReturnPremiumFacilityRiskSharingPoolStd() {
        return null;
    }

    @Override
    public void setReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

    }

    @Override
    public Double getAdditionalReturnPremiumFacilityRiskSharingPoolStd() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolStdShortRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolStdShortRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolStdProRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolStdProRated(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolFloor() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

    }

    @Override
    public Double getAdditionalPremiumFacilityRiskSharingPoolFloor() {
        return null;
    }

    @Override
    public void setAdditionalPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

    }

    @Override
    public Double getReturnPremiumFacilityRiskSharingPoolFloor() {
        return null;
    }

    @Override
    public void setReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

    }

    @Override
    public Double getAdditionalReturnPremiumFacilityRiskSharingPoolFloor() {
        return null;
    }

    @Override
    public void setAdditionalReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolFloorShortRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolFloorShortRated(Double aDouble) {

    }

    @Override
    public String getIntermediaryDataTrace() {
        return null;
    }

    @Override
    public void setIntermediaryDataTrace(String newIntermediaryDataTrace) {

    }

    @Override
    public Double getAnnualCededPremium() {
        return null;
    }

    @Override
    public void setAnnualCededPremium(Double aDouble) {

    }

    @Override
    public Double getFullTermCededPremium() {
        return null;
    }

    @Override
    public void setFullTermCededPremium(Double aDouble) {

    }

    @Override public Double getAnnualPremiumPreliminaryEnhancedCosting() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryEnhancedCosting(Double aDouble) {

    }

    @Override public Double getAnnualPremiumPreliminaryEnhancedPricing() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryEnhancedPricing(Double aDouble) {

    }

    @Override public Double getAnnualPremiumEnhancedCosting() {
        return null;
    }

    @Override public void setAnnualPremiumEnhancedCosting(Double aDouble) {

    }

    @Override public Double getAnnualPremiumEnhancedPricing() {
        return null;
    }

    @Override public void setAnnualPremiumEnhancedPricing(Double aDouble) {

    }

    @Override public Double getFullTermPremiumEnhancedCosting() {
        return null;
    }

    @Override public void setFullTermPremiumEnhancedCosting(Double aDouble) {

    }

    @Override public Double getFullTermPremiumEnhancedPricing() {
        return null;
    }

    @Override public void setFullTermPremiumEnhancedPricing(Double aDouble) {

    }

    @Override public Double getAnnualPremiumPreliminaryContentCostingVai() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryContentCostingVai(Double aDouble) {

    }

    @Override public Double getAnnualPremiumPreliminaryCostingVai() {
        return null;
    }

    @Override public void setAnnualPremiumPreliminaryCostingVai(Double aDouble) {

    }

    @Override
    public Double getFullTermPremiumFacilityRiskSharingPoolFloorProRated() {
        return null;
    }

    @Override
    public void setFullTermPremiumFacilityRiskSharingPoolFloorProRated(Double aDouble) {

    }

    @Override
    public void clearTheKindOfLossPremium() {
        // noop
    }

    @Override
    public List<KindOfLossPremium> getTheKindOfLossPremium() {
        return null;
    }

    @Override
    public KindOfLossPremium getTheKindOfLossPremium(String uniqueId) {
        return null;
    }

    @Override
    public KindOfLossPremium getTheKindOfLossPremium(int index) {
        return null;
    }

    @Override
    public KindOfLossPremium addTheKindOfLossPremium() {
        return null;
    }

    @Override
    public KindOfLossPremium addTheKindOfLossPremium(Class<? extends KindOfLossPremium> theInterface) {
        return null;
    }

    @Override
    public void addTheKindOfLossPremium(KindOfLossPremium newTheKindOfLossPremium) {
        // noop
    }

    @Override
    public void addTheKindOfLossPremium(int index, KindOfLossPremium newTheKindOfLossPremium) {
        // noop
    }

    @Override
    public void setTheKindOfLossPremium(int index, KindOfLossPremium newTheKindOfLossPremium) {
        // noop
    }

    @Override
    public void setTheKindOfLossPremium(List<KindOfLossPremium> objList) {
        // noop
    }

    @Override
    public void removeTheKindOfLossPremium(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheKindOfLossPremium(int index) {
        // noop
    }

    @Override
    public void clearTheSubCoveragePremium() {
        // noop
    }

    @Override
    public List<SubCoveragePremium> getTheSubCoveragePremium() {
        // Create the list if it doesn't already exist
        this.testSubCoveragePremiumList = this.testSubCoveragePremiumList == null ? new ArrayList<SubCoveragePremium>()
                : this.testSubCoveragePremiumList;
        return this.testSubCoveragePremiumList;
    }

    @Override
    public SubCoveragePremium getTheSubCoveragePremium(String uniqueId) {
        return null;
    }

    @Override
    public SubCoveragePremium getTheSubCoveragePremium(int index) {
        return null;
    }

    @Override
    public SubCoveragePremium addTheSubCoveragePremium() {
        // Create the list if it doesn't already exist
        this.testSubCoveragePremiumList = this.testSubCoveragePremiumList == null ? new ArrayList<SubCoveragePremium>()
                : this.testSubCoveragePremiumList;

        // Add a new sub coverage premium
        SubCoveragePremium newSubCoveragePremium = new MockSubCoveragePremium();
        this.testSubCoveragePremiumList.add(newSubCoveragePremium);
        return newSubCoveragePremium;
    }

    @Override
    public SubCoveragePremium addTheSubCoveragePremium(Class<? extends SubCoveragePremium> theInterface) {
        return null;
    }

    @Override
    public void addTheSubCoveragePremium(SubCoveragePremium newTheSubCoveragePremium) {
        // noop
    }

    @Override
    public void addTheSubCoveragePremium(int index, SubCoveragePremium newTheSubCoveragePremium) {
        // noop
    }

    @Override
    public void setTheSubCoveragePremium(int index, SubCoveragePremium newTheSubCoveragePremium) {
        // noop
    }

    @Override
    public void setTheSubCoveragePremium(List<SubCoveragePremium> objList) {
        // noop
    }

    @Override
    public void removeTheSubCoveragePremium(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheSubCoveragePremium(int index) {
        // noop
    }

    @Override
    public Coverage getTheCoveragePrincipal() {
        return null;
    }

    @Override
    public void setTheCoveragePrincipal(Coverage newTheCoveragePrincipal) {
        // noop
    }

    @Override
    public Coverage createTheCoveragePrincipal() {
        return null;
    }

    @Override
    public Coverage createTheCoveragePrincipal(Class<? extends Coverage> theInterface) {
        return null;
    }

    @Override
    public Coverage getTheCoverageOccasional() {
        return null;
    }

    @Override
    public void setTheCoverageOccasional(Coverage newTheCoverageOccasional) {
        // noop
    }

    @Override
    public Coverage createTheCoverageOccasional() {
        return null;
    }

    @Override
    public Coverage createTheCoverageOccasional(Class<? extends Coverage> theInterface) {
        return null;
    }

    @Override
    public void clearTheCoverageDiscountSurcharge() {
        // noop
    }

    @Override
    public List<CoverageDiscountSurcharge> getTheCoverageDiscountSurcharge() {
        return null;
    }

    @Override
    public CoverageDiscountSurcharge getTheCoverageDiscountSurcharge(String uniqueId) {
        return null;
    }

    @Override
    public CoverageDiscountSurcharge getTheCoverageDiscountSurcharge(int index) {
        return null;
    }

    @Override
    public CoverageDiscountSurcharge addTheCoverageDiscountSurcharge() {
        return null;
    }

    @Override
    public CoverageDiscountSurcharge addTheCoverageDiscountSurcharge(Class<? extends CoverageDiscountSurcharge> theInterface) {
        return null;
    }

    @Override
    public void addTheCoverageDiscountSurcharge(CoverageDiscountSurcharge newTheCoverageDiscountSurcharge) {
        // noop
    }

    @Override
    public void addTheCoverageDiscountSurcharge(int index, CoverageDiscountSurcharge newTheCoverageDiscountSurcharge) {
        // noop
    }

    @Override
    public void setTheCoverageDiscountSurcharge(int index, CoverageDiscountSurcharge newTheCoverageDiscountSurcharge) {
        // noop
    }

    @Override
    public void setTheCoverageDiscountSurcharge(List<CoverageDiscountSurcharge> objList) {
        // noop
    }

    @Override
    public void removeTheCoverageDiscountSurcharge(String uniqueId) {
        // noop
    }

    @Override
    public void removeTheCoverageDiscountSurcharge(int index) {
        // noop
    }

    @Override
    public void clearTheSubCoveragePremiumExternalFeedBreakdown() {

    }

    @Override
    public List<SubCoveragePremium> getTheSubCoveragePremiumExternalFeedBreakdown() {
        return null;
    }

    @Override
    public SubCoveragePremium getTheSubCoveragePremiumExternalFeedBreakdown(String uniqueId) {
        return null;
    }

    @Override
    public SubCoveragePremium getTheSubCoveragePremiumExternalFeedBreakdown(int index) {
        return null;
    }

    @Override
    public SubCoveragePremium addTheSubCoveragePremiumExternalFeedBreakdown() {
        return null;
    }

    @Override
    public SubCoveragePremium addTheSubCoveragePremiumExternalFeedBreakdown(Class<? extends SubCoveragePremium> theInterface) {
        return null;
    }

    @Override
    public void addTheSubCoveragePremiumExternalFeedBreakdown(SubCoveragePremium newTheSubCoveragePremiumExternalFeedBreakdown) {

    }

    @Override
    public void addTheSubCoveragePremiumExternalFeedBreakdown(int index, SubCoveragePremium newTheSubCoveragePremiumExternalFeedBreakdown) {

    }

    @Override
    public void setTheSubCoveragePremiumExternalFeedBreakdown(int index, SubCoveragePremium newTheSubCoveragePremiumExternalFeedBreakdown) {

    }

    @Override
    public void setTheSubCoveragePremiumExternalFeedBreakdown(List<SubCoveragePremium> objList) {

    }

    @Override
    public void removeTheSubCoveragePremiumExternalFeedBreakdown(String uniqueId) {

    }

    @Override
    public void removeTheSubCoveragePremiumExternalFeedBreakdown(int index) {

    }

    @Override
    public Double getCumulatedAdditionalReturnPremium() {
        return null;
    }

    @Override
    public void setCumulatedAdditionalReturnPremium(Double arg0) {
        // noop
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#getAnnualPremiumFleetRated()
     */
    @Override
    public Double getAnnualPremiumFleetRated() {
        return null;
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#setAnnualPremiumFleetRated(Double)
     */
    @Override
    public void setAnnualPremiumFleetRated(Double newAnnualPremiumFleetRated) {
        // noop
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#getFullTermPremiumSystem()
     */
    @Override
    public Double getFullTermPremiumSystem() {
        return null;
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#setFullTermPremiumSystem(Double)
     */
    @Override
    public void setFullTermPremiumSystem(Double newFullTermPremiumSystem) {
        // noop
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#getFullTermPremiumModified()
     */
    @Override
    public Double getFullTermPremiumModified() {
        return null;
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#setFullTermPremiumModified(Double)
     */
    @Override
    public void setFullTermPremiumModified(Double newFullTermPremiumModified) {
        // noop
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#getFullTermPremiumFleetRated()
     */
    @Override
    public Double getFullTermPremiumFleetRated() {
        return null;
    }

    /**
     * @see com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium#setFullTermPremiumFleetRated(Double)
     */
    @Override
    public void setFullTermPremiumFleetRated(Double newFullTermPremiumFleetRated) {
        // noop
    }

    @Override
    public String getUuid() {
        return null;
    }

    @Override
    public void setUuid(String arg0) {
        // noop
    }

    @Override
    public Double getMajorConvictionSurchargePercentage() {
        return null;
    }

    @Override
    public Double getMinorConvictionSurchargePercentage() {
        return null;
    }

    @Override
    public Double getSevereConvictionSurchargePercentage() {
        return null;
    }

    @Override
    public void setMajorConvictionSurchargePercentage(Double arg0) {
        // noop
    }

    @Override
    public void setMinorConvictionSurchargePercentage(Double arg0) {
        // noop
    }

    @Override
    public void setSevereConvictionSurchargePercentage(Double arg0) {
        // noop
    }

    @Override
    public String getAnnualPremiumVaiFeatureVector() {
        return null;
    }

    @Override
    public void setAnnualPremiumVaiFeatureVector(String newAnnualPremiumVaiFeatureVector) {

    }

    @Override
    public String getAnnualPremiumScoringVaiFeatureVector() {
        return null;
    }

    @Override
    public void setAnnualPremiumScoringVaiFeatureVector(String newAnnualPremiumScoringVaiFeatureVector) {

    }
}
