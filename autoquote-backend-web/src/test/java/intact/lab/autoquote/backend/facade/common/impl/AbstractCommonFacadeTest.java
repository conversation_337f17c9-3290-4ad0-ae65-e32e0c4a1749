/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.common.impl;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.common.util.holiday.HolidayManager;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.AgreementTypeCodeEnum;
import com.ing.canada.plp.domain.enums.InitialTransactionCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.enums.RatingBasisCodeEnum;
import com.ing.canada.plp.domain.enums.SpfCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.state.ComState;
import com.intact.com.state.enums.ComStateEnum;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import intact.lab.autoquote.backend.facade.impl.FacadeTestUtil;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import intact.lab.autoquote.backend.services.ICloneService;
import intact.lab.autoquote.backend.services.INewQuoteBusinessProcessService;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.common.ICreditScoreBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.nohit.INoHitBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.services.mediation.ICOMtoPLAdapter;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class AbstractCommonFacadeTest extends FacadeTestUtil {

	/**
	 * Class to be tested. Since it is the abstract class, it is accessed through an autoquote QC
	 * implementation (allows testing of callCreditScore, since ON implementation overrides it).
	 */
	@InjectMocks
	private AutoQuoteCommonFacadeQCIntactCL commonFacade;

	/** Other general objects */
	private PolicyVersion currentMockPv;

	private ComContext currentComContext;

	private CommunicationObjectModel expectedCom;

	@Mock
	private RatingConfig ratingConfig;

	@Mock
	private IMediatorComPlp mediatorComPlp;

	/** Mocks */
	@Mock
	private IBaseFacade mockBaseFacade;

	@Mock
	private ICloneService mockCloneService;

	@Mock
	private ICommonBusinessProcess mockCommonBusinessProcess;

	@Mock
	private ICOMtoPLAdapter mockComToPLAdapter;

	@Mock
	private ICoverageHelper mockCoverageHelper;

	@Mock
	private ICreditScoreBusinessProcess mockCreditScoreBusinessProcess;

	@Mock
	private IDriverBusinessProcess mockDriverBusinessProcess;

	@Mock
	private HolidayManager mockHoliday;

	@Mock
	private INewQuoteBusinessProcessService mockNewQuoteBusinessProcess;

	@Mock
	private INoHitBusinessProcess mockNoHitBusinessProcess;

	@Mock
	private IQuoteStateManager mockQuoteStateManager;

	@Mock
	private ITransactionHistoryService mockTransactionHistoryService;

	@Mock
	private IUsageBusinessProcess mockUsageBusinessProcess;

	@Mock
	private IVehicleBusinessProcess mockVehicleBusinessProcess;

	@Mock
	private IConfigurator mockConfigurator;


	/** Constants */

	private final long MOCK_POLICYVERSION_ID = 123456;

	private final String MOCK_QUOTATION_NUMBER = "QUOT_NUM_12345";

	/**
	 * Test method for
	 * Case for no roadblocks found. Method accessed through the validateRoadblocks method.
	 * @throws Exception
	 */
	@Test
	public void testValidateRoadblocksVehiclesNoRB() throws Exception {
		// General setup for tests that calls buildCom
		CommunicationObjectModel testCom = this.prepareGeneralForBuildCom(this.context_IN_QC_EN, this.context_IN_ON_FR);

		// We add an insurance risk to access the wanted method
		InsuranceRisk mockRisk = mock(InsuranceRisk.class);
		this.currentMockPv.addInsuranceRisk(mockRisk);

		// Setup to avoid nullPointerExceptions when in debug
		when(mockRisk.getVehicle()).thenReturn(mock(Vehicle.class));

		// Executing the tested method
		this.commonFacade.validateRoadblocks(testCom);

		// If this method is called, the roadblocks list wasn't empty as expected
		verify(this.mockTransactionHistoryService, never())
			.updateTransHistoryOnRoadBlock(any(RoadBlockException.class), any(ComStateEnum.class), any(PolicyVersion.class));
	}

	@Test
	public void testCallCreditScoreGeneralException() throws AutoquoteBusinessException {
		// General setup for tests that calls buildCom
		CommunicationObjectModel com = this.prepareGeneralForBuildCom(this.context_IN_QC_EN, this.context_IN_ON_FR);

		// Exception to be thrown
		doThrow(new RuntimeException("Exception thrown for the test method \"testCallCreditScoreGeneralException\""))
				.when(this.mockCreditScoreBusinessProcess).callTheCreditScore(any(PolicyVersion.class));

		// Using assertThrows to verify exception is thrown
		assertThrows(AutoquoteFacadeException.class, () -> {
			this.commonFacade.callCreditScore(com);
		});
		// No need for fail statement as assertThrows will fail if exception isn't thrown
	}

	/**
	 * General method used to create the necessary mocks, stubs and objects so the BaseFacade.buildCom method
	 * can be called without causing an error Receives the desired context as param as well as the expected
	 * context.
	 * @param context desired context.
	 * @param expectedContext expected context.
	 * @return COM to be passed to the tested method
	 */
	private CommunicationObjectModel prepareGeneralForBuildCom (ComContext context, ComContext expectedContext) {
		// create ComContext and PolicyVersion with that context
		this.currentComContext = context;
		this.currentComContext.setPartnershipId(222L);
		ManufacturingContext currentContext = MediatorUtils.convertContext(this.currentComContext);
		this.currentMockPv = initMockPolicyVersion(this.MOCK_QUOTATION_NUMBER, this.MOCK_POLICYVERSION_ID,
					currentContext, this.currentComContext.getLanguage().getCode());

		// Creation and setup for the mediatorCom mock as well as a mock for result validation
		this.expectedCom = new CommunicationObjectModel();
		this.expectedCom.setContext(expectedContext); // Needs to be a different context to validate correctly
		this.expectedCom.setAgreementNumber(this.MOCK_QUOTATION_NUMBER);
		this.expectedCom.setPolicyVersionId(this.MOCK_POLICYVERSION_ID);

		when(this.mockCommonBusinessProcess.loadPolicyVersion(any(Long.class))).thenReturn(this.currentMockPv);

		// Creation of the COM to pass to the tested method
		CommunicationObjectModel com = new CommunicationObjectModel();
		com.setContext(this.currentComContext);
		com.setPolicyVersionId(111L);
		ComState testState = new ComState();
		testState.setDataChanged(true);
		testState.setHasOffer(true);
		com.setState(testState);

		return com;
	}

	/**
	 * Private method to initialize a mock policy version, not made for persistance.
	 *
	 * @param agreementNbr - {@link String}
	 * @param manufacturingContext - {@link ManufacturingContext}
	 * @param lang - the language
	 * @return
	 */
	private static PolicyVersion initMockPolicyVersion(final String agreementNbr, final Long policyVersionId,
			final ManufacturingContext manufacturingContext, final String lang) {
		// set insurancePolicy part
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		insurancePolicy.setAgreementType(AgreementTypeCodeEnum.QUOTATION);
		insurancePolicy.setSpfCode(SpfCodeEnum.STANDARD_OWNERS_AUTOMOBILE_POLICY.getCode());
		insurancePolicy.setRatingBasis(RatingBasisCodeEnum.INDIVIDUALLY_RATED);
		insurancePolicy.setLineOfBusinessCode(LineOfBusinessCodeEnum.PERSONAL_LINES);
		insurancePolicy.setManufacturingContext(manufacturingContext);
		insurancePolicy.setAgreementNumber(agreementNbr);
		insurancePolicy.setCreditScoreInfoClientConfirmationInd(Boolean.FALSE);
		insurancePolicy.setTestDataIndicator(Boolean.FALSE);
		insurancePolicy.setManufacturerCompany(manufacturingContext.getManufacturerCompany());

		// set policyVersion part
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setId(policyVersionId);
		policyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL);

		if (lang.equalsIgnoreCase("fr")) {
			policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
		} else {
			policyVersion.setLanguageOfCommunication(LanguageCodeEnum.ENGLISH);
		}
		policyVersion.setInitialTransactionCode(InitialTransactionCodeEnum.NEW_BUSINESS);
		policyVersion.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);

		// Populate the insurance policy
		insurancePolicy.addPolicyVersion(policyVersion);
		policyVersion.setInsurancePolicy(insurancePolicy);

		return policyVersion;
	}

}
