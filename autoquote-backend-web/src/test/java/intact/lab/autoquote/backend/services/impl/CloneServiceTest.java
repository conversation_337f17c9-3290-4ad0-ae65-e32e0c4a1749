/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.common.domain.AffinityGroupCode;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.AffinityGroupRepositoryEntry;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyGroup;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPolicyAdditionalCoverageService;
import com.ing.canada.plp.service.IPolicyVersionService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * 
 */
@ExtendWith(MockitoExtension.class)
public class CloneServiceTest {
	
	/** Class to be tested.*/
	@InjectMocks
	private CloneService cloneService;

	/** Test objects **/	
	private Party currentTestParty;

	private PolicyVersion currentTestPV;
	
	/** Mocks **/
	@Mock
	private IAffinityGroupService mockAffinityGroupService;

	@Mock
	private IPolicyAdditionalCoverageService mockPolicyAdditionalCoverageService;
	
	@Mock
	private IPolicyVersionHelper mockPolicyVersionHelper;
	
	@Mock
	private IPolicyVersionService mockPolicyVersionService;
	
	/**
	 * Sets the up.
	 * 
	 * @throws Exception the exception
	 */
	@BeforeEach
	public void setUp() throws Exception {
		// Policy Version setup
		this.currentTestPV = new PolicyVersion(123L);
		InsurancePolicy testInsPolicy = new InsurancePolicy();
		ManufacturingContext testContext = new ManufacturingContext();
		testContext.setProvince(ProvinceCodeEnum.ONTARIO);
		testInsPolicy.setManufacturingContext(testContext);
		this.currentTestPV.setInsurancePolicy(testInsPolicy);
		
		// Party setup
		this.currentTestParty = new Party();
		this.currentTestParty.setPolicyVersion(this.currentTestPV);
		this.currentTestPV.addParty(this.currentTestParty);
	}

	/**
	 * Tear down.
	 * 
	 * @throws Exception the exception
	 */
	@AfterEach
	public void tearDown() throws Exception {
		this.currentTestParty = null;
		this.currentTestPV = null;
	}

	/**
	 * Test for
	 */
	@Test
	public void testClearInsuranceRiskOnClaim(){	
		// Adding a claim with an insurance risk to the test party which should be removed
		Claim testClaim = new Claim();
		InsuranceRisk testRisk = new InsuranceRisk();
		testClaim.setInsuranceRisk(testRisk);
		this.currentTestParty.addClaim(testClaim);
		
		// Creating the parties set returned by the policyVersionHelper
		Set<Party> testPartySet = new HashSet<Party>();
		testPartySet.add(this.currentTestParty);
		when(this.mockPolicyVersionHelper.getIndividualParties(any(PolicyVersion.class))).thenReturn(testPartySet);
		
		// Executing the tested method and validating the results
		this.cloneService.clearInsuranceRiskOnClaims(this.currentTestPV);
		assertNull("The test claim's insurance risk should've been set to null.", testClaim.getInsuranceRisk());
	}
	
	/**
	 * Private method to do the setup and execute a test for the setAffinityOnPartyGroup method for
	 * the province passed as parameter.
	 * @param provinceCode ProvinceCodeEnum value to test. Needs to match the Province.
	 * @param province Province value to test. Needs to match the ProvinceCodeEnum.
	 */
	private void setupAndExecuteGeneralSetAffinityOnPartyGroupForProvince(ProvinceCodeEnum provinceCode, Province province) {
		// Setting the province in the manufacturing context
		this.currentTestPV.getInsurancePolicy().getManufacturingContext().setProvince(provinceCode);

		// An AffinityGroupRepositoryEntry is needed in the policy version to access the desired branch
		AffinityGroupRepositoryEntry testAGRE = new AffinityGroupRepositoryEntry();
		String expectedAffinityGroupCode = "testAffinityGroupCode";
		testAGRE.setAffinityGroupCode(expectedAffinityGroupCode);
		this.currentTestPV.setAffinityGroupRepositoryEntry(testAGRE);

		// Policy holder and party group setup for the test
		PolicyHolder testHolder = new PolicyHolder();
		PartyGroup testPartyGroup = new PartyGroup();
		this.currentTestParty.addPartyGroup(testPartyGroup);
		testHolder.setParty(this.currentTestParty);
		GroupRepositoryEntry testGRE = new GroupRepositoryEntry();
		testPartyGroup.setGroupRepositoryEntry(testGRE);
		testGRE.setPartyGroupType(PartyGroupTypeCodeEnum.EMPLOYER);

		String testPartyGroupCode = "testPartyGroupCode";
		testGRE.setPartyGroupCode(testPartyGroupCode);
		AffinityGroupCode testAffinityGroup = new AffinityGroupCode();
		testAffinityGroup.setPartyGroupCode(testPartyGroupCode);

		// Use any() for the date to avoid timestamp mismatch issues
		// Match the exact province and uppercase group code that's being passed in the actual code
		when(this.mockAffinityGroupService.getAffinityGroupCode(any(), anyString(), any(), any(), any(), any()))
				.thenReturn(testAffinityGroup);

		// Stubs setup
		when(this.mockPolicyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(testHolder);

		// Executing the tested method and validating the results
		this.cloneService.setAffinityOnPartyGroup(this.currentTestPV);
		assertEquals(testAGRE, testPartyGroup.getAffinityGroupRepositoryEntry());
		verify(this.mockAffinityGroupService, times(1)).getAffinityGroupCode(eq(province), eq(expectedAffinityGroupCode.toUpperCase()),
				any(Date.class), any(DistributionChannelCodeEnum.class), any(InsuranceBusinessCodeEnum.class),
				any(DistributorCodeEnum.class));
	}
}
