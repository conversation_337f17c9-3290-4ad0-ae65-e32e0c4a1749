package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.plp.domain.enums.ApplicationIdEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionService;
import intact.lab.autoquote.backend.services.business.common.impl.BusinessTransactionLoggingService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BusinessTransactionLoggingServiceTest {

    @InjectMocks
    private BusinessTransactionLoggingService businessTransactionLoggingService;

    @Mock
    private IBusinessTransactionService btService;

    @Test
    void testCreateActivity() {
        when(btService.createActivity(eq(BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED), eq(0L),
                eq("1111"), isNull(), eq(UserTypeCodeEnum.CLIENT), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE)))
                .thenReturn(11111L);

        businessTransactionLoggingService.createActivity(BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED, 0L,
                "1111", null, UserTypeCodeEnum.CLIENT);

        verify(btService, times(1)).createActivity(eq(BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED),
                eq(0L), eq("1111"), isNull(), eq(UserTypeCodeEnum.CLIENT), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE));
    }

    @Test
    void testCreateActivityWithInvalidParam() {
        doThrow(new IllegalStateException()).when(btService).createActivity(eq(BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED),
                eq(0L), eq("toto"), isNull(), eq(UserTypeCodeEnum.IN_HOUSE_USER), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE));

        assertThrows(IllegalStateException.class, () -> businessTransactionLoggingService.createActivity(
                    BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED, 0L, "toto", null,
                    UserTypeCodeEnum.IN_HOUSE_USER));

        verify(btService, times(1)).createActivity(eq(BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED),
                eq(0L), eq("toto"), isNull(), eq(UserTypeCodeEnum.IN_HOUSE_USER), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE));
    }

    @Test
    void testCreateActivityWithInvalidParam2() {
        doThrow(new IllegalStateException()).when(btService).createActivity(isNull(), eq(0L), eq("toto"), isNull(),
                eq(UserTypeCodeEnum.IN_HOUSE_USER), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE));

        assertThrows(IllegalStateException.class, () -> businessTransactionLoggingService.createActivity(null,
                    0L, "toto", null, UserTypeCodeEnum.IN_HOUSE_USER));

        verify(btService, times(1)).createActivity(isNull(), eq(0L), eq("toto"),
                isNull(), eq(UserTypeCodeEnum.IN_HOUSE_USER), eq(ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE));
    }

    @Test
    void testCreateSubActivityComplInfo() {
        when(btService.createSubActivityComplInfo(eq("BR99999"), eq("test"), eq(0L)))
                .thenReturn(1L);

        businessTransactionLoggingService.createSubActivityComplInfo("BR99999", "test", 0);

        verify(btService, times(1)).createSubActivityComplInfo("BR99999", "test", 0L);
    }

    @Test
    void testCreateSubActivityComplInfoWithInvalidParam() {
        when(btService.createSubActivityComplInfo(isNull(), eq("test"), anyLong()))
                .thenThrow(new IllegalArgumentException());

        assertThrows(IllegalArgumentException.class, () ->
                businessTransactionLoggingService.createSubActivityComplInfo(null, "test", 0));

        verify(btService).createSubActivityComplInfo(isNull(), eq("test"), eq(0L));
    }

    @Test
    void testCreateSubActivityComplInfoWithInvalidParam2() {
        when(btService.createSubActivityComplInfo(anyString(), isNull(), anyLong()))
                .thenThrow(new IllegalArgumentException());

        assertThrows(IllegalArgumentException.class, () ->
                businessTransactionLoggingService.createSubActivityComplInfo("test", null, 0));

        verify(btService).createSubActivityComplInfo(eq("test"), isNull(), eq(0L));
    }

    @Test
    void testCreateSubActivityComplInfoWithInvalidParam3() {
        when(btService.createSubActivityComplInfo(eq("test"), eq("test"), anyLong()))
                .thenThrow(new IllegalStateException());

        assertThrows(IllegalStateException.class, () ->
                businessTransactionLoggingService.createSubActivityComplInfo("test", "test", 0));

        verify(btService).createSubActivityComplInfo(eq("test"), eq("test"), eq(0L));
    }

    @Test
    void testCreateSubActivity() {
        when(btService.createSubActivity(eq(BusinessTransactionSubActivityCodeEnum.ADD_DRIVER), eq(1L), eq(1L)))
                .thenReturn(1L);

        businessTransactionLoggingService.createSubActivity(BusinessTransactionSubActivityCodeEnum.ADD_DRIVER, 1, 1);

        verify(btService, times(1)).createSubActivity(
                BusinessTransactionSubActivityCodeEnum.ADD_DRIVER, 1L, 1L);
    }
}
