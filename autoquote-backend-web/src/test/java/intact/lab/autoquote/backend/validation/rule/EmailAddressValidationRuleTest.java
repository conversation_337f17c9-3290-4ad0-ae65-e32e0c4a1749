package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

public class EmailAddressValidationRuleTest {

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "party");
	}

	@Test
	public void testValidate_Valid_ShouldPass() {
		EmailAddressValidationRule.validate(this.errors, "<EMAIL>");
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Invalid_ShouldFail() {
		EmailAddressValidationRule.validate(this.errors, "test.com");
		ValidationTestUtils.assertHasError(this.errors, "emailAddress", "PATTERN");
	}

	@Test
	public void testValidate_InvalidAlias_ShouldFail() {
		EmailAddressValidationRule.validate(this.errors, "<EMAIL>");
		ValidationTestUtils.assertHasError(this.errors, "emailAddress", "PATTERN");
	}
}
