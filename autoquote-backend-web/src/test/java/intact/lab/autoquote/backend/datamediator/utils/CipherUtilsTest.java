package intact.lab.autoquote.backend.datamediator.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.plp.crypto.IIngCipher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class CipherUtilsTest {

    private static final String FINANCIAL_INSTITUCTION_NUMBER_DECRYPTED = "256";
    private static final String FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_VORMETRIC = "999";
    private static final String FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_CIPHER = "Tg2Nn7wUZOQ6Xc+1lenkZTQ9ZDf9a2/RBRiqJBCIX6o=";
    private static final String FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT = "*********";
    private static final String ROUTING_NUMBER_DECRYPTED = "56845";
    private static final String ROUTING_NUMBER_CRYPTED_IN_VORMETRIC = "99999";
    private static final String ROUTING_NUMBER_CRYPTED_IN_CIPHER = "Tg8Nn9wUZ5Q3Xc+1lenkZTQ7ZDf0a1/RBRiqJBCIX8o=";
    private static final String ROUTING_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT = "4654";
    private static final String BANKACCOUNT_NUMBER_CRYPTED_IN_CIPHER = "Tg8Nn2wUZOQ4Xc5lenkZTQ1ZDf0a3/RBRiqJBCIX6o=";
    private static final String BANKACCOUNT_NUMBER_DECRYPTED = "5698632";
    private static final String BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MIN_LENGTH = "9999999";
    private static final String BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MAX_LENGTH = "************";
    private static final String BANKACCOUNT_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT_MIN_LENGTH = "123456";
    private static final String BANKACCOUNT_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT_MAX_LENGTH = "*************";
    private static final String CREDIT_CARD_NUMBER_CRYPTED_IN_CIPHER = "Tg8Nn2wUZOQ4Xc5lenkZTQ1ZDf0a3/RBRiqJBCIX6o=";
    private static final String CREDIT_CARD_NUMBER_DECRYPTED = "****************";
    private static final String CREDIT_CARD_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT = "***************";
    private static final String CREDIT_CARD_EXPIRY_DATE_CRYPTED_IN_CIPHER = "Tg8Nn2wUZOHuoi454sdfdfQ4/RBRiqJBCIX6o=";
    private static final String CREDIT_CARD_EXPIRY_DATE_DECRYPTED = "112020";
    private static final String CREDIT_CARD_EXPIRY_DATE_CRYPTED_IN_WRONG_CIPHER_FORMAT = "6";
    private static final String CREDIT_CARD_HOLDER_NAME_CRYPTED_IN_CIPHER = "Tg8Nn2wUZOHu46546548fdQ4/RBRiqJBCIX6o=";
    private static final String CREDIT_CARD_HOLDER_DATE_DECRYPTED = "INTACT ASSURANCE";
    private static final String CREDIT_CARD_HOLDER_DATE_CRYPTED_IN_WRONG_CIPHER_FORMAT = "66934";

    @InjectMocks
    private CipherUtils underTest;

    @Mock
    private IIngCipher ingCipher;

    @Test
    public void getFinancialInstitutionNumber_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(FINANCIAL_INSTITUCTION_NUMBER_DECRYPTED);

        String financialInstutionNumber = underTest.getFinancialInstitutionNumber(FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_CIPHER);

        assertEquals(FINANCIAL_INSTITUCTION_NUMBER_DECRYPTED, financialInstutionNumber);
    }

    @Test
    public void getFinancialInstitutionNumber_should_returnVormetricValue_when_validVormetricValueIsPassed() {
        String financialInstutionNumber = underTest.getFinancialInstitutionNumber(FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_VORMETRIC);

        assertEquals(FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_VORMETRIC, financialInstutionNumber);
    }

    @Test
    public void getFinancialInstitutionNumber_should_throwException_when_wrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getFinancialInstitutionNumber(FINANCIAL_INSTITUCTION_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT)
        );
    }

    @Test
    public void getRoutingNumber_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(ROUTING_NUMBER_DECRYPTED);

        String routingNumberDecrypted = underTest.getRoutingNumber(ROUTING_NUMBER_CRYPTED_IN_CIPHER);

        assertEquals(ROUTING_NUMBER_DECRYPTED, routingNumberDecrypted);
    }

    @Test
    public void getRoutingNumber_should_returnVormetricValue_when_validVormetricValueIsPassed() {
        String routingNumber = underTest.getRoutingNumber(ROUTING_NUMBER_CRYPTED_IN_VORMETRIC);

        assertEquals(ROUTING_NUMBER_CRYPTED_IN_VORMETRIC, routingNumber);
    }

    @Test
    public void getRoutingNumber_should_throwException_when_wrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getRoutingNumber(ROUTING_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT)
        );
    }

    @Test
    public void getBankAccountNumber_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(BANKACCOUNT_NUMBER_DECRYPTED);

        String bankAccountDecrypted = underTest.getBankAccountNumber(BANKACCOUNT_NUMBER_CRYPTED_IN_CIPHER);

        assertEquals(BANKACCOUNT_NUMBER_DECRYPTED, bankAccountDecrypted);
    }

    @Test
    public void getBankAccountNumber_should_returnVormetricValue_when_minValidVormetricValueIsPassed() {
        String bankAccountNumber = underTest.getBankAccountNumber(BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MIN_LENGTH);

        assertEquals(BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MIN_LENGTH, bankAccountNumber);
    }

    @Test
    public void getBankAccountNumber_should_returnVormetricValue_when_maxValidVormetricValueIsPassed() {
        String bankAccountNumber = underTest.getBankAccountNumber(BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MAX_LENGTH);

        assertEquals(BANKACCOUNT_NUMBER_CRYPTED_IN_VORMETRIC_MAX_LENGTH, bankAccountNumber);
    }

    @Test
    public void getBankAccountNumber_should_throwException_when_minWrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getBankAccountNumber(BANKACCOUNT_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT_MIN_LENGTH)
        );
    }

    @Test
    public void getBankAccountNumber_should_throwException_when_maxWrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getBankAccountNumber(BANKACCOUNT_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT_MAX_LENGTH)
        );
    }

    @Test
    public void getCreditCardAccountNumber_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(CREDIT_CARD_NUMBER_DECRYPTED);

        String creditCardAccountNumberDecrypted = underTest.getCreditCardAccountNumber(CREDIT_CARD_NUMBER_CRYPTED_IN_CIPHER);

        assertEquals(CREDIT_CARD_NUMBER_DECRYPTED, creditCardAccountNumberDecrypted);
    }

    @Test
    public void getCreditCardAccountNumber_should_throwException_when_wrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getCreditCardAccountNumber(CREDIT_CARD_NUMBER_CRYPTED_IN_WRONG_CIPHER_FORMAT)
        );
    }

    @Test
    public void getCreditCardExpriryDate_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(CREDIT_CARD_EXPIRY_DATE_DECRYPTED);

        String creditCardExpiryDate = underTest.getCreditCardExpriryDate(CREDIT_CARD_EXPIRY_DATE_CRYPTED_IN_CIPHER);

        assertEquals(CREDIT_CARD_EXPIRY_DATE_DECRYPTED, creditCardExpiryDate);
    }

    @Test
    public void getCreditCardExpriryDate_should_throwException_when_wrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getCreditCardExpriryDate(CREDIT_CARD_EXPIRY_DATE_CRYPTED_IN_WRONG_CIPHER_FORMAT)
        );
    }

    @Test
    public void getCreditCardHolderName_should_returnTheDecryptedValue_when_validCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenReturn(CREDIT_CARD_HOLDER_DATE_DECRYPTED);

        String creditCardExpiryDate = underTest.getCreditCardHolderName(CREDIT_CARD_HOLDER_NAME_CRYPTED_IN_CIPHER);

        assertEquals(CREDIT_CARD_HOLDER_DATE_DECRYPTED, creditCardExpiryDate);
    }

    @Test
    public void getCreditCardHolderName_should_throwException_when_wrongCipherValueIsPassed() throws Exception {
        when(ingCipher.decryptToString(any(String.class))).thenThrow(new Exception());

        assertThrows(SystemException.class, () ->
                underTest.getCreditCardHolderName(CREDIT_CARD_HOLDER_DATE_CRYPTED_IN_WRONG_CIPHER_FORMAT)
        );
    }
}
