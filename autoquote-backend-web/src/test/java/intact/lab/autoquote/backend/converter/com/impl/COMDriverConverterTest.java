package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.driver.ComDriver;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.converter.impl.COMDriverConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class COMDriverConverterTest {

	@InjectMocks
	public COMDriverConverter comDriverConverter;

	@Test
	public void testToDTOWithComDriverWhoIsNotADriver() throws Exception {
		ComDriver comDriver = new ComDriver();
		comDriver.setIsDriver(false);
		DriverDTO driverDTO = this.comDriverConverter.toDTO(comDriver);
		assertNull(driverDTO);
	}

	@Test
	public void testToDTOWithCompleteDriverInfo() throws Exception {
		String licenseNumber = "zzzzzz";
		String minorInfractionCount = "2";
		String majorInfractionCount = "3";
		String principalInsuredSinceCode = "4";
		Boolean interestedInUBI = Boolean.TRUE;

		ComDriver comDriver = mock(ComDriver.class);
		when(comDriver.getIsDriver()).thenReturn(Boolean.TRUE);
		when(comDriver.getLicenseNumber()).thenReturn(licenseNumber);
		when(comDriver.getMinorInfractionCount()).thenReturn(minorInfractionCount);
		when(comDriver.getMajorInfractionCount()).thenReturn(majorInfractionCount);
		when(comDriver.getPrincipalInsuredSinceCode()).thenReturn(principalInsuredSinceCode);
		when(comDriver.getCustomerInterestedByUBI()).thenReturn(interestedInUBI);


		DriverDTO driverDTO = this.comDriverConverter.toDTO(comDriver);
		assertNotNull(driverDTO);
        assertEquals(licenseNumber, driverDTO.getLicenseNbr());
        assertSame(driverDTO.getNumberOfMinorInfractions(), Integer.valueOf(minorInfractionCount));
        assertSame(driverDTO.getNumberOfMajorInfractions(), Integer.valueOf(majorInfractionCount));
        assertSame(driverDTO.getInterestedByUbiInd(), interestedInUBI);
	}

}
