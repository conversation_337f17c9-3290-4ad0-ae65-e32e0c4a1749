package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import intact.lab.autoquote.backend.validation.impl.VehicleDTOValidator;
import intact.lab.autoquote.backend.validation.rule.BusinessKmValidationRule;
import intact.lab.autoquote.backend.validation.rule.MakeValidationRule;
import intact.lab.autoquote.backend.validation.rule.ModelValidationRule;
import intact.lab.autoquote.backend.validation.rule.YearValidationRule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.Calendar;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class VehicleDTOValidatorTest {

	@InjectMocks
	private VehicleDTOValidator vehicleDTOValidator;

	@Mock
	private YearValidationRule yearValidationRule;

	@Mock
	private MakeValidationRule makeValidationRule;

	@Mock
	private ModelValidationRule modelValidationRule;

	@Mock
	private BusinessKmValidationRule businessKmValidationRule;

	private VehicleDTO vehicleDTO;

	private Errors errors;

	private ValidationContext context;

	@BeforeEach
	public void setUp() {

		vehicleDTO = new VehicleDTO();
		vehicleDTO.setYear(2018);
		vehicleDTO.setMake("HONDA");
		vehicleDTO.setModel("ACCORD EX-L 4DR");
		vehicleDTO.setModelCode("021303");
		vehicleDTO.setCommercialUsageCategoryCd("CONTRACT");
		vehicleDTO.setCommercialUsageCd("CONSTR");
		vehicleDTO.setCommercialUsageSpecificCd("");

		errors = new BeanPropertyBindingResult(vehicleDTO, "vehicleDTO");
		context = new ValidationContext("ON", "EN", AutoQuoteConstants.STR_INTACT, null);
	}

	private static void assertHasError(Errors errors, String errorField, String BRuleException, String triggerValue) {
		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("Field %s should trigger an error", errorField));
		assertEquals(BRuleException, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleException));
	}

	@Test
	public void testValidateYear_happyPath() {
		vehicleDTO.setYear(Calendar.YEAR - 1);
		vehicleDTOValidator.validate(vehicleDTO, errors, context);
		assertFalse(errors.hasErrors());
	}

	@Test
	public void testValidateYear_yearIsNull_rejectedBlank() {
		vehicleDTO.setYear(null);
		vehicleDTOValidator.validate(vehicleDTO, errors, context);
		assertHasError(errors, "year", BRulesExceptionEnum.NotBlank.getErrorCode(), "Year is null");
	}
}
