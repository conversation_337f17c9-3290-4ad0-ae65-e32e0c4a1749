/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.partyRoleInRisk.DriverInfoPriorReference;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.GregorianCalendar;
import java.util.List;

/**
 * 
 * Mock class for a SOM Driver. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Driver}
 * 
 * <AUTHOR>
 *
 */
public class MockDriver implements Driver {
	String typeOfDriver;

	Party testParty;

	Integer age;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public GregorianCalendar getDriverCreationDate() {
		return null;
	}

	@Override
	public void setDriverCreationDate(GregorianCalendar newDriverCreationDate) {
		// noop
	}

	@Override
	public String getTypeOfDriver() {
		return this.typeOfDriver;
	}

	@Override
	public void setTypeOfDriver(String newTypeOfDriver) {
		this.typeOfDriver = newTypeOfDriver;
	}

	@Override
	public Integer getAgeSystem() {
		return null;
	}

	@Override
	public void setAgeSystem(Integer newAgeSystem) {
		// noop
	}

	@Override
	public Integer getAgeModified() {
		return null;
	}

	@Override
	public void setAgeModified(Integer newAgeModified) {
		// noop
	}

	@Override
	public Integer getAge() {
		return this.age;
	}

	@Override
	public void setAge(Integer newAge) {
		this.age = newAge;
	}

	@Override
	public String getAgeIncreaseInd() {
		return null;
	}

	@Override
	public void setAgeIncreaseInd(String newAgeIncreaseInd) {
		// noop
	}

	@Override
	public GregorianCalendar getDateDriverHasOwnedVehicle() {
		return null;
	}

	@Override
	public void setDateDriverHasOwnedVehicle(GregorianCalendar newDateDriverHasOwnedVehicle) {
		// noop
	}

	@Override
	public GregorianCalendar getPrincipalDriverSince() {
		return null;
	}

	@Override
	public void setPrincipalDriverSince(GregorianCalendar newPrincipalDriverSince) {
		// noop
	}

	@Override
	public Integer getDriverPercentageUse() {
		return null;
	}

	@Override
	public void setDriverPercentageUse(Integer newDriverPercentageUse) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsAdjusted() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsAdjusted(Integer newNumberOfMinorConvictions3YearsAdjusted) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsAdjusted() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsAdjusted(Integer newNumberOfMajorConvictions3YearsAdjusted) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions3YearsAdjusted() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3YearsAdjusted(Integer newNumberOfSevereConvictions3YearsAdjusted) {
		// noop
	}

	@Override
	public String getResponsibleDriverGuaranteeEligibilityInd() {
		return null;
	}

	@Override
	public void setResponsibleDriverGuaranteeEligibilityInd(String newResponsibleDriverGuaranteeEligibilityInd) {
		// noop
	}

	@Override
	public Integer getMvrsaaqReportOrderScore() {
		return null;
	}

	@Override
	public void setMvrsaaqReportOrderScore(Integer newMvrsaaqReportOrderScore) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingSystem() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingSystem(Integer newNumberOfMonthsDriverLicensedUnderwritingSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingModified() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingModified(Integer newNumberOfMonthsDriverLicensedUnderwritingModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwriting(Integer newNumberOfMonthsDriverLicensedUnderwriting) {
		// noop
	}

	@Override
	public String getDriverAddedByRatingEngineInd() {
		return null;
	}

	@Override
	public void setDriverAddedByRatingEngineInd(String newDriverAddedByRatingEngineInd) {
		// noop
	}

	@Override
	public String getAssignedToAnotherRiskWithRoadCoverageInd() {
		return null;
	}

	@Override
	public void setAssignedToAnotherRiskWithRoadCoverageInd(String newAssignedToAnotherRiskWithRoadCoverageInd) {
		// noop
	}

	@Override
	public Integer getAssignDriverScore() {
		return null;
	}

	@Override
	public void setAssignDriverScore(Integer newAssignDriverScore) {
		// noop
	}

	@Override
	public GregorianCalendar getMaxDriverCreationDateOrPolicyInceptionDate() {
		return null;
	}

	@Override
	public void setMaxDriverCreationDateOrPolicyInceptionDate(GregorianCalendar newMaxDriverCreationDateOrPolicyInceptionDate) {
		// noop
	}

	@Override
	public GregorianCalendar getMaxAdjustedDriverCreationDateOrPolicyInceptionDate() {
		return null;
	}

	@Override
	public void setMaxAdjustedDriverCreationDateOrPolicyInceptionDate(GregorianCalendar newMaxAdjustedDriverCreationDateOrPolicyInceptionDate) {
		// noop
	}

	@Override
	public InsuranceRisk getTheInsuranceRisk() {
		return null;
	}

	@Override
	public void setTheInsuranceRisk(InsuranceRisk newTheInsuranceRisk) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public Party getTheParty() {
		this.testParty = this.testParty == null ? new MockParty() : this.testParty;

		return this.testParty;
	}

	@Override
	public void setTheParty(Party newTheParty) {
		this.testParty = newTheParty;
	}

	@Override
	public Party createTheParty() {
		return null;
	}

	@Override
	public Party createTheParty(Class<? extends Party> theInterface) {
		return null;
	}

	@Override
	public Driver getTheDriverPriorTrans() {
		return null;
	}

	@Override
	public void setTheDriverPriorTrans(Driver newTheDriverPriorTrans) {
		// noop
	}

	@Override
	public Driver createTheDriverPriorTrans() {
		return null;
	}

	@Override
	public Driver createTheDriverPriorTrans(Class<? extends Driver> theInterface) {
		return null;
	}

	@Override
	public DriverInfoPriorReference getTheDriverInfoPriorReference() {
		return null;
	}

	@Override
	public void setTheDriverInfoPriorReference(DriverInfoPriorReference newTheDriverInfoPriorReference) {

	}

	@Override
	public DriverInfoPriorReference createTheDriverInfoPriorReference() {
		return null;
	}

	@Override
	public DriverInfoPriorReference createTheDriverInfoPriorReference(Class<? extends DriverInfoPriorReference> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getHighestVehicleUseInd() {
		return null;
	}

	@Override
	public void setHighestVehicleUseInd(String arg0) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensed() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensed(Integer newNumberOfMonthsDriverLicensed) {
		// noop
	}

	@Override
	public String getAssignedNonRatedDrToAnotherSubRiskWithRoadCoverageInd() {
		return null;
	}

	@Override
	public void setAssignedNonRatedDrToAnotherSubRiskWithRoadCoverageInd(String newAssignedNonRatedDrToAnotherSubRiskWithRoadCoverageInd) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedForGraduated(Integer newNumberOfDaysDriverLicensedForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedForIntl() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedForIntl(Integer newNumberOfDaysDriverLicensedForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedForLearner() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedForLearner(Integer newNumberOfDaysDriverLicensedForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedForProbation() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedForProbation(Integer newNumberOfDaysDriverLicensedForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedForUs() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedForUs(Integer newNumberOfDaysDriverLicensedForUs) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedMcyForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedMcyForGraduated(Integer newNumberOfDaysDriverLicensedMcyForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedMcyForIntl() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedMcyForIntl(Integer newNumberOfDaysDriverLicensedMcyForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedMcyForLearner() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedMcyForLearner(Integer newNumberOfDaysDriverLicensedMcyForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedMcyForProbation() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedMcyForProbation(Integer newNumberOfDaysDriverLicensedMcyForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedMcyForUs() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedMcyForUs(Integer newNumberOfDaysDriverLicensedMcyForUs) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionForGraduated(Integer newNumberOfDaysDriverLicensedSuspensionForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionForIntl() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionForIntl(Integer newNumberOfDaysDriverLicensedSuspensionForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionForLearner() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionForLearner(Integer newNumberOfDaysDriverLicensedSuspensionForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionForProbation() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionForProbation(Integer newNumberOfDaysDriverLicensedSuspensionForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionForUs() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionForUs(Integer newNumberOfDaysDriverLicensedSuspensionForUs) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcy() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcy(Integer newNumberOfDaysDriverLicensedSuspensionMcy) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcyForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcyForGraduated(Integer newNumberOfDaysDriverLicensedSuspensionMcyForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcyForIntl() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcyForIntl(Integer newNumberOfDaysDriverLicensedSuspensionMcyForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcyForLearner() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcyForLearner(Integer newNumberOfDaysDriverLicensedSuspensionMcyForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcyForProbation() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcyForProbation(Integer newNumberOfDaysDriverLicensedSuspensionMcyForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDriverLicensedSuspensionMcyForUs() {
		return null;
	}

	@Override
	public void setNumberOfDaysDriverLicensedSuspensionMcyForUs(Integer newNumberOfDaysDriverLicensedSuspensionMcyForUs) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDrivingExperience() {
		return null;
	}

	@Override
	public void setNumberOfDaysDrivingExperience(Integer newNumberOfDaysDrivingExperience) {
		// noop
	}

	@Override
	public Integer getNumberOfDaysDrivingExperienceMcy() {
		return null;
	}

	@Override
	public void setNumberOfDaysDrivingExperienceMcy(Integer newNumberOfDaysDrivingExperienceMcy) {

	}

	@Override
	public Integer getNumberOfMonthsDrivingExperience() {

		return null;
	}

	@Override
	public void setNumberOfMonthsDrivingExperience(Integer newNumberOfMonthsDrivingExperience) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDrivingExperienceMcy() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDrivingExperienceMcy(Integer newNumberOfMonthsDrivingExperienceMcy) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsDrivingExperience() {
		return null;
	}

	@Override
	public void setNumberOfYearsDrivingExperience(Integer newNumberOfYearsDrivingExperience) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsDrivingExperienceMcy() {
		return null;
	}

	@Override
	public void setNumberOfYearsDrivingExperienceMcy(Integer newNumberOfYearsDrivingExperienceMcy) {
		// noop
	}

	@Override
	public String getSafeDriverInd() {
		return null;
	}

	@Override
	public void setSafeDriverInd(String newSafeDriverInd) {

	}

	@Override
	public String getDrivingRecordLiabilityModified() {
		return null;
	}

	@Override
	public void setDrivingRecordLiabilityModified(String s) {

	}

}
