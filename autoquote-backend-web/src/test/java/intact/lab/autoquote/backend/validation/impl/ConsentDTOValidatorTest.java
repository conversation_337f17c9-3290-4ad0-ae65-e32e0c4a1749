package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.enums.ConsentTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test for the {@link ConsentDTOValidator}
 */
@ExtendWith(MockitoExtension.class)
public class ConsentDTOValidatorTest {

	// Class under test
	private ConsentDTOValidator consentDTOValidator;

	private ConsentDTO consentDTO;

	private Errors errors;

	/**
	 * Private method used to validate the errors rejected by the test class
	 *
	 * @param errors         {@link Errors}
	 * @param errorField     The field on which the error should be
	 * @param BRuleExpection The error code for the expected error
	 * @param triggerValue   The String that was sent to trigger the error
	 */
	private static void assertHasError(Errors errors, String errorField, String BRuleExpection, String triggerValue) {
		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("Field %s should trigger an error", errorField));
		assertEquals(BRuleExpection, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleExpection));
	}

	@BeforeEach
	public void setUp() {
		consentDTOValidator = new ConsentDTOValidator();
		consentDTO = new ConsentDTO();
		consentDTO.setConsentInd(true);
		consentDTO.setConsentType(ConsentTypeEnum.CREDIT_SCORE);
		errors = new BeanPropertyBindingResult(consentDTO, "quoteDTO");
	}

	@AfterEach
	public void tearDown() {
		consentDTOValidator = null;
		consentDTO = null;
		errors = null;
	}

	@Test
	public void testValidateConsentInd_consentIsNull_rejectedConsent() {
		// Given
		consentDTO.setConsentInd(null);

		// When
		consentDTOValidator.validate(consentDTO, errors);

		// Then
		String errorField = "consentInd";
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();

		assertHasError(errors, errorField, bRuleException, "null");
	}

	@Test
	public void testValidateConsentInd_consentIsTrue_valueAccepted() {
		// Given
		consentDTO.setConsentInd(true);

		// When
		consentDTOValidator.validate(consentDTO, errors);

		// Then
		assertFalse(errors.hasErrors(), "ConsentInd true should be accepted");
	}

	@Test
	public void testValidateConsentInd_consentIsFalse_valueAccepted() {
		// Given
		consentDTO.setConsentInd(false);

		// When
		consentDTOValidator.validate(consentDTO, errors);

		// Then
		assertFalse(errors.hasErrors(), "ConsentInd false should be accepted");
	}

	@Test
	public void testValidateConsentType_consentTypeIsNull_rejectedIsBlank() {
		// Given
		consentDTO.setConsentType(null);

		// When
		consentDTOValidator.validate(consentDTO, errors);

		// Then
		String errorField = "consentType";
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();

		assertHasError(errors, errorField, bRuleException, "null");
	}

	@Test
	public void testValidateConsentType_valideConsentType_valueAccepted() {
		List<ConsentTypeEnum> validConsentTypes = Arrays.asList(ConsentTypeEnum.CREDIT_SCORE, ConsentTypeEnum.MARKETING, ConsentTypeEnum.PROFILE);

		for (ConsentTypeEnum consentType : validConsentTypes) {
			// Given
			consentDTO.setConsentType(consentType);

			// When
			consentDTOValidator.validate(consentDTO, errors);

			// Then
			assertFalse(errors.hasErrors(), String.format("ConsentTypeEnum %s should be accepted", consentType));
		}
	}
}
