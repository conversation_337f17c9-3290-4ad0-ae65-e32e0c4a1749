/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2011, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.changebroker.impl;

import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.enums.AssignmentOriginatorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.service.ISubBrokerAssignmentService;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
//@ContextConfiguration
public class ChangeBrokerBusinessProcessTest {

	private ChangeBrokerBusinessProcess changeBrokerBusinessProcess;

	private ISubBrokerAssignmentService mockSubBrokerAssignmentService;

	private ISubBrokersService mockSubBrokersService;

	private ICommonBusinessProcess mockCommonBusinessProcess;

	/**
	 * Sets the up.
	 *
	 * @throws Exception the exception
	 */
	@BeforeEach
	public void setUp() throws Exception {
		this.changeBrokerBusinessProcess = new ChangeBrokerBusinessProcess();
		this.mockSetUp();
	}

	private void mockSetUp() {
		this.mockSubBrokersService = mock(ISubBrokersService.class);
		ReflectionTestUtils.setField(this.changeBrokerBusinessProcess, "subBrokersService", this.mockSubBrokersService,
				ISubBrokersService.class);

		this.mockSubBrokerAssignmentService = mock(ISubBrokerAssignmentService.class);
		ReflectionTestUtils.setField(this.changeBrokerBusinessProcess, "subBrokerAssignmentService",
				this.mockSubBrokerAssignmentService, ISubBrokerAssignmentService.class);

		this.mockCommonBusinessProcess = mock(ICommonBusinessProcess.class);
		ReflectionTestUtils.setField(this.changeBrokerBusinessProcess, "commonBusinessProcess",
				this.mockCommonBusinessProcess, ICommonBusinessProcess.class);
	}

	/**
	 * Same has previous don't need to add a new subBrokerAssignment.
	 */
	@Test
	void ChangeClientBroker_Should_NotAddNewSubBrokerAssignment_WhenBrokerIdsAreTheSame() {

		long policyVersionId = 1;
		long previousCifSubBrokerId = 4;
		long currentCifSubBrokerId = 4;
		SubBrokers subBroker = new SubBrokers();
		subBroker.setSubBrokerId(currentCifSubBrokerId);
		SubBrokerAssignment subBrokerAssignment = new SubBrokerAssignment();
		subBrokerAssignment.setCifSubBrokerId(previousCifSubBrokerId);
		PolicyVersion policyVersion = new PolicyVersion();
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		policyVersion.setInsurancePolicy(insurancePolicy);

		when(this.mockSubBrokersService.getSubBroker("0022", "A")).thenReturn(subBroker);
		when(this.mockSubBrokerAssignmentService.getLastSubBrokerAssignmentForPolicy(policyVersionId)).thenReturn(
				subBrokerAssignment);

		this.changeBrokerBusinessProcess.changeClientBroker(policyVersionId, "0022", "A",
				AssignmentReasonCodeEnum.OTHER);

		assertFalse(insurancePolicy.getSubBrokerAssignments().iterator().hasNext());

		verify(this.mockSubBrokersService).getSubBroker("0022", "A");
		verify(this.mockSubBrokerAssignmentService).getLastSubBrokerAssignmentForPolicy(policyVersionId);

	}

	/**
	 * add a new subBrokerAssignment.
	 */
	@Test
	void ChangeClientBroker_Should_AddNewSubBrokerAssignment_WhenBrokerIdsAreDifferent() {

		long policyVersionId = 1;
		long previousCifSubBrokerId = 4;
		long currentCifSubBrokerId = 5;
		long insurancePolicyId = 7;
		long subBrokerAssignmentId = 9;
		SubBrokers subBroker = new SubBrokers();
		subBroker.setSubBrokerId(currentCifSubBrokerId);
		SubBrokerAssignment subBrokerAssignment = new SubBrokerAssignment();
		subBrokerAssignment.setCifSubBrokerId(previousCifSubBrokerId);
		subBrokerAssignment.setId(subBrokerAssignmentId);
		PolicyVersion policyVersion = new PolicyVersion();
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		insurancePolicy.setId(insurancePolicyId);
		policyVersion.setInsurancePolicy(insurancePolicy);

		when(this.mockSubBrokersService.getSubBroker("0022", "A")).thenReturn(subBroker);
		when(this.mockSubBrokerAssignmentService.getLastSubBrokerAssignmentForPolicy(policyVersionId)).thenReturn(
				subBrokerAssignment);
		when(this.mockCommonBusinessProcess.loadPolicyVersion(policyVersionId)).thenReturn(policyVersion);


		this.changeBrokerBusinessProcess.changeClientBroker(policyVersionId, "0022", "A",
				AssignmentReasonCodeEnum.BROKER_DISSATISFACTION);

		SubBrokerAssignment newSubBrokerAssignment = insurancePolicy.getSubBrokerAssignments().iterator().next();
		assertEquals(AssignmentOriginatorTypeCodeEnum.CLIENT.toString(), newSubBrokerAssignment
				.getAssignmentOriginatorType().toString());
		assertEquals(currentCifSubBrokerId, newSubBrokerAssignment.getCifSubBrokerId());
		assertEquals(insurancePolicyId, newSubBrokerAssignment.getInsurancePolicy().getId());
		assertEquals(AssignmentReasonCodeEnum.BROKER_DISSATISFACTION.toString(), newSubBrokerAssignment
				.getAssignmentReason().toString());
		assertNotNull(newSubBrokerAssignment.getEffectiveDate());
		assertEquals(subBrokerAssignmentId, newSubBrokerAssignment.getPreviousSubBrokerAssignment()
				.getId());

		verify(this.mockSubBrokersService).getSubBroker("0022", "A");
		verify(this.mockSubBrokerAssignmentService).getLastSubBrokerAssignmentForPolicy(policyVersionId);
		verify(this.mockCommonBusinessProcess).loadPolicyVersion(policyVersionId);

	}

}
