package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.driver.ComDriverConviction;
import intact.lab.autoquote.backend.common.dto.ConvictionDTO;
import intact.lab.autoquote.backend.common.enums.ConvictionTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class COMConvictionConverterTest {

	@InjectMocks
	private COMConvictionConverter converter;

	@Test
	public void testToDTO() throws Exception {
		ComDriverConviction comDriverConviction = new ComDriverConviction();
		comDriverConviction.setConvictionSequence((short) 0);
		comDriverConviction.setConvictionCode("OT1");
		comDriverConviction.setConvictionType("MIN");
		comDriverConviction.setConvictionYear("00");

		ConvictionDTO convictionDTO = this.converter.toDTO(comDriverConviction);

		assertEquals(ConvictionTypeEnum.MINOR, convictionDTO.getType());
		assertEquals("00", convictionDTO.getNbYearsOld());
	}

	@Test
	public void testToCOM_Minor() throws Exception {
		ConvictionDTO convictionDTO = new ConvictionDTO();
		convictionDTO.setNbYearsOld("00");
		convictionDTO.setType(ConvictionTypeEnum.MINOR);

		ComDriverConviction comDriverConviction = this.converter.toCOM(convictionDTO, null);
		assertEquals("OT1", comDriverConviction.getConvictionCode());
		assertEquals("MIN", comDriverConviction.getConvictionType());
		assertEquals("00", comDriverConviction.getConvictionYear());
	}

	@Test
	public void testToCOM_Major() throws Exception {
		ConvictionDTO convictionDTO = new ConvictionDTO();
		convictionDTO.setNbYearsOld("00");
		convictionDTO.setType(ConvictionTypeEnum.MAJOR);

		ComDriverConviction comDriverConviction = this.converter.toCOM(convictionDTO, null);
		assertEquals("OT2", comDriverConviction.getConvictionCode());
		assertEquals("MAJ", comDriverConviction.getConvictionType());
		assertEquals("00", comDriverConviction.getConvictionYear());
	}

}
