package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.enums.RoleTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test for {@link PartyRoleDTOValidator}
 */
@ExtendWith(MockitoExtension.class)
public class PartyRoleDTOValidatorTest {

	@InjectMocks
	private PartyRoleDTOValidator partyRoleDTOValidator;

	private PartyRoleDTO partyRoleDTO;

	private Errors errors;

	/**
	 * Private method used to validate the errors rejected by the test class
	 *
	 * @param errors         {@link Errors}
	 * @param errorField     The field on which the error should be
	 * @param BRuleExpection The error code for the expected error
	 * @param triggerValue   The String that was sent to trigger the error
	 */
	private static void assertHasError(Errors errors, String errorField, String BRuleExpection, String triggerValue) {
		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("%s error", errorField));
		assertEquals(BRuleExpection, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleExpection));
	}

	@BeforeEach
	public void setUp() {
		partyRoleDTOValidator = new PartyRoleDTOValidator();
		partyRoleDTO = new PartyRoleDTO();
		errors = new BeanPropertyBindingResult(partyRoleDTO, "partyRoleDTO");
	}

	@AfterEach
	public void tearDown() {
		partyRoleDTOValidator = null;
		partyRoleDTO = null;
		errors = null;
	}

	@Test
	public void testValidateRoleType_roleTypeIsNull_rejectedNotBlank() {
		partyRoleDTO.setRoleType(null);

		partyRoleDTOValidator.validate(partyRoleDTO, errors);

		String errorField = "roleType";
		String BRuleExpection = BRulesExceptionEnum.NotBlank.getErrorCode();

		assertHasError(errors, errorField, BRuleExpection, "null");
	}

	/**
	 * Test for {@link PartyRoleDTOValidator#validateRoleType(RoleTypeEnum, Errors)}
	 * If the {@link PartyRoleDTO#roleType} is not null, it should be accepted
	 */
	@Test
	public void testValidateRoleType_roleTypeIsNotNull_valueAccepted() {
		List<RoleTypeEnum> roleTypes = Arrays.asList(RoleTypeEnum.BUSINESS_OWNER, RoleTypeEnum.OCCASIONAL_DRIVER, RoleTypeEnum.PRINCIPAL_DRIVER, RoleTypeEnum.REGISTERED_OWNER);

		for (RoleTypeEnum roleType : roleTypes) {
			// Given
			partyRoleDTO.setRoleType(roleType);

			// When
			partyRoleDTOValidator.validate(partyRoleDTO, errors);

			// Then
			assertFalse(errors.hasErrors(), String.format("RoleTypeEnum %s should be accepted (because it is not null)", roleType));
		}
	}
}
