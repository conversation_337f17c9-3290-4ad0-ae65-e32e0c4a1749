package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.broker.ComBrokerInfo;
import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class COMDistributorConverterTest {

	@InjectMocks
	private COMDistributorConverter converter;

	@Test
	public void testToDTO() throws Exception {
		ComBrokerInfo comBrokerInfo = new ComBrokerInfo();
		comBrokerInfo.setPhoneNumber("11111111111");
		comBrokerInfo.setSubBrokerNumber("12345");
		byte[] logo = new byte[1];
		logo[0] = 0;
		comBrokerInfo.setLogo(logo);

		DistributorDTO distributorDTO = this.converter.toDTO(comBrokerInfo);

		assertEquals("11111111111", distributorDTO.getPhoneNumber());
		assertEquals("12345", distributorDTO.getNumber());
		assertEquals("AA==", distributorDTO.getLogoBase64());
	}

	@Test
	public void toCOM() {
		assertThrows(UnsupportedOperationException.class, () -> {
			this.converter.toCOM(null, null);
		});
	}

}
