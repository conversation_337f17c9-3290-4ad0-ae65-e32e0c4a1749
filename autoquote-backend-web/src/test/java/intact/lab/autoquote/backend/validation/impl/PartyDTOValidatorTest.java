package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.common.enums.RoleTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IAddressDTOValidator;
import intact.lab.autoquote.backend.validation.IConsentDTOValidator;
import intact.lab.autoquote.backend.validation.IPartyRoleDTOValidator;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class PartyDTOValidatorTest {

	@InjectMocks
	private PartyDTOValidator partyDTOValidator;

	private BindingResult errors;

	private ValidationContext context;

	private ValidationContext contextON;

	@Mock
	private IConsentDTOValidator consentDTOValidator;

	private PartyDTO partyDTO;

	private static IAddressDTOValidator createAddressDTOValidator() {
		return (addressDTO, errors, context) -> {
			// noop
		};
	}

	private static IPartyRoleDTOValidator createPartyRoleValidator() {
		return (partyRoleDTO, errors) -> {
			// noop
		};
	}

	private static PartyRoleDTO setupPartyRole() {
		PartyRoleDTO partyRoleDTO = new PartyRoleDTO();
		partyRoleDTO.setPartyId(0);
		partyRoleDTO.setRiskId(0);
		partyRoleDTO.setRoleType(RoleTypeEnum.REGISTERED_OWNER);
		return partyRoleDTO;
	}

	private static void assertHasError(Errors errors, String errorField, String bRuleException) {
		assertTrue(errors.hasErrors());
		assertNotNull(errors.getFieldError(errorField));
		assertEquals(bRuleException, errors.getAllErrors().getFirst().getCode());
	}

	private static void assertNoErrors(Errors errors, String errorField) {
		assertFalse(errors.hasErrors());
		assertNull(errors.getFieldError(errorField));
	}

	@BeforeEach
	public void setUp() {

		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "quoteDTO");
		this.context = new ValidationContext("QC", "EN", "PkTAzgzKJvHcQQTgh9dSAJRq", null);
		this.contextON = new ValidationContext("ON", "EN", "PkTAzgzKJvHcQQTgh9dSAJRo", null);

		this.setupData();

		ReflectionTestUtils.setField(this.partyDTOValidator, "partyRoleDTOValidator", createPartyRoleValidator());
		ReflectionTestUtils.setField(this.partyDTOValidator, "addressDTOValidator", createAddressDTOValidator());
	}

	private void setupData() {
		this.partyDTO = new PartyDTO();
		this.partyDTO.setId(0);
		this.partyDTO.setPartyType(PartyTypeEnum.COMPANY);
		this.partyDTO.setUnstructuredName("Some Company Name");
		this.partyDTO.setPhoneNumber("************");
		this.partyDTO.setDateOfBirth(new LocalDate(1980, 1, 1));
		this.partyDTO.setFirstName("FirstName");
		this.partyDTO.setLastName("LastName");
		this.partyDTO.setGender("M");
		List<PartyRoleDTO> partyRoleDTOs = new ArrayList<>();
		partyRoleDTOs.add(setupPartyRole());
		this.partyDTO.setPartyRoles(partyRoleDTOs);
	}

	@Test
	public void testNoAddressForCompany() {
		this.partyDTO.setAddress(null);
		this.partyDTO.setPartyType(PartyTypeEnum.COMPANY);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);

		String errorField = "address";
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();
		assertHasError(this.errors, errorField, bRuleException);
	}

	@Test
	public void testNoAddressForPerson() {
		this.partyDTO.setAddress(null);
		this.partyDTO.setPartyType(PartyTypeEnum.PERSON);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);

		String errorField = "address";
		assertNoErrors(this.errors, errorField);
	}

	@Test
	public void testNoAddressForDriver() {
		this.partyDTO.setAddress(null);
		this.partyDTO.setPartyType(PartyTypeEnum.DRIVER);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);

		String errorField = "address";
		assertNoErrors(this.errors, errorField);
	}

	@Test
	public void testNoGenderForPerson() {
		this.partyDTO.setGender(null);
		this.partyDTO.setPartyType(PartyTypeEnum.PERSON);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);

		String errorField = "gender";
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();
		assertHasError(this.errors, errorField, bRuleException);
	}

	@Test
	public void testNoGenderForDriver() {
		this.partyDTO.setGender(null);
		this.partyDTO.setPartyType(PartyTypeEnum.DRIVER);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);

		String errorField = "gender";
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();
		assertHasError(this.errors, errorField, bRuleException);
	}

	@Test
	public void testGenderValuesForPerson() {
		this.partyDTO.setPartyType(PartyTypeEnum.PERSON);
		String errorField = "gender";

		this.partyDTO.setGender("M");
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);
		assertNoErrors(this.errors, errorField);

		this.partyDTO.setGender("F");
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);
		assertNoErrors(this.errors, errorField);
	}

	@Test
	public void testGenderWrongValuesForPerson() {
		this.partyDTO.setPartyType(PartyTypeEnum.PERSON);
		String errorField = "gender";

		this.partyDTO.setGender("MM");
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);
		String bRuleException = BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode();
		assertHasError(this.errors, errorField, bRuleException);

		this.partyDTO.setGender("Z");
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.context);
		bRuleException = BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode();
		assertHasError(this.errors, errorField, bRuleException);
	}

	@Test
	public void testConsentsNotRequired() {
		String bRuleException = BRulesExceptionEnum.NotBlank.getErrorCode();
		this.partyDTO.setConsents(null);
		this.partyDTOValidator.validateConsents(this.partyDTO.getConsents(), this.partyDTO.getPartyType(), this.errors);

		String errorField = "consents";
		assertHasError(this.errors, errorField, bRuleException);
	}

	@Test
	public void testConsentsNotRequiredON() {
		this.partyDTO.setAddress(new AddressDTO());
		this.partyDTO.setConsents(null);
		this.partyDTOValidator.validate(this.partyDTO, this.errors, this.contextON);

		String errorField = "consents";
		assertNoErrors(this.errors, errorField);
	}
}
