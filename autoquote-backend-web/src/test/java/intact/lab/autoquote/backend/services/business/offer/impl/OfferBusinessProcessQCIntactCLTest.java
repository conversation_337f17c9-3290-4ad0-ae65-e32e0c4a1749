/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.exception.BusinessRuleException;
import com.intact.business.rules.offer.BR2497_CreditScoreUnderThresholdOrNoConsent;
import intact.lab.autoquote.backend.services.business.offer.IOfferBusinessProcess;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 
 * <AUTHOR>
 * 
 */
@ExtendWith(MockitoExtension.class)
public class OfferBusinessProcessQCIntactCLTest {
	/** Private class implementation used for tests **/
	private static class OfferBusinessProcessQCImplTest extends OfferBusinessProcessQCIntactCL {
		@Override
		protected IExecuteService getExecuteService() {
			throw new NullPointerException("Implementation unavailable for this context.");
		}
	}

	/** Class to be tested.*/
	@InjectMocks
	private IOfferBusinessProcess offerBusinessProcess = new OfferBusinessProcessQCImplTest();

	/** Test objects **/
	private PolicyVersion currentTestPV;

	@Mock
	protected BR2497_CreditScoreUnderThresholdOrNoConsent br2497;

	@BeforeEach
	public void setUp() throws Exception {
		// Policy Version setup
		this.currentTestPV = new PolicyVersion(123L);
	}

	@Test
	public void testValidatePostBR_RaiseBR255() throws BusinessRuleException {

		// Executing the tested method and verifying the exception is thrown
		assertThrows(BusinessRuleException.class, () -> this.offerBusinessProcess.validatePostBR(this.currentTestPV));
	}
}
