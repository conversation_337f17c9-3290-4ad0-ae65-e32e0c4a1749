package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

@ExtendWith(MockitoExtension.class)
public class LicenseObtentionDateValidationRuleTest {

	@InjectMocks
	private LicenseObtentionDateValidationRule validationRule;

	private Errors errors;

	private DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd");

	private PartyDTO testParty;

	public LicenseObtentionDateValidationRuleTest() {
		this.testParty = new PartyDTO();
		this.testParty.setDateOfBirth(LocalDate.parse("1980-01-01"));
	}

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new DriverDTO(), "driver");
	}

	@Test
	public void testValidate_Valid_ShouldPass() {
		this.validationRule.validate(LocalDate.parse("2000-01-01", this.fmt), this.testParty, "QC", "EN", this.errors, "licenseObtentionDate");
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Null_ShouldFail() {
		this.validationRule.validate(null, this.testParty, "QC", "EN", this.errors, "licenseObtentionDate");
		ValidationTestUtils.assertHasError(this.errors, "licenseObtentionDate", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_Future_ShouldFail() {
		this.validationRule.validate(LocalDate.now().plusDays(1), this.testParty, "QC", "EN", this.errors, "licenseObtentionDate");
		ValidationTestUtils.assertHasError(this.errors, "licenseObtentionDate", BRulesExceptionEnum.Pattern.getErrorCode());
	}

	@Test
	public void testValidate_LessThanMinimumAge_ShouldFail() {
		this.validationRule.validate(LocalDate.parse("1995-12-12"), this.testParty, "QC", "EN", this.errors, "licenseObtentionDate");
		ValidationTestUtils.assertHasError(this.errors, "licenseObtentionDate", BRulesExceptionEnum.Pattern.getErrorCode());
	}
}
