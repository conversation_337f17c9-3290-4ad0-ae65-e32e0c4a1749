package intact.lab.autoquote.backend.common.utils;

import com.intact.com.context.ComContext;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ContextUtilTest {

    @Test
    void LoadInitialComContext_Should_ReturnComContextForOntario() {

        ComContext context = ContextUtil.loadInitialComContext(null, "ON", null,
                null, null, null);

        assertEquals(ComCompanyEnum.INTACT, context.getCompany());
        assertEquals(ComProvinceCodeEnum.ONTARIO, context.getProvince());
        assertEquals(ComDistributionChannelCodeEnum.THROUGH_BROKERS, context.getDistributionChannel());
    }

    @Test
    void LoadInitialComContext_Should_ReturnComContextForAlberta() {

        ComContext context = ContextUtil.loadInitialComContext(null, "AB", null,
                null, null, null);

        assertEquals(ComCompanyEnum.INTACT, context.getCompany());
        assertEquals(ComProvinceCodeEnum.ALBERTA, context.getProvince());
        assertEquals(ComDistributionChannelCodeEnum.THROUGH_BROKERS, context.getDistributionChannel());
    }

    @Test
    void LoadInitialComContext_Should_ReturnComContextForQuebec() {

        ComContext context = ContextUtil.loadInitialComContext(null, "QC", null,
                null, null, null);

        assertEquals(ComCompanyEnum.INTACT, context.getCompany());
        assertEquals(ComProvinceCodeEnum.QUEBEC, context.getProvince());
        assertEquals(ComDistributionChannelCodeEnum.THROUGH_BROKERS, context.getDistributionChannel());
    }
}
