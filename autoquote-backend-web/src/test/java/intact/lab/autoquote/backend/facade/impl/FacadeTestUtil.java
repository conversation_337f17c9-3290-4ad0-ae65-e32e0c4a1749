package intact.lab.autoquote.backend.facade.impl;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockitoAnnotations;

import com.ing.canada.plp.domain.enums.ClaimConsideredCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimLossDateCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimStatusCodeEnum;
import com.ing.canada.plp.domain.enums.NatureOfClaimCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;

public abstract class FacadeTestUtil {

    protected ComContext context_IN_QC_EN;
    protected ComContext context_IN_QC_FR;
    protected ComContext context_IN_ON_EN;
    protected ComContext context_IN_ON_FR;
    protected ComContext context_IN_AB_EN;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        this.context_IN_QC_EN = newContext_IN_QC_EN();
        this.context_IN_QC_FR = newContext_IN_QC_FR();
        this.context_IN_ON_EN = newContext_IN_ON_EN();
        this.context_IN_ON_FR = newContext_IN_ON_FR();
        this.context_IN_AB_EN = newContext_IN_AB_EN();
    }

    @AfterEach
    void tearDown() {
        this.context_IN_QC_EN = null;
        this.context_IN_QC_FR = null;
        this.context_IN_ON_EN = null;
        this.context_IN_ON_FR = null;
        this.context_IN_AB_EN = null;
    }

    protected ComContext newContext_IN_QC_EN() {
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setCompany(ComCompanyEnum.INTACT);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);
        context.setProvince(ComProvinceCodeEnum.QUEBEC);
        context.setTestDataInd(true);
        return context;
    }

    protected ComContext newContext_IN_QC_FR() {
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setCompany(ComCompanyEnum.INTACT);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.FRENCH);
        context.setProvince(ComProvinceCodeEnum.QUEBEC);
        context.setTestDataInd(true);
        return context;
    }

    protected ComContext newContext_IN_ON_EN() {
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setCompany(ComCompanyEnum.INTACT);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);
        context.setProvince(ComProvinceCodeEnum.ONTARIO);
        context.setTestDataInd(true);
        return context;
    }

    protected ComContext newContext_IN_ON_FR() {
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setCompany(ComCompanyEnum.INTACT);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.FRENCH);
        context.setProvince(ComProvinceCodeEnum.ONTARIO);
        context.setTestDataInd(true);
        return context;
    }

    protected ComContext newContext_IN_AB_EN() {
        ComContext context = new ComContext();
        context.setApplication(ComApplicationEnum.AUTOQUOTE);
        context.setCompany(ComCompanyEnum.INTACT);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);
        context.setProvince(ComProvinceCodeEnum.ALBERTA);
        context.setTestDataInd(true);
        return context;
    }

    protected void setAsPolicyHolder(Party aParty) {
        PolicyHolder policyHolder = new PolicyHolder();
        aParty.getPolicyVersion().addPolicyHolder(policyHolder);
        policyHolder.setParty(aParty);
        policyHolder.setPolicyHolderType(PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED);
    }

    protected Claim newClaim(NatureOfClaimCodeEnum natureOfClaim, ClaimLossDateCodeEnum claimYear, Short sequence) {
        Claim aClaim = new Claim();
        aClaim.setNatureOfClaim(natureOfClaim);
        aClaim.setClaimLossDateCode(claimYear);
        aClaim.setClaimOrdering(sequence);
        aClaim.setClaimStatus(ClaimStatusCodeEnum.CLOSED);
        aClaim.setClaimConsideredCode(ClaimConsideredCodeEnum.CLAIM_CONSIDERED);
        return aClaim;
    }
}
