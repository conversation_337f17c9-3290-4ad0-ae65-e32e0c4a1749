package intact.lab.autoquote.backend.validation.rule;

import com.intact.com.driver.ComDriver;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.services.impl.AutoQuoteServiceCache;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DriverLicenseTypeValidationRuleTest {

	@InjectMocks
	private DriverLicenseTypeValidationRule validationRule;

	@Mock
	private AutoQuoteServiceCache autoQuoteServiceCache;

	private Errors errors;

	@Test
	public void testValidate_Valid_ShouldPass() {
		this.errors = new BeanPropertyBindingResult(new Object(), "driver");
		List<ValidValueBO> validValueList = new ArrayList<>();
		validValueList.add(new ValidValueBO("G", "license"));

		when(this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(anyString(), anyString(), anyString()))
				.thenReturn(validValueList);

		this.validationRule.validate("G", "QC", "EN", this.errors, "driverLicenseType");
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Empty_ShouldFail() {

		ComDriver driver = mock(ComDriver.class);
		when(driver.getDriverLicenseType()).thenReturn("G");
		this.errors = new BeanPropertyBindingResult(driver, "driver");

		List<ValidValueBO> validValueList = new ArrayList<>();
		validValueList.add(new ValidValueBO("G", "license"));

		when(this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(anyString(), anyString(), anyString()))
				.thenReturn(validValueList);

		this.validationRule.validate("", "QC", "EN", this.errors, "driverLicenseType");
		ValidationTestUtils.assertHasError(this.errors, "driverLicenseType", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_Invalid_ShouldFail() {

		ComDriver driver = mock(ComDriver.class);
		when(driver.getDriverLicenseType()).thenReturn("G");
		this.errors = new BeanPropertyBindingResult(driver, "driver");

		List<ValidValueBO> validValueList = new ArrayList<>();
		validValueList.add(new ValidValueBO("R", "license"));

		when(this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(anyString(), anyString(), anyString()))
				.thenReturn(validValueList);

		this.validationRule.validate("G", "QC", "EN", this.errors, "driverLicenseType");
		ValidationTestUtils.assertHasError(this.errors, "driverLicenseType", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode());
	}
}
