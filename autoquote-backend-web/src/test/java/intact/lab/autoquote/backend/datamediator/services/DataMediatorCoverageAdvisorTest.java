package intact.lab.autoquote.backend.datamediator.services;

import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.service.ICoverageService;
import com.ing.canada.som.impl.agreement.PolicyVersionImpl;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.product.CoverageProduct;
import com.ing.canada.som.interfaces.product.CoverageRepositoryEntry;
import com.ing.canada.som.interfaces.risk.CoverageOption;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.rootclasses.GenericRootObject;
import com.ing.canada.som.sdo.risk.CoverageOptionBO;
import com.ing.canada.sombase.IGenericRootObject;
import com.ing.canada.sombase.ModelFactory;
import commonj.sdo.ChangeSummary;
import commonj.sdo.DataGraph;
import commonj.sdo.DataObject;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorCoverageAdvisor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DataMediatorCoverageAdvisorTest {

    @InjectMocks
    private DataMediatorCoverageAdvisor dataMediatorCoverageAdvisor;

    @Mock
    private ICoverageService coverageService;

    @Mock
    private IBaseEntityDAO baseEntityDAO;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(dataMediatorCoverageAdvisor, "coverageService", coverageService);
        ReflectionTestUtils.setField(dataMediatorCoverageAdvisor, "baseEntityDAO", baseEntityDAO);
    }

    @Test
    public void testWithPolicyVersionNull() {
        assertDoesNotThrow(() -> dataMediatorCoverageAdvisor.processCoverationOptionAddition(null, null));
    }

    @Test
    public void testWithPlInsurancePolicyNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();
        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        CoverageOption coverageOption = insuranceRisk.addTheCoverageOption();

        CoverageProduct coverageProduct = coverageOption.createTheCoverageProduct();
        CoverageRepositoryEntry coverageRepositoryEntry = coverageProduct.createTheCoverageRepositoryEntryBase();
        coverageRepositoryEntry.setCoverageCode("A1");

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream()
                .filter(dataObject -> dataObject instanceof CoverageOptionBO)
                .findFirst()
                .orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();

        assertDoesNotThrow(() -> dataMediatorCoverageAdvisor.processCoverationOptionAddition(plPolicyVersion, changedDataObject));
    }

    @Test
    public void testWithPlManufacturingContextNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();
        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        CoverageOption coverageOption = insuranceRisk.addTheCoverageOption();

        CoverageProduct coverageProduct = coverageOption.createTheCoverageProduct();
        CoverageRepositoryEntry coverageRepositoryEntry = coverageProduct.createTheCoverageRepositoryEntryBase();
        coverageRepositoryEntry.setCoverageCode("A1");

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream()
                .filter(dataObject -> dataObject instanceof CoverageOptionBO)
                .findFirst()
                .orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        InsurancePolicy insurancePolicy = new InsurancePolicy();
        plPolicyVersion.setInsurancePolicy(insurancePolicy);

        assertDoesNotThrow(() -> dataMediatorCoverageAdvisor.processCoverationOptionAddition(plPolicyVersion, changedDataObject));
    }

    @Test
    public void testWithPlCoverageRepositoryEntryNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();
        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        CoverageOption coverageOption = insuranceRisk.addTheCoverageOption();

        CoverageProduct coverageProduct = coverageOption.createTheCoverageProduct();
        CoverageRepositoryEntry coverageRepositoryEntry = coverageProduct.createTheCoverageRepositoryEntryBase();
        coverageRepositoryEntry.setCoverageCode("A1");

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream()
                .filter(dataObject -> dataObject instanceof CoverageOptionBO)
                .findFirst()
                .orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        InsurancePolicy insurancePolicy = new InsurancePolicy();
        plPolicyVersion.setInsurancePolicy(insurancePolicy);
        ManufacturingContext context = new ManufacturingContext(
                DistributionChannelCodeEnum.DIRECT_SELLER,
                InsuranceBusinessCodeEnum.REGULAR,
                ProvinceCodeEnum.QUEBEC,
                ManufacturerCompanyCodeEnum.BELAIRDIRECT);
        insurancePolicy.setManufacturingContext(context);

        when(coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(
                eq("A1"), eq(ProvinceCodeEnum.QUEBEC), any(), isNull()))
                .thenReturn(null);

        assertDoesNotThrow(() -> dataMediatorCoverageAdvisor.processCoverationOptionAddition(plPolicyVersion, changedDataObject));
    }

    @Test
    public void testWithInsuranceRiskPersistenceIdNull() {
        GenericRootObject groList = new GenericRootObject(PolicyVersion.class);
        groList.beginLogging();
        PolicyVersionImpl somPolicyVersion = (PolicyVersionImpl) groList.createTheRootObject();
        InsuranceRisk insuranceRisk = somPolicyVersion.addTheInsuranceRisk();
        CoverageOption coverageOption = insuranceRisk.addTheCoverageOption();

        CoverageProduct coverageProduct = coverageOption.createTheCoverageProduct();
        CoverageRepositoryEntry coverageRepositoryEntry = coverageProduct.createTheCoverageRepositoryEntryBase();
        coverageRepositoryEntry.setCoverageCode("A1");

        IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
        gro.endLogging();
        DataGraph dataGraph = gro.getDataGraph();
        ChangeSummary cs = dataGraph.getChangeSummary();

        List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
        DataObject changedDataObject = changedDataObjectsList.stream()
                .filter(dataObject -> dataObject instanceof CoverageOptionBO)
                .findFirst()
                .orElse(null);

        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = new com.ing.canada.plp.domain.policyversion.PolicyVersion();
        InsurancePolicy insurancePolicy = new InsurancePolicy();
        ManufacturingContext context = new ManufacturingContext(
                DistributionChannelCodeEnum.DIRECT_SELLER,
                InsuranceBusinessCodeEnum.REGULAR,
                ProvinceCodeEnum.QUEBEC,
                ManufacturerCompanyCodeEnum.BELAIRDIRECT);
        insurancePolicy.setManufacturingContext(context);
        insurancePolicy.setManufacturerCompany(ManufacturerCompanyCodeEnum.BELAIRDIRECT);
        plPolicyVersion.setInsurancePolicy(insurancePolicy);
        plPolicyVersion.setRatingDate(new Date());

        when(coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(
                "A1", ProvinceCodeEnum.QUEBEC, ManufacturerCompanyCodeEnum.BELAIRDIRECT, plPolicyVersion.getRatingDate()))
                .thenReturn(new com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry());

        assertDoesNotThrow(() -> dataMediatorCoverageAdvisor.processCoverationOptionAddition(plPolicyVersion, changedDataObject));
    }
}
