/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.plp.dao.insuranceriskoffer.IInsuranceRiskOfferDAO;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.CoverageGroupCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.NumberStabilityMonthsCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IIPRestrictionService;
import com.ing.canada.plp.service.IPolicyOfferRatingService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.ss.base.BaseException;
import com.intact.business.rules.offer.BR1760;
import com.intact.business.rules.offer.BR1857_ValidateCoverageLeasedVehicle;
import com.intact.business.rules.offer.BR226_CustomOfferRecalculateCondition;
import com.intact.business.rules.offer.BR364_CollisionComprehensive;
import com.intact.canada.common.segmentation.api.ISegmentationBusinessProcess;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOM;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.common.IBusinessTransactionLoggingService;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.offer.IOfferBusinessProcess;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OfferBusinessProcessTest {

	/**
	 * Private class implementation used for tests
	 **/
	private static class OfferBusinessProcessImplTest extends OfferBusinessProcess {
		private IExecuteService executeService;
		private IRateManagerService rateManagerService;
		private IRatingService ratingService;

		// Method used to set the execute service that will be returned by the overriden getter
		protected void setExecuteService(IExecuteService newExecuteService) {
			this.executeService = newExecuteService;
		}

		@Override
		protected IExecuteService getExecuteService() {
			return this.executeService;
		}


		@Override
		public IRateManagerService getRateManagerService() {
			return this.rateManagerService;
		}

		@Override
		public IRatingService getRatingService() {
			return this.ratingService;
		}

		// Method to allow access to the protected parent method
		@Override
		public Exception getRealException(BaseException e) {
			return super.getRealException(e);
		}
	}

	/**
	 * Class to be tested.
	 */
	@InjectMocks
	private IOfferBusinessProcess offerBusinessProcess = new OfferBusinessProcessImplTest();

	/**
	 * Test objects
	 **/
	private ManufacturingContext manufacturingContext;

	private InsuranceRisk currentTestRisk;

	private Locale locale;

	private PolicyHolder currentTestHolder;

	private PolicyVersion currentTestPV;

	/**
	 * Mocks
	 **/
	@Mock
	private IConfigurator mockAppConfig;

	@Mock
	private BR226_CustomOfferRecalculateCondition mockBR226;

	@Mock
	private BR364_CollisionComprehensive mockBR364;

	@Mock
	private BR1760 mockBR1760;

	@Mock
	private BR1857_ValidateCoverageLeasedVehicle mockBR1857;

	@Mock
	private IBusinessTransactionLoggingService mockBussTransLoggingService;

	@Mock
	private ICoverageHelper mockCoverageHelper;

	@Mock
	private IDataMediatorToPL mockDataMediatorToPL;

	@Mock
	private DataMediatorToSOMServiceFactory mockDataMediatorToSOMFactory;

	@Mock(name = "dateManagerService")
	private IDateManagerService dateManagerService;

	@Mock(name = "capiPolicyChangeDateService")
	private IDateManagerService capiPolicyChangeDateService;

	@Mock
	private IDriverBusinessProcess mockDriverBusinessProcess;

	@Mock
	private IInsuranceRiskOfferHelper mockInsuranceRiskOfferHelper;

	@Mock
	private IInsuranceRiskOfferDAO mockOfferDAO;

	@Mock
	private IIPRestrictionService mockIPRestrictionService;

	@Mock
	private IPolicyOfferRatingService mockPolicyOfferRatingService;

	@Mock
	private IPolicyVersionHelper mockPolicyVersionHelper;

	@Mock
	private IPolicyVersionService mockPolicyVersionService;

	@Mock
	private IRateManagerService mockRateManagerService;

	@Mock
	private IRatingService mockRatingService;

	@Mock
	private ISegmentationBusinessProcess mockSegmentationBusinessProcess;

	// Execute service mock shouldn't be injected
	private IExecuteService mockExecuteService;

	/**
	 * Sets up the test fixture.
	 */
	@BeforeEach
	public void setUp() throws Exception {
		this.locale = new Locale("fr", "CA", "QC");

		// Context setup
		this.manufacturingContext = new ManufacturingContext();
		this.manufacturingContext.setProvince(ProvinceCodeEnum.QUEBEC);
		this.manufacturingContext.setManufacturerCompany(ManufacturerCompanyCodeEnum.BELAIRDIRECT);
		this.manufacturingContext.setInsuranceBusiness(InsuranceBusinessCodeEnum.REGULAR);
		this.manufacturingContext.setDistributionChannel(DistributionChannelCodeEnum.DIRECT_SELLER);

		// Policy Version setup
		this.currentTestPV = new PolicyVersion(123L);

		InsurancePolicy testInsurancePolicy = new InsurancePolicy();
		testInsurancePolicy.setManufacturingContext(this.manufacturingContext);
		testInsurancePolicy.setManufacturerCompany(ManufacturerCompanyCodeEnum.BELAIRDIRECT);
		this.currentTestPV.setInsurancePolicy(testInsurancePolicy);

		// Insurance Risk setup
		this.currentTestRisk = new InsuranceRisk();
		this.currentTestPV.addInsuranceRisk(this.currentTestRisk);

		// Other mocks setup
		this.mockExecuteService = mock(IExecuteService.class);
		((OfferBusinessProcessImplTest) this.offerBusinessProcess).setExecuteService(this.mockExecuteService);
	}

	@Test
	public void testRatePolicy() throws AutoquoteRatingException, AutoQuoteRoadBlockException, AutoquoteBusinessException {
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentTestPV.addPolicyOfferRating(testOfferRating);

		// Stubs setup
		when(this.mockRateManagerService.rateTheWholeQuotation(any(PolicyVersion.class), any(Boolean.class), any(Boolean.class)))
				.thenReturn(this.currentTestPV);

		// Executing the tested method and validating the results
		PolicyOfferRating resultRating = this.offerBusinessProcess.ratePolicy(this.currentTestPV, true, true);
		assertEquals(testOfferRating, resultRating);
		verify(this.mockRateManagerService, times(1)).rateTheWholeQuotation(any(PolicyVersion.class), any(Boolean.class), any(Boolean.class));
	}

	@Test
	public void testRatePolicy_RaiseAutoquoteRatingException() throws AutoquoteRatingException, AutoQuoteRoadBlockException, AutoquoteBusinessException {
		// Setting the exception to be thrown
		AutoquoteRatingException testException = new AutoquoteRatingException("Exception thrown for the test \"testRatePolicy_RaiseAutoquoteRatingException\"");
		when(this.mockRateManagerService.rateTheWholeQuotation(any(PolicyVersion.class), any(Boolean.class), any(Boolean.class)))
				.thenThrow(testException);

		// Executing the tested method and verifying the exception is thrown
		assertThrows(AutoquoteBusinessException.class, () -> {
			this.offerBusinessProcess.ratePolicy(this.currentTestPV, true, true);
		});
	}

	/**
	 * Test method for
	 * {@link OfferBusinessProcess#selectOffer(PolicyVersion, Boolean)}
	 *
	 * @throws Exception
	 */
	@Test
	public void testSelectOffer() throws Exception {
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentTestPV.addPolicyOfferRating(testOfferRating);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.selectOffer(this.currentTestPV, null);
		verify(this.mockPolicyOfferRatingService, times(1)).persist(any(PolicyOfferRating.class));
		verify(this.mockPolicyVersionService, times(1)).persist(any(PolicyVersion.class));
		verify(this.mockAppConfig, times(1)).isUbi(anyString(), any());
	}

	@Test
	public void testSetQuoteAnnualPremium_PolicyTermIn6Months() throws Exception {
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentTestPV.addPolicyOfferRating(testOfferRating);

		// Adding a custom offer with an annual premium to the policy version
		Integer annualPremium = 42;
		InsuranceRiskOffer testCustomOffer = new InsuranceRiskOffer();
		testCustomOffer.setAnnualPremium(annualPremium);
		this.currentTestRisk.addInsuranceRiskOffer(testCustomOffer);
		this.currentTestRisk.setSelectedInsuranceRiskOffer(testCustomOffer);

		// Calculating the expected results
		double yearMultiplicator = 0.5; // Determined by the months to policy term
		double untaxablePremium = 10.0;
		Integer fullTermPremium = (int) (annualPremium * yearMultiplicator);
		Integer fullTermPremiumTaxable = (int) ((annualPremium - untaxablePremium) * yearMultiplicator);

		// Setting the policy term to be in 6 months
		this.currentTestPV.setPolicyTermInMonths(PolicyTermInMonthsEnum.SIX_MONTHS);

		// Adding an untaxable amount to avoid errors
		when(this.mockRatingService.getUntaxableAmount(any(PolicyOfferRating.class))).thenReturn(untaxablePremium);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.selectOffer(this.currentTestPV, null);
		assertEquals(annualPremium, testOfferRating.getAnnualPremium());
		assertEquals(fullTermPremium, testOfferRating.getFullTermPremium());
		assertEquals(fullTermPremiumTaxable, testOfferRating.getFullTermPremiumTaxable());
	}

	@Test
	public void testSetQuoteAnnualPremium_PolicyTermIn12Months() throws Exception {
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentTestPV.addPolicyOfferRating(testOfferRating);

		// Adding a custom offer with an annual premium to the policy version
		Integer annualPremium = 42;
		InsuranceRiskOffer testCustomOffer = new InsuranceRiskOffer();
		testCustomOffer.setAnnualPremium(annualPremium);
		this.currentTestRisk.addInsuranceRiskOffer(testCustomOffer);
		this.currentTestRisk.setSelectedInsuranceRiskOffer(testCustomOffer);

		// Calculating the expected results
		double yearMultiplicator = 1; // Determined by the months to policy term
		double untaxablePremium = 10.0;
		Integer fullTermPremium = (int) (annualPremium * yearMultiplicator);
		Integer fullTermPremiumTaxable = (int) ((annualPremium - untaxablePremium) * yearMultiplicator);

		// Setting the policy term to be in 12 months
		this.currentTestPV.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);

		// Adding an untaxable amount to avoid errors
		when(this.mockRatingService.getUntaxableAmount(any(PolicyOfferRating.class))).thenReturn(untaxablePremium);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.selectOffer(this.currentTestPV, null);
		assertEquals(annualPremium, testOfferRating.getAnnualPremium());
		assertEquals(fullTermPremium, testOfferRating.getFullTermPremium());
		assertEquals(fullTermPremiumTaxable, testOfferRating.getFullTermPremiumTaxable());
	}

	@Test
	public void testSetQuoteAnnualPremium_PolicyTermIn24Months() throws Exception {
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentTestPV.addPolicyOfferRating(testOfferRating);

		// Adding a custom offer with an annual premium to the policy version
		Integer annualPremium = 42;
		InsuranceRiskOffer testCustomOffer = new InsuranceRiskOffer();
		testCustomOffer.setAnnualPremium(annualPremium);
		this.currentTestRisk.addInsuranceRiskOffer(testCustomOffer);
		this.currentTestRisk.setSelectedInsuranceRiskOffer(testCustomOffer);

		// Calculating the expected results
		double yearMultiplicator = 2; // Determined by the months to policy term
		double untaxablePremium = 10.0;
		Integer fullTermPremium = (int) (annualPremium * yearMultiplicator);
		Integer fullTermPremiumTaxable = (int) ((annualPremium - untaxablePremium) * yearMultiplicator);

		// Setting the policy term to be in 24 months
		this.currentTestPV.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWENTYFOUR_MONTHS);

		// Adding an untaxable amount to avoid errors
		when(this.mockRatingService.getUntaxableAmount(any(PolicyOfferRating.class))).thenReturn(untaxablePremium);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.selectOffer(this.currentTestPV, null);
		assertEquals(annualPremium, testOfferRating.getAnnualPremium());
		assertEquals(fullTermPremium, testOfferRating.getFullTermPremium());
		assertEquals(fullTermPremiumTaxable, testOfferRating.getFullTermPremiumTaxable());
	}

	/**
	 * Test method for
	 * {@link OfferBusinessProcess#determineQuoteInfos(PolicyVersion)}
	 * Case for the policy holder to have less than 6 stability months.
	 *
	 * @throws AutoquoteBusinessException
	 */
	@Test
	public void testDetermineQuoteInfos_LessThan6StabilityMonths() throws AutoquoteBusinessException {
		/*
		 * Special case : for some reason, the number of stability months needed in the enum is 6, but
		 * the number substracted is 5.
		 */
		// General setup for the test
		GregorianCalendar expectedDateOfLastMove = this.setupGeneralDetermineQuoteInfosStabilityMonthsTest(6, 5);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.determineQuoteInfos(this.currentTestPV);
		assertEquals(NumberStabilityMonthsCodeEnum.LESS_THAN_SIX, this.currentTestHolder.getNumberStabilityMonthsCode());
		assertEquals(expectedDateOfLastMove.getTime(), this.currentTestHolder.getParty().getDateOfLastMove());
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for
	 * {@link OfferBusinessProcess#determineQuoteInfos(PolicyVersion)}
	 * Case for the policy holder to have between 6 and 24 stability months (9 for enum).
	 *
	 * @throws AutoquoteBusinessException
	 */
	@Test
	public void testDetermineQuoteInfos_Between6And24StabilityMonths() throws AutoquoteBusinessException {
		// General setup for the test
		GregorianCalendar expectedDateOfLastMove = this.setupGeneralDetermineQuoteInfosStabilityMonthsTest(9, 9);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.determineQuoteInfos(this.currentTestPV);
		assertEquals(NumberStabilityMonthsCodeEnum.BETWEEN_SIX_AND_TWENTYFOUR, this.currentTestHolder.getNumberStabilityMonthsCode());
		assertEquals(expectedDateOfLastMove.getTime(), this.currentTestHolder.getParty().getDateOfLastMove());
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for
	 * {@link OfferBusinessProcess#determineQuoteInfos(PolicyVersion)}
	 * Case for the policy holder to have 24 or more stability months (54 for enum).
	 *
	 * @throws AutoquoteBusinessException
	 */
	@Test
	public void testDetermineQuoteInfos_24OrMoreStabilityMonths() throws AutoquoteBusinessException {
		// General setup for the test
		GregorianCalendar expectedDateOfLastMove = this.setupGeneralDetermineQuoteInfosStabilityMonthsTest(54, 54);

		// Executing the tested method and validating the results
		this.offerBusinessProcess.determineQuoteInfos(this.currentTestPV);
		assertEquals(NumberStabilityMonthsCodeEnum.TWENTYFOUR_OR_MORE, this.currentTestHolder.getNumberStabilityMonthsCode());
		assertEquals(expectedDateOfLastMove.getTime(), this.currentTestHolder.getParty().getDateOfLastMove());
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	@Test
	public void testDetermineQuoteInfos_RaiseBaseException() throws AutoquoteBusinessException, BaseException {
		// Setup the exception to be thrown
		BaseException testException = new BaseException("Exception thrown for test \"testDetermineQuoteInfos_RaiseBaseException\"");
		when(this.mockExecuteService.determineQuoteInfos(any(), any())).thenThrow(testException);

		// Other stubs setup to avoid other exceptions
		when(this.mockDataMediatorToSOMFactory.getService(anyString())).thenReturn(mock(IDataMediatorToSOM.class));

		// Executing the tested method and verifying the exception is thrown
		assertThrows(AutoquoteBusinessException.class, () -> {
			this.offerBusinessProcess.determineQuoteInfos(this.currentTestPV);
		});
	}

	/**
	 * Test method for
	 * {@link OfferBusinessProcess#determineQuoteInfos(PolicyVersion)}
	 *
	 * @throws AutoquoteBusinessException
	 */
	@Test
	public void testGetRealException() throws Exception {
		// Setting the exceptions for the test
		BaseException testException = new BaseException();
		RuntimeException expectedException = new RuntimeException("Test exception for \"testGetRealException\"");
		testException.setRealException(expectedException);

		// Executing the tested method and verifying the results
		Exception resultException = ((OfferBusinessProcessImplTest) this.offerBusinessProcess).getRealException(testException);
		assertEquals(expectedException, resultException);
	}

	/**
	 * Private method used to do the general setup for tests for methods that verify eligibility
	 * (except for liability eligibility).
	 * Note that this method is deprecated since the methods tested are currently not used (tests are for coverage).
	 *
	 * @param autocomfortSelected     Boolean for whether or not the autocomfort coverage is selected
	 * @param collisionDeductible     Deductible amount for the collision coverage
	 * @param comprehensiveDeductible Deductible amount for the comprehensive coverage
	 * @return The insurance risk offer created for the test.
	 */
	@Deprecated
	private InsuranceRiskOffer setupVerifyEligibilityTest(boolean autocomfortSelected, Integer collisionDeductible,
														  Integer comprehensiveDeductible) {
		// Setup for the insurance risk to be passed as parameter
		InsuranceRiskOffer testRiskOffer = new InsuranceRiskOffer();
		testRiskOffer.addCoverageOffer(mock(CoverageOffer.class));
		this.currentTestRisk.addInsuranceRiskOffer(testRiskOffer);
		this.currentTestRisk.setSelectedInsuranceRiskOffer(testRiskOffer);

		// Setting the autocomfort coverage to be returned by the helper
		CoverageOffer testAutocomfortCoverage = new CoverageOffer();
		testAutocomfortCoverage.setCoverageSelectedIndicator(autocomfortSelected);
		when(this.mockCoverageHelper.getSelectedEndorsement(testRiskOffer.getCoverageOffers(), CoverageGroupCodeEnum.AUTOCOMFORT))
				.thenReturn(testAutocomfortCoverage);

		// Setting the collision coverage to be returned by the helper
		CoverageOffer testCollisionCoverage = new CoverageOffer();
		testCollisionCoverage.setDeductibleAmount(collisionDeductible);
		when(this.mockCoverageHelper.getSelectedEndorsement(testRiskOffer.getCoverageOffers(), CoverageGroupCodeEnum.BSC_COV_COLLISION))
				.thenReturn(testCollisionCoverage);

		// Setting the comprehensive coverage to be returned by the helper
		CoverageOffer testComprehensiveCoverage = new CoverageOffer();
		testComprehensiveCoverage.setDeductibleAmount(comprehensiveDeductible);
		when(this.mockCoverageHelper.getSelectedEndorsement(testRiskOffer.getCoverageOffers(), CoverageGroupCodeEnum.BSC_COV_COMPREH))
				.thenReturn(testComprehensiveCoverage);

		return testRiskOffer;
	}

	/**
	 * Private method used to do the general setup for tests for different numbers of stability months
	 * for the determineQuoteInfos() method.
	 * Note that it only receives different values for number of stability months and months to substract
	 * because there is one case where those are different.
	 *
	 * @param numberStabilityMonths
	 * @param monthsToSubstract
	 * @return
	 */
	private GregorianCalendar setupGeneralDetermineQuoteInfosStabilityMonthsTest(Integer numberStabilityMonths, Integer monthsToSubstract) {
		// Creating the policy holder
		this.currentTestHolder = new PolicyHolder();
		Party testParty = new Party();
		this.currentTestHolder.setNumberStabilityMonths(numberStabilityMonths);
		this.currentTestHolder.setParty(testParty);
		when(this.mockPolicyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(this.currentTestHolder);

		// Setting the date last moved to be set in the test party and used for validation
		Date testDate = new Date();
		GregorianCalendar expectedDateOfLastMove = new GregorianCalendar();
		expectedDateOfLastMove.setTime(testDate);
		expectedDateOfLastMove.add(Calendar.MONTH, -monthsToSubstract);
		when(this.capiPolicyChangeDateService.getReferenceDate(any(DateHelperEnum.class), any(Long.class)))
				.thenReturn(testDate);

		// Other stubs setup to avoid other exceptions
		when(this.mockDataMediatorToSOMFactory.getService(anyString())).thenReturn(mock(IDataMediatorToSOM.class));
		when(this.mockDataMediatorToPL.convertTo_PL(any(),
				any(PolicyVersion.class), any(boolean.class))).thenReturn(this.currentTestPV);

		return expectedDateOfLastMove;
	}
}
