package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.intact.rating.IPremiumDerivationService;
import com.intact.rating.exception.RatingException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.IRatingServiceHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.StopWatch;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RateManagerServiceTest {

	private static class RateManager extends RateManagerService {

		@Override
		public PolicyVersion rateTheWholeQuotation(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled)
				throws AutoquoteRatingException {
			return super.rateTheWholeQuotation(aPolicyVersion, isAgent, isUbiEnabled);
		}

		@Override
		public IRatingService getRatingService() {
			return this.ratingService;
		}

		@Override
		public void selectOffer(PolicyVersion aPolicyVersion, StopWatch performanceWatch, boolean isUbiEnabled) throws AutoquoteRatingException {

		}
	}

	/** The service to test */
	@InjectMocks
	private RateManagerService rateManagerService = new RateManager();

	/** Other general objects */
	private PolicyVersion currentPolicyVersion;

	/** Mocks */
	@Mock
	private IRatingService mockRatingService;
	
	@Mock
	private IPremiumDerivationService mockPremiumDeviationService;

	@Mock
	private IRatingServiceHelper mockHelper;

	@Mock
	private IInsuranceRiskOfferHelper mockIInsuranceRiskOfferHelper;

	@Mock
	private ICoverageHelper mockCoverageHelper;
	
	/**
	 * Initialization
	 */
	@BeforeEach
	public void setUp() throws Exception {		
		// PL Policy version setup
		this.currentPolicyVersion = new PolicyVersion();
		this.currentPolicyVersion.setId(222L);
		InsurancePolicy insPolicy = new InsurancePolicy();
		insPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		this.currentPolicyVersion.setInsurancePolicy(insPolicy);
		
		// Mock rating service setup to be certain it is set in the tested class (not just the test impl)
		ReflectionTestUtils.setField(this.rateManagerService, "ratingService", this.mockRatingService,
				IRatingService.class);
	}
	
	/**
	 * Test method for
	 * {@link RateManagerService#rateTheWholeQuotation(PolicyVersion, boolean, boolean)}
	 * @throws RatingException 
	 */
	@Test
	public void testRateTheWholeQuotation() throws RatingException, AutoquoteRatingException {
		// Return the current policy version to be used for validation
		when(this.mockRatingService.rateOffer(any(PolicyVersion.class), any(Boolean.class), any(Boolean.class)))
			.thenReturn(this.currentPolicyVersion);
		
		// Executing the tested method and validating the stub calls
		PolicyVersion resultPV = this.rateManagerService.rateTheWholeQuotation(this.currentPolicyVersion, true, true);
		verify(this.mockRatingService, times(1)).manageUbi(any(PolicyVersion.class), any(), any(StopWatch.class));
		
		assertEquals(this.currentPolicyVersion, resultPV);
	}
	
	/**
	 * Test method for
	 * {@link RateManagerService#rateTheWholeQuotation(PolicyVersion, boolean, boolean)}
	 * Case for a rating exception to be raised during the method's execution.
	 * @throws RatingException 
	 */
	@Test
	public void testRateTheWholeQuotation_RaiseRatingException() throws RatingException, AutoquoteRatingException {
		// Setup so the correct exception is raised
		RatingException testException = new RatingException("Exception raised for the test \"testRateTheWholeQuotation_RaiseRatingException\"");
		doThrow(testException).when(this.mockPremiumDeviationService).rateService(any(PolicyVersion.class));
		when(this.mockRatingService.rateOffer(any(PolicyVersion.class), any(Boolean.class), any(Boolean.class)))
			.thenReturn(currentPolicyVersion);

		// Executing the tested method and verifying the exception is thrown
		assertThrows(AutoquoteRatingException.class, () -> {
			this.rateManagerService.rateTheWholeQuotation(this.currentPolicyVersion, true, true);
		});
	}
}
