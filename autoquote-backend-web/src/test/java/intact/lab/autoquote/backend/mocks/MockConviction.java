/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo;
import com.ing.canada.som.interfaces.registration.Conviction;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.GregorianCalendar;
import java.util.List;

/**
 * 
 * Mock class for a SOM Conviction. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Conviction}
 * 
 * <AUTHOR>
 *
 */
public class MockConviction implements Conviction {

	private Integer convictionSequence;

	private GregorianCalendar convictionDate;

	private String convictionCode;

	private String convictionType;

	private String convictionTypeRsp;

	private String convictionChargeabilityInd;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		return false;
	}

	@Override
	public Integer getConvictionSequence() {
		return this.convictionSequence;
	}

	@Override
	public void setConvictionSequence(Integer newConvictionSequence) {
		this.convictionSequence = newConvictionSequence;
	}

	@Override
	public String getConvictionCode() {
		return this.convictionCode;
	}

	@Override
	public void setConvictionCode(String newConvictionCode) {
		this.convictionCode = newConvictionCode;
	}

	@Override
	public String getConvictionType() {
		return this.convictionType;
	}

	@Override
	public void setConvictionType(String newConvictionType) {
		this.convictionType = newConvictionType;
	}

	@Override
	public String getConvictionTypeRsp() {
		return this.convictionTypeRsp;
	}

	@Override
	public void setConvictionTypeRsp(String newConvictionTypeRsp) {
		this.convictionTypeRsp = newConvictionTypeRsp;
	}

	@Override
	public String getConvictionTypeGrid() {
		return null;
	}

	@Override
	public void setConvictionTypeGrid(String newConvictionTypeGrid) {
		// noop
	}

	@Override
	public String getProvince() {
		return null;
	}

	@Override
	public void setProvince(String newProvince) {

	}

	@Override
	public String getConvictionAccommodationInd() {
		return null;
	}

	@Override
	public void setConvictionAccommodationInd(String newConvictionAccommodationInd) {
		// noop
	}

	@Override
	public String getConvictionChargeabilityInd() {
		return this.convictionChargeabilityInd;
	}

	@Override
	public void setConvictionChargeabilityInd(String newConvictionChargeabilityInd) {
		this.convictionChargeabilityInd = newConvictionChargeabilityInd;
	}

	@Override
	public String getConvictionProtectionCoverageInd() {
		return null;
	}

	@Override
	public void setConvictionProtectionCoverageInd(String newConvictionProtectionCoverageInd) {
		// noop
	}

	@Override
	public GregorianCalendar getCreationDate() {
		return null;
	}

	@Override
	public void setCreationDate(GregorianCalendar newCreationDate) {
		// noop
	}

	@Override
	public GregorianCalendar getConvictionDate() {
		return this.convictionDate;
	}

	@Override
	public void setConvictionDate(GregorianCalendar newConvictionDate) {
		this.convictionDate = newConvictionDate;
	}

	@Override
	public String getLicenseSuspensionType() {
		return null;
	}

	@Override
	public void setLicenseSuspensionType(String newLicenseSuspensionType) {
		// noop
	}

	@Override
	public String getLicenseSuspensionReason() {
		return null;
	}

	@Override
	public void setLicenseSuspensionReason(String newLicenseSuspensionReason) {
		// noop
	}

	@Override
	public GregorianCalendar getSuspensionStartDate() {
		return null;
	}

	@Override
	public void setSuspensionStartDate(GregorianCalendar newSuspensionStartDate) {
		// noop
	}

	@Override
	public GregorianCalendar getSuspensionEndDate() {
		return null;
	}

	@Override
	public void setSuspensionEndDate(GregorianCalendar newSuspensionEndDate) {
		// noop
	}

	@Override
	public String getViolationDescription() {
		return null;
	}

	@Override
	public void setViolationDescription(String newViolationDescription) {
		// noop
	}

	@Override
	public String getForgivenInd() {
		return null;
	}

	@Override
	public void setForgivenInd(String newForgivenInd) {

	}

	@Override
	public String getForgivenReason() {
		return null;
	}

	@Override
	public void setForgivenReason(String newForgivenReason) {

	}

	@Override
	public DriverComplementInfo getTheDriverComplementInfo() {
		return null;
	}

	@Override
	public void setTheDriverComplementInfo(DriverComplementInfo newTheDriverComplementInfo) {
		// noop
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfo() {
		return null;
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfo(Class<? extends DriverComplementInfo> theInterface) {
		return null;
	}

	@Override
	public Conviction getTheConvictionPriorTrans() {
		return null;
	}

	@Override
	public void setTheConvictionPriorTrans(Conviction newTheConvictionPriorTrans) {
		// noop
	}

	@Override
	public Conviction createTheConvictionPriorTrans() {
		return null;
	}

	@Override
	public Conviction createTheConvictionPriorTrans(Class<? extends Conviction> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRisk() {
		return null;
	}

	@Override
	public void setTheInsuranceRisk(InsuranceRisk newTheInsuranceRisk) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public String getWasConsideredByRatingInd() {
		return null;
	}

	@Override
	public void setWasConsideredByRatingInd(String arg0) {
		// noop
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

}
