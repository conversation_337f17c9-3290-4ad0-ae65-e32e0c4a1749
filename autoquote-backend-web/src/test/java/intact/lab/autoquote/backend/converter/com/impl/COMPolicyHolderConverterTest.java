package intact.lab.autoquote.backend.converter.com.impl;


import com.intact.com.driver.ComDriver;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.converter.impl.COMPolicyHolderConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class COMPolicyHolderConverterTest {

	@InjectMocks
	COMPolicyHolderConverter comPolicyHolderConverter;

	@Test
	public void testToDTOWithCompleteComPolicyHolder() throws Exception {
		ComDriver policyHolder = new ComDriver();
		policyHolder.setWebMsgId(1);
		policyHolder.setIsDriver(Boolean.FALSE);
		policyHolder.setDurationOfInsuranceTerm("3");
		PolicyHolderDTO policyHolderDTO = this.comPolicyHolderConverter.toDTO(policyHolder);
		assertNotNull(policyHolderDTO);
		assertEquals(1, policyHolderDTO.getPartyId());
	}

	@Test
	public void testToCOMWithCompletePolicyHolderDTO() throws Exception {
		ComDriver policyHolder = null;
		PolicyHolderDTO policyHolderDTO = new PolicyHolderDTO();
		policyHolderDTO.setPartyId(1);
		policyHolder = this.comPolicyHolderConverter.toCOM(policyHolderDTO, policyHolder);
		assertNotNull(policyHolder);
	}
}
