/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.driver.impl;

import com.ing.canada.cif.domain.IPostalCode;
import com.ing.canada.cif.domain.impl.PostalCode;
import com.ing.canada.common.domain.Municipality;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.enums.ComErrorCodeEnum;
import com.intact.com.enums.ComErrorFieldEnum;
import com.intact.com.util.ComValidationError;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.facade.impl.FacadeTestUtil;
import intact.lab.autoquote.backend.services.address.IAddressService;
import intact.lab.autoquote.backend.services.IMunicipalityService;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class DriverFacadeTest extends FacadeTestUtil {

	/**
	 * Class to be tested. Since it is the abstract class, it is accessed through a quickquote QC implementation .
	 */
	@InjectMocks
	private DriverFacade driverFacade = new DriverFacadeQCIntactCL();

	/** Mocks */
	@Mock
	private IAddressService mockAddressService;

	@Mock
	private IDriverBusinessProcess mockDriverBusinessProcess;

	@Mock
	private IFormFieldValidatorService mockFormFieldValidatorService;

	@Mock
	private IMunicipalityService mockMunicipalityService;


	@Test
	public void testGetMunicipalityByPostalCode() throws AutoquoteFacadeException {
		// Setup to avoid getting any validation error
		String postalCode = "H2H3H2";
		when(this.mockFormFieldValidatorService.isPostalCodeValid(anyString())).thenReturn(true);
		when(this.mockFormFieldValidatorService.getFormattedPostalCode(anyString())).thenReturn(postalCode);

		Municipality municipality = new Municipality();
		when(
				this.mockMunicipalityService.getFirstMunicipalityForPostalCode(any(Locale.class), anyString(),
						any(ManufacturingContext.class))).thenReturn(municipality);
		when(
				this.mockAddressService.isPolicyHolderInCurrentProvince(any(ProvinceCodeEnum.class),
						any(Municipality.class), anyString(), any(ManufacturingContext.class))).thenReturn(true);


		// Postal code setup
		IPostalCode testPostalCode = new PostalCode();
		String expectedStreetDirection = "East";
		String expectedStreetName = "Saint-Josheph";
		String expectedStreetType = "Boulevard";
		testPostalCode.setStreetDirection(expectedStreetDirection);
		testPostalCode.setStreetName(expectedStreetName);
		testPostalCode.setStreetType(expectedStreetType);
		when(this.mockDriverBusinessProcess.getPostalCodeInfo(any(Locale.class), anyString())).thenReturn(
				testPostalCode);

		// Province setup
		ProvinceCodeEnum expectedProvince = ProvinceCodeEnum.QUEBEC;
		when(
				this.mockDriverBusinessProcess.getProvinceForMunicipality(any(Municipality.class), anyString(),
						any(ManufacturingContext.class))).thenReturn(expectedProvince);

		// Municipalities setup
		String expectedMunicipalityDesc = "municipalityDescriptionTest";
		String expectedMunicipalityId = "munID";
		municipality.setId(expectedMunicipalityId);
		municipality.setDescription(expectedMunicipalityDesc);
		List<Municipality> municipalitiesList = new ArrayList<>();
		municipalitiesList.add(municipality);
		when(
				this.mockDriverBusinessProcess.getMunicipalitiesFromPostalCode(any(Locale.class), anyString(),
						any(ManufacturingContext.class))).thenReturn(municipalitiesList);

		ComMunicipalityInfo resultInfo = this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN,
				postalCode);
		ValidValue resultMunicipality = resultInfo.getMunicipalities().getFirst(); // There should only be one resulting
																				// municipality

		assertEquals("CA", resultInfo.getCountry());
		assertEquals(expectedProvince.getCode(), resultInfo.getProvince());
		assertEquals(expectedStreetDirection, resultInfo.getStreetDirection());
		assertEquals(expectedStreetName, resultInfo.getStreetName());
		assertEquals(expectedStreetType, resultInfo.getStreetType());
		assertEquals(expectedMunicipalityId, resultMunicipality.getValue());
		assertEquals(expectedMunicipalityDesc, resultMunicipality.getLabel());
		assertFalse(
				resultInfo.isProhibitedPostalCode(), "Resulting info's prohibitedPostalCode property should've been false.");
	}

	/**
	 * Test method for
	 * Case for a postal code format error to be found
	 * 
	 * @throws AutoquoteFacadeException
	 */
	@Test
	public void testGetMunicipalityByPostalCode_PostalCodeFormatError() throws AutoquoteFacadeException {
		// This method is set to return false to have an invalid postal code error (municipality is not set to trigger
		// the error)
		when(this.mockFormFieldValidatorService.isPostalCodeValid(anyString())).thenReturn(false);

		// Obtaining and validating the results
		ComMunicipalityInfo resultInfo = this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN, "H2H3H2");
		ComValidationError errorFound = resultInfo.getValidationErrors().getFirst();
		assertEquals(1, resultInfo.getValidationErrors().size()); // There should only be one error found
		assertEquals(ComErrorCodeEnum.FORMAT_ERROR, errorFound.getErrorCode());
		assertEquals(ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE, errorFound.getErrorField());
	}

	/**
	 * Test method for
	 * Case for a municipality not found error to be found.
	 * 
	 * @throws AutoquoteFacadeException
	 */
	@Test
	public void testGetMunicipalityByPostalCode_NullMunicipality() throws AutoquoteFacadeException {
		// Setup to avoid any validation error until the one for the current test
		when(this.mockFormFieldValidatorService.isPostalCodeValid(anyString())).thenReturn(true);
		when(this.mockFormFieldValidatorService.getFormattedPostalCode(anyString())).thenReturn("H2H3H2");

		// Obtaining and validating the results
		ComMunicipalityInfo resultInfo = this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN, "H2H3H2");
		ComValidationError errorFound = resultInfo.getValidationErrors().getFirst();
		assertEquals(1, resultInfo.getValidationErrors().size()); // There should only be one error found
		assertEquals(ComErrorCodeEnum.MUNICIPALITY_NOT_FOUND, errorFound.getErrorCode());
		assertEquals(ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE, errorFound.getErrorField());
	}

	/**
	 * Test method for
	 * Case for a municipality outside province error to be found.
	 * 
	 * @throws AutoquoteFacadeException
	 */
	@Test
	public void testGetMunicipalityByPostalCode_MunicipalityOutsideProvince() throws AutoquoteFacadeException {
		// This method is set to return false to have an invalid postal code error (municipality is not set to trigger
		// the error)
		when(this.mockFormFieldValidatorService.isPostalCodeValid(anyString())).thenReturn(true);
		when(this.mockFormFieldValidatorService.getFormattedPostalCode(anyString())).thenReturn("H2H3H2");

		Municipality municipality = new Municipality();
		when(
				this.mockMunicipalityService.getFirstMunicipalityForPostalCode(any(Locale.class), anyString(),
						any(ManufacturingContext.class))).thenReturn(municipality);
		when(
				this.mockAddressService.isPolicyHolderInCurrentProvince(any(ProvinceCodeEnum.class),
						any(Municipality.class), anyString(), any(ManufacturingContext.class))).thenReturn(false); // This
																													// will
																													// allow
																													// the
																													// error
																													// to
																													// be
																													// found

		// Obtaining and validating the results
		ComMunicipalityInfo resultInfo = this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN, "H2H3H2");
		ComValidationError errorFound = resultInfo.getValidationErrors().getFirst();
		assertEquals(1, resultInfo.getValidationErrors().size()); // There should only be one error found
		assertEquals(ComErrorCodeEnum.MUNICIPALITY_OUTSIDE_PROVINCE, errorFound.getErrorCode());
		assertEquals(ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE, errorFound.getErrorField());
	}

	/**
	 * Test method for
	 * Case for a the postal code to be null when passed as parameter.
	 * 
	 * @throws AutoquoteFacadeException
	 */
	@Test
	public void testGetMunicipalityByPostalCode_NullPostalCode() throws AutoquoteFacadeException {
		// Obtaining and validating the results (passing null for postal code)
		ComMunicipalityInfo resultInfo = this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN, null);

		ComValidationError errorFound = resultInfo.getValidationErrors().getFirst();
		assertEquals(1, resultInfo.getValidationErrors().size()); // There should only be one error found
		assertEquals(ComErrorCodeEnum.MANDATORY_FIELD, errorFound.getErrorCode());
		assertEquals(ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE, errorFound.getErrorField());
	}

	/**
	 * Test method for
	 * Case for a general exception to be thrown.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetMunicipalityByPostalCodeException() {
		when(this.mockFormFieldValidatorService.isPostalCodeValid(anyString())).thenThrow(
				new RuntimeException("Error thrown for the test \"testGetMunicipalityByPostalCodeException\""));

		assertThrows(AutoQuoteQuoteServiceException.class, () -> this.driverFacade.getMunicipalityByPostalCode(this.context_IN_ON_EN, "H2H3H2"));
	}
}
