/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.agreement.InsurancePolicy;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.claim.ClaimDerivedInfo;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo;
import com.ing.canada.som.interfaces.registration.Conviction;
import com.ing.canada.som.interfaces.registration.DriverLicenseClass;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

/**
 *
 * Mock class for a SOM DriverComplementInfo. Created because of lack of an implementation that can be mocked and
 * because Mockito/Powermock can't mock the interface. Implements
 * {@link DriverComplementInfo}
 *
 * <AUTHOR>
 *
 */
public class MockDriverComplementInfo implements DriverComplementInfo {
	Integer driverSequence;

	@Override
	public Integer getNumberOfSuspensions3Years() {
		return null;
	}

	@Override
	public Integer getNumberOfSuspensions6YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfSuspensions6YearsUnderwriting(Integer newNumberOfSuspensions6YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfDistractedDrivingConvictions3yearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfDistractedDrivingConvictions3yearsUnderwriting(Integer newNumberOfDistractedDrivingConvictions3yearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfSevereConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3YearsUnderwriting(Integer newNumberOfSevereConvictions3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsUnderwriting(Integer newNumberOfMajorConvictions3YearsUnderwriting) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiableCollision3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiableCollision3Years(Integer newNumberOfClaimsNonLiableCollision3Years) {

	}

	@Override
	public Integer getNumberOfClaimsNonLiable6YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable6YearsUnderwriting(Integer newNumberOfClaimsNonLiable6YearsUnderwriting) {

	}

	@Override
	public void setNumberOfSuspensions3Years(Integer newNumberOfSuspensions3Years) {

	}

	@Override
	public Integer getNumberOfSuspensions6Years() {
		return null;
	}

	@Override
	public void setNumberOfSuspensions6Years(Integer newNumberOfSuspensions6Years) {

	}

	@Override
	public Integer getNumberOfSuspensions10Years() {
		return null;
	}

	@Override
	public void setNumberOfSuspensions10Years(Integer newNumberOfSuspensions10Years) {

	}

	@Override
	public Integer getNumberOfDistractedDrivingConvictions3years() {
		return null;
	}

	@Override
	public void setNumberOfDistractedDrivingConvictions3years(Integer newNumberOfDistractedDrivingConvictions3years) {

	}

	@Override
	public Integer getNumberOfMinorConvictions6Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions6Years(Integer newNumberOfMinorConvictions6Years) {

	}

	@Override
	public Integer getNumberOfMajorConvictions6Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions6Years(Integer newNumberOfMajorConvictions6Years) {

	}

	@Override
	public Integer getNumberOfSevereConvictions6Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions6Years(Integer newNumberOfSevereConvictions6Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableWithAccommodation6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableWithAccommodation6Years(Integer newNumberOfClaimsLiableWithAccommodation6Years) {

	}

	@Override
	public Integer getNumberOfClaimsDisregarded5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded5Years(Integer newNumberOfClaimsDisregarded5Years) {

	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedMotorcycle() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedMotorcycle(Integer newNumberOfMonthsDriverLicensedMotorcycle) {

	}

	@Override
	public Integer getNumberOfYearsDriverLicensedInCanadaAndUs() {
		return null;
	}

	@Override
	public void setNumberOfYearsDriverLicensedInCanadaAndUs(Integer newNumberOfYearsDriverLicensedInCanadaAndUs) {

	}

	@Override
	public GregorianCalendar getUbiLastAssessmentEffectiveDate() {
		return null;
	}

	@Override
	public void setUbiLastAssessmentEffectiveDate(GregorianCalendar newUbiLastAssessmentEffectiveDate) {

	}

	@Override
	public GregorianCalendar getUbiNextAssessmentEffectiveDate() {
		return null;
	}

	@Override
	public void setUbiNextAssessmentEffectiveDate(GregorianCalendar newUbiNextAssessmentEffectiveDate) {

	}

	String ubiStatus;

	String ubiEligibilityInd;

	String interestedByUbiInd;

	String normalLicenseProgressionInd;

	Integer numberOfNonPaymentCancellationsIn3Years;

	Integer numberOfMinorConvictions3Years;

	Integer numberOfMajorConvictions3Years;

	Integer numberOfClaims3Years;

	Integer numberOfClaims5Years;

	Integer numberOfLiableClaims3Years;

	Integer numberOfLiableClaims5Years;

	Integer numberOfClaimsAtFault6Years;

	Integer numberOfLiableClaims10Years;

	List<Conviction> testConvictionList;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public Integer getDriverSequence() {
		return this.driverSequence;
	}

	@Override
	public void setDriverSequence(Integer newDriverSequence) {
		this.driverSequence = newDriverSequence;
	}

	@Override
	public String getRequestStatusToClaimCenterFile() {
		return null;
	}

	@Override
	public void setRequestStatusToClaimCenterFile(String newRequestStatusToClaimCenterFile) {
		// noop
	}

	@Override
	public GregorianCalendar getDateOfLastRequestToClaimCenterFile() {
		return null;
	}

	@Override
	public void setDateOfLastRequestToClaimCenterFile(GregorianCalendar newDateOfLastRequestToClaimCenterFile) {
		// noop
	}

	@Override
	public GregorianCalendar getDateOfLastRequestToMVRSAAQ() {
		return null;
	}

	@Override
	public void setDateOfLastRequestToMVRSAAQ(GregorianCalendar newDateOfLastRequestToMVRSAAQ) {
		// noop
	}

	@Override
	public String getSubmitRequestToMVRSAAQ() {
		return null;
	}

	@Override
	public void setSubmitRequestToMVRSAAQ(String newSubmitRequestToMVRSAAQ) {
		// noop
	}

	@Override
	public String getMvrsaaqReportRequestStatus() {
		return null;
	}

	@Override
	public void setMvrsaaqReportRequestStatus(String newMvrsaaqReportRequestStatus) {
		// noop
	}

	@Override
	public String getMvrsaaqAuthorizationCode() {
		return null;
	}

	@Override
	public void setMvrsaaqAuthorizationCode(String newMvrsaaqAuthorizationCode) {
		// noop
	}

	@Override
	public String getFinancialRespCertificateInd() {
		return null;
	}

	@Override
	public void setFinancialRespCertificateInd(String newFinancialRespCertificateInd) {
		// noop
	}

	@Override
	public String getRelationshipToNamedInsured() {
		return null;
	}

	@Override
	public void setRelationshipToNamedInsured(String newRelationshipToNamedInsured) {
		// noop
	}

	@Override
	public String getParentsInsuredWithCompanyInd() {
		return null;
	}

	@Override
	public void setParentsInsuredWithCompanyInd(String newParentsInsuredWithCompanyInd) {
		// noop
	}

	@Override
	public GregorianCalendar getEffectiveDate() {
		return null;
	}

	@Override
	public void setEffectiveDate(GregorianCalendar newEffectiveDate) {
		// noop
	}

	@Override
	public String getDriverTrainingInd() {
		return null;
	}

	@Override
	public void setDriverTrainingInd(String newDriverTrainingInd) {
		// noop
	}

	@Override
	public String getDriverTrainingStudentGradeCode() {
		return null;
	}

	@Override
	public void setDriverTrainingStudentGradeCode(String newDriverTrainingStudentGradeCode) {
		// noop
	}

	@Override
	public String getTrainingTerminatedSinceCode() {
		return null;
	}

	@Override
	public void setTrainingTerminatedSinceCode(String newTrainingTerminatedSinceCode) {
		// noop
	}

	@Override
	public String getGraduatedLicenseDiscountInd() {
		return null;
	}

	@Override
	public void setGraduatedLicenseDiscountInd(String newGraduatedLicenseDiscountInd) {
		// noop
	}

	@Override
	public String getFirstChanceDriverDiscountInd() {
		return null;
	}

	@Override
	public void setFirstChanceDriverDiscountInd(String newFirstChanceDriverDiscountInd) {
		// noop
	}

	@Override
	public String getDriverLicenseType() {
		return null;
	}

	@Override
	public void setDriverLicenseType(String newDriverLicenseType) {
		// noop
	}

	@Override
	public String getLicenseJurisdiction() {
		return null;
	}

	@Override
	public void setLicenseJurisdiction(String newLicenseJurisdiction) {
		// noop
	}

	@Override
	public String getLicenseNumber() {
		return null;
	}

	@Override
	public void setLicenseNumber(String newLicenseNumber) {
		// noop
	}

	@Override
	public String getLicenseStatusCode() {
		return null;
	}

	@Override
	public void setLicenseStatusCode(String newLicenseStatusCode) {
		// noop
	}

	@Override
	public String getLicenseSuspensionInd() {
		return null;
	}

	@Override
	public void setLicenseSuspensionInd(String newLicenseSuspensionInd) {
		// noop
	}

	@Override
	public Integer getNumberOfSuspensions1Year() {
		return null;
	}

	@Override
	public void setNumberOfSuspensions1Year(Integer newNumberOfSuspensions1Year) {
		// noop
	}

	@Override
	public Integer getLicenseSuspensionPeriodInMonths() {
		return null;
	}

	@Override
	public void setLicenseSuspensionPeriodInMonths(Integer newLicenseSuspensionPeriodInMonths) {
		// noop
	}

	@Override
	public Integer getLicenseSuspensionPeriodInMonthsGrid() {
		return null;
	}

	@Override
	public void setLicenseSuspensionPeriodInMonthsGrid(Integer newLicenseSuspensionPeriodInMonthsGrid) {
		// noop
	}

	@Override
	public String getNormalLicenseProgressionInd() {
		return this.normalLicenseProgressionInd;
	}

	@Override
	public void setNormalLicenseProgressionInd(String newNormalLicenseProgressionInd) {
		this.normalLicenseProgressionInd = newNormalLicenseProgressionInd;
	}

	@Override
	public String getDriverTrainingSnowmobileInd() {
		return null;
	}

	@Override
	public void setDriverTrainingSnowmobileInd(String newDriverTrainingSnowmobileInd) {
		// noop
	}

	@Override
	public String getDriverTrainingMotorcycleInd() {
		return null;
	}

	@Override
	public void setDriverTrainingMotorcycleInd(String newDriverTrainingMotorcycleInd) {
		// noop
	}

	@Override
	public String getDriverTrainingAllTerrVehInd() {
		return null;
	}

	@Override
	public void setDriverTrainingAllTerrVehInd(String newDriverTrainingAllTerrVehInd) {
		// noop
	}

	@Override
	public GregorianCalendar getDateDriverLicenseCertificateObtained() {
		return null;
	}

	@Override
	public void setDateDriverLicenseCertificateObtained(GregorianCalendar newDateDriverLicenseCertificateObtained) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceDriverLicenseCertificateObtained() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceDriverLicenseCertificateObtained(Integer newNumberOfMonthsSinceDriverLicenseCertificateObtained) {
		// noop
	}

	@Override
	public GregorianCalendar getDateDriverLicenseObtained() {
		return null;
	}

	@Override
	public void setDateDriverLicenseObtained(GregorianCalendar newDateDriverLicenseObtained) {
		// noop
	}

	@Override
	public GregorianCalendar getDateDriverLicenseObtainedMotorcycle() {
		return null;
	}

	@Override
	public void setDateDriverLicenseObtainedMotorcycle(GregorianCalendar newDateDriverLicenseObtainedMotorcycle) {
		// noop
	}

	@Override
	public Integer getAgeDriverLicenseObtained() {
		return null;
	}

	@Override
	public void setAgeDriverLicenseObtained(Integer newAgeDriverLicenseObtained) {
		// noop
	}

	@Override
	public String getObtainedG2orGclassInLast12MonthsInd() {
		return null;
	}

	@Override
	public void setObtainedG2orGclassInLast12MonthsInd(String newObtainedG2orGclassInLast12MonthsInd) {
		// noop
	}

	@Override
	public String getTrainingSchoolName() {
		return null;
	}

	@Override
	public void setTrainingSchoolName(String newTrainingSchoolName) {
		// noop
	}

	@Override
	public Integer getDrivingExperience() {
		return null;
	}

	@Override
	public void setDrivingExperience(Integer newDrivingExperience) {
		// noop
	}

	@Override
	public String getDrivingExperienceCodeLegacy() {
		return null;
	}

	@Override
	public void setDrivingExperienceCodeLegacy(String newDrivingExperienceCodeLegacy) {
		// noop
	}

	@Override
	public Integer getDrivingExperienceMotorcycle() {
		return null;
	}

	@Override
	public void setDrivingExperienceMotorcycle(Integer newDrivingExperienceMotorcycle) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsDrivingExperience() {
		return null;
	}

	@Override
	public void setNumberOfYearsDrivingExperience(Integer newNumberOfYearsDrivingExperience) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsDrivingExperienceGrid() {
		return null;
	}

	@Override
	public void setNumberOfYearsDrivingExperienceGrid(Integer newNumberOfYearsDrivingExperienceGrid) {
		// noop
	}

	@Override
	public GregorianCalendar getDateInsuredWithCompany() {
		return null;
	}

	@Override
	public void setDateInsuredWithCompany(GregorianCalendar newDateInsuredWithCompany) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsInsuredWithCompany() {
		return null;
	}

	@Override
	public void setNumberOfYearsInsuredWithCompany(Integer newNumberOfYearsInsuredWithCompany) {
		// noop
	}

	@Override
	public Integer getNumberOfmonthsAnnuallyStudyingAway() {
		return null;
	}

	@Override
	public void setNumberOfmonthsAnnuallyStudyingAway(Integer newNumberOfmonthsAnnuallyStudyingAway) {
		// noop
	}

	@Override
	public String getResideWithParentsInd() {
		return null;
	}

	@Override
	public void setResideWithParentsInd(String newResideWithParentsInd) {
		// noop
	}

	@Override
	public Integer getNumberOfKmToGoToSchool() {
		return null;
	}

	@Override
	public void setNumberOfKmToGoToSchool(Integer newNumberOfKmToGoToSchool) {
		// noop
	}

	@Override
	public String getDrivingDuringWeekendOnlyInd() {
		return null;
	}

	@Override
	public void setDrivingDuringWeekendOnlyInd(String newDrivingDuringWeekendOnlyInd) {
		// noop
	}

	@Override
	public String getDistantStudentInd() {
		return null;
	}

	@Override
	public void setDistantStudentInd(String newDistantStudentInd) {
		// noop
	}

	@Override
	public Integer getNumberOfConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfConvictions3Years(Integer newNumberOfConvictions3Years) {

	}

	@Override
	public Integer getNumberOfAlcoholConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfAlcoholConvictions3Years(Integer newNumberOfAlcoholConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfAlcoholConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfAlcoholConvictions10Years(Integer integer) {

	}

	@Override
	public Integer getNumberOfMinorConvictions1Year() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions1Year(Integer newNumberOfMinorConvictions1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions2YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions2YearsSystem(Integer newNumberOfMinorConvictions2YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions2YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions2YearsModified(Integer newNumberOfMinorConvictions2YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions2Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions2Years(Integer newNumberOfMinorConvictions2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions2YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions2YearsGrid(Integer newNumberOfMinorConvictions2YearsGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsSystem(Integer newNumberOfMinorConvictions3YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsModified(Integer newNumberOfMinorConvictions3YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3Years() {
		return this.numberOfMinorConvictions3Years;
	}

	@Override
	public void setNumberOfMinorConvictions3Years(Integer newNumberOfMinorConvictions3Years) {
		this.numberOfMinorConvictions3Years = newNumberOfMinorConvictions3Years;
	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsGrid(Integer newNumberOfMinorConvictions3YearsGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions4Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions4Years(Integer newNumberOfMinorConvictions4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions5Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions5Years(Integer newNumberOfMinorConvictions5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions10Years(Integer newNumberOfMinorConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastConviction() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastConviction(Integer newNumberOfMonthsSinceLastConviction) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConviction() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConviction(Integer newNumberOfMonthsSinceLastMajorConviction) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastSevereConviction() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSevereConviction(Integer newNumberOfMonthsSinceLastSevereConviction) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConvictionSystem() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConvictionSystem(Integer newNumberOfMonthsSinceLastMinorConvictionSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConvictionModified() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConvictionModified(Integer newNumberOfMonthsSinceLastMinorConvictionModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastMinorConviction() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMinorConviction(Integer newNumberOfMonthsSinceLastMinorConviction) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions1Year() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions1Year(Integer newNumberOfMajorConvictions1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsSystem(Integer newNumberOfMajorConvictions3YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsModified(Integer newNumberOfMajorConvictions3YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions3Years() {
		return this.numberOfMajorConvictions3Years;
	}

	@Override
	public void setNumberOfMajorConvictions3Years(Integer newNumberOfMajorConvictions3Years) {
		this.numberOfMajorConvictions3Years = newNumberOfMajorConvictions3Years;
	}

	@Override
	public Integer getNumberOfMajorConvictions3YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions3YearsGrid(Integer newNumberOfMajorConvictions3YearsGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions5Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions5Years(Integer newNumberOfMajorConvictions5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfMajorConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfMajorConvictions10Years(Integer newNumberOfMajorConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictionsExcludingAlcohol3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictionsExcludingAlcohol3Years(Integer newNumberOfSevereConvictionsExcludingAlcohol3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions1Year() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions1Year(Integer newNumberOfSevereConvictions1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3Years(Integer newNumberOfSevereConvictions3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions3YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions3YearsGrid(Integer newNumberOfSevereConvictions3YearsGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions4Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions4Years(Integer newNumberOfSevereConvictions4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions4YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions4YearsGrid(Integer newNumberOfSevereConvictions4YearsGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions5Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions5Years(Integer newNumberOfSevereConvictions5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfSevereConvictions10Years() {
		return null;
	}

	@Override
	public void setNumberOfSevereConvictions10Years(Integer newNumberOfSevereConvictions10Years) {
		// noop
	}

	@Override
	public Integer getNumberOfUninsuredConvictions3Years() {
		return null;
	}

	@Override
	public void setNumberOfUninsuredConvictions3Years(Integer newNumberOfUninsuredConvictions3Years) {
		// noop
	}

	@Override
	public Integer getConvictionSurchargePercentage() {
		return null;
	}

	@Override
	public void setConvictionSurchargePercentage(Integer newConvictionSurchargePercentage) {
		// noop
	}

	@Override
	public Integer getNumberOfNonPaymentCancellationsIn2Years() {
		return null;
	}

	@Override
	public void setNumberOfNonPaymentCancellationsIn2Years(Integer newNumberOfNonPaymentCancellationsIn2Years) {

	}

	@Override
	public Integer getNumberOfNonPaymentCancellationsIn3Years() {
		return this.numberOfNonPaymentCancellationsIn3Years;
	}

	@Override
	public void setNumberOfNonPaymentCancellationsIn3Years(Integer newNumberOfNonPaymentCancellationsIn3Years) {
		this.numberOfNonPaymentCancellationsIn3Years = newNumberOfNonPaymentCancellationsIn3Years;
	}

	@Override
	public Integer getNumberOfClaims3Years() {
		return this.numberOfClaims3Years;
	}

	@Override
	public void setNumberOfClaims3Years(Integer newNumberOfClaims3Years) {
		this.numberOfClaims3Years = newNumberOfClaims3Years;
	}

	@Override
	public Integer getNumberOfClaims5Years() {
		return this.numberOfClaims5Years;
	}

	@Override
	public void setNumberOfClaims5Years(Integer newNumberOfClaims5Years) {
		this.numberOfClaims5Years = newNumberOfClaims5Years;
	}

	@Override
	public Integer getNumberOfLiableClaims6Months() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims6Months(Integer newNumberOfLiableClaims6Months) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims1Year() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims1Year(Integer newNumberOfLiableClaims1Year) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims2Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims2Years(Integer newNumberOfLiableClaims2Years) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims3Years() {
		return this.numberOfLiableClaims3Years;
	}

	@Override
	public void setNumberOfLiableClaims3Years(Integer newNumberOfLiableClaims3Years) {
		this.numberOfLiableClaims3Years = newNumberOfLiableClaims3Years;
	}

	@Override
	public Integer getNumberOfLiableClaims4Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims4Years(Integer newNumberOfLiableClaims4Years) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims5YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims5YearsSystem(Integer newNumberOfLiableClaims5YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims5YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims5YearsModified(Integer newNumberOfLiableClaims5YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims5Years() {
		return this.numberOfLiableClaims5Years;
	}

	@Override
	public void setNumberOfLiableClaims5Years(Integer newNumberOfLiableClaims5Years) {
		this.numberOfLiableClaims5Years = newNumberOfLiableClaims5Years;
	}

	@Override
	public Integer getNumberOfClaimsNonLiable5Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable5Years(Integer newNumberOfClaimsNonLiable5Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsNonLiable6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsNonLiable6Years(Integer newNumberOfClaimsNonLiable6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsLiable6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable6Years(Integer newNumberOfClaimsLiable6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsAtFault6YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfClaimsAtFault6YearsSystem(Integer newNumberOfClaimsAtFault6YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsAtFault6YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfClaimsAtFault6YearsModified(Integer newNumberOfClaimsAtFault6YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsAtFault6Years() {
		return this.numberOfClaimsAtFault6Years;
	}

	@Override
	public void setNumberOfClaimsAtFault6Years(Integer newNumberOfClaimsAtFault6Years) {
		this.numberOfClaimsAtFault6Years = newNumberOfClaimsAtFault6Years;
	}

	@Override
	public Integer getNumberOfLiableClaims7YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims7YearsSystem(Integer newNumberOfLiableClaims7YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims7YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims7YearsModified(Integer newNumberOfLiableClaims7YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims7Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims7Years(Integer newNumberOfLiableClaims7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsDisregarded3Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded3Years(Integer newNumberOfClaimsDisregarded3Years) {
		// noop
	}

	@Override
	public Integer getNumberOfClaimsDisregarded6Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsDisregarded6Years(Integer newNumberOfClaimsDisregarded6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims6YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims6YearsSystem(Integer newNumberOfForgivenClaims6YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims6YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims6YearsModified(Integer newNumberOfForgivenClaims6YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims6Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims6Years(Integer newNumberOfForgivenClaims6Years) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims7YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims7YearsSystem(Integer newNumberOfForgivenClaims7YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims7YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims7YearsModified(Integer newNumberOfForgivenClaims7YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfForgivenClaims7Years() {
		return null;
	}

	@Override
	public void setNumberOfForgivenClaims7Years(Integer newNumberOfForgivenClaims7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims9YearsSystem() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims9YearsSystem(Integer newNumberOfLiableClaims9YearsSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims9YearsModified() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims9YearsModified(Integer newNumberOfLiableClaims9YearsModified) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims9Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims9Years(Integer newNumberOfLiableClaims9Years) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaims10Years() {
		return this.numberOfLiableClaims10Years;
	}

	@Override
	public void setNumberOfLiableClaims10Years(Integer newNumberOfLiableClaims10Years) {
		this.numberOfLiableClaims10Years = newNumberOfLiableClaims10Years;
	}

	@Override
	public Integer getNumberOfLiableClaimsOnPpvAsPrincipalDriver7Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaimsOnPpvAsPrincipalDriver7Years(Integer newNumberOfLiableClaimsOnPpvAsPrincipalDriver7Years) {
		// noop
	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableClaim(Integer newNumberOfYearsSinceLastLiableClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaimSystem() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaimSystem(Integer newNumberOfMonthsSinceLastLiableClaimSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaimModified() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaimModified(Integer newNumberOfMonthsSinceLastLiableClaimModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastForgLiableClaim() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastForgLiableClaim(Integer newNumberOfMonthsSinceLastForgLiableClaim) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaim() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaim(Integer newNumberOfMonthsSinceLastLiableClaim) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingSystem() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingSystem(Integer newNumberOfMonthsDriverLicensedUnderwritingSystem) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingModified() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingModified(Integer newNumberOfMonthsDriverLicensedUnderwritingModified) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwriting(Integer newNumberOfMonthsDriverLicensedUnderwriting) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedGrid() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedGrid(Integer newNumberOfMonthsDriverLicensedGrid) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverHasHeldCurrentLicenseClass() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverHasHeldCurrentLicenseClass(Integer newNumberOfMonthsDriverHasHeldCurrentLicenseClass) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsContinuouslyLicensedForMotorcycle() {
		return null;
	}

	@Override
	public void setNumberOfMonthsContinuouslyLicensedForMotorcycle(Integer newNumberOfMonthsContinuouslyLicensedForMotorcycle) {
		// noop
	}

	@Override
	public String getMostRecentDriverLicenseClassOnPpv() {
		return null;
	}

	@Override
	public void setMostRecentDriverLicenseClassOnPpv(String newMostRecentDriverLicenseClassOnPpv) {
		// noop
	}

	@Override
	public Integer getGridLevel() {
		return null;
	}

	@Override
	public void setGridLevel(Integer newGridLevel) {
		// noop
	}

	@Override
	public GregorianCalendar getGridLevelDate() {
		return null;
	}

	@Override
	public void setGridLevelDate(GregorianCalendar newGridLevelDate) {
		// noop
	}

	@Override
	public Integer getPriorGridLevel() {
		return null;
	}

	@Override
	public void setPriorGridLevel(Integer newPriorGridLevel) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastGridLevelDate() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastGridLevelDate(Integer newNumberOfMonthsSinceLastGridLevelDate) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaimSinceLastGridLevelDate() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaimSinceLastGridLevelDate(Integer newNumberOfLiableClaimSinceLastGridLevelDate) {
		// noop
	}

	@Override
	public Integer getNumberOfLiableClaimPriorLastGridLevelDate6Years() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaimPriorLastGridLevelDate6Years(Integer newNumberOfLiableClaimPriorLastGridLevelDate6Years) {
		// noop
	}

	@Override
	public GregorianCalendar getContinuouslyInsuredSince() {
		return null;
	}

	@Override
	public void setContinuouslyInsuredSince(GregorianCalendar newContinuouslyInsuredSince) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsContinuouslyInsured() {
		return null;
	}

	@Override
	public void setNumberOfMonthsContinuouslyInsured(Integer newNumberOfMonthsContinuouslyInsured) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceLastSuspension() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastSuspension(Integer newNumberOfMonthsSinceLastSuspension) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedSuspensionForLearner() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionForLearner(Integer newNumberOfMonthsDriverLicensedSuspensionForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedSuspensionForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionForGraduated(Integer newNumberOfMonthsDriverLicensedSuspensionForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedSuspensionForProbation() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionForProbation(Integer newNumberOfMonthsDriverLicensedSuspensionForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedSuspensionForIntl() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionForIntl(Integer newNumberOfMonthsDriverLicensedSuspensionForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedSuspensionForUs() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionForUs(Integer newNumberOfMonthsDriverLicensedSuspensionForUs) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingForLearner() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingForLearner(Integer newNumberOfMonthsDriverLicensedUnderwritingForLearner) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingForGraduated() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingForGraduated(Integer newNumberOfMonthsDriverLicensedUnderwritingForGraduated) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingForProbation() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingForProbation(Integer newNumberOfMonthsDriverLicensedUnderwritingForProbation) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingForIntl() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingForIntl(Integer newNumberOfMonthsDriverLicensedUnderwritingForIntl) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensedUnderwritingForUs() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingForUs(Integer newNumberOfMonthsDriverLicensedUnderwritingForUs) {
		// noop
	}

	@Override
	public String getMvrsaaqReportOrderInd() {
		return null;
	}

	@Override
	public void setMvrsaaqReportOrderInd(String newMvrsaaqReportOrderInd) {
		// noop
	}

	@Override
	public String getUbiEligibilityInd() {
		return this.ubiEligibilityInd;
	}

	@Override
	public void setUbiEligibilityInd(String newUbiEligibilityInd) {
		this.ubiEligibilityInd = newUbiEligibilityInd;
	}

	@Override
	public String getInterestedByUbiInd() {
		return this.interestedByUbiInd;
	}

	@Override
	public void setInterestedByUbiInd(String newInterestedByUbiInd) {
		this.interestedByUbiInd = newInterestedByUbiInd;
	}

	@Override
	public String getUbiServiceProviderCode() {
		return null;
	}

	@Override
	public void setUbiServiceProviderCode(String newUbiServiceProviderCode) {

	}

	@Override
	public String getUbiStatus() {
		return this.ubiStatus;
	}

	@Override
	public void setUbiStatus(String newUbiStatus) {
		this.ubiStatus = newUbiStatus;
	}

	@Override
	public Integer getUbiScore() {
		return null;
	}

	@Override
	public void setUbiScore(Integer newUbiScore) {
		// noop
	}

	@Override
	public Double getUbiBehaviorScoreMinDistance() {
		return null;
	}

	@Override
	public void setUbiBehaviorScoreMinDistance(Double newUbiBehaviorScoreMinDistance) {

	}

	@Override
	public String getUbiDeviceType() {
		return null;
	}

	@Override
	public void setUbiDeviceType(String newUbiDeviceType) {
		// noop
	}

	@Override
	public String getUbiAlgorithmCode() {
		return null;
	}

	@Override
	public void setUbiAlgorithmCode(String newUbiAlgorithmCode) {
		// noop
	}

	@Override
	public Party getTheParty() {
		return null;
	}

	@Override
	public void setTheParty(Party newTheParty) {
		// noop
	}

	@Override
	public Party createTheParty() {
		return null;
	}

	@Override
	public Party createTheParty(Class<? extends Party> theInterface) {
		return null;
	}

	@Override
	public InsurancePolicy getTheInsurancePolicyParents() {
		return null;
	}

	@Override
	public void setTheInsurancePolicyParents(InsurancePolicy newTheInsurancePolicyParents) {
		// noop
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyParents() {
		return null;
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyParents(Class<? extends InsurancePolicy> theInterface) {
		return null;
	}

	@Override
	public DriverComplementInfo getTheDriverComplementInfoPriorTrans() {
		return null;
	}

	@Override
	public void setTheDriverComplementInfoPriorTrans(DriverComplementInfo newTheDriverComplementInfoPriorTrans) {
		// noop
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfoPriorTrans() {
		return null;
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfoPriorTrans(Class<? extends DriverComplementInfo> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskUbiReference() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskUbiReference(InsuranceRisk newTheInsuranceRiskUbiReference) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskUbiReference() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskUbiReference(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheConviction() {
		// noop
	}

	@Override
	public List<Conviction> getTheConviction() {
		return null;
	}

	@Override
	public Conviction getTheConviction(String uniqueId) {
		return null;
	}

	@Override
	public Conviction getTheConviction(int index) {
		return this.testConvictionList.get(index);
	}

	@Override
	public Conviction addTheConviction() {
		this.testConvictionList = this.testConvictionList == null ? new ArrayList<Conviction>() : this.testConvictionList;
		Conviction newConviction = new MockConviction();
		this.testConvictionList.add(newConviction);
		return newConviction;
	}

	@Override
	public Conviction addTheConviction(Class<? extends Conviction> theInterface) {
		return null;
	}

	@Override
	public void addTheConviction(Conviction newTheConviction) {
		// noop
	}

	@Override
	public void addTheConviction(int index, Conviction newTheConviction) {
		// noop
	}

	@Override
	public void setTheConviction(int index, Conviction newTheConviction) {
		// noop
	}

	@Override
	public void setTheConviction(List<Conviction> objList) {
		// noop
	}

	@Override
	public void removeTheConviction(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheConviction(int index) {
		// noop
	}

	@Override
	public void clearTheDriverLicenseClass() {
		// noop
	}

	@Override
	public List<DriverLicenseClass> getTheDriverLicenseClass() {
		return null;
	}

	@Override
	public DriverLicenseClass getTheDriverLicenseClass(String uniqueId) {
		return null;
	}

	@Override
	public DriverLicenseClass getTheDriverLicenseClass(int index) {
		return null;
	}

	@Override
	public DriverLicenseClass addTheDriverLicenseClass() {
		return null;
	}

	@Override
	public DriverLicenseClass addTheDriverLicenseClass(Class<? extends DriverLicenseClass> theInterface) {
		return null;
	}

	@Override
	public void addTheDriverLicenseClass(DriverLicenseClass newTheDriverLicenseClass) {
		// noop
	}

	@Override
	public void addTheDriverLicenseClass(int index, DriverLicenseClass newTheDriverLicenseClass) {
		// noop
	}

	@Override
	public void setTheDriverLicenseClass(int index, DriverLicenseClass newTheDriverLicenseClass) {
		// noop
	}

	@Override
	public void setTheDriverLicenseClass(List<DriverLicenseClass> objList) {
		// noop
	}

	@Override
	public void removeTheDriverLicenseClass(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDriverLicenseClass(int index) {
		// noop
	}

	@Override
	public String getUbiDeviceDiscountCriteria() {
		return null;
	}

	@Override
	public void setUbiDeviceDiscountCriteria(String arg0) {
		// noop
	}

	/**
	 * @see DriverComplementInfo#getTheDriverComplementInfoPriorTerm()
	 */
	@Override
	public DriverComplementInfo getTheDriverComplementInfoPriorTerm() {
		return null;
	}

	/**
	 * @see DriverComplementInfo#setTheDriverComplementInfoPriorTerm(DriverComplementInfo)
	 */
	@Override
	public void setTheDriverComplementInfoPriorTerm(DriverComplementInfo newTheDriverComplementInfoPriorTerm) {
		// noop
	}

	/**
	 * @see DriverComplementInfo#createTheDriverComplementInfoPriorTerm()
	 */
	@Override
	public DriverComplementInfo createTheDriverComplementInfoPriorTerm() {
		return null;
	}

	/**
	 * @see DriverComplementInfo#createTheDriverComplementInfoPriorTerm(Class)
	 */
	@Override
	public DriverComplementInfo createTheDriverComplementInfoPriorTerm(Class<? extends DriverComplementInfo> theInterface) {
		return null;
	}

	@Override public InsurancePolicy getTheInsurancePolicyUbiReference() {
		return null;
	}

	@Override public void setTheInsurancePolicyUbiReference(InsurancePolicy insurancePolicy) {

	}

	@Override public InsurancePolicy createTheInsurancePolicyUbiReference() {
		return null;
	}

	@Override public InsurancePolicy createTheInsurancePolicyUbiReference(Class<? extends InsurancePolicy> aClass) {
		return null;
	}

	@Override
	public String getUuid() {

		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getAssignedToPpaRiskInd() {

		return null;
	}

	@Override
	public String getGraduatedDriverDiscountEligibilityInd() {

		return null;
	}

	@Override
	public String getMostRecentDriverLicenseClassOnMotorcycle() {

		return null;
	}

	@Override
	public Integer getNumberOfClaimsLiableWithAccommodation9Years() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcy() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcyForGraduated() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcyForIntl() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcyForLearner() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcyForProbation() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedSuspensionMcyForUs() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcy() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcyForGraduate() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcyForIntl() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcyForLearner() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcyForProb() {

		return null;
	}

	@Override
	public Double getNumberOfMonthsDriverLicensedUnderwritingMcyForUs() {

		return null;
	}

	@Override
	public GregorianCalendar getUbiDiscountEffectiveDate() {

		return null;
	}

	@Override
	public Double getUbiDiscountPercentage() {

		return null;
	}

	@Override
	public Double getUbiDiscountPercentageModified() {

		return null;
	}

	@Override
	public Double getUbiDiscountPercentageSystem() {

		return null;
	}

	@Override
	public void setAssignedToPpaRiskInd(String arg0) {
		// noop
	}

	@Override
	public void setGraduatedDriverDiscountEligibilityInd(String arg0) {
		// noop
	}

	@Override
	public void setMostRecentDriverLicenseClassOnMotorcycle(String arg0) {
		// noop
	}

	@Override
	public Integer getGridLevelSystem() {
		return null;
	}

	@Override
	public void setGridLevelSystem(Integer newGridLevelSystem) {

	}

	@Override
	public Integer getGridLevelModified() {
		return null;
	}

	@Override
	public void setGridLevelModified(Integer newGridLevelModified) {

	}

	@Override
	public void setNumberOfClaimsLiableWithAccommodation9Years(Integer arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcy(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcyForGraduated(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcyForIntl(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcyForLearner(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcyForProbation(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedSuspensionMcyForUs(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcy(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcyForGraduate(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcyForIntl(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcyForLearner(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcyForProb(Double arg0) {
		// noop
	}

	@Override
	public void setNumberOfMonthsDriverLicensedUnderwritingMcyForUs(Double arg0) {
		// noop
	}

	@Override
	public void setUbiDiscountEffectiveDate(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public void setUbiDiscountPercentage(Double arg0) {
		// noop
	}

	@Override
	public void setUbiDiscountPercentageModified(Double arg0) {
		// noop
	}

	@Override
	public void setUbiDiscountPercentageSystem(Double arg0) {
		// noop
	}

	@Override
	public Integer getNumberOfMinorConvictions3YearsUnderwriting() {
		return null;
	}

	@Override
	public void setNumberOfMinorConvictions3YearsUnderwriting(Integer newNumberOfMinorConvictions3YearsUnderwriting) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsDriverLicensed() {
		return null;
	}

	@Override
	public void setNumberOfMonthsDriverLicensed(Integer newNumberOfMonthsDriverLicensed) {
		// noop
	}

	@Override
	public Integer getUbiNumberOfKmOnDevice() {
		return null;
	}

	@Override
	public void setUbiNumberOfKmOnDevice(Integer newUbiNumberOfKmOnDevice) {
		// noop
	}

	@Override
	public String getUbiManualCompleteInd() {
		return null;
	}

	@Override
	public void setUbiManualCompleteInd(String newUbiManualCompleteInd) {
		// noop
	}

	@Override
	public Integer getUbiAssessmentPeriodSystem() {
		return null;
	}

	@Override
	public void setUbiAssessmentPeriodSystem(Integer newUbiAssessmentPeriodSystem) {
		// noop
	}

	@Override
	public Integer getUbiAssessmentPeriodModified() {
		return null;
	}

	@Override
	public void setUbiAssessmentPeriodModified(Integer newUbiAssessmentPeriodModified) {
		// noop
	}

	@Override
	public Integer getUbiAssessmentPeriod() {
		return null;
	}

	@Override
	public void setUbiAssessmentPeriod(Integer newUbiAssessmentPeriod) {
		// noop
	}

	@Override
	public GregorianCalendar getUbiAssessmentEffectiveDate() {
		return null;
	}

	@Override
	public void setUbiAssessmentEffectiveDate(GregorianCalendar newUbiAssessmentEffectiveDate) {
		// noop
	}

	@Override
	public ClaimDerivedInfo getTheClaimDerivedInfo() {
		return null;
	}

	@Override
	public void setTheClaimDerivedInfo(ClaimDerivedInfo newTheClaimDerivedInfo) {
		// noop
	}

	@Override
	public ClaimDerivedInfo createTheClaimDerivedInfo() {
		return null;
	}

	@Override
	public ClaimDerivedInfo createTheClaimDerivedInfo(Class<? extends ClaimDerivedInfo> theInterface) {
		return null;
	}

	@Override
	public String getUbiPreviousAssessmentCompletedInd() {
		return null;
	}

	@Override
	public void setUbiPreviousAssessmentCompletedInd(String newUbiPreviousAssessmentCompletedInd) {
		// noop
	}

	@Override
	public String getUbiPreviousServiceProviderCode() {
		return null;
	}

	@Override
	public void setUbiPreviousServiceProviderCode(String newUbiPreviousServiceProviderCode) {

	}

	@Override
	public GregorianCalendar getUbiDriverEnrollmentRequestedStartDate() {
		return null;
	}

	@Override
	public void setUbiDriverEnrollmentRequestedStartDate(GregorianCalendar newUbiDriverEnrollmentRequestedStartDate) {
		// null
	}

	@Override
	public String getUbiProgramVersionCode() {
		return null;
	}

	@Override
	public void setUbiProgramVersionCode(String newUbiProgramVersionCode) {

	}

	@Override
	public Double getGridFactor() {
		return null;
	}

	@Override
	public void setGridFactor(Double aDouble) {

	}

	@Override
	public String getDriverHasConvictionInd() {
		return null;
	}

	@Override
	public void setDriverHasConvictionInd(String s) {

	}

	@Override
	public Integer getYoungestPartyWithOccasionalDriverRank() {
		return null;
	}

	@Override
	public void setYoungestPartyWithOccasionalDriverRank(Integer newYoungestPartyWithOccasionalDriverRank) {

	}

	@Override
	public Integer getYoungestPartyWithNonRatedDriverRank() {
		return null;
	}

	@Override
	public void setYoungestPartyWithNonRatedDriverRank(Integer newYoungestPartyWithNonRatedDriverRank) {

	}

	@Override
	public String getAddressHasChangedInd() {
		return null;
	}

	@Override
	public void setAddressHasChangedInd(String s) {

	}

	@Override public String getUbiApplicableDiscountType() {
		return null;
	}

	@Override public void setUbiApplicableDiscountType(String s) {

	}

	@Override public GregorianCalendar getDateForDrivingExperience() {
		return null;
	}

	@Override public void setDateForDrivingExperience(GregorianCalendar gregorianCalendar) {

	}

	@Override public GregorianCalendar getDateForDrivingExperienceMotorcycle() {
		return null;
	}

	@Override public void setDateForDrivingExperienceMotorcycle(GregorianCalendar gregorianCalendar) {

	}

	@Override public String getUbiMigrationInd() {
		return null;
	}

	@Override public void setUbiMigrationInd(String s) {

	}

	@Override public Integer getGridScore() {
		return null;
	}

	@Override public void setGridScore(Integer integer) {

	}

	@Override public String getAssignedToMcyRiskInd() {
		return null;
	}

	@Override public void setAssignedToMcyRiskInd(String s) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConvictionSystem() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConvictionSystem(Integer newNumberOfMonthsSinceLastMajorConvictionSystem) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConvictionModified() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConvictionModified(Integer newNumberOfMonthsSinceLastMajorConvictionModified) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastMajorConvOccDrvr() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastMajorConvOccDrvr(Integer newNumberOfMonthsSinceLastMajorConvOccDrvr) {

	}

	@Override
	public Integer getNumberOfLiableClaims6MonthsGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims6MonthsGrid(Integer newNumberOfLiableClaims6MonthsGrid) {

	}

	@Override
	public Integer getNumberOfLiableClaims1YearGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims1YearGrid(Integer newNumberOfLiableClaims1YearGrid) {

	}

	@Override
	public Integer getNumberOfLiableClaims2YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims2YearsGrid(Integer newNumberOfLiableClaims2YearsGrid) {

	}

	@Override
	public Integer getNumberOfLiableClaims3YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims3YearsGrid(Integer newNumberOfLiableClaims3YearsGrid) {

	}

	@Override
	public Integer getNumberOfLiableClaims4YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims4YearsGrid(Integer newNumberOfLiableClaims4YearsGrid) {

	}

	@Override
	public Integer getNumberOfLiableClaims5YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfLiableClaims5YearsGrid(Integer newNumberOfLiableClaims5YearsGrid) {

	}

	@Override
	public Integer getNumberOfClaimsLiable6YearsGrid() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable6YearsGrid(Integer newNumberOfClaimsLiable6YearsGrid) {

	}

	@Override
	public Integer getNumberOfClaimsLiable6YearsCvi() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiable6YearsCvi(Integer newNumberOfClaimsLiable6YearsCvi) {

	}

	@Override
	public Integer getNumberOfClaimsLiableCollision9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision9Years(Integer newNumberOfClaimsLiableCollision9Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableCollision15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableCollision15Years(Integer newNumberOfClaimsLiableCollision15Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableLiability9Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableLiability9Years(Integer newNumberOfClaimsLiableLiability9Years) {

	}

	@Override
	public Integer getNumberOfClaimsLiableLiability15Years() {
		return null;
	}

	@Override
	public void setNumberOfClaimsLiableLiability15Years(Integer newNumberOfClaimsLiableLiability15Years) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastForgLiableClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastForgLiableClaim(Integer newNumberOfYearsSinceLastForgLiableClaim) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableClaimGrid() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableClaimGrid(Integer newNumberOfYearsSinceLastLiableClaimGrid) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableCollisionClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableCollisionClaim(Integer newNumberOfYearsSinceLastLiableCollisionClaim) {

	}

	@Override
	public Integer getNumberOfYearsSinceLastLiableLiabilityClaim() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLastLiableLiabilityClaim(Integer newNumberOfYearsSinceLastLiableLiabilityClaim) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableClaimGrid() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableClaimGrid(Integer newNumberOfMonthsSinceLastLiableClaimGrid) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableCollisionClaim() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableCollisionClaim(Integer newNumberOfMonthsSinceLastLiableCollisionClaim) {

	}

	@Override
	public Integer getNumberOfMonthsSinceLastLiableLiabilityClaim() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceLastLiableLiabilityClaim(Integer newNumberOfMonthsSinceLastLiableLiabilityClaim) {

	}
}
