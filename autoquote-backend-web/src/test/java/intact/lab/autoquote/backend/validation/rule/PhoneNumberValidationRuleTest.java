package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

public class PhoneNumberValidationRuleTest {

	private Errors errors;

	@BeforeEach
	public void setUp() {
		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "party");
	}

	@Test
	public void testValidate_Valid_ShouldPass() {
		PhoneNumberValidationRule.validate("5555555555", this.errors, "phoneNumber");
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_ValidDash_ShouldPass() {
		PhoneNumberValidationRule.validate("************", this.errors, "phoneNumber");
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Empty_ShouldFail() {
		PhoneNumberValidationRule.validate("", this.errors, "phoneNumber");
		ValidationTestUtils.assertHasError(this.errors, "phoneNumber", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_InvalidLength_ShouldFail() {
		PhoneNumberValidationRule.validate("5555555", this.errors, "phoneNumber");
		ValidationTestUtils.assertHasError(this.errors, "phoneNumber", BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode());
	}

	@Test
	public void testValidate_InvalidChars_ShouldFail() {
		PhoneNumberValidationRule.validate("aaaaaaaaaa", this.errors, "phoneNumber");
		ValidationTestUtils.assertHasError(this.errors, "phoneNumber", BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode());
	}

	@Test
	public void testValidate_InvalidFirstDigit_ShouldFail() {
		PhoneNumberValidationRule.validate("0555555555", this.errors, "phoneNumber");
		ValidationTestUtils.assertHasError(this.errors, "phoneNumber", BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode());
	}
}
