package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.services.impl.AutoQuoteServiceCache;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class YearValidationRuleTest {

	@InjectMocks
	private YearValidationRule validationRule;

	@Mock
	private AutoQuoteServiceCache autoQuoteServiceCache;

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new VehicleDTO(), "vehicle");
	}

	@Test
	public void testValidate_Valid_ShouldPass() {
		List<ValidValueBO> validValueList = new ArrayList<>();
		validValueList.add(new ValidValueBO("2020", "year"));

		when(this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(anyString(), anyString(), anyString())).thenReturn(validValueList);

		this.validationRule.validate("2020", "QC", "EN", this.errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_NotInteger_ShouldFail() {
		this.validationRule.validate("abcd", "QC", "EN", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "year", BRulesExceptionEnum.Pattern.getErrorCode());
	}

	@Test
	public void testValidate_InvalidValueDomain_ShouldFail() {
		List<ValidValueBO> validValueList = new ArrayList<>();
		validValueList.add(new ValidValueBO("2020", "year"));

		when(this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(anyString(), anyString(), anyString())).thenReturn(validValueList);

		this.validationRule.validate("2010", "QC", "EN", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "year", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode());
	}
}
