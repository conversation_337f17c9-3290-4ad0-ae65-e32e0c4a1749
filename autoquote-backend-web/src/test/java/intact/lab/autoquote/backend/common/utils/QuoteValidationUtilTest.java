package intact.lab.autoquote.backend.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComErrorCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.state.ComState;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

class QuoteValidationUtilTest {

    @Test
    void testComTransactionStatusNotFound() {
        CommunicationObjectModel com = new CommunicationObjectModel();
        com.setContext(new ComContext());
        com.setState(new ComState());
        com.getContext().setProvince(ComProvinceCodeEnum.ONTARIO);

        QuoteValidationUtil.validatePolicyVersion("test-uuid", null, com);

        assertEquals(ComState.ComTransactionStatus.NOT_FOUND, com.getState().getTransactionStatus());
    }

    @Test
    void testComErrorCodeInvalidProvince() {
        PolicyVersion policyVersion = new PolicyVersion();
        policyVersion.setInsurancePolicy(new InsurancePolicy());
        policyVersion.getInsurancePolicy().setManufacturingContext(new ManufacturingContext());
        policyVersion.getInsurancePolicy().getManufacturingContext().setProvince(ProvinceCodeEnum.QUEBEC);

        CommunicationObjectModel com = new CommunicationObjectModel();
        com.setContext(new ComContext());
        com.setState(new ComState());
        com.getContext().setProvince(ComProvinceCodeEnum.ONTARIO);

        QuoteValidationUtil.validatePolicyVersion("test-uuid", policyVersion, com);

        assertEquals(ComErrorCodeEnum.INVALID_PROVINCE, com.getValidationErrors().get(0).getErrorCode());
    }

    @Test
    void testComErrorCodeExpiredQuote() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        Date expiredDate = calendar.getTime();

        PolicyVersion policyVersion = new PolicyVersion();
        policyVersion.setInsurancePolicy(new InsurancePolicy());
        policyVersion.getInsurancePolicy().setManufacturingContext(new ManufacturingContext());
        policyVersion.getInsurancePolicy().getManufacturingContext().setProvince(ProvinceCodeEnum.ONTARIO);
        policyVersion.getInsurancePolicy().setQuotationValidityExpiryDate(expiredDate);

        CommunicationObjectModel com = new CommunicationObjectModel();
        com.setContext(new ComContext());
        com.setState(new ComState());
        com.getContext().setProvince(ComProvinceCodeEnum.ONTARIO);

        QuoteValidationUtil.validatePolicyVersion("test-uuid", policyVersion, com);

        assertEquals(ComErrorCodeEnum.EXPIRED_QUOTE, com.getValidationErrors().get(0).getErrorCode());
    }
}
