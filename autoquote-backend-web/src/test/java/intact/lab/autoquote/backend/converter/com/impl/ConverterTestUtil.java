package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.address.ComAddress;
import com.intact.com.context.ComContext;
import com.intact.com.driver.ComDriver;
import com.intact.com.enums.ComPolicyDiscountCodeEnum;
import com.intact.com.offer.ComCoverageItem;
import com.intact.com.offer.ComItemChoice;
import com.intact.com.offer.ComOffer;
import com.intact.com.offer.enums.ComCoverageNameEnum;
import com.intact.com.state.ComState;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.ContextDTO;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.enums.LanguageEnum;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.common.enums.RoleTypeEnum;
import intact.lab.autoquote.backend.common.model.BusinessContext;
import jakarta.inject.Named;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;


@Named
public class ConverterTestUtil {

	public static ComItemChoice buildComItemChoice() {
		ComItemChoice comItemChoice = new ComItemChoice();
		comItemChoice.setValue(2000);
		return comItemChoice;
	}

	public static ComOffer buildComOffer() {
		ComOffer comOffer = new ComOffer();
		comOffer.setOfferType("CUSTOM");

		Map<String, ComCoverageItem> coverages = new HashMap<>();
		ComCoverageItem comCoverageB2 = new ComCoverageItem();
		comCoverageB2.setCoverageValue("500");
		comCoverageB2.setCoverageInternalCode("B2");
		coverages.put("collision", comCoverageB2);

		ComCoverageItem comCoverageB3 = new ComCoverageItem();
		comCoverageB3.setCoverageValue("250");
		comCoverageB3.setCoverageInternalCode("B3");
		coverages.put("comprehensive", comCoverageB3);
		comOffer.setCoverages(coverages);

		ComCoverageItem comCoverageCrashproof_sav = new ComCoverageItem();
		comCoverageCrashproof_sav.setCoverageName(ComCoverageNameEnum.CRASHPROOF_SAV);
		comCoverageCrashproof_sav.setCoverageValue("false");
		coverages.put("crashproof_sav", comCoverageCrashproof_sav);

		return comOffer;
	}

	public static QuoteDTO buildQuoteDTO() {
		QuoteDTO quote = new QuoteDTO();
		VehicleDTO vehicleDTO = new VehicleDTO();
		vehicleDTO.setId(1);
		quote.getRisks().add(vehicleDTO);

		PartyDTO company = new PartyDTO();
		company.setId(1);
		company.setPartyType(PartyTypeEnum.COMPANY);
		AddressDTO addressDTO = new AddressDTO();
		addressDTO.setPostalCode("J3L6W7");
		addressDTO.setMunicipalityCode("652000");
		company.setAddress(addressDTO);

		PartyDTO owner = new PartyDTO();
		owner.setId(2);
		owner.setPartyType(PartyTypeEnum.PERSON);

		PartyRoleDTO principalDriver = new PartyRoleDTO();
		principalDriver.setRiskId(1);
		principalDriver.setPartyId(2);
		principalDriver.setRoleType(RoleTypeEnum.PRINCIPAL_DRIVER);
		owner.getPartyRoles().add(principalDriver);

		PartyRoleDTO businessOwner = new PartyRoleDTO();
		businessOwner.setPartyId(2);
		businessOwner.setRoleType(RoleTypeEnum.BUSINESS_OWNER);
		owner.getPartyRoles().add(principalDriver);
		owner.getPartyRoles().add(businessOwner);

		quote.getParties().add(company);
		quote.getParties().add(owner);

		DriverDTO driverDTO = new DriverDTO();
		driverDTO.setId(1);
		PartyRoleDTO partyRole = new PartyRoleDTO();
		partyRole.setPartyId(1);
		partyRole.setRoleType(RoleTypeEnum.PRINCIPAL_DRIVER);
		driverDTO.getPartyRoles().add(partyRole);

		DriverDTO ownerDTO = new DriverDTO();
		ownerDTO.setId(2);
		PartyRoleDTO partyRoleOwner = new PartyRoleDTO();
		partyRoleOwner.setPartyId(2);
		partyRoleOwner.setRoleType(RoleTypeEnum.BUSINESS_OWNER);
		ownerDTO.getPartyRoles().add(partyRoleOwner);


		quote.getDrivers().add(driverDTO);
		quote.getDrivers().add(ownerDTO);

		PolicyHolderDTO policyHolderDTO = new PolicyHolderDTO();
		quote.getPolicyHolders().add(policyHolderDTO);
		ContextDTO contextDTO = new ContextDTO();
		Locale locale = Locale.getDefault();
		contextDTO.setLocale(locale);
		contextDTO.setLanguage(LanguageEnum.ENGLISH);
		BusinessContext businessContext = new BusinessContext();
		businessContext.setLineOfBusiness("COMMERCIAL_LINES");
		businessContext.setProvince("qc");
		contextDTO.setBusinessContext(businessContext);
		quote.setContextDTO(contextDTO);
		return quote;
	}

	public static CommunicationObjectModel buildCom() {
		CommunicationObjectModel com = new CommunicationObjectModel();
		ComContext context = new ComContext();
		com.setContext(context);
		com.setAgreementNumber("IR111536320");
		com.setPolicyVersionId(1000562757L);
		com.setPolicyDiscountCode(ComPolicyDiscountCodeEnum.HOMEOWNER);
		ComState comState = new ComState();
		com.setState(comState);

		ComDriver driver = new ComDriver();
		driver.setIsDriver(Boolean.TRUE);
		driver.setPolicyHolderInd(Boolean.FALSE);
		driver.setWebMsgId(1);
		driver.setDriverId(1);
		ComAddress comAddress = new ComAddress();
		driver.setCurrentAddress(comAddress);

		ComDriver policyHolder = new ComDriver();
		policyHolder.setIsDriver(Boolean.FALSE);
		policyHolder.setPolicyHolderInd(Boolean.TRUE);
		policyHolder.setWebMsgId(2);
		policyHolder.setDriverId(2);

		com.getDrivers().add(driver);
		com.getDrivers().add(policyHolder);

		ComVehicle comVehicle = new ComVehicle();
		comVehicle.setWebMsgId(1);
		comVehicle.setPrincipalDriver(1);
		com.getVehicles().add(comVehicle);

		return com;
	}

}
