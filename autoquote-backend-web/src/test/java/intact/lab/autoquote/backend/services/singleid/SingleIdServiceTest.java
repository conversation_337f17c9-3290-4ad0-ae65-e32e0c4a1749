/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid;

import com.ing.canada.cif.service.IClientService;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.singleid.accessmanager.domain.IState;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.ing.canada.singleid.accessmanager.service.IClientAccountService;
import com.ing.canada.singleid.accessmanager.service.IUserAccountService;
import intact.lab.autoquote.backend.services.singleid.impl.SingleIdService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class SingleIdServiceTest {

	/** Class to be tested. */
	@InjectMocks
	private SingleIdService singleIdService;

	/** Test objects **/
	private PolicyVersion currentTestPV;

	/** Mocks **/
	@Mock
	private IClientAccountService mockClientAccountService;

	@Mock
	private IUserAccountService mockUserAccountService;

	@Mock
	private IPartyHelper mockPartyHelper;

	@Mock
	private IClientService mockClientService;

	@Mock
	private IProfileService mockProfileService;

	/**
	 * Sets the up.
	 *
	 * @throws Exception the exception
	 */
	@BeforeEach
	public void setUp() throws Exception {
		when(this.mockProfileService.getClientAccountService()).thenReturn(this.mockClientAccountService);

		// Policy Version setup
		this.currentTestPV = new PolicyVersion(123L);
		InsurancePolicy testInsurancePolicy = new InsurancePolicy();
		this.currentTestPV.setInsurancePolicy(testInsurancePolicy);
	}

	/**
	 * Tear down.
	 *
	 * @throws Exception the exception
	 */
	@AfterEach
	public void tearDown() throws Exception {
		this.currentTestPV = null;
		this.mockProfileService = null;
	}

	/**
	 * Test for {@link SingleIdService#hasActiveLifeAutoOrResidentialProduct(Long)} Case for the named insured to not have an active Life Insurance or
	 * Residential Policy.
	 *
	 * @throws AccessManagerException
	 */
	@Test
	public void testIsRegisterFalseNoElectronicalContact() throws AccessManagerException {
		// Executing the tested method and validating the results
		boolean result = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(123L);
		assertFalse(result, "The method should've returned false - no life insurance or residential policy");
	}

	/**
	 * Test for {@link SingleIdService#hasActiveLifeAutoOrResidentialProduct(Long)} Case for the residential policy state to be STATE_RESIDENTIALPOLICY_VALID.
	 *
	 * @throws AccessManagerException
	 */
	@Test
	public void testIsRegisterFalseNoElectronicalContact_ResidentialStateValid() throws AccessManagerException {
		// Setting the residential policy state to the desired value
		when(this.mockClientAccountService.getResidentialPolicyState(any(Long.class))).thenReturn(IState.RESIDENTIALPOLICY_VALID);

		// Executing the tested method and validating the results
		boolean result = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(123L);
		assertTrue(result, "The method should've returned true - STATE_RESIDENTIALPOLICY_VALID");
	}

	/**
	 * Test for {@link SingleIdService#hasActiveLifeAutoOrResidentialProduct(Long)} Case for the life insurance state to be STATE_LIFEPOLICY_VALID
	 *
	 * @throws AccessManagerException
	 */
	@Test
	public void testIsRegisterFalseNoElectronicalContact_LifepolicyStateValid() throws AccessManagerException {
		// Setting the life insurance state to the desired value
		when(this.mockClientAccountService.getLifeInsuranceState(any(Long.class))).thenReturn(IState.LIFEPOLICY_VALID);

		// Executing the tested method and validating the results
		boolean result = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(123L);
		assertTrue(result, "The method should've returned true - STATE_LIFEPOLICY_VALID");
	}

	/**
	 * Test for {@link SingleIdService#hasActiveLifeAutoOrResidentialProduct(Long)} Case for the life insurance state to be STATE_AUTOPOLICY_VALID
	 *
	 * @throws AccessManagerException
	 */
	@Test
	public void testIsRegisterFalseNoElectronicalContact_AutoPolicyStateValid() throws AccessManagerException {
		// Setting the life insurance state to the desired value
		when(this.mockClientAccountService.getAutoPolicyState(any(Long.class))).thenReturn(IState.AUTOPOLICY_VALID);

		// Executing the tested method and validating the results
		boolean result = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(123L);
		assertTrue(result, "The method should've returned true - STATE_AUTOPOLICY_VALID");
	}

	/**
	 * Test for {@link SingleIdService#hasActiveLifeAutoOrResidentialProduct(Long)} Case for the life insurance state to be STATE_AUTOPOLICY_FUTURE
	 *
	 * @throws AccessManagerException
	 */
	@Test
	public void testIsRegisterFalseNoElectronicalContact_AutoPolicyStateFuture() throws AccessManagerException {
		// Setting the life insurance state to the desired value
		when(this.mockClientAccountService.getAutoPolicyState(any(Long.class))).thenReturn(IState.AUTOPOLICY_FUTURE);

		// Executing the tested method and validating the results
		boolean result = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(123L);
		assertTrue(result, "The method should've returned true - STATE_AUTOPOLICY_FUTURE");
	}
}
