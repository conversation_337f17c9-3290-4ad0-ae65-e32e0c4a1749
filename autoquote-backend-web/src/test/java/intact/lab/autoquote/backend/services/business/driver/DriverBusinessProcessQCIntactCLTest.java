package intact.lab.autoquote.backend.services.business.driver;

import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.driver.BR10617_TooManyClaims6years;
import com.intact.business.rules.driver.BR759_ConvictionsHistory;
import com.intact.business.rules.driver.BR7996_TooManyAtFaultClaims6years;
import com.intact.business.rules.driver.BR7998_TooManyClaims2years;
import com.intact.business.rules.exception.RuleExceptionResult;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.driver.impl.DriverBusinessProcessQCIntactCL;
import intact.lab.autoquote.backend.services.singleid.IProfileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class DriverBusinessProcessQCIntactCLTest {

    @InjectMocks
    private DriverBusinessProcessQCIntactCL driverBusinessProcess;

    @Mock
    private IProfileService profileService;

    @Mock
    private BR10617_TooManyClaims6years mockBR10617;

    @Mock
    private BR759_ConvictionsHistory mockBR759;

    @Mock
    private BR7998_TooManyClaims2years mockBR7998;

    @Mock
    private BR7996_TooManyAtFaultClaims6years mockBR7996;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(driverBusinessProcess, "profileService", profileService);
    }

    /**
     * Test method for
     * {@link DriverBusinessProcessQCIntactCL#manageProfile(PolicyVersion, ApplicationEnum)}
     *
     * @throws AutoQuoteRoadBlockException any {@link AutoQuoteRoadBlockException}
     * @throws AccessManagerException      any {@link AccessManagerException}
     */
    @Test
    public void testManageProfile() throws AutoQuoteRoadBlockException, AccessManagerException, SingleIdActiveProductException {
        PolicyVersion testPV = new PolicyVersion(123L);

        // Executing the tested method and verifying the stub calls
        this.driverBusinessProcess.manageProfile(testPV, ApplicationEnum.INTACT);
        verify(this.profileService, times(1)).manageProfile(any(Long.class),
                any(RoadBlockExceptionContextEnum.class), any(ApplicationEnum.class));
    }

    /**
     * Test method for
     * {@link DriverBusinessProcessQCIntactCL#manageProfile(PolicyVersion, ApplicationEnum)}
     * Case for an AutoquoteRoadBlockException to be raised during the method's execution.
     *
     * @throws AutoQuoteRoadBlockException any {@link AutoQuoteRoadBlockException}
     * @throws AccessManagerException      any {@link AccessManagerException}
     */
    @Test
    void ManageProfile_Should_ThrowSingleIdActiveProductException_WhenRoadBlockExceptionRaised() throws AutoQuoteRoadBlockException, AccessManagerException, SingleIdActiveProductException {
        PolicyVersion testPV = new PolicyVersion(123L);

        doThrow(AutoQuoteRoadBlockException.class).when(this.profileService).manageProfile(any(Long.class),
                any(RoadBlockExceptionContextEnum.class), any(ApplicationEnum.class));

        assertThrows(SingleIdActiveProductException.class, () -> {
            this.driverBusinessProcess.manageProfile(testPV, ApplicationEnum.INTACT);
        });

        verify(this.profileService, times(1)).manageProfile(any(Long.class),
                any(RoadBlockExceptionContextEnum.class), any(ApplicationEnum.class));
    }

    @Test
    public void testValidateQuickQuoteSoftRoadblock() throws Exception {

        DriverComplementInfo driverComplementInfo = new DriverComplementInfo();
        driverComplementInfo.setParty(new Party());
        List<RuleExceptionResult> roadblocks = this.driverBusinessProcess.validateQuickQuoteSoftRoadblock(driverComplementInfo, ProvinceCodeEnum.QUEBEC);

        verify(this.mockBR10617, times(1)).validateBR(any(Party.class));
        verify(this.mockBR759, times(1)).validateBR(any(DriverComplementInfo.class));
        verify(this.mockBR7998, times(1)).validateBR(any(Party.class));
        verify(this.mockBR7996, times(1)).validateBR(any(Party.class));

        assertEquals(4, roadblocks.size());
    }
}
