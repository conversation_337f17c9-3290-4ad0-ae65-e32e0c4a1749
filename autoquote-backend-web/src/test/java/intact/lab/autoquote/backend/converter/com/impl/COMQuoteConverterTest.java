package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.driver.ComDriver;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.converter.impl.COMPartyRoleConverter;
import intact.lab.autoquote.backend.converter.impl.COMQuoteConverter;
import intact.lab.autoquote.backend.converter.impl.MunicipalityHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class COMQuoteConverterTest {

	@Mock
	protected MunicipalityHelper municipaliyHelper;
	@InjectMocks
	private COMQuoteConverter comQuoteConverter;
	@Mock
	private ICOMConverter<PolicyHolderDTO, ComDriver> comPolicyHolderConverter;
	@Mock
	private ICOMConverter<PartyDTO, ComDriver> comPartyConverter;
	@Mock
	private ICOMConverter<VehicleDTO, ComVehicle> comVehicleConverter;
	@Mock
	private ICOMConverter<DriverDTO, ComDriver> comDriverConverter;
	@Mock
	private COMPartyRoleConverter comPartyRoleConverter;

	@BeforeEach
	public void initMocks() {
		MockitoAnnotations.openMocks(this);
		when(this.comDriverConverter.toDTO(any(ComDriver.class))).thenReturn(new DriverDTO());
		when(this.comPolicyHolderConverter.toDTO(any(ComDriver.class))).thenReturn(new PolicyHolderDTO());
		when(this.comPartyConverter.toDTO(any(ComDriver.class))).thenReturn(new PartyDTO());
		when(this.comVehicleConverter.toDTO(any(ComVehicle.class))).thenReturn(new VehicleDTO());

		ComMunicipalityInfo comMunicipalityInfo = new ComMunicipalityInfo();
		comMunicipalityInfo.setStreetType("chemin");
		comMunicipalityInfo.setStreetName("Des Patriotes");
		comMunicipalityInfo.setStreetDirection("East");
		List<ValidValue> municipalities = new ArrayList<>();
		ValidValue municipality = new ValidValue("652000", "testLabel", 0);
		municipalities.add(municipality);
		comMunicipalityInfo.setMunicipalities(municipalities);
	}

	@Test
	public void testToDTO() throws Exception {
		CommunicationObjectModel com = ConverterTestUtil.buildCom();
		QuoteDTO quote = this.comQuoteConverter.toDTO(com);
		assertNotNull(quote);
        assertEquals(1, quote.getDrivers().size());
        assertEquals(2, quote.getParties().size());
        assertEquals(1, quote.getPolicyHolders().size());
        assertEquals(1, quote.getRisks().size());
	}

}
