package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test class for {@link PolicyHolderDTOValidator}
 */
@ExtendWith(MockitoExtension.class)
public class PolicyHolderDTOValidatorTest {

	// Class under test
	@InjectMocks
	private PolicyHolderDTOValidator policyHolderDTOValidator;

	private PolicyHolderDTO policyHolderDTO;

	private Errors errors;

	/**
	 * Private method used to validate the errors rejected by the test class
	 *
	 * @param errors         {@link Errors}
	 * @param errorField     The field on which the error should be
	 * @param BRuleExpection The error code for the expected error
	 * @param triggerValue   The String that was send to trigger the error
	 */
	private static void assertHasError(Errors errors, String errorField, String BRuleExpection, String triggerValue) {

		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("%s error", errorField));
		assertEquals(BRuleExpection, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleExpection));

	}

	@BeforeEach
	public void setUp() throws Exception {
		policyHolderDTO = new PolicyHolderDTO();
		errors = new BeanPropertyBindingResult(policyHolderDTO, "policyHolderDTO");
	}

	@AfterEach
	public void tearDown() throws Exception {
		policyHolderDTOValidator = null;
		policyHolderDTO = null;
		errors = null;

	}

	/**
	 * Test for {@link PolicyHolderDTOValidator#validate(PolicyHolderDTO, Errors)}
	 */
	@Test
	public void testValidate_partyIdIsNull_rejectedIsBlank() throws Exception {

		// Given
		policyHolderDTO.setPartyId(null);

		// When
		policyHolderDTOValidator.validate(policyHolderDTO, errors);

		// Then
		String errorField = "partyId";
		String BRuleExpection = BRulesExceptionEnum.NotBlank.getErrorCode();

		assertHasError(errors, errorField, BRuleExpection, "null");

	}

	/**
	 * Test for {@link PolicyHolderDTOValidator#validate(PolicyHolderDTO, Errors)}
	 */
	@Test
	public void testValidate_partyIdIsValid_happyPath() throws Exception {

		// Given
		policyHolderDTO.setPartyId(99);

		// When
		policyHolderDTOValidator.validate(policyHolderDTO, errors);

		// Then
		assertFalse(this.errors.hasErrors(), "PolicyHolderDTO party id is not null. No error should be present");

	}
}
