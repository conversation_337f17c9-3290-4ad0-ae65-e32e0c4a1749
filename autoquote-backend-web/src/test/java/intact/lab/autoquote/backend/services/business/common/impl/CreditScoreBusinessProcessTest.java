package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IPolicyVersionService;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOM;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.rating.ICreditScoreService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StopWatch;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CreditScoreBusinessProcessTest {

	/** Class to be tested.*/
	@InjectMocks
	private CreditScoreBusinessProcess creditScoreBusinessProcess;

	/** Test objects **/
	private PolicyVersion currentTestPV;

	private PolicyHolder currentTestPolicyHolder;

	@Mock
	private ICreditScoreService mockCreditScoreService;

	@Mock
	private IDataMediatorToPL mockDataMediatorToPL;

	@Mock
	private DataMediatorToSOM mockDataMediatorToSOM;

	@Mock
	private IPolicyVersionService mockPolicyVersionService;

	@Mock
	private DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

	@BeforeEach
	public void setUp() throws Exception {		
		// PolicyVersion setup
		this.currentTestPV = new PolicyVersion(123L);
		
		InsurancePolicy testInsurancePolicy = new InsurancePolicy();
		ManufacturingContext testContext = new ManufacturingContext();
		testContext.setProvince(ProvinceCodeEnum.ALBERTA);
		testInsurancePolicy.setManufacturingContext(testContext);
		testInsurancePolicy.setManufacturerCompany(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		
		this.currentTestPV.setInsurancePolicy(testInsurancePolicy);
		
		// Policy Holder setup
		this.currentTestPolicyHolder = new PolicyHolder();
		this.currentTestPolicyHolder.setPolicyHolderType(PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED);
		Party testParty = new Party();
		testParty.addCreditScore(new CreditScore());
		this.currentTestPolicyHolder.setParty(testParty);
		this.currentTestPV.addPolicyHolder(currentTestPolicyHolder);

		when(dataMediatorToSOMServiceFactory.getService("dataMediatorToSOM")).thenReturn(this.mockDataMediatorToSOM);
	}
	
	@AfterEach
	public void tearDown() throws Exception {
		this.creditScoreBusinessProcess = null;
		this.currentTestPolicyHolder = null;
		this.currentTestPV = null;
		this.mockPolicyVersionService = null;
	}
	
	/**
	 * Test method for
	 * @throws AutoquoteBusinessException
	 * @throws AutoquoteRatingException 
	 */
	@Test
	public void testCallTheCreditScore() throws AutoquoteBusinessException, AutoquoteRatingException {

		// Executing the tested method and validating the results
		this.creditScoreBusinessProcess.callTheCreditScore(this.currentTestPV);
		assertEquals((Integer)0, (Integer)this.currentTestPolicyHolder.getParty().getCreditScores().size());
		verify(this.mockCreditScoreService, times(1)).callTheCreditScore(any(),
				any(ManufacturingContext.class), any(StopWatch.class));
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}
	
	/**
	 * Test method for
	 * Case for GreyPower to be the manufacturer company
	 * @throws AutoquoteBusinessException 
	 * @throws AutoquoteRatingException 
	 */
	@Test
	public void testCallTheCreditScore_GreyPower() throws AutoquoteBusinessException, AutoquoteRatingException{

		// Setting the manufacturer company to Grey Power for this test
		this.currentTestPV.getInsurancePolicy().setManufacturerCompany(ManufacturerCompanyCodeEnum.GREY_POWER);
		
		// Executing the tested method and validating the results
		this.creditScoreBusinessProcess.callTheCreditScore(this.currentTestPV);
	
		verify(this.mockCreditScoreService, times(1)).callTheCreditScoreWithoutClientEligibility(any(),
				any(ManufacturingContext.class), any(StopWatch.class));
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}
	
	/**
	 * Test method for
	 * Case for ING_WESTERN_REGION to be the manufacturer company
	 * @throws AutoquoteBusinessException 
	 * @throws AutoquoteRatingException 
	 */
	@Test
	public void testCallTheCreditScore_IngWesternRegion() throws AutoquoteBusinessException, AutoquoteRatingException{

		// Setting the manufacturer company to Grey Power for this test
		this.currentTestPV.getInsurancePolicy().setManufacturerCompany(ManufacturerCompanyCodeEnum.ING_WESTERN_REGION);
		
		// Executing the tested method and validating the results
		this.creditScoreBusinessProcess.callTheCreditScore(this.currentTestPV);
		
		verify(this.mockCreditScoreService, times(1)).callTheCreditScoreWithoutClientEligibility(any(),
				any(ManufacturingContext.class), any(StopWatch.class));
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}
	
	/**
	 * Test method for
	 * Case for a general exception to be raised during the method's execution.
	 * @throws AutoquoteBusinessException 
	 * @throws AutoquoteRatingException 
	 */
	@Test
	void testCallTheCreditScore_raiseGeneralException() throws AutoquoteRatingException {
		RuntimeException testException = new RuntimeException("Test exception thrown for the test \"testCallTheCreditScore_raiseGeneralException\"");
		doThrow(testException).when(this.mockCreditScoreService).callTheCreditScore(
				any(com.ing.canada.som.interfaces.agreement.PolicyVersion.class),
				any(ManufacturingContext.class),
				any(StopWatch.class));

		// Executing the tested method and asserting the exception
		AutoquoteBusinessException exception = assertThrows(AutoquoteBusinessException.class, () -> {
			this.creditScoreBusinessProcess.callTheCreditScore(this.currentTestPV);
		});
	}
}
