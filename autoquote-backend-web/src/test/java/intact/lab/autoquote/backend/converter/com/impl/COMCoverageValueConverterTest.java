package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.offer.ComItemChoice;
import intact.lab.autoquote.backend.common.dto.CoverageValueDTO;
import intact.lab.autoquote.backend.converter.impl.COMCoverageValueConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class COMCoverageValueConverterTest {

	@InjectMocks
	private COMCoverageValueConverter comCoverageValueConverter;

	@Test
	public void testToDTO() throws Exception {
		ComItemChoice comItemChoice = ConverterTestUtil.buildComItemChoice();
		CoverageValueDTO comValueDTO = this.comCoverageValueConverter.toDTO(comItemChoice);
		assertNotNull(comValueDTO);
        assertEquals(2000, (int) comValueDTO.getAmount());
	}

}
