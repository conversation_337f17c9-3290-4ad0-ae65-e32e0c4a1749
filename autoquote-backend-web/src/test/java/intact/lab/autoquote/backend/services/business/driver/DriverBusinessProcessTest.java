package intact.lab.autoquote.backend.services.business.driver;

import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import intact.lab.autoquote.backend.services.business.driver.impl.DriverBusinessProcessONIntactCL;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DriverBusinessProcessTest {

    @InjectMocks
    private DriverBusinessProcessONIntactCL driverBusinessProcess;

    @Mock
    private IPolicyVersionHelper policyVersionHelper;

    @Mock
    private PolicyVersion mockPolicyVersion;

    @Test
    void testGetDriversCount_WithDrivers() {
        Set<Party> mockDrivers = Set.of(mock(Party.class), mock(Party.class));
        when(policyVersionHelper.getIndividualParties(mockPolicyVersion)).thenReturn(mockDrivers);

        int result = driverBusinessProcess.getDriversCount(mockPolicyVersion);

        assertEquals(2, result, "Expected driver count to be 2");
        verify(policyVersionHelper).getIndividualParties(mockPolicyVersion);
    }

    @Test
    void testGetDriversCount_NoDrivers() {
        when(policyVersionHelper.getIndividualParties(mockPolicyVersion)).thenReturn(Collections.emptySet());

        int result = driverBusinessProcess.getDriversCount(mockPolicyVersion);

        assertEquals(0, result, "Expected driver count to be 0");
        verify(policyVersionHelper).getIndividualParties(mockPolicyVersion);
    }

    @Test
    void testGetDriversCount_NullDrivers() {
        when(policyVersionHelper.getIndividualParties(mockPolicyVersion)).thenReturn(null);

        int result = driverBusinessProcess.getDriversCount(mockPolicyVersion);

        assertEquals(0, result, "Expected driver count to be 0 when drivers are null");
        verify(policyVersionHelper).getIndividualParties(mockPolicyVersion);
    }
}


