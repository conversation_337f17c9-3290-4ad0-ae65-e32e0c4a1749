/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.driver.AffinityGroupRepositoryEntry;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyScenarioCodeEnum;
import com.ing.canada.plp.domain.enums.DriverTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.RatingRiskTypeApplyCodeEnum;
import com.ing.canada.plp.domain.enums.UBIStatusCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.ICoverageService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.som.interfaces.agreement.PriorCarrierPolicyInfo;
import com.ing.canada.som.interfaces.businessTransaction.ReferenceDate;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Producer;
import com.ing.canada.ss.base.BaseException;
import intact.lab.autoquote.backend.mocks.MockClaim;
import intact.lab.autoquote.backend.mocks.MockDriver;
import intact.lab.autoquote.backend.mocks.MockInsurancePolicy;
import intact.lab.autoquote.backend.mocks.MockInsuranceRisk;
import intact.lab.autoquote.backend.mocks.MockParty;
import intact.lab.autoquote.backend.services.rating.ISelectOfferType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 
 * Test class for autoquote/intact/rating/impl/RatingService class. Uses custom mocks for SOM objects from
 * Uses a test implementation of RatingService to access the different protected and private methods to test :
 * 
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class RatingServiceTest {
	/** Other general objects */
	private PolicyVersion currentPolicyVersion;

	private com.ing.canada.som.interfaces.agreement.PolicyVersion currentMockSomPV;

	private InsuranceRisk currentPLRisk;

	private com.ing.canada.som.interfaces.risk.InsuranceRisk currentSomRisk;

	private com.ing.canada.som.interfaces.partyRoleInRisk.Driver currentTestDriver;

	private com.ing.canada.som.interfaces.businessTransaction.ReferenceDate currentRefDate;

	@InjectMocks
	private RatingService ratingService = new RatingServiceTestImpl();

	/** Mocks */
	@Mock
	private ISelectOfferType mockSelectOfferType;

	@Mock
	private IPartyHelper mockPartyHelper;

	@Mock
	private IPolicyVersionHelper policyVersionHelper;

	@Mock
	private IPolicyVersionService mockPolicyVersionService;

	@Mock
	private IQuotationService mockQuotationService;

	@Mock
	private ICoverageService mockCoverageService;

	@Mock
	private ICoverageHelper mockCoverageHelper;

	/**
	 * @throws Exception
	 */
	@BeforeEach
	public void setUp() throws Exception {
		// PL Policy version setup
		this.currentPolicyVersion = new PolicyVersion();
		this.currentPolicyVersion.setId(222L);
		InsurancePolicy insPolicy = new InsurancePolicy();
		insPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		this.currentPolicyVersion.setInsurancePolicy(insPolicy);
	}

	/**
	 * Test method for
	 * {@link RatingService#setPolicyTermAndExpiry(PolicyVersion, com.ing.canada.som.interfaces.agreement.PolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testSetPolicyTermAndExpiry() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		PolicyTermInMonthsEnum expectedTerm = PolicyTermInMonthsEnum.TWELVE_MONTHS;
		when(this.mockQuotationService.calculatePolicyTermInMonths(any(PolicyVersion.class))).thenReturn(expectedTerm);

		// Creating the date will be set in the policy version
		Date expectedDate = new Date();
		this.currentPolicyVersion.setPolicyInceptionDate(expectedDate);

		Calendar expectedCal = Calendar.getInstance();
		expectedCal.setTime(expectedDate);
		expectedCal.add(Calendar.MONTH, expectedTerm.getCode());

		// Executing the tested method and validating the results
		this.ratingService.setPolicyTermAndExpiry(this.currentPolicyVersion, this.currentMockSomPV);

		assertEquals(expectedCal.getTime(), this.currentPolicyVersion.getPolicyExpiryDate());
	}

	/**
	 * Test method for
	 * {@link RatingService#selectOfferForRating(com.ing.canada.som.interfaces.agreement.PolicyVersion, OfferTypeCodeEnum)}
	 * Case for there to be no match between the som risk's offer type and the one passed as parameter.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testSelectOfferForRating_NoOfferTypeMatch() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setting the SOM risk's offer type to does not match the one passed as parameter
		this.currentSomRisk.setOfferType(OfferTypeCodeEnum.CUSTOM.getCode());
		// Executing the tested method and validating the results
		this.ratingService.selectOfferForRating(this.currentMockSomPV, OfferTypeCodeEnum.RECOMMENDED);

		assertNull(this.currentSomRisk.getRiskSelectedInd(), "The SOM risk's selected index should've been set to null");
	}

	/**
	 * Test method for {@link RatingService#preprocessPL(PolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPreprocessPL() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Policy Holder setup with a number of stability months for the test
		PolicyHolder testHolder = new PolicyHolder();
		Integer expectedStabilityMonths = 6;
		testHolder.setNumberStabilityMonths(expectedStabilityMonths);
		when(this.policyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(testHolder);

		Boolean expectedLicenseRevokeInd = false;
		this.currentPolicyVersion.setLicenseSuspendedOrRevokedIndicator(expectedLicenseRevokeInd);// Set for better test
		                                                                                          // validation

		// Executing the tested method and verifying the results
		this.ratingService.preprocessPL(this.currentPolicyVersion);

		assertEquals(expectedLicenseRevokeInd, this.currentPLRisk.getLicenseSuspensionPrincipalIndicator());
		assertEquals((Short) expectedStabilityMonths.shortValue(), this.currentPLRisk.getNumberOfStabilityMonths());
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for {@link RatingService#preprocessPL(PolicyVersion)} Case for the
	 * PL insurance risk to have a party role in risk with PRINCIPAL as it's driver type.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPreprocessPL_PrincipalDriver() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		PolicyHolder testHolder = new PolicyHolder();
		when(this.policyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(testHolder); // To
		                                                                                                                     // avoid
		                                                                                                                     // NullPointerException

		// Creating the party role in risk to be set in the insurance risk
		PartyRoleInRisk testRoleInRisk = new PartyRoleInRisk();
		testRoleInRisk.setDriverType(DriverTypeCodeEnum.PRINCIPAL);
		this.currentPLRisk.addPartyRoleInRisk(testRoleInRisk);

		// Executing the tested method and verifying the results
		this.ratingService.preprocessPL(this.currentPolicyVersion);

		// Since the rating risk created is not caught by an object, simply verify if the method executed completely
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for {@link RatingService#preprocessPL(PolicyVersion)} Case for the
	 * PL insurance risk to have a party role in risk with OCCASIONAL as it's driver type.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPreprocessPL_OccasionalDriver() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		PolicyHolder testHolder = new PolicyHolder();
		when(this.policyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(testHolder); // To
		                                                                                                                     // avoid
		                                                                                                                     // NullPointerException

		// Creating the party role in risk to be set in the insurance risk
		PartyRoleInRisk testRoleInRisk = new PartyRoleInRisk();
		testRoleInRisk.setDriverType(DriverTypeCodeEnum.OCCASIONAL);
		this.currentPLRisk.addPartyRoleInRisk(testRoleInRisk);

		// Executing the tested method and verifying the results
		this.ratingService.preprocessPL(this.currentPolicyVersion);

		// Since the rating risk created is not caught by an object, simply verify if the method executed completely
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for {@link RatingService#preprocessPL(PolicyVersion)} Case for a
	 * current address to be found by the party helper.
	 * 
	 * @throws Exception
	 */
	@Disabled
	@Test
	public void testPreprocessPL_CurrentAddressFound() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		PolicyHolder testHolder = new PolicyHolder();
		when(this.policyVersionHelper.getPrincipalInsuredPolicyHolder(any(PolicyVersion.class))).thenReturn(testHolder); // To
		                                                                                                                     // avoid
		                                                                                                                     // NullPointerException

		// Current address setup with specifications
		Address testAddress = new Address();
		MunicipalityDetailSpecification testSpec = new MunicipalityDetailSpecification();
		String expectedTerritoryStat = "testStat";
		String expectedMunicipalAdjustmentAutoCode = "testAdjCode";
		testSpec.setAutomobileTerritoryStat(expectedTerritoryStat);
		testSpec.setMunicipalAdjustmentAuto(expectedMunicipalAdjustmentAutoCode);
		testAddress.setMunicipalityDetailSpecification(testSpec);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any(Party.class))).thenReturn(testAddress);

		// Executing the tested method and verifying the results
		this.ratingService.preprocessPL(this.currentPolicyVersion);

		assertEquals(testAddress, this.currentPLRisk.getAddress());
		assertEquals(expectedTerritoryStat, this.currentPLRisk.getTerritoryStat());
		assertEquals(expectedMunicipalAdjustmentAutoCode, this.currentPLRisk.getMunicipalAdjustment());
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessPLForAlbertaAndOntario(PolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPreprocessPLForAlbertaAndOntario() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Current address setup with specifications
		Address testAddress = new Address();
		MunicipalityDetailSpecification testSpec = new MunicipalityDetailSpecification();
		String expectedTerritoryStat = "testStat";
		String expectedMunicipalAdjustmentAutoCode = "testAdjCode";
		testSpec.setAutomobileTerritoryStat(expectedTerritoryStat);
		testSpec.setMunicipalAdjustmentAuto(expectedMunicipalAdjustmentAutoCode);
		testAddress.setMunicipalityDetailSpecification(testSpec);
		when(this.mockPartyHelper.getCurrentResidentialAddress(any())).thenReturn(testAddress);

		// Date setup for better validation
		Date expectedDate = new Date();
		this.currentPolicyVersion.setRatingDate(expectedDate);
		BusinessTransaction testTransaction = new BusinessTransaction();
		testTransaction.setTransactionEffectiveDateTime(expectedDate);
		this.currentPolicyVersion.setBusinessTransaction(testTransaction);

		// Executing the tested method and verifying the results
		this.ratingService.preprocessPLForAlbertaAndOntario(this.currentPolicyVersion);

		assertEquals(expectedDate, this.currentPLRisk.getRatingDate());
		assertEquals(expectedDate, this.currentPLRisk.getEffectiveDate());
		assertEquals(testAddress, this.currentPLRisk.getAddress());
		assertEquals(expectedTerritoryStat, this.currentPLRisk.getTerritoryStat());
		assertEquals(expectedMunicipalAdjustmentAutoCode, this.currentPLRisk.getMunicipalAdjustment());
	}

	/**
	 * Test method for
	 * {@link RatingService#createRiskOffers(PolicyVersion plPolicyVersion, com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateRiskOffers() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Adding a PolicyOfferRating to the PL policy version to validate the results
		PolicyOfferRating testOfferRating = new PolicyOfferRating();
		this.currentPolicyVersion.addPolicyOfferRating(testOfferRating);

		// Setup to access the desired branch
		List<com.ing.canada.som.interfaces.agreement.PolicyVersion> testSomPVList = new ArrayList<com.ing.canada.som.interfaces.agreement.PolicyVersion>();
		testSomPVList.add(this.currentMockSomPV);
		when(this.currentMockSomPV.getThePolicyVersionOffer()).thenReturn(testSomPVList);

		// Executing the tested method and verifying the stub calls
		this.ratingService.createRiskOffers(this.currentPolicyVersion, this.currentMockSomPV);

		Iterator<InsuranceRiskOffer> iterator = testOfferRating.getInsuranceRisksOffers().iterator();
		InsuranceRiskOffer resultIRO = iterator.next();
		assertTrue(resultIRO.getRecalculatableOfferIndicator(), "The test offer rating should have an Insurance risk offer with it's RecalculatableOfferIndicator set to true.");
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for {@link RatingService#getInsuredGroup(PolicyVersion) }
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetInsuredGroup() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setup for the method to return the group repository entry's insured group
		this.currentPolicyVersion.setAffinityGroupRepositoryEntry(mock(AffinityGroupRepositoryEntry.class));
		GroupRepositoryEntry testRepoEntry = new GroupRepositoryEntry();
		String expectedInsuredGroup = "TestInsuredGroup";
		testRepoEntry.setInsuredGroup(expectedInsuredGroup);
		when(this.mockPolicyVersionService.getGroupRepositoryEntry(any(PolicyVersion.class))).thenReturn(testRepoEntry);

		// Executing the tested method and verifying the stub calls
		String resultInsuredGroup = this.ratingService.getInsuredGroup(this.currentPolicyVersion);

		assertEquals(expectedInsuredGroup, resultInsuredGroup);
	}

	/**
	 * Test method for {@link RatingService#getInsuredGroup(PolicyVersion) } Case for
	 * no affinity group repository entry to be set.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetInsuredGroup_NoAffinityGroupRepositoryEntry() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Executing the tested method and verifying the stub calls
		String resultInsuredGroup = this.ratingService.getInsuredGroup(this.currentPolicyVersion);

		assertNull(resultInsuredGroup, "The resulting Insured group should've been null");
	}

	/**
	 * Test method for {@link RatingService#getInsuredGroup(PolicyVersion) }
	 * 
	 * @throws Exception Case for no group repository entry to be found, so null should be returned (from
	 *             getInsuredGroupBeforeOffer method).
	 */
	@Test
	public void testGetInsuredGroup_NoGroupRepositoryEntry() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setup to access the desired branch
		this.currentPolicyVersion.setAffinityGroupRepositoryEntry(mock(AffinityGroupRepositoryEntry.class));

		// Executing the tested method and verifying the stub calls
		String resultInsuredGroup = this.ratingService.getInsuredGroup(this.currentPolicyVersion);

		assertNull(resultInsuredGroup, "The resulting Insured group should've been null");
	}

	/**
	 * Test method for {@link RatingService#getInsuredGroupBeforeOffer(PolicyVersion) }
	 * Case for a Group repository entry to be found.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetInsuredGroupBeforeOffer() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setup for the method to return the group repository entry's insured group
		this.currentPolicyVersion.setAffinityGroupRepositoryEntry(mock(AffinityGroupRepositoryEntry.class));
		GroupRepositoryEntry testRepoEntry = new GroupRepositoryEntry();
		String expectedInsuredGroup = "TestInsuredGroup";
		testRepoEntry.setInsuredGroup(expectedInsuredGroup);
		when(this.mockPolicyVersionService.getGroupRepositoryEntryBeforeOffer(any(PolicyVersion.class))).thenReturn(testRepoEntry);

		// Executing the tested method and verifying the stub calls
		String resultInsuredGroup = this.ratingService.getInsuredGroupBeforeOffer(this.currentPolicyVersion);

		assertEquals(expectedInsuredGroup, resultInsuredGroup);
	}

	/**
	 * Test method for {@link RatingService#postprocessPL(PolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPostprocessPL() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Adding a business transaction to the pl policy version for better validation
		BusinessTransaction testTransaction = new BusinessTransaction();
		Short initialSeq = 42;
		Short expectedSeq = initialSeq++;
		testTransaction.setLastRatingSequence(expectedSeq);

		// Executing the tested method and verifying the stub calls
		this.ratingService.postprocessPL(this.currentPolicyVersion);

		assertTrue(this.currentPLRisk.getInsuranceRiskOfferSystemSelectedIndicator(), "The insurance risk's InsuranceRiskOfferSystemSelectedIndicator should've been true.");
		assertEquals(expectedSeq, testTransaction.getLastRatingSequence());
		verify(this.mockPolicyVersionService, times(1)).persistCascadeAll(any(PolicyVersion.class));
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion, PolicyVersion)}
	 * 
	 * @throws Exception
	 */
	@Disabled
	public void testPreprocessSOM() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		this.setupGeneralPreprocessSOMTest("P");

		com.ing.canada.som.interfaces.physicalObject.Vehicle testVehicle = this.currentSomRisk.getTheVehicle();
		Integer expectedReading = 42;
		testVehicle.setOdometerReading(expectedReading);

		// Executing the tested method and verifying the stub calls
		this.ratingService.preprocessSOM(this.currentMockSomPV, this.currentPolicyVersion);

		assertEquals(0, this.currentSomRisk.getNumberOfAdditionalInterests());
		assertEquals("P", testVehicle.getRatingTableIdentification());
		assertEquals("A", testVehicle.getActionTaken());
		assertEquals(expectedReading, testVehicle.getPurchaseOdometerReading());
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion, PolicyVersion)}
	 * Case for the SOM driver to be of OCCASIONAL type
	 * 
	 * @throws Exception
	 */
	@Disabled
	public void testPreprocessSOM_OccasionalDriver() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		this.setupGeneralPreprocessSOMTest("O");

		// Driver needs to be a male below age 25
		this.currentTestDriver.setAge(18);
		this.currentTestDriver.getTheParty().setSex("M");

		// Setup for better test validation
		Integer expectedNumberConvictions = 2;
		this.currentTestDriver.getTheParty().getTheDriverComplementInfo().setNumberOfMinorConvictions3Years(expectedNumberConvictions);

		// Executing the tested method and verifying the stub calls
		this.ratingService.preprocessSOM(this.currentMockSomPV, this.currentPolicyVersion);
		assertEquals(expectedNumberConvictions, this.currentSomRisk.getNumberOfMinorConvictionsOccasionalDriver3Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion, PolicyVersion)}
	 * Case for a SOM PriorCarrierPolicyInfo to be present in the SOM policy version.
	 * 
	 * @throws Exception
	 */
	@Disabled
	public void testPreprocessSOM_WithPriorCarrierPolicyInfo() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		this.setupGeneralPreprocessSOMTest("P");

		// PriorCarrierPolicyInfo needs to be present in the SOM policy version
		com.ing.canada.som.interfaces.agreement.PriorCarrierPolicyInfo testCarrierInfo = mock(PriorCarrierPolicyInfo.class);
		testCarrierInfo.setNumberOfYearsContinuouslyInsuredWithPriorCarrier(null); // To better validate the change
		when(this.currentMockSomPV.getThePriorCarrierPolicyInfo()).thenReturn(testCarrierInfo);

		// Executing the tested method and verifying the stub calls
		this.ratingService.preprocessSOM(this.currentMockSomPV, this.currentPolicyVersion);
		assertEquals(0, testCarrierInfo.getNumberOfYearsContinuouslyInsuredWithPriorCarrier());
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion, PolicyVersion)}
	 * Case for a SOM Producer to be present in the SOM policy version.
	 * 
	 * @throws Exception
	 */
	@Disabled
	public void testPreprocessSOM_WithProducer() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		this.setupGeneralPreprocessSOMTest("P");

		// Producer needs to be present in the SOM policy version
		com.ing.canada.som.interfaces.partyRoleInAgreement.Producer testProducer = mock(Producer.class);
		testProducer.setCreditScoreEligibilityInd(null); // To better validate the change
		when(this.currentMockSomPV.getTheProducer()).thenReturn(testProducer);

		// Executing the tested method and verifying the stub calls
		this.ratingService.preprocessSOM(this.currentMockSomPV, this.currentPolicyVersion);
		assertEquals("Y", testProducer.getCreditScoreEligibilityInd());
	}

	/**
	 * Test method for
	 * {@link RatingService#preprocessSOMParties(com.ing.canada.som.interfaces.agreement.PolicyVersion) }
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPreprocessSOMParties() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		this.setupGeneralPreprocessSOMTest("P");

		// Adding a party to the SOM policy version with INDIVIDUAL as it's type
		List<com.ing.canada.som.interfaces.party.Party> testPartyList = new ArrayList<>();
		com.ing.canada.som.interfaces.party.Party testParty = new MockParty();
		testParty.setPartyType(PartyTypeCodeEnum.INDIVIDUAL.getCode());
		testPartyList.add(testParty);
		when(this.currentMockSomPV.getTheParty()).thenReturn(testPartyList);

		// Setup for the party's driver complement info
		com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo testDCI = testParty.getTheDriverComplementInfo();
		testDCI.setNumberOfMinorConvictions3Years(1);
		testDCI.setNumberOfMajorConvictions3Years(1);
		testParty.setTheDriverComplementInfo(testDCI);

		GregorianCalendar claimRefDate = new GregorianCalendar();
		claimRefDate.setTime(new Date());

		ReferenceDate mockRefDate = mock(ReferenceDate.class);
		when(mockRefDate.getClaimReferenceDate()).thenReturn(claimRefDate);
		when(this.currentMockSomPV.getTheReferenceDate()).thenReturn(mockRefDate);

		// Executing the tested method and verifying the results
		this.ratingService.preprocessSOMParties(this.currentMockSomPV);

		com.ing.canada.som.interfaces.registration.Conviction resultMinorConviction = testDCI.getTheConviction(0);
		com.ing.canada.som.interfaces.registration.Conviction resultMajorConviction = testDCI.getTheConviction(1);
		GregorianCalendar expectedDate = mockRefDate.getClaimReferenceDate();
		expectedDate.add(Calendar.YEAR, -1);

		assertEquals("Y", testDCI.getNormalLicenseProgressionInd());
		assertEquals(0, testDCI.getNumberOfNonPaymentCancellationsIn3Years());

		assertEquals(1, resultMinorConviction.getConvictionSequence());
		assertEquals("MINOR", resultMinorConviction.getConvictionCode());
		assertEquals(expectedDate, resultMinorConviction.getConvictionDate());
		assertEquals("MIN", resultMinorConviction.getConvictionType());
		assertEquals("MIN", resultMinorConviction.getConvictionTypeRsp());
		assertEquals("Y", resultMinorConviction.getConvictionChargeabilityInd());

		assertEquals((Integer) 2, resultMajorConviction.getConvictionSequence());
		assertEquals("MAJOR", resultMajorConviction.getConvictionCode());
		assertEquals(expectedDate, resultMajorConviction.getConvictionDate());
		assertEquals("MAJ", resultMajorConviction.getConvictionType());
		assertEquals("MAJ", resultMajorConviction.getConvictionTypeRsp());
		assertEquals("Y", resultMajorConviction.getConvictionChargeabilityInd());
	}

	/**
	 * Case for a liable claim in the last year.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim1Year() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last year to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Case for a liable claim in the last 2 years, but not in the last year.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim2Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 2 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -1);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Case for a liable claim in the last 3 years, but not in the last 2 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim3Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 3 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -2);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Case for a liable claim in the last 4 years, but not in the last 3 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim4Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 4 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -3);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Case for a liable claim in the last 5 years, but not in the last 4 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim5Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 5 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -4);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Case for a liable claim in the last 6 years, but not in the last 5 years. Also, the claim and the driver's party
	 * are the same and are about an occasional driver.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim6YearsOccasionalDriver() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 6 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -5);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Setting the driver and the claim party with an occasional driver
		this.currentTestDriver = new MockDriver();
		this.currentTestDriver.setTypeOfDriver("O");
		com.ing.canada.som.interfaces.party.Party testParty = new MockParty();
		testParty.setUniqueId("TestUniqueId");
		this.currentSomRisk.addTheDriver(this.currentTestDriver);
		this.currentTestDriver.setTheParty(testParty);
		testClaim.setTheParty(testParty);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiableOccasionalDriver6Years());
	}

	/**
	 * Case for a liable claim in the last 6 years, but not in the last 5 years. Also, the claim and the driver's party
	 * are the same and are about a principal driver.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim6YearsPrincipalDriver() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 6 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -5);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Setting the driver and the claim party with an principal driver
		this.currentTestDriver = new MockDriver();
		this.currentTestDriver.setTypeOfDriver("P");
		com.ing.canada.som.interfaces.party.Party testParty = new MockParty();
		testParty.setUniqueId("TestUniqueId");
		this.currentSomRisk.addTheDriver(this.currentTestDriver);
		this.currentTestDriver.setTheParty(testParty);
		testClaim.setTheParty(testParty);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());

		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiableOccasionalDriver6Years());
	}

	/**
	 * Case for a liable claim in the last 10 years, but not in the last 6 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForRisk_LiableClaim10Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 10 years to the SOM insurance risk
		com.ing.canada.som.interfaces.claim.Claim testClaim = new MockClaim();
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -9);
		testClaim.setDateOfLoss(dateOfLoss);
		this.currentSomRisk.getTheClaim().add(testClaim);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentSomRisk, testDate);

		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable1Year());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable2Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable3Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable4Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable5Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsLiable6Years());
		assertEquals(1, this.currentSomRisk.getNumberOfClaimsLiable10Years());
		assertEquals(0, this.currentSomRisk.getNumberOfClaimsExcludingGlassBreakage3Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#calculateCounters(com.ing.canada.som.interfaces.partyRoleInRisk.Driver, Date) }
	 * Case for a liable claim in the last 3 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForDriver_LiableClaim3Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 3 years to the SOM Driver's Party
		this.currentTestDriver = new MockDriver();

		com.ing.canada.som.interfaces.claim.Claim testClaim = this.currentTestDriver.getTheParty().getTheClaim().get(0);
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -2);
		testClaim.setDateOfLoss(dateOfLoss);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentTestDriver, testDate);

		com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo resultDCI = this.currentTestDriver.getTheParty().getTheDriverComplementInfo();

		assertEquals(1, resultDCI.getNumberOfClaims3Years());
		assertEquals(1, resultDCI.getNumberOfClaims5Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims3Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims5Years());
		assertEquals(1, resultDCI.getNumberOfClaimsAtFault6Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims10Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#calculateCounters(com.ing.canada.som.interfaces.partyRoleInRisk.Driver, Date) }
	 * Case for a liable claim in the last 5 years, but not in the last 3 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForDriver_LiableClaim5Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 5 years to the SOM Driver's Party
		this.currentTestDriver = new MockDriver();

		com.ing.canada.som.interfaces.claim.Claim testClaim = this.currentTestDriver.getTheParty().getTheClaim().get(0);
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -4);
		testClaim.setDateOfLoss(dateOfLoss);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentTestDriver, testDate);

		com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo resultDCI = this.currentTestDriver.getTheParty().getTheDriverComplementInfo();

		assertEquals(0, resultDCI.getNumberOfClaims3Years());
		assertEquals(1, resultDCI.getNumberOfClaims5Years());
		assertEquals(0, resultDCI.getNumberOfLiableClaims3Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims5Years());
		assertEquals(1, resultDCI.getNumberOfClaimsAtFault6Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims10Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#calculateCounters(com.ing.canada.som.interfaces.partyRoleInRisk.Driver, Date) }
	 * Case for a liable claim in the last 6 years, but not in the last 5 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForDriver_LiableClaim6Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 6 years to the SOM Driver's Party
		this.currentTestDriver = new MockDriver();

		com.ing.canada.som.interfaces.claim.Claim testClaim = this.currentTestDriver.getTheParty().getTheClaim().get(0);
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -5);
		testClaim.setDateOfLoss(dateOfLoss);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentTestDriver, testDate);

		com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo resultDCI = this.currentTestDriver.getTheParty().getTheDriverComplementInfo();

		assertEquals(0, resultDCI.getNumberOfClaims3Years());
		assertEquals(0, resultDCI.getNumberOfClaims5Years());
		assertEquals(0, resultDCI.getNumberOfLiableClaims3Years());
		assertEquals(0, resultDCI.getNumberOfLiableClaims5Years());
		assertEquals(1, resultDCI.getNumberOfClaimsAtFault6Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims10Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#calculateCounters(com.ing.canada.som.interfaces.partyRoleInRisk.Driver, Date) }
	 * Case for a liable claim in the last 10 years, but not in the last 6 years.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCalculateCountersForDriver_LiableClaim10Years() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		Date testDate = new Date();

		// Adding a SOM liable claim in the last 6 years to the SOM Driver's Party
		this.currentTestDriver = new MockDriver();

		com.ing.canada.som.interfaces.claim.Claim testClaim = this.currentTestDriver.getTheParty().getTheClaim().get(0);
		testClaim.setClaimAtFaultInd("Y");
		GregorianCalendar dateOfLoss = new GregorianCalendar();
		dateOfLoss.setTime(testDate);
		dateOfLoss.add(Calendar.YEAR, -9);
		testClaim.setDateOfLoss(dateOfLoss);

		// Executing the tested method and verifying the results
		this.ratingService.calculateCounters(this.currentTestDriver, testDate);

		com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo resultDCI = this.currentTestDriver.getTheParty().getTheDriverComplementInfo();

		assertEquals(0, resultDCI.getNumberOfClaims3Years());
		assertEquals(0, resultDCI.getNumberOfClaims5Years());
		assertEquals(0, resultDCI.getNumberOfLiableClaims3Years());
		assertEquals(0, resultDCI.getNumberOfLiableClaims5Years());
		assertEquals(0, resultDCI.getNumberOfClaimsAtFault6Years());
		assertEquals(1, resultDCI.getNumberOfLiableClaims10Years());
	}

	/**
	 * Test method for
	 * {@link RatingService#postprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion, GregorianCalendar, GregorianCalendar, String)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testPostProcessSOM() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		Date testDate = new Date();
		GregorianCalendar expectedCal = new GregorianCalendar();
		expectedCal.setTime(testDate);

		// Adding an insurance policy to the SOM Policy version to avoid a NullPointerException
		com.ing.canada.som.interfaces.agreement.InsurancePolicy testPolicy = new MockInsurancePolicy();
		when(this.currentMockSomPV.getTheInsurancePolicy()).thenReturn(testPolicy);

		// Executing the tested method and verifying the results
		this.ratingService.postprocessSOM(this.currentMockSomPV, expectedCal, expectedCal, "testCode");

		assertEquals(expectedCal, testPolicy.getOriginalInceptionDate());
	}

	/**
	 * Test method for
	 * {@link RatingService#basicCoverageCodesAsList(Set<BasicCoverageCodeEnum>)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testBasicCoverageCodesAsList() throws Exception {
		// Creating the entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Executing the tested method
		List<String> resultList = this.ratingService.basicCoverageCodesAsList(entrySet);

		// Validating the result list
		assertEquals(expectedCode.getCode(), resultList.get(0));
	}

	/**
	 * Test method for
	 * Set<BasicCoverageCodeEnum>)} Case for the coverage's rating risk type apply to be for principal risk
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateEndorsmentCoveragePremiums_RatingRiskApplyPrincipal() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("P");

		// Executing the tested method and verifying the results
		this.ratingService.createEndorsmentCoveragePremiums(this.currentSomRisk, entrySet);

		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
	}

	/**
	 * Test method for
	 * Set<BasicCoverageCodeEnum>)} Case for the coverage's rating risk type apply to be for occasional risk
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateEndorsmentCoveragePremiums_RatingRiskApplyOccasional() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("O");

		// Executing the tested method and verifying the results
		this.ratingService.createEndorsmentCoveragePremiums(this.currentSomRisk, entrySet);

		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	/**
	 * Boolean, Set<BasicCoverageCodeEnum>, Set<BasicCoverageCodeEnum>)} Case for the coverage's rating risk type apply
	 * to be for principal risk
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateCoveragePremiumsAll_RatingRiskApplyPrincipal() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("P");

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, false, entrySet, entrySet);

		assertEquals("Y", testCoverage.getCoverageEligibleInd());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
	}

	/**
	 * Boolean, Set<BasicCoverageCodeEnum>, Set<BasicCoverageCodeEnum>)} Case for the coverage's rating risk type apply
	 * to be for occasional risk
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateCoveragePremiumsAll_RatingRiskApplyOccasional() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("O");

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, true, entrySet, entrySet);

		assertEquals("Y", testCoverage.getCoverageEligibleInd());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	/**
	 * Boolean, Set<BasicCoverageCodeEnum>, Set<BasicCoverageCodeEnum>)} Case for the coverage's rating risk type apply
	 * to be for both occasional and principal risks.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateCoveragePremiumsAll_RatingRiskApplyBothRisk() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("B");

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, true, entrySet, entrySet);

		assertEquals("Y", testCoverage.getCoverageEligibleInd());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	/**
	 * Set, Set, CoverageRepositoryEntry)} Case for the coverage's rating
	 * risk type apply to be for principal risk.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateCoveragePremiumsTypeApplyFromPLP_RatingRiskTypeApplyPrincipal() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Creating the coverage repository entry containing the apply type from plp
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.PRINCIPAL_RISK);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(testCoverage, true, entrySet, entrySet, testCoverageRepoEntry);

		assertEquals("P", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
	}

	@Test
	public void testCreateCoveragePremiumsTypeApplyFromPLP_RatingRiskTypeApplyOccasional() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Creating the coverage repository entry containing the apply type from plp
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.OCCASIONAL_RISK);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(testCoverage, true, entrySet, entrySet, testCoverageRepoEntry);

		assertEquals("O", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	@Test
	public void testCreateCoveragePremiumsTypeApplyFromPLP_RatingRiskTypeApplyBothRisk() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> entrySet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		entrySet.add(expectedCode);

		// Creating the coverage repository entry containing the apply type from plp
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.BOTH_RISK);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(testCoverage, true, entrySet, entrySet, testCoverageRepoEntry);

		assertEquals("B", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	@Test
	public void testCreateCoveragePremiumsAllWithTypePLP_RatingRiskTypeApplyPrincipal() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> basicCoverageSet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		basicCoverageSet.add(expectedCode);

		// Creating the coverage repository map
		Map<String, CoverageRepositoryEntry> coverageRepoEntrySet = new HashMap<String, CoverageRepositoryEntry>();
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.PRINCIPAL_RISK);
		String testCoverageCode = "testCode";
		coverageRepoEntrySet.put(testCoverageCode, testCoverageRepoEntry);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageCode(testCoverageCode);
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, true, basicCoverageSet, basicCoverageSet, coverageRepoEntrySet);

		assertEquals("P", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
	}

	@Test
	public void testCreateCoveragePremiumsAllWithTypePLP_RatingRiskTypeApplyOccasional() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> basicCoverageSet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		basicCoverageSet.add(expectedCode);

		// Creating the coverage repository map
		Map<String, CoverageRepositoryEntry> coverageRepoEntrySet = new HashMap<String, CoverageRepositoryEntry>();
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.OCCASIONAL_RISK);
		String testCoverageCode = "testCode";
		coverageRepoEntrySet.put(testCoverageCode, testCoverageRepoEntry);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageCode(testCoverageCode);
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, true, basicCoverageSet, basicCoverageSet, coverageRepoEntrySet);

		assertEquals("O", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().get(0).getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	@Test
	public void testCreateCoveragePremiumsAllWithTypePLP_RatingRiskTypeApplyBothRisk() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> basicCoverageSet = new HashSet<>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		basicCoverageSet.add(expectedCode);

		// Creating the coverage repository map
		Map<String, CoverageRepositoryEntry> coverageRepoEntrySet = new HashMap<>();
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.BOTH_RISK);
		String testCoverageCode = "testCode";
		coverageRepoEntrySet.put(testCoverageCode, testCoverageRepoEntry);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageCode(testCoverageCode);
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createCoveragePremiums(this.currentSomRisk, true, basicCoverageSet, basicCoverageSet, coverageRepoEntrySet);

		assertEquals("B", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	@Test
	public void testCreateEndorsmentCoveragePremiumsWithPLP_RatingRiskTypeApplyPrincipal() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> basicCoverageSet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		basicCoverageSet.add(expectedCode);

		// Creating the coverage repository map
		Map<String, CoverageRepositoryEntry> coverageRepoEntrySet = new HashMap<String, CoverageRepositoryEntry>();
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.PRINCIPAL_RISK);
		String testCoverageCode = "testCode";
		coverageRepoEntrySet.put(testCoverageCode, testCoverageRepoEntry);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageCode(testCoverageCode);
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createEndorsmentCoveragePremiums(this.currentSomRisk, basicCoverageSet, basicCoverageSet, coverageRepoEntrySet);

		assertEquals("P", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumPrincipal().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumPrincipal(), "A Coverage Premium Principal should've been created in the test coverage.");
	}

	/**
	 * Test method for
	 * Case for the coverage's rating risk type apply to be for occasional risk.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testCreateEndorsmentCoveragePremiumsWithPLP_RatingRiskTypeApplyOccasional() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();
		// Creating the basic coverage codes entry set
		Set<BasicCoverageCodeEnum> basicCoverageSet = new HashSet<BasicCoverageCodeEnum>();
		BasicCoverageCodeEnum expectedCode = BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB;
		basicCoverageSet.add(expectedCode);

		// Creating the coverage repository map
		Map<String, CoverageRepositoryEntry> coverageRepoEntrySet = new HashMap<>();
		CoverageRepositoryEntry testCoverageRepoEntry = new CoverageRepositoryEntry();
		testCoverageRepoEntry.setRatingRiskTypeApply(RatingRiskTypeApplyCodeEnum.OCCASIONAL_RISK);
		String testCoverageCode = "testCode";
		coverageRepoEntrySet.put(testCoverageCode, testCoverageRepoEntry);

		// Setup for the test SOM coverage
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().getFirst();
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageCode(testCoverageCode);
		testCoverage.setCoverageEligibleInd("Y");
		testCoverage.setRatingRiskTypeApply("A"); // Should be changed by the plp repository entry

		// Executing the tested method and verifying the results
		this.ratingService.createEndorsmentCoveragePremiums(this.currentSomRisk, basicCoverageSet, basicCoverageSet, coverageRepoEntrySet);

		assertEquals("O", testCoverage.getRatingRiskTypeApply());
		assertEquals(expectedCode.getCode(), testCoverage.getTheCoveragePremiumOccasional().getTheSubCoveragePremium().getFirst().getBasicCoverageCode());
		assertNotNull(testCoverage.getTheCoveragePremiumOccasional(), "A Coverage Premium Occasional should've been created in the test coverage.");
	}

	/**
	 * Test method for
	 * Case for the root risk not to be found.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetRootInsuranceRisk_RootRiskNotFound() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Creating a second SOM insurance risk with a different sequence id to serve as branch insurance risk
		com.ing.canada.som.interfaces.risk.InsuranceRisk branchRisk = mock(com.ing.canada.som.interfaces.risk.InsuranceRisk.class);
		branchRisk.setInsuranceRiskSequence(102);
		this.currentSomRisk.setInsuranceRiskSequence(42); // Needs to be different

		// Executing the tested method and verifying the results
		com.ing.canada.som.interfaces.risk.InsuranceRisk resultRisk = this.ratingService.getRootInsuranceRisk(branchRisk, this.currentMockSomPV);

		assertNull(resultRisk, "No root risk should've been found");
	}

	/**
	 * Test method for {@link RatingService#removeEndorsments(com.ing.canada.som.interfaces.risk.InsuranceRisk)} (InsuranceRisk)}
	 *
	 * @throws Exception
	 */
	@Test
	public void testRemoveEndorsments() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setting the coverage for the test
		com.ing.canada.som.interfaces.risk.Coverage testCoverage = this.currentSomRisk.getTheCoverage().get(0);
		testCoverage.setCoverageType("EN");
		testCoverage.setCoverageEligibleInd("Y"); // Set to help verify the change

		// Executing the tested method and verifying the results
		this.ratingService.removeEndorsments(this.currentSomRisk);

		assertEquals("N", testCoverage.getCoverageEligibleInd());
	}

	/**
	 * Test method for {@link RatingService#getRealException(BaseException)}
	 * 
	 * @throws Exception
	 */
	@Test
	public void testGetRealException() throws Exception {
		// General setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// Setting the exceptions for the test
		BaseException testException = new BaseException();
		RuntimeException expectedException = new RuntimeException("Test exception for \"testGetRealException\"");
		testException.setRealException(expectedException);

		// Executing the tested method and verifying the results
		Exception resultException = this.ratingService.getRealException(testException);

		assertEquals(expectedException, resultException);
	}


	/**
	 * Test method for {@link RatingServiceQCIntactCL#manageUbiStatus(PolicyVersion)}
	 * Case for the PL UBI to be selected.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testManageUbiStatus_PlUBISelected() throws Exception {
		// Executing the general setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// PL Insurance risk setup to access the desired branch
		InsuranceRiskOffer testRiskOffer = new InsuranceRiskOffer();
		testRiskOffer.setOfferType(OfferTypeCodeEnum.CUSTOM);
		this.currentPLRisk.addInsuranceRiskOffer(testRiskOffer);
		this.currentPLRisk.setSelectedInsuranceRiskOffer(testRiskOffer);
		this.currentPLRisk.setInsuranceRiskOfferSystemSelectedIndicator(false);

		// A PL Ubi coverage that is selected is created and set to be returned by the coverage helper
		BaseCoverage testPLUbi = new CoverageOffer();
		testPLUbi.setCoverageSelectedIndicator(true);
		when(this.mockCoverageHelper.getSelectedEndorsement(anySet(), eq(EndorsementCodeEnum.UBI))).thenReturn(testPLUbi);

		// A principal driver with a complement info needs to be returned for result validation
		Party testDriver = new Party();
		DriverComplementInfo testDCI = new DriverComplementInfo();
		testDriver.setDriverComplementInfo(testDCI);
		when(this.mockPartyHelper.getPrincipalDriver(any(InsuranceRisk.class))).thenReturn(testDriver);

		// Executing the tested method and validating the results
		this.ratingService.manageUbiStatus(this.currentPolicyVersion);

		assertEquals(UBIStatusCodeEnum.ENROLLED, testDriver.getDriverComplementInfo().getUBIStatusCode());
	}

	/**
	 * Test method for {@link RatingServiceQCIntactCL#manageUbiStatus(PolicyVersion)}
	 * Case for no PL UBI to be found by the coverage helper.
	 * 
	 * @throws Exception
	 */
	@Test
	public void testManageUbiStatus_NoPlUBIFound() throws Exception {
		// Executing the general setup for the test
		this.setupGeneralSomPlPolicyVersions();

		// PL Insurance risk setup to access the desired branch
		InsuranceRiskOffer testRiskOffer = new InsuranceRiskOffer();
		testRiskOffer.setOfferType(OfferTypeCodeEnum.CUSTOM);
		this.currentPLRisk.addInsuranceRiskOffer(testRiskOffer);
		this.currentPLRisk.setSelectedInsuranceRiskOffer(testRiskOffer);
		this.currentPLRisk.setInsuranceRiskOfferSystemSelectedIndicator(false);

		// A principal driver with a complement info needs to be returned for result validation
		Party testDriver = new Party();
		DriverComplementInfo testDCI = new DriverComplementInfo();
		testDriver.setDriverComplementInfo(testDCI);
		when(this.mockPartyHelper.getPrincipalDriver(any(InsuranceRisk.class))).thenReturn(testDriver);

		// Executing the tested method and validating the results
		this.ratingService.manageUbiStatus(this.currentPolicyVersion);

		assertNull(testDriver.getDriverComplementInfo().getUBIStatusCode(), "The principal driver's UBI status code should've been null.");
	}

	/**
	 * Private method to do the general setup for tests on the preprocessSOM method.
	 * 
	 * @param typeOfDriver The driver's type for the test
	 */
	private void setupGeneralPreprocessSOMTest(String typeOfDriver) {

		// SOM Driver setup
		this.currentTestDriver = new MockDriver();
		this.currentSomRisk.addTheDriver(this.currentTestDriver);
		this.currentTestDriver.setTypeOfDriver(typeOfDriver);
		this.currentTestDriver.getTheParty().setPersistenceUniqueId("42");

		// Dates setup
		GregorianCalendar expectedCalendar = new GregorianCalendar();
		expectedCalendar.setTime(new Date());
		this.currentRefDate = mock(ReferenceDate.class);
		this.currentRefDate.setClaimReferenceDate(expectedCalendar);
		when(this.currentMockSomPV.getTheReferenceDate()).thenReturn(this.currentRefDate);
		this.currentTestDriver.getTheParty().getTheClaim().get(0).setDateOfLoss(expectedCalendar);

		// PL Policy version setup
		this.currentPolicyVersion.setCombinedPolicyCode(CombinedPolicyCodeEnum.MONOLINE_POLICY);
		this.currentPolicyVersion.setCombinedPolicyScenarioCode(CombinedPolicyScenarioCodeEnum.COMBO_POLICY);
	}

	/**
	 * Private method to do the necessary setup for tests involving a SOM and/or a PL Policy version.
	 *
	 * @throws Exception
	 */
	private void setupGeneralSomPlPolicyVersions() throws Exception {
		// Setup for the PL policy version with an insurance risk
		this.currentPolicyVersion.getInsurancePolicy().setQuoteValidityPeriodInDays((short) 2);
		this.currentPolicyVersion.setBusinessTransaction(mock(BusinessTransaction.class));
		this.currentPLRisk = new InsuranceRisk();
		this.currentPLRisk.setId(42L);
		this.currentPLRisk.setInsuranceRiskSequence((short) 42);
		this.currentPolicyVersion.addInsuranceRisk(this.currentPLRisk);

		// Initial mock SOM policy version setup with SOM insurance risk
		this.currentMockSomPV = mock(com.ing.canada.som.interfaces.agreement.PolicyVersion.class);
		List<com.ing.canada.som.interfaces.risk.InsuranceRisk> testInsuranceRiskList = new ArrayList<>();
		this.currentSomRisk = new MockInsuranceRisk();
		testInsuranceRiskList.add(this.currentSomRisk);
	}
}
