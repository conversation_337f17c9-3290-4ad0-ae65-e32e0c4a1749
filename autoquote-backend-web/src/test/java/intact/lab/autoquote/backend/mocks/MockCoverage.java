/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessModel.AttributeValidValue;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium;
import com.ing.canada.som.interfaces.note.Note;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.som.interfaces.partyRoleInRisk.AdditionalInterest;
import com.ing.canada.som.interfaces.partyRoleInRisk.ReinsurerCoverage;
import com.ing.canada.som.interfaces.product.CoverageProduct;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.som.interfaces.risk.CoverageCommercial;
import com.ing.canada.som.interfaces.risk.CoverageMarine;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.Question;
import com.ing.canada.som.interfaces.risk.ReinsuranceComponentGroupCoverage;
import com.ing.canada.som.interfaces.risk.ScheduledArticle;
import com.ing.canada.som.interfaces.risk.ScheduledArticleItemCoverage;
import com.ing.canada.som.interfaces.risk.SubscriptionComponentGroupCoverage;
import com.ing.canada.som.interfaces.risk.UnderlyingSchedule;
import com.ing.canada.som.interfaces.service.MlModel;

import java.util.GregorianCalendar;
import java.util.List;

/**
 * Mock class for a SOM Coverage. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Coverage}
 *
 * <AUTHOR>
 */
public class MockCoverage implements Coverage {

	// Creating the attributes needed to be set during test with a default value to validate the changes
	private String selectableInd = "Y";

	private String selectedInd = "Y";

	private String eligibleInd = "Y";

	private String selectedType = "Y";

	private String coverageCode;

	private String coverageCodeNative;

	private String coverageType;

	private String actionTaken;

	private String persistenceUniqueId;

	private String ratingRiskTypeApply;

	private Integer coverageTermInMonths;

	private GregorianCalendar effectiveDate;

	private CoveragePremium coveragePremiumPrincipal;

	private CoveragePremium coveragePremiumOccasional;

	@Override
	public String getActionTaken() {
		return this.actionTaken;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		this.actionTaken = newActionTaken;
	}

	@Override
	public String getPersistenceUniqueId() {
		// For test purposes, return "42" by defaultreturn
		return this.persistenceUniqueId == null ? "42" : this.persistenceUniqueId;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		this.persistenceUniqueId = newPersistenceUniqueId;
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return "TestId";
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		return false;
	}

	@Override
	public Integer getCoverageSequence() {
		return null;
	}

	@Override
	public void setCoverageSequence(Integer newCoverageSequence) {
		// noop
	}

	@Override
	public String getEndorsementPackage() {
		return null;
	}

	@Override
	public void setEndorsementPackage(String newEndorsementPackage) {
		// noop
	}

	@Override
	public String getConvertedCoverageInd() {
		return null;
	}

	@Override
	public void setConvertedCoverageInd(String newConvertedCoverageInd) {
		// noop
	}

	@Override
	public GregorianCalendar getEffectiveDateSystem() {
		return null;
	}

	@Override
	public void setEffectiveDateSystem(GregorianCalendar newEffectiveDateSystem) {

	}

	@Override
	public GregorianCalendar getEffectiveDateModified() {
		return null;
	}

	@Override
	public void setEffectiveDateModified(GregorianCalendar newEffectiveDateModified) {

	}

	@Override
	public GregorianCalendar getEffectiveDate() {
		return this.effectiveDate;
	}

	@Override
	public void setEffectiveDate(GregorianCalendar newEffectiveDate) {
		this.effectiveDate = newEffectiveDate;
	}

	@Override
	public GregorianCalendar getOriginalEffectiveDate() {
		return null;
	}

	@Override
	public void setOriginalEffectiveDate(GregorianCalendar newOriginalEffectiveDate) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDateSystem() {
		return null;
	}

	@Override
	public void setExpiryDateSystem(GregorianCalendar newExpiryDateSystem) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDateModified() {
		return null;
	}

	@Override
	public void setExpiryDateModified(GregorianCalendar newExpiryDateModified) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDate() {
		return null;
	}

	@Override
	public void setExpiryDate(GregorianCalendar newExpiryDate) {
		// noop
	}

	@Override
	public GregorianCalendar getContextStartDate() {
		return null;
	}

	@Override
	public void setContextStartDate(GregorianCalendar newContextStartDate) {
		// noop
	}

	@Override
	public GregorianCalendar getContextEndDate() {
		return null;
	}

	@Override
	public void setContextEndDate(GregorianCalendar newContextEndDate) {
		// noop
	}

	@Override
	public Integer getCoverageTermInDays() {
		return null;
	}

	@Override
	public void setCoverageTermInDays(Integer newCoverageTermInDays) {
		// noop
	}

	@Override
	public Integer getCoverageTermInMonths() {
		return this.coverageTermInMonths;
	}

	@Override
	public void setCoverageTermInMonths(Integer newCoverageTermInMonths) {
		this.coverageTermInMonths = newCoverageTermInMonths;
	}

	@Override
	public Integer getDuration() {
		return null;
	}

	@Override
	public void setDuration(Integer newDuration) {
		// noop
	}

	@Override
	public Integer getCoverageYearLevel() {
		return null;
	}

	@Override
	public void setCoverageYearLevel(Integer newCoverageYearLevel) {
		// noop
	}

	@Override
	public GregorianCalendar getRatingDate() {
		return null;
	}

	@Override
	public void setRatingDate(GregorianCalendar newRatingDate) {
		// noop
	}

	@Override
	public GregorianCalendar getOriginalDateRated() {
		return null;
	}

	@Override
	public void setOriginalDateRated(GregorianCalendar newOriginalDateRated) {

	}

	@Override
	public String getDeductibleCreditInd() {
		return null;
	}

	@Override
	public void setDeductibleCreditInd(String newDeductibleCreditInd) {
		// noop
	}

	@Override
	public Double getDeductiblePercentageSystem() {
		return null;
	}

	@Override
	public void setDeductiblePercentageSystem(Double newDeductiblePercentageSystem) {

	}

	@Override
	public Double getDeductiblePercentageModified() {
		return null;
	}

	@Override
	public void setDeductiblePercentageModified(Double newDeductiblePercentageModified) {

	}

	@Override
	public Double getDeductiblePercentage() {
		return null;
	}

	@Override
	public void setDeductiblePercentage(Double newDeductiblePercentage) {
		// noop
	}

	@Override
	public Double getAnnualPremium() {
		return null;
	}

	@Override
	public void setAnnualPremium(Double newAnnualPremium) {
		// noop
	}

	@Override
	public Double getAnnualPremiumBasicCoverageScoring() {
		return null;
	}

	@Override
	public void setAnnualPremiumBasicCoverageScoring(Double newAnnualPremiumBasicCoverageScoring) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoring() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoring(Double newAnnualPremiumScoring) {
		// noop
	}

	@Override
	public Double getAnnualPremiumScoringRai() {
		return null;
	}

	@Override
	public void setAnnualPremiumScoringRai(Double newAnnualPremiumScoringRai) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurcharged(Double newAnnualPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCapped(Double newAnnualPremiumDiscountedSurchargedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumDiscountedSurchargedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumDiscountedSurchargedCappedPreapproved(Double newAnnualPremiumDiscountedSurchargedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidated() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidated(Double newAnnualPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCapped(Double newAnnualPremiumConsolidatedCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumConsolidatedCappedPreapproved() {
		return null;
	}

	@Override
	public void setAnnualPremiumConsolidatedCappedPreapproved(Double newAnnualPremiumConsolidatedCappedPreapproved) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCapped() {
		return null;
	}

	@Override
	public void setAnnualPremiumCapped(Double newAnnualPremiumCapped) {
		// noop
	}

	@Override
	public Double getAnnualPremiumCappedAfterDeviation() {
		return null;
	}

	@Override
	public void setAnnualPremiumCappedAfterDeviation(Double newAnnualPremiumCappedAfterDeviation) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPool(Double newAnnualPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getAnnualPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAnnualPremiumFacilityRiskSharingPoolFloor(Double newAnnualPremiumFacilityRiskSharingPoolFloor) {
		// noop
	}

	@Override
	public Double getFullTermPremium() {
		return null;
	}

	@Override
	public void setFullTermPremium(Double newFullTermPremium) {
		// noop
	}

	@Override
	public Double getFullTermPremiumMinimum() {
		return null;
	}

	@Override
	public void setFullTermPremiumMinimum(Double newFullTermPremiumMinimum) {
		// noop
	}

	@Override
	public Double getFullTermPremiumConsolidated() {
		return null;
	}

	@Override
	public void setFullTermPremiumConsolidated(Double newFullTermPremiumConsolidated) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDiscountedSurcharged() {
		return null;
	}

	@Override
	public void setFullTermPremiumDiscountedSurcharged(Double newFullTermPremiumDiscountedSurcharged) {
		// noop
	}

	@Override
	public Double getFullTermPremiumDifferenceWithPriorTrans() {
		return null;
	}

	@Override
	public void setFullTermPremiumDifferenceWithPriorTrans(Double newFullTermPremiumDifferenceWithPriorTrans) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPool(Double newFullTermPremiumFacilityRiskSharingPool) {
		// noop
	}

	@Override
	public Double getManualPremium() {
		return null;
	}

	@Override
	public void setManualPremium(Double newManualPremium) {
		// noop
	}

	@Override
	public Double getManualAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setManualAdditionalReturnPremium(Double newManualAdditionalReturnPremium) {

	}

	@Override
	public Double getManualAdditionalCoveragePercentage() {
		return null;
	}

	@Override
	public void setManualAdditionalCoveragePercentage(Double newManualAdditionalCoveragePercentage) {

	}

	@Override
	public Double getAdditionalReturnPremium() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremium(Double newAdditionalReturnPremium) {
		// noop
	}

	@Override
	public Double getAdditionalReturnPremiumGrid() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumGrid(Double newAdditionalReturnPremiumGrid) {
		// noop
	}

	@Override
	public Double getReturnPremium() {
		return null;
	}

	@Override
	public void setReturnPremium(Double newReturnPremium) {
		// noop
	}

	@Override
	public Double getAdditionalPremium() {
		return null;
	}

	@Override
	public void setAdditionalPremium(Double newAdditionalPremium) {
		// noop
	}

	@Override
	public String getSystemRatedRequiredInd() {
		return null;
	}

	@Override
	public void setSystemRatedRequiredInd(String newSystemRatedRequiredInd) {
		// noop
	}

	@Override
	public Double getPremiumRetainedPercentage() {
		return null;
	}

	@Override
	public void setPremiumRetainedPercentage(Double newPremiumRetainedPercentage) {
		// noop
	}

	@Override
	public Double getPremiumChangePercentage() {
		return null;
	}

	@Override
	public void setPremiumChangePercentage(Double newPremiumChangePercentage) {
		// noop
	}

	@Override
	public String getOverallDiscountSurchargeCode() {
		return null;
	}

	@Override
	public void setOverallDiscountSurchargeCode(String newOverallDiscountSurchargeCode) {
		// noop
	}

	@Override
	public String getOverallDiscountSurchargeCode2() {
		return null;
	}

	@Override
	public void setOverallDiscountSurchargeCode2(String newOverallDiscountSurchargeCode2) {
		// noop
	}

	@Override
	public Double getOverallDiscountSurchargePercentage() {
		return null;
	}

	@Override
	public void setOverallDiscountSurchargePercentage(Double newOverallDiscountSurchargePercentage) {
		// noop
	}

	@Override
	public Double getOverallDiscountSurchargePercentage2() {
		return null;
	}

	@Override
	public void setOverallDiscountSurchargePercentage2(Double newOverallDiscountSurchargePercentage2) {
		// noop
	}

	@Override
	public String getCoverageHiddenInd() {
		return null;
	}

	@Override
	public void setCoverageHiddenInd(String newCoverageHiddenInd) {
		// noop
	}

	@Override
	public String getCoverageSelectableInd() {
		return this.selectableInd;
	}

	@Override
	public void setCoverageSelectableInd(String newCoverageSelectableInd) {
		this.selectableInd = newCoverageSelectableInd;
	}

	@Override
	public String getCoverageSelectedInd() {
		return this.selectedInd;
	}

	@Override
	public void setCoverageSelectedInd(String newCoverageSelectedInd) {
		this.selectedInd = newCoverageSelectedInd;
	}

	@Override
	public String getCoverageSelectedType() {
		return this.selectedType;
	}

	@Override
	public void setCoverageSelectedType(String newCoverageSelectedType) {
		this.selectedType = newCoverageSelectedType;
	}

	@Override
	public String getCoverageSelectedTypeBeforeRating() {
		return null;
	}

	@Override
	public void setCoverageSelectedTypeBeforeRating(String newCoverageSelectedTypeBeforeRating) {
		// noop
	}

	@Override
	public String getCoverageSuspensionInd() {
		return null;
	}

	@Override
	public void setCoverageSuspensionInd(String newCoverageSuspensionInd) {
		// noop
	}

	@Override
	public String getPrerequisiteCoveragesSelectedInd() {
		return null;
	}

	@Override
	public void setPrerequisiteCoveragesSelectedInd(String newPrerequisiteCoveragesSelectedInd) {
		// noop
	}

	@Override
	public String getCoverageEligibleInd() {
		return this.eligibleInd;
	}

	@Override
	public void setCoverageEligibleInd(String newCoverageEligibleInd) {
		this.eligibleInd = newCoverageEligibleInd;
	}

	@Override
	public String getCoverageEligibleIndBeforeRating() {
		return null;
	}

	@Override
	public void setCoverageEligibleIndBeforeRating(String newCoverageEligibleIndBeforeRating) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalRisks() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalRisks(Integer newNumberOfAdditionalRisks) {
		// noop
	}

	@Override
	public Integer getDeductibleAmountSystem() {
		return null;
	}

	@Override
	public void setDeductibleAmountSystem(Integer newDeductibleAmountSystem) {

	}

	@Override
	public Integer getDeductibleAmountModified() {
		return null;
	}

	@Override
	public void setDeductibleAmountModified(Integer newDeductibleAmountModified) {

	}

	@Override
	public Integer getDeductibleAmount() {
		return new Integer(42); // Set to return something for tests purposes
	}

	@Override
	public void setDeductibleAmount(Integer newDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getReducedDeductibleAmount() {
		return null;
	}

	@Override
	public void setReducedDeductibleAmount(Integer newReducedDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getLimitOfInsuranceSystem() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceSystem(Integer newLimitOfInsuranceSystem) {
		// noop
	}

	@Override
	public Integer getLimitOfInsuranceModified() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceModified(Integer newLimitOfInsuranceModified) {
		// noop
	}

	@Override
	public Long getLimitOfInsurance() {
		return null;
	}

	@Override public void setLimitOfInsurance(Long aLong) {

	}

	@Override
	public String getLimitOfInsuranceText() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceText(String newLimitOfInsuranceText) {
		// noop
	}

	@Override
	public String getLimitOfInsuranceTextInd() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceTextInd(String newLimitOfInsuranceTextInd) {
		// noop
	}

	@Override
	public Integer getLimitOfInsuranceOnPremises() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceOnPremises(Integer newLimitOfInsuranceOnPremises) {
		// noop
	}

	@Override
	public Integer getLimitOfInsuranceOffPremises() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceOffPremises(Integer newLimitOfInsuranceOffPremises) {
		// noop
	}

	@Override
	public Double getLimitOfInsurancePercentageRated() {
		return null;
	}

	@Override
	public void setLimitOfInsurancePercentageRated(Double newLimitOfInsurancePercentageRated) {
		// noop
	}

	@Override
	public Integer getAdditionalLimitOfInsurance() {
		return null;
	}

	@Override
	public void setAdditionalLimitOfInsurance(Integer newAdditionalLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getLimitOfInsuranceFrozen() {
		return null;
	}

	@Override
	public void setLimitOfInsuranceFrozen(Integer newLimitOfInsuranceFrozen) {
		// noop
	}

	@Override
	public Double getLimitMedicalExpensesPerPerson() {
		return null;
	}

	@Override
	public void setLimitMedicalExpensesPerPerson(Double newLimitMedicalExpensesPerPerson) {
		// noop
	}

	@Override
	public Double getLimitMutilationAndDeathIndemnity() {
		return null;
	}

	@Override
	public void setLimitMutilationAndDeathIndemnity(Double newLimitMutilationAndDeathIndemnity) {
		// noop
	}

	@Override
	public Long getStockLimitOfInsurance() {
		return null;
	}

	@Override public void setStockLimitOfInsurance(Long aLong) {

	}

	@Override
	public Long getBuildingLimitOfInsurance() {
		return null;
	}

	@Override public void setBuildingLimitOfInsurance(Long aLong) {

	}

	@Override
	public Long getContentLimitOfInsurance() {
		return null;
	}

	@Override public void setContentLimitOfInsurance(Long aLong) {

	}

	@Override
	public Integer getBusinessInterruptionLimitOfInsurance() {
		return null;
	}

	@Override
	public void setBusinessInterruptionLimitOfInsurance(Integer newBusinessInterruptionLimitOfInsurance) {

	}

	@Override
	public Long getEquipmentLimitOfInsurance() {
		return null;
	}

	@Override public void setEquipmentLimitOfInsurance(Long aLong) {

	}

	@Override
	public Integer getActualLossSustainedLimitOfInsurance() {
		return null;
	}

	@Override
	public void setActualLossSustainedLimitOfInsurance(Integer newActualLossSustainedLimitOfInsurance) {
		// noop
	}

	@Override
	public Double getWeeklyBenefits() {
		return null;
	}

	@Override
	public void setWeeklyBenefits(Double newWeeklyBenefits) {
		// noop
	}

	@Override
	public String getReplacementCost() {
		return null;
	}

	@Override
	public void setReplacementCost(String newReplacementCost) {
		// noop
	}

	@Override
	public Integer getVehicleRateGroupAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAdjustment(Integer newVehicleRateGroupAdjustment) {
		// noop
	}

	@Override
	public String getCappingEligibilityInd() {
		return null;
	}

	@Override
	public void setCappingEligibilityInd(String newCappingEligibilityInd) {
		// noop
	}

	@Override
	public Double getGeneralCappingPercentageMaximum() {
		return null;
	}

	@Override
	public void setGeneralCappingPercentageMaximum(Double newGeneralCappingPercentageMaximum) {
		// noop
	}

	@Override
	public Double getGeneralCappingPercentageMinimum() {
		return null;
	}

	@Override
	public void setGeneralCappingPercentageMinimum(Double newGeneralCappingPercentageMinimum) {
		// noop
	}

	@Override
	public Double getAdjustedCappingPercentageMaximum() {
		return null;
	}

	@Override
	public void setAdjustedCappingPercentageMaximum(Double newAdjustedCappingPercentageMaximum) {
		// noop
	}

	@Override
	public Double getAdjustedCappingPercentageMinimum() {
		return null;
	}

	@Override
	public void setAdjustedCappingPercentageMinimum(Double newAdjustedCappingPercentageMinimum) {
		// noop
	}

	@Override
	public Double getCappingPercentage() {
		return null;
	}

	@Override
	public void setCappingPercentage(Double newCappingPercentage) {
		// noop
	}

	@Override
	public String getDiscountRoundUpInd() {
		return null;
	}

	@Override
	public void setDiscountRoundUpInd(String newDiscountRoundUpInd) {
		// noop
	}

	@Override
	public String getSurchargeRoundUpInd() {
		return null;
	}

	@Override
	public void setSurchargeRoundUpInd(String newSurchargeRoundUpInd) {
		// noop
	}

	@Override
	public Double getReturnOnEquityAdjustmentFactor() {
		return null;
	}

	@Override
	public void setReturnOnEquityAdjustmentFactor(Double newReturnOnEquityAdjustmentFactor) {
		// noop
	}

	@Override
	public Double getReturnOnEquityAdjustmentFactorTarget() {
		return null;
	}

	@Override
	public void setReturnOnEquityAdjustmentFactorTarget(Double newReturnOnEquityAdjustmentFactorTarget) {
		// noop
	}

	@Override
	public Double getReturnOnEquityAdjustmentFactorCostOfCapital() {
		return null;
	}

	@Override
	public void setReturnOnEquityAdjustmentFactorCostOfCapital(Double newReturnOnEquityAdjustmentFactorCostOfCapital) {
		// noop
	}

	@Override
	public Double getReturnOnEquityAdjustmentFactorFloor() {
		return null;
	}

	@Override
	public void setReturnOnEquityAdjustmentFactorFloor(Double newReturnOnEquityAdjustmentFactorFloor) {
		// noop
	}

	@Override
	public String getFullyEarnedEndorsementInd() {
		return null;
	}

	@Override
	public void setFullyEarnedEndorsementInd(String newFullyEarnedEndorsementInd) {
		// noop
	}

	@Override
	public Double getBrokerCommissionPercentageSystem() {
		return null;
	}

	@Override
	public void setBrokerCommissionPercentageSystem(Double newBrokerCommissionPercentageSystem) {
		// noop
	}

	@Override
	public Double getBrokerCommissionPercentageModified() {
		return null;
	}

	@Override
	public void setBrokerCommissionPercentageModified(Double newBrokerCommissionPercentageModified) {
		// noop
	}

	@Override
	public Double getBrokerCommissionPercentage() {
		return null;
	}

	@Override
	public void setBrokerCommissionPercentage(Double newBrokerCommissionPercentage) {
		// noop
	}

	@Override
	public Double getInflationGuardPercentage() {
		return null;
	}

	@Override
	public void setInflationGuardPercentage(Double newInflationGuardPercentage) {
		// noop
	}

	@Override
	public Double getInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setInflationGuardPercentageProRated(Double newInflationGuardPercentageProRated) {
		// noop
	}

	@Override
	public Double getBuildingInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setBuildingInflationGuardPercentageProRated(Double aDouble) {

	}

	@Override
	public Double getContentInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setContentInflationGuardPercentageProRated(Double aDouble) {

	}

	@Override
	public Double getStockInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setStockInflationGuardPercentageProRated(Double aDouble) {

	}

	@Override
	public Double getEquipmentInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setEquipmentInflationGuardPercentageProRated(Double aDouble) {

	}

	@Override
	public Double getBusinessInterruptionInflationGuardPercentageProRated() {
		return null;
	}

	@Override
	public void setBusinessInterruptionInflationGuardPercentageProRated(Double aDouble) {

	}

	@Override
	public String getTechnicalCoverageSource() {
		return null;
	}

	@Override
	public void setTechnicalCoverageSource(String newTechnicalCoverageSource) {
		// noop
	}

	@Override
	public String getSpecialAdvantageCode() {
		return null;
	}

	@Override
	public void setSpecialAdvantageCode(String newSpecialAdvantageCode) {
		// noop
	}

	@Override
	public String getCoinsuranceCodeSystem() {
		return null;
	}

	@Override
	public void setCoinsuranceCodeSystem(String newCoinsuranceCodeSystem) {

	}

	@Override
	public String getCoinsuranceCodeModified() {
		return null;
	}

	@Override
	public void setCoinsuranceCodeModified(String newCoinsuranceCodeModified) {

	}

	@Override
	public String getCoinsuranceCode() {
		return null;
	}

	@Override
	public void setCoinsuranceCode(String newCoinsuranceCode) {
		// noop
	}

	@Override
	public GregorianCalendar getCircDateTime() {
		return null;
	}

	@Override
	public void setCircDateTime(GregorianCalendar newCircDateTime) {
		// noop
	}

	@Override
	public GregorianCalendar getCircCommercialDateTime() {
		return null;
	}

	@Override
	public void setCircCommercialDateTime(GregorianCalendar newCircCommercialDateTime) {
		// noop
	}

	@Override
	public String getTypeOfApplicant() {
		return null;
	}

	@Override
	public void setTypeOfApplicant(String newTypeOfApplicant) {
		// noop
	}

	@Override
	public String getValuationMethod() {
		return null;
	}

	@Override
	public void setValuationMethod(String newValuationMethod) {
		// noop
	}

	@Override
	public String getFormCode() {
		return null;
	}

	@Override
	public void setFormCode(String newFormCode) {
		// noop
	}

	@Override
	public String getFormTextContent() {
		return null;
	}

	@Override
	public void setFormTextContent(String newFormTextContent) {
		// noop
	}

	@Override
	public String getRatingExternalizationEligibilityInd() {
		return null;
	}

	@Override
	public void setRatingExternalizationEligibilityInd(String newRatingExternalizationEligibilityInd) {
		// noop
	}

	@Override
	public String getReinsuredInd() {
		return null;
	}

	@Override
	public void setReinsuredInd(String newReinsuredInd) {
		// noop
	}

	@Override
	public String getSubscribedInd() {
		return null;
	}

	@Override
	public void setSubscribedInd(String newSubscribedInd) {
		// noop
	}

	@Override
	public GregorianCalendar getRetroactiveDate() {
		return null;
	}

	@Override
	public void setRetroactiveDate(GregorianCalendar newRetroactiveDate) {
		// noop
	}

	@Override
	public String getHomeBasedBusinessClass() {
		return null;
	}

	@Override
	public void setHomeBasedBusinessClass(String newHomeBasedBusinessClass) {
		// noop
	}

	@Override
	public Double getRatePerUnit() {
		return null;
	}

	@Override
	public void setRatePerUnit(Double newRatePerUnit) {
		// noop
	}

	@Override
	public Integer getRateUnit() {
		return null;
	}

	@Override
	public void setRateUnit(Integer newRateUnit) {
		// noop
	}

	@Override
	public String getCoverageLevel() {
		return null;
	}

	@Override
	public void setCoverageLevel(String newCoverageLevel) {
		// noop
	}

	@Override
	public Integer getNumberOfAdditionalNamedInsured() {
		return null;
	}

	@Override
	public void setNumberOfAdditionalNamedInsured(Integer newNumberOfAdditionalNamedInsured) {
		// noop
	}

	@Override
	public String getOfficeType() {
		return null;
	}

	@Override
	public void setOfficeType(String newOfficeType) {
		// noop
	}

	@Override
	public String getEmployeeType() {
		return null;
	}

	@Override
	public void setEmployeeType(String newEmployeeType) {
		// noop
	}

	@Override
	public String getMaximumLimitAndLimitPerItemCode() {
		return null;
	}

	@Override
	public void setMaximumLimitAndLimitPerItemCode(String newMaximumLimitAndLimitPerItemCode) {
		// noop
	}

	@Override
	public Integer getEquipmentValue() {
		return null;
	}

	@Override
	public void setEquipmentValue(Integer newEquipmentValue) {
		// noop
	}

	@Override
	public String getRenovationAmplitude() {
		return null;
	}

	@Override
	public void setRenovationAmplitude(String newRenovationAmplitude) {

	}

	@Override
	public String getRenovationValueCode() {
		return null;
	}

	@Override
	public void setRenovationValueCode(String newRenovationValueCode) {
		// noop
	}

	@Override
	public String getClientSignedInd() {
		return null;
	}

	@Override
	public void setClientSignedInd(String newClientSignedInd) {
		// noop
	}

	@Override
	public String getPriceOptimizationRedistributionInd() {
		return null;
	}

	@Override
	public void setPriceOptimizationRedistributionInd(String newPriceOptimizationRedistributionInd) {
		// noop
	}

	@Override
	public ScheduledArticle getTheScheduledArticle() {
		return null;
	}

	@Override
	public void setTheScheduledArticle(ScheduledArticle newTheScheduledArticle) {
		// noop
	}

	@Override
	public ScheduledArticle createTheScheduledArticle() {
		return null;
	}

	@Override
	public ScheduledArticle createTheScheduledArticle(Class<? extends ScheduledArticle> theInterface) {
		return null;
	}

	@Override
	public void clearTheMlModel() {

	}

	@Override
	public List<MlModel> getTheMlModel() {
		return null;
	}

	@Override
	public MlModel getTheMlModel(String s) {
		return null;
	}

	@Override
	public MlModel getTheMlModel(int i) {
		return null;
	}

	@Override
	public MlModel addTheMlModel() {
		return null;
	}

	@Override
	public MlModel addTheMlModel(Class<? extends MlModel> aClass) {
		return null;
	}

	@Override
	public void addTheMlModel(MlModel mlModel) {

	}

	@Override
	public void addTheMlModel(int i, MlModel mlModel) {

	}

	@Override
	public void setTheMlModel(int i, MlModel mlModel) {

	}

	@Override
	public void setTheMlModel(List<MlModel> list) {

	}

	@Override
	public void removeTheMlModel(String s) {

	}

	@Override
	public void removeTheMlModel(int i) {

	}

	@Override
	public void clearTheMlModelCompleted() {

	}

	@Override
	public List<MlModel> getTheMlModelCompleted() {
		return null;
	}

	@Override
	public MlModel getTheMlModelCompleted(String s) {
		return null;
	}

	@Override
	public MlModel getTheMlModelCompleted(int i) {
		return null;
	}

	@Override
	public MlModel addTheMlModelCompleted() {
		return null;
	}

	@Override
	public MlModel addTheMlModelCompleted(Class<? extends MlModel> aClass) {
		return null;
	}

	@Override
	public void addTheMlModelCompleted(MlModel mlModel) {

	}

	@Override
	public void addTheMlModelCompleted(int i, MlModel mlModel) {

	}

	@Override
	public void setTheMlModelCompleted(int i, MlModel mlModel) {

	}

	@Override
	public void setTheMlModelCompleted(List<MlModel> list) {

	}

	@Override
	public void removeTheMlModelCompleted(String s) {

	}

	@Override
	public void removeTheMlModelCompleted(int i) {

	}

	@Override
	public void clearTheAttributeValidValue() {
		// noop
	}

	@Override
	public List<AttributeValidValue> getTheAttributeValidValue() {
		return null;
	}

	@Override
	public AttributeValidValue getTheAttributeValidValue(String uniqueId) {
		return null;
	}

	@Override
	public AttributeValidValue getTheAttributeValidValue(int index) {
		return null;
	}

	@Override
	public AttributeValidValue addTheAttributeValidValue() {
		return null;
	}

	@Override
	public AttributeValidValue addTheAttributeValidValue(Class<? extends AttributeValidValue> theInterface) {
		return null;
	}

	@Override
	public void addTheAttributeValidValue(AttributeValidValue newTheAttributeValidValue) {
		// noop
	}

	@Override
	public void addTheAttributeValidValue(int index, AttributeValidValue newTheAttributeValidValue) {
		// noop
	}

	@Override
	public void setTheAttributeValidValue(int index, AttributeValidValue newTheAttributeValidValue) {
		// noop
	}

	@Override
	public void setTheAttributeValidValue(List<AttributeValidValue> objList) {
		// noop
	}

	@Override
	public void removeTheAttributeValidValue(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAttributeValidValue(int index) {
		// noop
	}

	@Override
	public void clearTheNote() {
		// noop
	}

	@Override
	public List<Note> getTheNote() {
		return null;
	}

	@Override
	public Note getTheNote(String uniqueId) {
		return null;
	}

	@Override
	public Note getTheNote(int index) {
		return null;
	}

	@Override
	public Note addTheNote() {
		return null;
	}

	@Override
	public Note addTheNote(Class<? extends Note> theInterface) {
		return null;
	}

	@Override
	public void addTheNote(Note newTheNote) {
		// noop
	}

	@Override
	public void addTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(List<Note> objList) {
		// noop
	}

	@Override
	public void removeTheNote(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheNote(int index) {
		// noop
	}

	@Override
	public CoverageProduct getTheCoverageProduct() {
		return null;
	}

	@Override
	public void setTheCoverageProduct(CoverageProduct newTheCoverageProduct) {
		// noop
	}

	@Override
	public CoverageProduct createTheCoverageProduct() {
		return null;
	}

	@Override
	public CoverageProduct createTheCoverageProduct(Class<? extends CoverageProduct> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRisk() {
		return null;
	}

	@Override
	public void setTheInsuranceRisk(InsuranceRisk newTheInsuranceRisk) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheAdditionalInterest() {
		// noop
	}

	@Override
	public List<AdditionalInterest> getTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(String uniqueId) {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(int index) {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest(Class<? extends AdditionalInterest> theInterface) {
		return null;
	}

	@Override
	public void addTheAdditionalInterest(AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void addTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(List<AdditionalInterest> objList) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(int index) {
		// noop
	}

	@Override
	public Coverage getTheCoveragePriorTrans() {
		return null;
	}

	@Override
	public void setTheCoveragePriorTrans(Coverage newTheCoveragePriorTrans) {
		// noop
	}

	@Override
	public Coverage createTheCoveragePriorTrans() {
		return null;
	}

	@Override
	public Coverage createTheCoveragePriorTrans(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public CoverageCommercial getTheCoverageCommercial() {
		return null;
	}

	@Override
	public void setTheCoverageCommercial(CoverageCommercial newTheCoverageCommercial) {
		// noop
	}

	@Override
	public CoverageCommercial createTheCoverageCommercial() {
		return null;
	}

	@Override
	public CoverageCommercial createTheCoverageCommercial(Class<? extends CoverageCommercial> theInterface) {
		return null;
	}

	@Override
	public void clearTheQuestion() {
		// noop
	}

	@Override
	public List<Question> getTheQuestion() {
		return null;
	}

	@Override
	public Question getTheQuestion(String uniqueId) {
		return null;
	}

	@Override
	public Question getTheQuestion(int index) {
		return null;
	}

	@Override
	public Question addTheQuestion() {
		return null;
	}

	@Override
	public Question addTheQuestion(Class<? extends Question> theInterface) {
		return null;
	}

	@Override
	public void addTheQuestion(Question newTheQuestion) {
		// noop
	}

	@Override
	public void addTheQuestion(int index, Question newTheQuestion) {
		// noop
	}

	@Override
	public void setTheQuestion(int index, Question newTheQuestion) {
		// noop
	}

	@Override
	public void setTheQuestion(List<Question> objList) {
		// noop
	}

	@Override
	public void removeTheQuestion(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheQuestion(int index) {
		// noop
	}

	@Override
	public CoverageMarine getTheCoverageMarine() {
		return null;
	}

	@Override
	public void setTheCoverageMarine(CoverageMarine newTheCoverageMarine) {
		// noop
	}

	@Override
	public CoverageMarine createTheCoverageMarine() {
		return null;
	}

	@Override
	public CoverageMarine createTheCoverageMarine(Class<? extends CoverageMarine> theInterface) {
		return null;
	}

	@Override
	public void clearTheScheduledArticleItemCoverage() {
		// noop
	}

	@Override
	public List<ScheduledArticleItemCoverage> getTheScheduledArticleItemCoverage() {
		return null;
	}

	@Override
	public ScheduledArticleItemCoverage getTheScheduledArticleItemCoverage(String uniqueId) {
		return null;
	}

	@Override
	public ScheduledArticleItemCoverage getTheScheduledArticleItemCoverage(int index) {
		return null;
	}

	@Override
	public ScheduledArticleItemCoverage addTheScheduledArticleItemCoverage() {
		return null;
	}

	@Override
	public ScheduledArticleItemCoverage addTheScheduledArticleItemCoverage(Class<? extends ScheduledArticleItemCoverage> theInterface) {
		return null;
	}

	@Override
	public void addTheScheduledArticleItemCoverage(ScheduledArticleItemCoverage newTheScheduledArticleItemCoverage) {
		// noop
	}

	@Override
	public void addTheScheduledArticleItemCoverage(int index, ScheduledArticleItemCoverage newTheScheduledArticleItemCoverage) {
		// noop
	}

	@Override
	public void setTheScheduledArticleItemCoverage(int index, ScheduledArticleItemCoverage newTheScheduledArticleItemCoverage) {
		// noop
	}

	@Override
	public void setTheScheduledArticleItemCoverage(List<ScheduledArticleItemCoverage> objList) {
		// noop
	}

	@Override
	public void removeTheScheduledArticleItemCoverage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheScheduledArticleItemCoverage(int index) {
		// noop
	}

	@Override
	public void clearTheReinsurerCoverage() {
		// noop
	}

	@Override
	public List<ReinsurerCoverage> getTheReinsurerCoverage() {
		return null;
	}

	@Override
	public ReinsurerCoverage getTheReinsurerCoverage(String uniqueId) {
		return null;
	}

	@Override
	public ReinsurerCoverage getTheReinsurerCoverage(int index) {
		return null;
	}

	@Override
	public ReinsurerCoverage addTheReinsurerCoverage() {
		return null;
	}

	@Override
	public ReinsurerCoverage addTheReinsurerCoverage(Class<? extends ReinsurerCoverage> theInterface) {
		return null;
	}

	@Override
	public void addTheReinsurerCoverage(ReinsurerCoverage newTheReinsurerCoverage) {
		// noop
	}

	@Override
	public void addTheReinsurerCoverage(int index, ReinsurerCoverage newTheReinsurerCoverage) {
		// noop
	}

	@Override
	public void setTheReinsurerCoverage(int index, ReinsurerCoverage newTheReinsurerCoverage) {
		// noop
	}

	@Override
	public void setTheReinsurerCoverage(List<ReinsurerCoverage> objList) {
		// noop
	}

	@Override
	public void removeTheReinsurerCoverage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheReinsurerCoverage(int index) {
		// noop
	}

	@Override
	public CoveragePremium getTheCoveragePremiumCommercial() {
		return null;
	}

	@Override
	public void setTheCoveragePremiumCommercial(CoveragePremium newTheCoveragePremiumCommercial) {
		// noop
	}

	@Override
	public CoveragePremium createTheCoveragePremiumCommercial() {
		return null;
	}

	@Override
	public CoveragePremium createTheCoveragePremiumCommercial(Class<? extends CoveragePremium> theInterface) {
		return null;
	}

	@Override
	public Coverage getTheCoveragePriorTerm() {
		return null;
	}

	@Override
	public void setTheCoveragePriorTerm(Coverage newTheCoveragePriorTerm) {
		// noop
	}

	@Override
	public Coverage createTheCoveragePriorTerm() {
		return null;
	}

	@Override
	public Coverage createTheCoveragePriorTerm(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskPackage() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskPackage(InsuranceRisk newTheInsuranceRiskPackage) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPackage() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskPackage(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public void clearTheCoveragePackageComposition() {
		// noop
	}

	@Override
	public List<Coverage> getTheCoveragePackageComposition() {
		return null;
	}

	@Override
	public Coverage getTheCoveragePackageComposition(String uniqueId) {
		return null;
	}

	@Override
	public Coverage getTheCoveragePackageComposition(int index) {
		return null;
	}

	@Override
	public Coverage addTheCoveragePackageComposition() {
		return null;
	}

	@Override
	public Coverage addTheCoveragePackageComposition(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public void addTheCoveragePackageComposition(Coverage newTheCoveragePackageComposition) {
		// noop
	}

	@Override
	public void addTheCoveragePackageComposition(int index, Coverage newTheCoveragePackageComposition) {
		// noop
	}

	@Override
	public void setTheCoveragePackageComposition(int index, Coverage newTheCoveragePackageComposition) {
		// noop
	}

	@Override
	public void setTheCoveragePackageComposition(List<Coverage> objList) {
		// noop
	}

	@Override
	public void removeTheCoveragePackageComposition(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheCoveragePackageComposition(int index) {
		// noop
	}

	@Override
	public CoveragePremium getTheCoveragePremiumPrincipal() {
		return this.coveragePremiumPrincipal;
	}

	@Override
	public void setTheCoveragePremiumPrincipal(CoveragePremium newTheCoveragePremiumPrincipal) {
		// noop
	}

	@Override
	public CoveragePremium createTheCoveragePremiumPrincipal() {
		this.coveragePremiumPrincipal = new MockCoveragePremium();
		return this.coveragePremiumPrincipal;
	}

	@Override
	public CoveragePremium createTheCoveragePremiumPrincipal(Class<? extends CoveragePremium> theInterface) {
		return null;
	}

	@Override
	public CoveragePremium getTheCoveragePremiumOccasional() {
		return this.coveragePremiumOccasional;
	}

	@Override
	public void setTheCoveragePremiumOccasional(CoveragePremium newTheCoveragePremiumOccasional) {
		// noop
	}

	@Override
	public CoveragePremium createTheCoveragePremiumOccasional() {
		this.coveragePremiumOccasional = new MockCoveragePremium();
		return this.coveragePremiumOccasional;
	}

	@Override
	public CoveragePremium createTheCoveragePremiumOccasional(Class<? extends CoveragePremium> theInterface) {
		return null;
	}

	@Override
	public String getProductCode() {
		return null;
	}

	@Override
	public void setProductCode(String newProductCode) {
		// noop
	}

	@Override
	public String getCoverageKind() {
		return null;
	}

	@Override
	public void setCoverageKind(String newCoverageKind) {
		// noop
	}

	@Override
	public String getCategory() {
		return null;
	}

	@Override
	public void setCategory(String newCategory) {
		// noop
	}

	@Override
	public String getSubCategory() {
		return null;
	}

	@Override
	public void setSubCategory(String newSubCategory) {
		// noop
	}

	@Override
	public Integer getMinimumDeductibleAmount() {
		return null;
	}

	@Override
	public void setMinimumDeductibleAmount(Integer newMinimumDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getMaximumDeductibleAmount() {
		return null;
	}

	@Override
	public void setMaximumDeductibleAmount(Integer newMaximumDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getUsualDeductibleAmount() {
		return null;
	}

	@Override
	public void setUsualDeductibleAmount(Integer newUsualDeductibleAmount) {
		// noop
	}

	@Override
	public Integer getMinimumLimitOfInsurance() {
		return null;
	}

	@Override
	public void setMinimumLimitOfInsurance(Integer newMinimumLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getMaximumLimitOfInsurance() {
		return null;
	}

	@Override
	public void setMaximumLimitOfInsurance(Integer newMaximumLimitOfInsurance) {
		// noop
	}

	@Override
	public String getCoverageNoteInd() {
		return null;
	}

	@Override
	public void setCoverageNoteInd(String newCoverageNoteInd) {
		// noop
	}

	@Override
	public String getRatingGroup() {
		return null;
	}

	@Override
	public void setRatingGroup(String newRatingGroup) {
		// noop
	}

	@Override
	public String getRatingMethod() {
		return null;
	}

	@Override
	public void setRatingMethod(String newRatingMethod) {
		// noop
	}

	@Override
	public String getPremiumType() {
		return null;
	}

	@Override
	public void setPremiumType(String newPremiumType) {
		// noop
	}

	@Override
	public String getRateType() {
		return null;
	}

	@Override
	public void setRateType(String newRateType) {
		// noop
	}

	@Override
	public String getCoveragePrintingInd() {
		return null;
	}

	@Override
	public void setCoveragePrintingInd(String newCoveragePrintingInd) {
		// noop
	}

	@Override
	public String getRiskAttachableCoverageInd() {
		return null;
	}

	@Override
	public void setRiskAttachableCoverageInd(String newRiskAttachableCoverageInd) {
		// noop
	}

	@Override
	public String getBasicDeductibleInd() {
		return null;
	}

	@Override
	public void setBasicDeductibleInd(String newBasicDeductibleInd) {
		// noop
	}

	@Override
	public Integer getDeductibleAsPercentageOfRelatedCoverage() {
		return null;
	}

	@Override
	public void setDeductibleAsPercentageOfRelatedCoverage(Integer newDeductibleAsPercentageOfRelatedCoverage) {
		// noop
	}

	@Override
	public Integer getUsualLimitOfInsurance() {
		return null;
	}

	@Override
	public void setUsualLimitOfInsurance(Integer newUsualLimitOfInsurance) {
		// noop
	}

	@Override
	public Integer getLimitAsPercentageOfRelatedCoverage() {
		return null;
	}

	@Override
	public void setLimitAsPercentageOfRelatedCoverage(Integer newLimitAsPercentageOfRelatedCoverage) {
		// noop
	}

	@Override
	public String getNonAdjustablePremiumInd() {
		return null;
	}

	@Override
	public void setNonAdjustablePremiumInd(String newNonAdjustablePremiumInd) {
		// noop
	}

	@Override
	public Double getDefaultDeductiblePercentage() {
		return null;
	}

	@Override
	public void setDefaultDeductiblePercentage(Double newDefaultDeductiblePercentage) {

	}

	@Override
	public Integer getDefaultDeductibleAmount() {
		return null;
	}

	@Override
	public void setDefaultDeductibleAmount(Integer newDefaultDeductibleAmount) {

	}

	@Override
	public String getDefaultCoinsuranceCode() {
		return null;
	}

	@Override
	public void setDefaultCoinsuranceCode(String newDefaultCoinsuranceCode) {

	}

	@Override
	public Integer getDefaultLimitOfInsurance() {
		return null;
	}

	@Override
	public void setDefaultLimitOfInsurance(Integer newDefaultLimitOfInsurance) {

	}

	@Override
	public String getCoverageCode() {
		return this.coverageCode;
	}

	@Override
	public void setCoverageCode(String newCoverageCode) {
		this.coverageCode = newCoverageCode;
	}

	@Override
	public String getCoverageCodeNative() {
		return this.coverageCodeNative;
	}

	@Override
	public void setCoverageCodeNative(String newCoverageCodeNative) {
		this.coverageCodeNative = newCoverageCodeNative;
	}

	@Override
	public String getCoverageCodeNativeFromProduct() {
		return null;
	}

	@Override
	public void setCoverageCodeNativeFromProduct(String newCoverageCodeNativeFromProduct) {
		// noop
	}

	@Override
	public String getCoverageType() {
		return this.coverageType == null ? "FR" : this.coverageType;
		// Returns a string other than "EN" by default for better test coverage.
	}

	@Override
	public void setCoverageType(String newCoverageType) {
		this.coverageType = newCoverageType;
	}

	@Override
	public String getCoverageDescription() {
		return null;
	}

	@Override
	public void setCoverageDescription(String newCoverageDescription) {
		// noop
	}

	@Override
	public String getCoverageDescriptionShortEng() {
		return null;
	}

	@Override
	public void setCoverageDescriptionShortEng(String newCoverageDescriptionShortEng) {
		// noop
	}

	@Override
	public String getCoverageDescriptionShortFre() {
		return null;
	}

	@Override
	public void setCoverageDescriptionShortFre(String newCoverageDescriptionShortFre) {
		// noop
	}

	@Override
	public String getCoverageDescriptionPrintEng() {
		return null;
	}

	@Override
	public void setCoverageDescriptionPrintEng(String newCoverageDescriptionPrintEng) {
		// noop
	}

	@Override
	public String getCoverageDescriptionPrintFre() {
		return null;
	}

	@Override
	public void setCoverageDescriptionPrintFre(String newCoverageDescriptionPrintFre) {
		// noop
	}

	@Override
	public Integer getCoverageOrdering() {
		return null;
	}

	@Override
	public void setCoverageOrdering(Integer newCoverageOrdering) {
		// noop
	}

	@Override
	public Integer getRatingPriority() {
		return null;
	}

	@Override
	public void setRatingPriority(Integer newRatingPriority) {

	}

	@Override
	public String getEndorsementApplicabilityType() {
		return null;
	}

	@Override
	public void setEndorsementApplicabilityType(String newEndorsementApplicabilityType) {
		// noop
	}

	@Override
	public String getLineOfInsurance() {
		return null;
	}

	@Override
	public void setLineOfInsurance(String newLineOfInsurance) {
		// noop
	}

	@Override
	public String getSurchargeDiscountInd() {
		return null;
	}

	@Override
	public void setSurchargeDiscountInd(String newSurchargeDiscountInd) {
		// noop
	}

	@Override
	public String getGroupDiscountInd() {
		return null;
	}

	@Override
	public void setGroupDiscountInd(String newGroupDiscountInd) {
		// noop
	}

	@Override
	public String getPremiumOrPercentageInd() {
		return null;
	}

	@Override
	public void setPremiumOrPercentageInd(String newPremiumOrPercentageInd) {
		// noop
	}

	@Override
	public String getCreationFormInd() {
		return null;
	}

	@Override
	public void setCreationFormInd(String newCreationFormInd) {
		// noop
	}

	@Override
	public String getRatingRiskTypeApply() {
		return this.ratingRiskTypeApply;
	}

	@Override
	public void setRatingRiskTypeApply(String newRatingRiskTypeApply) {
		this.ratingRiskTypeApply = newRatingRiskTypeApply;
	}

	@Override
	public String getEndorsementProcessingType() {
		return null;
	}

	@Override
	public void setEndorsementProcessingType(String newEndorsementProcessingType) {
		// noop
	}

	@Override
	public String getEndorsementMethodOfCalculation() {
		return null;
	}

	@Override
	public void setEndorsementMethodOfCalculation(String newEndorsementMethodOfCalculation) {
		// noop
	}

	@Override
	public String getRenewalActionCode() {
		return null;
	}

	@Override
	public void setRenewalActionCode(String newRenewalActionCode) {
		// noop
	}

	@Override
	public Double getRatingFactorLiabilityBodilyInjury() {
		return null;
	}

	@Override
	public void setRatingFactorLiabilityBodilyInjury(Double newRatingFactorLiabilityBodilyInjury) {
		// noop
	}

	@Override
	public Double getRatingFactorLiabilityPropertyDamage() {
		return null;
	}

	@Override
	public void setRatingFactorLiabilityPropertyDamage(Double newRatingFactorLiabilityPropertyDamage) {
		// noop
	}

	@Override
	public Double getRatingFactorAccidentBenefit() {
		return null;
	}

	@Override
	public void setRatingFactorAccidentBenefit(Double newRatingFactorAccidentBenefit) {
		// noop
	}

	@Override
	public Double getRatingFactorAllPerils() {
		return null;
	}

	@Override
	public void setRatingFactorAllPerils(Double newRatingFactorAllPerils) {
		// noop
	}

	@Override
	public Double getRatingFactorCollision() {
		return null;
	}

	@Override
	public void setRatingFactorCollision(Double newRatingFactorCollision) {
		// noop
	}

	@Override
	public Double getRatingFactorComprehensive() {
		return null;
	}

	@Override
	public void setRatingFactorComprehensive(Double newRatingFactorComprehensive) {
		// noop
	}

	@Override
	public Double getRatingFactorSpecifiedPerils() {
		return null;
	}

	@Override
	public void setRatingFactorSpecifiedPerils(Double newRatingFactorSpecifiedPerils) {
		// noop
	}

	@Override
	public Double getRatingFactorLiability() {
		return null;
	}

	@Override
	public void setRatingFactorLiability(Double newRatingFactorLiability) {
		// noop
	}

	@Override
	public Double getRatingFactorMedicalExpenses() {
		return null;
	}

	@Override
	public void setRatingFactorMedicalExpenses(Double newRatingFactorMedicalExpenses) {
		// noop
	}

	@Override
	public Double getRatingFactorTotalDisability() {
		return null;
	}

	@Override
	public void setRatingFactorTotalDisability(Double newRatingFactorTotalDisability) {
		// noop
	}

	@Override
	public String getBrokerAuthorizationEndorsement() {
		return null;
	}

	@Override
	public void setBrokerAuthorizationEndorsement(String newBrokerAuthorizationEndorsement) {
		// noop
	}

	@Override
	public String getCoverageSubtypePrinting() {
		return null;
	}

	@Override
	public void setCoverageSubtypePrinting(String newCoverageSubtypePrinting) {
		// noop
	}

	@Override
	public String getCoverageCodePrinted() {
		return null;
	}

	@Override
	public void setCoverageCodePrinted(String newCoverageCodePrinted) {
		// noop
	}

	@Override
	public String getCoverageGroupPrinting() {
		return null;
	}

	@Override
	public void setCoverageGroupPrinting(String newCoverageGroupPrinting) {

	}

	@Override
	public String getCoverageRatingExternalizedInd() {
		return null;
	}

	@Override
	public void setCoverageRatingExternalizedInd(String newCoverageRatingExternalizedInd) {

	}

	@Override
	public String getAdvantageOfferingOption() {
		return null;
	}

	@Override
	public String getMostRecentUserActionTaken() {
		return null;
	}

	@Override
	public void setAdvantageOfferingOption(String arg0) {
		// noop
	}

	@Override
	public void setMostRecentUserActionTaken(String arg0) {
		// noop
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getCoverageManualInd() {
		return null;
	}

	@Override
	public String getGrandfatherPremiumClauseInd() {
		return null;
	}

	@Override
	public String getPriorTermReplacedCoveragePersistenceUniqueId() {
		return null;
	}

	@Override
	public String getProductType() {
		return null;
	}

	@Override
	public void setCoverageManualInd(String arg0) {
		// noop
	}

	@Override
	public void setGrandfatherPremiumClauseInd(String arg0) {
		// noop
	}

	@Override
	public void setPriorTermReplacedCoveragePersistenceUniqueId(String arg0) {
		// noop
	}

	@Override
	public String getPassengersCarryingReason() {
		return null;
	}

	@Override
	public void setPassengersCarryingReason(String newPassengersCarryingReason) {

	}

	@Override
	public String getPassengersCarryingReasonOther() {
		return null;
	}

	@Override
	public void setPassengersCarryingReasonOther(String newPassengersCarryingReasonOther) {

	}

	@Override
	public String getEarthquakeBasedOnContents() {
		return null;
	}

	@Override
	public void setEarthquakeBasedOnContents(String newEarthquakeBasedOnContents) {

	}

	@Override
	public String getIbcStatClass() {
		return null;
	}

	@Override
	public void setIbcStatClass(String newIbcStatClass) {
		// noop
	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolCeded() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolCeded(Double aDouble) {

	}

	@Override
	public Double getPremiumRetainedPercentageFacilityRiskSharingPool() {
		return null;
	}

	@Override
	public void setPremiumRetainedPercentageFacilityRiskSharingPool(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolStd() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolStd(Double aDouble) {

	}

	@Override
	public Double getFullTermPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setFullTermPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getAdditionalReturnPremiumFacilityRiskSharingPoolFloor() {
		return null;
	}

	@Override
	public void setAdditionalReturnPremiumFacilityRiskSharingPoolFloor(Double aDouble) {

	}

	@Override
	public Double getTermBalancePercentageProRate() {
		return null;
	}

	@Override
	public void setTermBalancePercentageProRate(Double newTermBalancePercentageProRate) {

	}

	@Override
	public String getCollisionCoverageLimitation() {
		return null;
	}

	@Override
	public void setCollisionCoverageLimitation(String newCollisionCoverageLimitation) {

	}

	@Override
	public String getComprehensiveSpCoverageLimitation() {
		return null;
	}

	@Override
	public void setComprehensiveSpCoverageLimitation(String newComprehensiveSpCoverageLimitation) {

	}

	@Override
	public String getAllPerilsCoverageLimitation() {
		return null;
	}

	@Override
	public void setAllPerilsCoverageLimitation(String newAllPerilsCoverageLimitation) {

	}

	@Override
	public String getVehicleModificationValueCode() {
		return null;
	}

	@Override
	public void setVehicleModificationValueCode(String newVehicleModificationValueCode) {

	}

	@Override
	public Double getUbiRelativityScoring() {
		return null;
	}

	@Override
	public void setUbiRelativityScoring(Double newUbiRelativityScoring) {

	}

	@Override
	public String getIntermediaryDataTrace() {
		return null;
	}

	@Override
	public void setIntermediaryDataTrace(String newIntermediaryDataTrace) {

	}

	@Override
	public Integer getHomeworkerGrossProfitFactor() {
		return null;
	}

	@Override
	public void setHomeworkerGrossProfitFactor(Integer newHomeworkerGrossProfitFactor) {

	}

	@Override
	public String getTypeOfLetter() {
		return null;
	}

	@Override
	public void setTypeOfLetter(String s) {

	}

	@Override
	public String getFreeTextUserModifiedInd() {
		return null;
	}

	@Override
	public void setFreeTextUserModifiedInd(String s) {

	}

	@Override
	public Double getUbiRelativityPricing() {
		return null;
	}

	@Override
	public void setUbiRelativityPricing(Double aDouble) {

	}

	@Override public Double getRenewalImpactPremiumPricingForCapping() {
		return null;
	}

	@Override public void setRenewalImpactPremiumPricingForCapping(Double aDouble) {

	}

	@Override public String getParentCoverageUuid() {
		return null;
	}

	@Override public void setParentCoverageUuid(String s) {

	}

	@Override public Double getManualRetainedPremium() {
		return null;
	}

	@Override public void setManualRetainedPremium(Double aDouble) {

	}

	@Override public Double getManualAdditionalReturnRetainedPremium() {
		return null;
	}

	@Override public void setManualAdditionalReturnRetainedPremium(Double aDouble) {

	}

	@Override
	public void setProductType(String arg0) {
		// noop
	}

	@Override
	public void clearTheUnderlyingSchedule() {
		// noop
	}

	@Override
	public List<UnderlyingSchedule> getTheUnderlyingSchedule() {
		return null;
	}

	@Override
	public UnderlyingSchedule getTheUnderlyingSchedule(String uniqueId) {
		return null;
	}

	@Override
	public UnderlyingSchedule getTheUnderlyingSchedule(int index) {
		return null;
	}

	@Override
	public UnderlyingSchedule addTheUnderlyingSchedule() {
		return null;
	}

	@Override
	public UnderlyingSchedule addTheUnderlyingSchedule(Class<? extends UnderlyingSchedule> theInterface) {
		return null;
	}

	@Override
	public void addTheUnderlyingSchedule(UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void addTheUnderlyingSchedule(int index, UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void setTheUnderlyingSchedule(int index, UnderlyingSchedule newTheUnderlyingSchedule) {
		// noop
	}

	@Override
	public void setTheUnderlyingSchedule(List<UnderlyingSchedule> objList) {
		// noop
	}

	@Override
	public void removeTheUnderlyingSchedule(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheUnderlyingSchedule(int index) {
		// noop
	}

	@Override
	public void clearTheParty() {

	}

	@Override
	public List<Party> getTheParty() {
		return null;
	}

	@Override
	public Party getTheParty(String uniqueId) {
		return null;
	}

	@Override
	public Party getTheParty(int index) {
		return null;
	}

	@Override
	public Party addTheParty() {
		return null;
	}

	@Override
	public Party addTheParty(Class<? extends Party> theInterface) {
		return null;
	}

	@Override
	public void addTheParty(Party newTheParty) {

	}

	@Override
	public void addTheParty(int index, Party newTheParty) {

	}

	@Override
	public void setTheParty(int index, Party newTheParty) {

	}

	@Override
	public void setTheParty(List<Party> objList) {

	}

	@Override
	public void removeTheParty(String uniqueId) {

	}

	@Override
	public void removeTheParty(int index) {

	}

	@Override
	public void clearTheCoverageReplacedPriorTrans() {

	}

	@Override
	public List<Coverage> getTheCoverageReplacedPriorTrans() {
		return null;
	}

	@Override
	public Coverage getTheCoverageReplacedPriorTrans(String uniqueId) {
		return null;
	}

	@Override
	public Coverage getTheCoverageReplacedPriorTrans(int index) {
		return null;
	}

	@Override
	public Coverage addTheCoverageReplacedPriorTrans() {
		return null;
	}

	@Override
	public Coverage addTheCoverageReplacedPriorTrans(Class<? extends Coverage> theInterface) {
		return null;
	}

	@Override
	public void addTheCoverageReplacedPriorTrans(Coverage newTheCoverageReplacedPriorTrans) {

	}

	@Override
	public void addTheCoverageReplacedPriorTrans(int index, Coverage newTheCoverageReplacedPriorTrans) {

	}

	@Override
	public void setTheCoverageReplacedPriorTrans(int index, Coverage newTheCoverageReplacedPriorTrans) {

	}

	@Override
	public void setTheCoverageReplacedPriorTrans(List<Coverage> objList) {

	}

	@Override
	public void removeTheCoverageReplacedPriorTrans(String uniqueId) {

	}

	@Override
	public void removeTheCoverageReplacedPriorTrans(int index) {

	}

	@Override
	public void clearTheReinsuranceComponentGroupCoverage() {

	}

	@Override
	public List<ReinsuranceComponentGroupCoverage> getTheReinsuranceComponentGroupCoverage() {
		return null;
	}

	@Override
	public ReinsuranceComponentGroupCoverage getTheReinsuranceComponentGroupCoverage(String s) {
		return null;
	}

	@Override
	public ReinsuranceComponentGroupCoverage getTheReinsuranceComponentGroupCoverage(int i) {
		return null;
	}

	@Override
	public ReinsuranceComponentGroupCoverage addTheReinsuranceComponentGroupCoverage() {
		return null;
	}

	@Override
	public ReinsuranceComponentGroupCoverage addTheReinsuranceComponentGroupCoverage(Class<? extends ReinsuranceComponentGroupCoverage> aClass) {
		return null;
	}

	@Override
	public void addTheReinsuranceComponentGroupCoverage(ReinsuranceComponentGroupCoverage reinsuranceComponentGroupCoverage) {

	}

	@Override
	public void addTheReinsuranceComponentGroupCoverage(int i, ReinsuranceComponentGroupCoverage reinsuranceComponentGroupCoverage) {

	}

	@Override
	public void setTheReinsuranceComponentGroupCoverage(int i, ReinsuranceComponentGroupCoverage reinsuranceComponentGroupCoverage) {

	}

	@Override
	public void setTheReinsuranceComponentGroupCoverage(List<ReinsuranceComponentGroupCoverage> list) {

	}

	@Override
	public void removeTheReinsuranceComponentGroupCoverage(String s) {

	}

	@Override
	public void removeTheReinsuranceComponentGroupCoverage(int i) {

	}

	@Override public void clearTheSubscriptionComponentGroupCoverage() {

	}

	@Override public List<SubscriptionComponentGroupCoverage> getTheSubscriptionComponentGroupCoverage() {
		return null;
	}

	@Override public SubscriptionComponentGroupCoverage getTheSubscriptionComponentGroupCoverage(String s) {
		return null;
	}

	@Override public SubscriptionComponentGroupCoverage getTheSubscriptionComponentGroupCoverage(int i) {
		return null;
	}

	@Override public SubscriptionComponentGroupCoverage addTheSubscriptionComponentGroupCoverage() {
		return null;
	}

	@Override
	public SubscriptionComponentGroupCoverage addTheSubscriptionComponentGroupCoverage(
		Class<? extends SubscriptionComponentGroupCoverage> aClass) {
		return null;
	}

	@Override
	public void addTheSubscriptionComponentGroupCoverage(
		SubscriptionComponentGroupCoverage subscriptionComponentGroupCoverage) {

	}

	@Override
	public void addTheSubscriptionComponentGroupCoverage(int i,
		SubscriptionComponentGroupCoverage subscriptionComponentGroupCoverage) {

	}

	@Override
	public void setTheSubscriptionComponentGroupCoverage(int i,
		SubscriptionComponentGroupCoverage subscriptionComponentGroupCoverage) {

	}

	@Override public void setTheSubscriptionComponentGroupCoverage(List<SubscriptionComponentGroupCoverage> list) {

	}

	@Override public void removeTheSubscriptionComponentGroupCoverage(String s) {

	}

	@Override public void removeTheSubscriptionComponentGroupCoverage(int i) {

	}

}
