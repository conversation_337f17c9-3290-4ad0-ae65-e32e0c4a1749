/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.utils;



import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.DirectChanDistRepEntry;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.singleid.accessmanager.domain.SecureDomain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SecureDomainUtilTest {

    PolicyVersion testedPV;

    @BeforeEach
    public void setUp() {
        this.testedPV = new PolicyVersion();
        InsurancePolicy testedIP = new InsurancePolicy();
        ManufacturingContext testedMC = new ManufacturingContext();
        testedIP.setManufacturingContext(testedMC);
        this.testedPV.setInsurancePolicy(testedIP);

        DirectChanDistRepEntry testedDCDRE = new DirectChanDistRepEntry();
        this.testedPV.setDirectChanDistRepEntry(testedDCDRE);
    }


    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_DIRECTSELLER_DEFAULT(){
        // set distribution channel
        this.testedPV.getInsurancePolicy().getManufacturingContext().setDistributionChannel(DistributionChannelCodeEnum.DIRECT_SELLER);

        // null directChanDisRepEntry test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));

        // set direct channel distributor DEFAULT
        DirectChanDistRepEntry testedDCDRE = new DirectChanDistRepEntry();
        testedDCDRE.setCode(DistributorCodeEnum.DEFAULT);
        this.testedPV.setDirectChanDistRepEntry(testedDCDRE);

        // DEFAULT direchChanDitRepEntry code test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));
    }


    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_DIRECTSELLER_BEL(){
        // set distribution channel
        this.testedPV.getInsurancePolicy().getManufacturingContext().setDistributionChannel(DistributionChannelCodeEnum.DIRECT_SELLER);

        // null directChanDisRepEntry test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));

        // set direct channel distributor BEL
        DirectChanDistRepEntry testedDCDRE = new DirectChanDistRepEntry();
        testedDCDRE.setCode(DistributorCodeEnum.BEL);
        this.testedPV.setDirectChanDistRepEntry(testedDCDRE);

        // BEL direchChanDitRepEntry code test
        assertEquals(SecureDomain.BELAIRDIRECT, SecureDomainUtil.getSecureDomain(this.testedPV));
    }

    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_DIRECTSELLER_BNA(){
        // set distribution channel
        this.testedPV.getInsurancePolicy().getManufacturingContext().setDistributionChannel(DistributionChannelCodeEnum.DIRECT_SELLER);

        // null directChanDisRepEntry test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));

        // set direct channel distributor BNA
        DirectChanDistRepEntry testedDCDRE = new DirectChanDistRepEntry();
        testedDCDRE.setCode(DistributorCodeEnum.BNA);
        this.testedPV.setDirectChanDistRepEntry(testedDCDRE);

        // BNA direchChanDitRepEntry code test
        assertEquals(SecureDomain.BNA, SecureDomainUtil.getSecureDomain(this.testedPV));
    }

    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_DIRECTSELLER_nullDCDRE(){
        // set distribution channel
        this.testedPV.getInsurancePolicy().getManufacturingContext().setDistributionChannel(DistributionChannelCodeEnum.DIRECT_SELLER);

        // null directChanDisRepEntry test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));

        // set direct channel distributor null
        DirectChanDistRepEntry testedDCDRE = new DirectChanDistRepEntry();
        testedDCDRE.setCode(null);
        this.testedPV.setDirectChanDistRepEntry(testedDCDRE);

        // null direchChanDitRepEntry code test
        assertEquals(SecureDomain.DEFAULT, SecureDomainUtil.getSecureDomain(this.testedPV));
    }

    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_THROUGHBROKERS(){
        // set distribution channel
        this.testedPV.getInsurancePolicy().getManufacturingContext().setDistributionChannel(DistributionChannelCodeEnum.THROUGH_BROKERS);

        // null directChanDisRepEntry test
        assertEquals(SecureDomain.INTACT, SecureDomainUtil.getSecureDomain(this.testedPV));

        // set direct channel distributor null
        this.testedPV.setDirectChanDistRepEntry(null);

        // null direchChanDitRepEntry code test
        assertEquals(SecureDomain.INTACT, SecureDomainUtil.getSecureDomain(this.testedPV));
    }

    /**
     * Test method for {@link SecureDomainUtil#getSecureDomain(com.ing.canada.plp.domain.policyversion.PolicyVersion)}.
     */
    @Test
    public void testGetSecureDomain_nulls() {
        this.testedPV = null; // reset tested class for this test's purposes.

        // null test
        assertNull(SecureDomainUtil.getSecureDomain(null));

        // init policyVersion
        this.testedPV = new PolicyVersion();

        // empty policyVersion test
        assertNull(SecureDomainUtil.getSecureDomain(this.testedPV));

        // set insurancePlolicy
        InsurancePolicy testedIP = new InsurancePolicy();
        this.testedPV.setInsurancePolicy(testedIP);

        // empty insurancePolicy test
        assertNull(SecureDomainUtil.getSecureDomain(this.testedPV));

        // set manufacturingContext
        ManufacturingContext testedMC = new ManufacturingContext();
        testedIP.setManufacturingContext(testedMC);

        // empty manufacturingContext test
        assertNull(SecureDomainUtil.getSecureDomain(this.testedPV));

    }

}

