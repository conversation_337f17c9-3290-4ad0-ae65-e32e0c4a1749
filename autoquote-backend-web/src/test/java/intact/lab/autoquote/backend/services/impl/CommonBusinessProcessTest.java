package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.service.IClientService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IPartyHelper;
import intact.lab.autoquote.backend.services.business.common.IBusinessTransactionLoggingService;
import intact.lab.autoquote.backend.services.business.common.impl.CommonBusinessProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
public class CommonBusinessProcessTest {

    @InjectMocks
    private CommonBusinessProcess commonBusinessProcess;

    @Mock
    private IPartyHelper mockPartyHelper;

    @Mock
    private IClientService mockClientService;

    @Mock
    private IBusinessTransactionLoggingService mockBussTransLoggingService;

    private static PolicyVersion currentTestPV;

    @BeforeEach
    public void setUp() {

        // PolicyVersion setup
        currentTestPV = new PolicyVersion(123L);

        InsurancePolicy testInsurancePolicy = new InsurancePolicy();
        ManufacturingContext testContext = new ManufacturingContext();
        testContext.setProvince(ProvinceCodeEnum.ALBERTA);
        testInsurancePolicy.setManufacturingContext(testContext);
        testInsurancePolicy.setManufacturerCompany(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);

        currentTestPV.setInsurancePolicy(testInsurancePolicy);

        // Insurance risk setup
        InsuranceRisk currentTestInsuranceRisk = new InsuranceRisk();
        Vehicle currentTestVehicle = new Vehicle(currentTestInsuranceRisk);
        currentTestVehicle.setId(753L);
        currentTestInsuranceRisk.setVehicle(currentTestVehicle);
        currentTestPV.addInsuranceRisk(currentTestInsuranceRisk);

        // Vehicle detail setup
        VehicleDetailSpecificationRepositoryEntry currentTestVehicleDetail = new VehicleDetailSpecificationRepositoryEntry();
        VehicleRepositoryEntry testVehicleRepoEntry = new VehicleRepositoryEntry();
        currentTestVehicleDetail.setVehicleRepositoryEntry(testVehicleRepoEntry);
        currentTestVehicle.setVehicleDetailSpecificationRepositoryEntry(currentTestVehicleDetail);
    }

    @Test
    public void testGetCifClient() {
        // Setting a party with a cif client id to be returned by the helper
        Party testParty = new Party();
        testParty.setCifClientId(1L);
        when(this.mockPartyHelper.getNamedInsured(any(PolicyVersion.class))).thenReturn(testParty);

        // Setting the mock client to be used for validation
        IClient mockClient = mock(IClient.class);
        when(this.mockClientService.getClientById(anyLong())).thenReturn(mockClient);

        // Executing the tested method and validating the results
        IClient resultClient = this.commonBusinessProcess.getCifClient(currentTestPV);
        assertEquals(mockClient, resultClient);
    }

    @Test
    public void testCreateActivity() {

        Party testParty = new Party();
        testParty.setCifClientId(1L);
        when(this.mockPartyHelper.getNamedInsured(any(PolicyVersion.class))).thenReturn(testParty);

        // Setting the mock client to be used for validation
        IClient mockClient = mock(IClient.class);
        when(this.mockClientService.getClientById(anyLong())).thenReturn(mockClient);

        // Executing the tested method and validating the results
        this.commonBusinessProcess.createActivity(currentTestPV,
                BusinessTransactionActivityCodeEnum.INITIAL_CONVERSION_FROM_AQS, UserTypeCodeEnum.CLIENT);
        verify(this.mockBussTransLoggingService, times(1)).createActivity(any(BusinessTransactionActivityCodeEnum.class),
                anyLong(), eq(null), eq(null), any(UserTypeCodeEnum.class));
    }
}
