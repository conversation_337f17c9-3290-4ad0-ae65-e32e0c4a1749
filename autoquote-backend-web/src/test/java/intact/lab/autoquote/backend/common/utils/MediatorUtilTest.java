package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.*;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComDistributor;
import com.intact.com.enums.ComProvinceCodeEnum;
import org.apache.commons.lang3.NotImplementedException;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class MediatorUtilTest {

    @Test
    void ConvertContext_Should_ReturnManufacturingContext_ForAlberta() {
        ComContext context = new ComContext();
        context.setCompany(ComCompanyEnum.INTACT);
        context.setProvince(ComProvinceCodeEnum.ALBERTA);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);

        ManufacturingContext result = MediatorUtil.convertContext(context);

        assertNotNull(result);
        assertEquals(DistributionChannelCodeEnum.THROUGH_BROKERS, result.getDistributionChannel());
        assertEquals(InsuranceBusinessCodeEnum.REGULAR, result.getInsuranceBusiness());
        assertEquals(ProvinceCodeEnum.ALBERTA, result.getProvince());
        assertEquals(ManufacturerCompanyCodeEnum.ING_WESTERN_REGION, result.getManufacturerCompany());
    }

    @Test
    void ConvertContext_Should_ReturnManufacturingContext_ForOntario() {
        ComContext context = new ComContext();
        context.setCompany(ComCompanyEnum.INTACT);
        context.setProvince(ComProvinceCodeEnum.ONTARIO);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);

        ManufacturingContext result = MediatorUtil.convertContext(context);

        assertNotNull(result);
        assertEquals(DistributionChannelCodeEnum.THROUGH_BROKERS, result.getDistributionChannel());
        assertEquals(InsuranceBusinessCodeEnum.REGULAR, result.getInsuranceBusiness());
        assertEquals(ProvinceCodeEnum.ONTARIO, result.getProvince());
        assertEquals(ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION, result.getManufacturerCompany());
    }

    @Test
    void ConvertContext_Should_ReturnManufacturingContext_ForQuebec() {
        ComContext context = new ComContext();
        context.setCompany(ComCompanyEnum.INTACT);
        context.setProvince(ComProvinceCodeEnum.QUEBEC);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);

        ManufacturingContext result = MediatorUtil.convertContext(context);

        assertNotNull(result);
        assertEquals(DistributionChannelCodeEnum.THROUGH_BROKERS, result.getDistributionChannel());
        assertEquals(InsuranceBusinessCodeEnum.REGULAR, result.getInsuranceBusiness());
        assertEquals(ProvinceCodeEnum.QUEBEC, result.getProvince());
        assertEquals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, result.getManufacturerCompany());
    }

    @Test
    void ConvertContext_Should_ThrowNotImplementedException_ForInvalidProvince() {
        ComContext context = new ComContext();
        context.setCompany(ComCompanyEnum.INTACT);
        context.setProvince(ComProvinceCodeEnum.SASKATCHEWAN);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);

        Exception exception = assertThrows(NotImplementedException.class, () -> MediatorUtil.convertContext(context));
        assertTrue(exception.getMessage().contains("There is currently no context implemented"));
    }

    @Test
    void ConvertContext_Should_ThrowNotImplementedException_ForInvalidCompany() {
        ComContext context = new ComContext();
        context.setCompany(ComCompanyEnum.BELAIR);
        context.setProvince(ComProvinceCodeEnum.QUEBEC);
        context.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);

        Exception exception = assertThrows(NotImplementedException.class, () -> MediatorUtil.convertContext(context));
        assertTrue(exception.getMessage().contains("There is currently no context implemented"));
    }

    @Test
    void GetPLPdistributor_ValidDistributorBEL() {
        DistributorCodeEnum result = MediatorUtil.getPLPdistributor(ComDistributor.BEL);

        assertEquals(DistributorCodeEnum.BEL, result);
    }

    @Test
    void GetPLPdistributor_ValidDistributorBNA() {
        DistributorCodeEnum result = MediatorUtil.getPLPdistributor(ComDistributor.BNA);

        assertEquals(DistributorCodeEnum.BNA, result);
    }

    @Test
    void testGetPLPdistributor_NullDistributor() {
        DistributorCodeEnum result = MediatorUtil.getPLPdistributor(null);

        assertNull(result);
    }
}
