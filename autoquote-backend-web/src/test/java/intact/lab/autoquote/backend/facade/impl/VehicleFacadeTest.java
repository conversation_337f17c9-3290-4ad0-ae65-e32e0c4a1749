package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteVehicleException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.common.utils.MediatorUtil;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.validation.impl.GeneralValidator;
import com.ing.canada.common.domain.VehicleModel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehicleFacadeTest {

    @Mock
    private IVehicleBusinessProcess vehicleBusinessProcess;

    @InjectMocks
    private VehicleFacade vehicleFacade;

    @BeforeEach
    public void setUp() {
        try (MockedStatic<GeneralValidator> mockedValidator = mockStatic(GeneralValidator.class);
             MockedStatic<ContextUtil> mockedContextUtil = mockStatic(ContextUtil.class);
             MockedStatic<MediatorUtil> mockedMediatorUtil = mockStatic(MediatorUtil.class)) {

            mockedValidator.when(() -> GeneralValidator.validateApiParameter("en", "ON"))
                    .thenAnswer(invocationOnMock -> null);

            ManufacturingContext mockContext = new ManufacturingContext();

            mockedContextUtil.when(() -> ContextUtil.loadInitialComContext(null, "ON", null,
                            null, null, null))
                    .thenReturn(new ComContext());
            mockedMediatorUtil.when(() -> MediatorUtil.convertContext(any())).thenReturn(mockContext);
        }
    }

    @Test
    void GetVehicleMakeList_Should_Return_ListOfMakeDTO() {

        List<String> mockMakes = Arrays.asList("ACURA", "AUDI");

        when(vehicleBusinessProcess.getVehicleManufacturerListByYear(any(), any(), any(), any()))
                .thenReturn(mockMakes);

        List<MakeDTO> result = vehicleFacade.getVehicleMakeList("2023", "ON", "en");

        assertEquals(2, result.size());
        assertEquals(mockMakes.getFirst(), result.get(0).getCode());
        assertEquals(mockMakes.getFirst(), result.get(0).getValue());
        assertEquals(mockMakes.get(1), result.get(1).getCode());
        assertEquals(mockMakes.get(1), result.get(1).getValue());
        verify(vehicleBusinessProcess, times(1))
                .getVehicleManufacturerListByYear(any(), any(), any(), any());
    }

    @Test
    void GetVehicleMakeList_Should_ThrowException_WhenInvalidYear() {

        assertThrows(AutoQuoteVehicleException.class, () -> vehicleFacade
                .getVehicleMakeList("XXXXXX", "ON", "en"));
    }

    @Test
    void GetVehicleModels_Should_Return_ListOfModelDTO() {

        VehicleModel model1 = new VehicleModel();
        model1.setCode("123");
        model1.setModel("ACURA");
        VehicleModel model2 = new VehicleModel();
        model2.setCode("456");
        model2.setModel("CAMRY");
        List<VehicleModel> mockModels = Arrays.asList(model1, model2);

        when(vehicleBusinessProcess.getVehicleModelList(any(), anyString(), anyString(), any(), any(), any()))
                .thenReturn(mockModels);

        List<ModelDTO> result = vehicleFacade.getVehicleModels("2023", "ACURA", "ON", "en");

        assertEquals(2, result.size());
        assertEquals("123", result.get(0).getCode());
        assertEquals("ACURA", result.get(0).getValue());
        assertEquals("456", result.get(1).getCode());
        assertEquals("CAMRY", result.get(1).getValue());
        verify(vehicleBusinessProcess, times(1))
                .getVehicleModelList(any(), anyString(), anyString(), any(), any(), any());
    }

    @Test
    void GetVehicleModels_Should_ThrowException_WhenInvalidYear() {

        assertThrows(AutoQuoteVehicleException.class, () ->
                vehicleFacade.getVehicleModels("XXXXXX", "Toyota", "ON", "en"));
    }
}
