/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.agreement.Partnership;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessModel.EnvironmentContext;
import com.ing.canada.som.interfaces.businessModel.ManufacturingContext;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.contactPoint.Address;
import com.ing.canada.som.interfaces.contactPoint.EMailAddress;
import com.ing.canada.som.interfaces.contactPoint.Phone;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.intermediary.DistributorRepositoryEntry;
import com.ing.canada.som.interfaces.intermediary.PreferencePerCriteria;
import com.ing.canada.som.interfaces.intermediary.ProducerRepositoryEntry;
import com.ing.canada.som.interfaces.intermediary.Program;
import com.ing.canada.som.interfaces.party.Consent;
import com.ing.canada.som.interfaces.party.ContactHistory;
import com.ing.canada.som.interfaces.party.CreditScore;
import com.ing.canada.som.interfaces.party.InsuranceHistory;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.som.interfaces.party.PartyCommercialInfo;
import com.ing.canada.som.interfaces.party.PartyGroup;
import com.ing.canada.som.interfaces.party.PartyInsuranceLapse;
import com.ing.canada.som.interfaces.party.PartyParsing;
import com.ing.canada.som.interfaces.party.PartyRelation;
import com.ing.canada.som.interfaces.party.PriorCarrierDeclinedOrCancelled;
import com.ing.canada.som.interfaces.partyRoleInAgreement.AlternateReceiver;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Manufacturer;
import com.ing.canada.som.interfaces.partyRoleInAgreement.PolicyHolder;
import com.ing.canada.som.interfaces.partyRoleInRisk.AdditionalInterest;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo;
import com.ing.canada.som.interfaces.partyRoleInRisk.Insured;
import com.ing.canada.som.interfaces.partyRoleInRisk.LocationOtherOccupant;
import com.ing.canada.som.interfaces.partyRoleInRisk.Owner;
import com.ing.canada.som.interfaces.partyRoleInRisk.ReinsurerRepositoryEntry;
import com.ing.canada.som.interfaces.partyRoleInRisk.SubscriberRepositoryEntry;
import com.ing.canada.som.interfaces.registration.DriverLicense;
import com.ing.canada.som.interfaces.security.Credential;

import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * Mock class for a SOM Party. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Party}
 *
 * <AUTHOR>
 */
public class MockParty implements Party {
	DriverComplementInfo testDCI;

	private String clientEligibilityLevel;

	private String partyType;

	private String ingStaffInd;

	private String persistenceUniqueId;

	private String sex;

	private String uniqueId;

	private List<Claim> testClaimList;

	private GregorianCalendar dateOfBirth;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return this.persistenceUniqueId;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		this.persistenceUniqueId = newPersistenceUniqueId;
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String _uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String _uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String _uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return this.uniqueId;
	}

	@Override
	public void setUniqueId(String string) {
		this.uniqueId = string;
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		// For test purposes, the unique Id will be the only thing determining if tow parties are equal
		return this.uniqueId.equals(_baseObject.getUniqueId());
	}

	@Override
	public String getPartyType() {
		return this.partyType;
	}

	@Override
	public void setPartyType(String newPartyType) {
		this.partyType = newPartyType;
	}

	@Override
	public String getLanguageOfCommunication() {
		return null;
	}

	@Override
	public void setLanguageOfCommunication(String newLanguageOfCommunication) {
		// noop
	}

	@Override
	public String getEdiMachineAddress() {
		return null;
	}

	@Override
	public void setEdiMachineAddress(String newEdiMachineAddress) {
		// noop
	}

	@Override
	public String getBrokerClientId() {
		return null;
	}

	@Override
	public void setBrokerClientId(String newBrokerClientId) {
		// noop
	}

	@Override
	public String getCisClientId() {
		return null;
	}

	@Override
	public void setCisClientId(String newCisClientId) {
		// noop
	}

	@Override
	public Integer getPartyVersionNumber() {
		return null;
	}

	@Override
	public void setPartyVersionNumber(Integer newPartyVersionNumber) {

	}

	@Override
	public GregorianCalendar getDateOfLastMove() {
		return null;
	}

	@Override
	public void setDateOfLastMove(GregorianCalendar newDateOfLastMove) {
		// noop
	}

	@Override
	public String getIngStaffInd() {
		return this.ingStaffInd;
	}

	@Override
	public void setIngStaffInd(String newIngStaffInd) {
		this.ingStaffInd = newIngStaffInd;
	}

	@Override
	public String getStaffFamilyMemberInd() {
		return null;
	}

	@Override
	public void setStaffFamilyMemberInd(String s) {
		// noop
	}

	@Override
	public String getEmployerName() {
		return null;
	}

	@Override
	public void setEmployerName(String newEmployerName) {
		// noop
	}

	@Override
	public String getEmployeeNumber() {
		return null;
	}

	@Override
	public void setEmployeeNumber(String newEmployeeNumber) {
		// noop
	}

	@Override
	public String getSocialInsuranceNumber() {
		return null;
	}

	@Override
	public void setSocialInsuranceNumber(String newSocialInsuranceNumber) {
		// noop
	}

	@Override
	public String getSex() {
		return this.sex;
	}

	@Override
	public void setSex(String newSex) {
		this.sex = newSex;
	}

	@Override
	public String getOccupationCode() {
		return null;
	}

	@Override
	public void setOccupationCode(String newOccupationCode) {
		// noop
	}

	@Override
	public String getOccupation() {
		return null;
	}

	@Override
	public void setOccupation(String newOccupation) {
		// noop
	}

	@Override
	public String getCurrentlyStudyCanadianCollegeOrUniversityInd() {
		return null;
	}

	@Override
	public void setCurrentlyStudyCanadianCollegeOrUniversityInd(String newCurrentlyStudyCanadianCollegeOrUniversityInd) {
		// noop
	}

	@Override
	public String getHolderOfDiplomaFromCanadianUniversityInd() {
		return null;
	}

	@Override
	public void setHolderOfDiplomaFromCanadianUniversityInd(String newHolderOfDiplomaFromCanadianUniversityInd) {
		// noop
	}

	@Override
	public String getEducationLevel() {
		return null;
	}

	@Override
	public void setEducationLevel(String s) {

	}

	@Override
	public String getStudentGradeCode() {
		return null;
	}

	@Override
	public void setStudentGradeCode(String newStudentGradeCode) {
		// noop
	}

	@Override
	public String getStudentStatus() {
		return null;
	}

	@Override
	public void setStudentStatus(String newStudentStatus) {
		// noop
	}

	@Override
	public String getOccupationRiskInd() {
		return null;
	}

	@Override
	public void setOccupationRiskInd(String newOccupationRiskInd) {
		// noop
	}

	@Override
	public String getMaritalStatus() {
		return null;
	}

	@Override
	public void setMaritalStatus(String newMaritalStatus) {
		// noop
	}

	@Override
	public GregorianCalendar getDateOfBirth() {
		return this.dateOfBirth;
	}

	@Override
	public void setDateOfBirth(GregorianCalendar newDateOfBirth) {
		this.dateOfBirth = newDateOfBirth;
	}

	@Override
	public String getDisabilityDescription() {
		return null;
	}

	@Override
	public void setDisabilityDescription(String newDisabilityDescription) {
		// noop
	}

	@Override
	public GregorianCalendar getDisabilityDate() {
		return null;
	}

	@Override
	public void setDisabilityDate(GregorianCalendar newDisabilityDate) {
		// noop
	}

	@Override
	public String getRetiredInd() {
		return null;
	}

	@Override
	public void setRetiredInd(String newRetiredInd) {
		// noop
	}

	@Override
	public GregorianCalendar getRetiredDate() {
		return null;
	}

	@Override
	public void setRetiredDate(GregorianCalendar newRetiredDate) {
		// noop
	}

	@Override
	public String getReceivePensionInd() {
		return null;
	}

	@Override
	public void setReceivePensionInd(String newReceivePensionInd) {
		// noop
	}

	@Override
	public Integer getNumberOfWeeksSinceRetirement() {
		return null;
	}

	@Override
	public void setNumberOfWeeksSinceRetirement(Integer newNumberOfWeeksSinceRetirement) {
		// noop
	}

	@Override
	public String getFirstName() {
		return null;
	}

	@Override
	public void setFirstName(String newFirstName) {
		// noop
	}

	@Override
	public String getLastName() {
		return null;
	}

	@Override
	public void setLastName(String newLastName) {
		// noop
	}

	@Override
	public String getMiddleName() {
		return null;
	}

	@Override
	public void setMiddleName(String newMiddleName) {
		// noop
	}

	@Override
	public String getSuffixName() {
		return null;
	}

	@Override
	public void setSuffixName(String newSuffixName) {
		// noop
	}

	@Override
	public String getUnstructuredName() {
		return null;
	}

	@Override
	public void setUnstructuredName(String newUnstructuredName) {
		// noop
	}

	@Override
	public String getCompanyCode() {
		return null;
	}

	@Override
	public void setCompanyCode(String newCompanyCode) {
		// noop
	}

	@Override
	public String getUnderwritingCompanyHarmonized() {
		return null;
	}

	@Override
	public void setUnderwritingCompanyHarmonized(String newUnderwritingCompanyHarmonized) {
		// noop
	}

	@Override
	public String getExternalCompanyCode() {
		return null;
	}

	@Override
	public void setExternalCompanyCode(String newExternalCompanyCode) {

	}

	@Override
	public String getClientEligibilityLevel() {
		return this.clientEligibilityLevel;
	}

	@Override
	public void setClientEligibilityLevel(String newClientEligibilityLevel) {
		this.clientEligibilityLevel = newClientEligibilityLevel;
	}

	@Override
	public String getCreditScoreClientEligibilityInd() {
		return null;
	}

	@Override
	public void setCreditScoreClientEligibilityInd(String newCreditScoreClientEligibilityInd) {
		// noop
	}

	@Override
	public Integer getAdditionalInterestSequence() {
		return null;
	}

	@Override
	public void setAdditionalInterestSequence(Integer newAdditionalInterestSequence) {
		// noop
	}

	@Override
	public String getCriminalRecordStatus() {
		return null;
	}

	@Override
	public void setCriminalRecordStatus(String newCriminalRecordStatus) {
		// noop
	}

	@Override
	public Integer getNumberOfInsufficientFundsToDate() {
		return null;
	}

	@Override
	public void setNumberOfInsufficientFundsToDate(Integer newNumberOfInsufficientFundsToDate) {
		// noop
	}

	@Override
	public String getWorkingInd() {
		return null;
	}

	@Override
	public void setWorkingInd(String newWorkingInd) {
		// noop
	}

	@Override
	public String getWebAddress() {
		return null;
	}

	@Override
	public void setWebAddress(String newWebAddress) {
		// noop
	}

	@Override
	public void clearThePartyInsuranceLapse() {
		// noop
	}

	@Override
	public List<PartyInsuranceLapse> getThePartyInsuranceLapse() {
		return null;
	}

	@Override
	public PartyInsuranceLapse getThePartyInsuranceLapse(String _uniqueId) {
		return null;
	}

	@Override
	public PartyInsuranceLapse getThePartyInsuranceLapse(int index) {
		return null;
	}

	@Override
	public PartyInsuranceLapse addThePartyInsuranceLapse() {
		return null;
	}

	@Override
	public PartyInsuranceLapse addThePartyInsuranceLapse(Class<? extends PartyInsuranceLapse> theInterface) {
		return null;
	}

	@Override
	public void addThePartyInsuranceLapse(PartyInsuranceLapse newThePartyInsuranceLapse) {
		// noop
	}

	@Override
	public void addThePartyInsuranceLapse(int index, PartyInsuranceLapse newThePartyInsuranceLapse) {
		// noop
	}

	@Override
	public void setThePartyInsuranceLapse(int index, PartyInsuranceLapse newThePartyInsuranceLapse) {
		// noop
	}

	@Override
	public void setThePartyInsuranceLapse(List<PartyInsuranceLapse> objList) {
		// noop
	}

	@Override
	public void removeThePartyInsuranceLapse(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePartyInsuranceLapse(int index) {
		// noop
	}

	@Override
	public void clearThePartnership() {

	}

	@Override
	public List<Partnership> getThePartnership() {
		return null;
	}

	@Override
	public Partnership getThePartnership(String s) {
		return null;
	}

	@Override
	public Partnership getThePartnership(int i) {
		return null;
	}

	@Override
	public Partnership addThePartnership() {
		return null;
	}

	@Override
	public Partnership addThePartnership(Class<? extends Partnership> aClass) {
		return null;
	}

	@Override
	public void addThePartnership(Partnership partnership) {

	}

	@Override
	public void addThePartnership(int i, Partnership partnership) {

	}

	@Override
	public void setThePartnership(int i, Partnership partnership) {

	}

	@Override
	public void setThePartnership(List<Partnership> list) {

	}

	@Override
	public void removeThePartnership(String s) {

	}

	@Override
	public void removeThePartnership(int i) {

	}

	@Override
	public EnvironmentContext getTheEnvironmentContext() {
		return null;
	}

	@Override
	public void setTheEnvironmentContext(EnvironmentContext newTheEnvironmentContext) {
		// noop
	}

	@Override
	public EnvironmentContext createTheEnvironmentContext() {
		return null;
	}

	@Override
	public EnvironmentContext createTheEnvironmentContext(Class<? extends EnvironmentContext> theInterface) {
		return null;
	}

	@Override
	public void clearTheClaim() {
		// noop
	}

	@Override
	public List<Claim> getTheClaim() {
		//For test purposes, if the list is null, create it and add a single test claim before returning the list.
		if (this.testClaimList == null) {
			this.testClaimList = new ArrayList<Claim>();
			MockClaim testClaim = new MockClaim();
			this.testClaimList.add(testClaim);
		}
		return this.testClaimList;
	}

	@Override
	public Claim getTheClaim(String _uniqueId) {
		return null;
	}

	@Override
	public Claim getTheClaim(int index) {
		return null;
	}

	@Override
	public Claim addTheClaim() {
		return null;
	}

	@Override
	public Claim addTheClaim(Class<? extends Claim> theInterface) {
		return null;
	}

	@Override
	public void addTheClaim(Claim newTheClaim) {
		// noop
	}

	@Override
	public void addTheClaim(int index, Claim newTheClaim) {
		// noop
	}

	@Override
	public void setTheClaim(int index, Claim newTheClaim) {
		// noop
	}

	@Override
	public void setTheClaim(List<Claim> objList) {
		// noop
	}

	@Override
	public void removeTheClaim(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheClaim(int index) {
		// noop
	}

	@Override
	public void clearTheAddress() {
		// noop
	}

	@Override
	public List<Address> getTheAddress() {
		return null;
	}

	@Override
	public Address getTheAddress(String _uniqueId) {
		return null;
	}

	@Override
	public Address getTheAddress(int index) {
		return null;
	}

	@Override
	public Address addTheAddress() {
		return null;
	}

	@Override
	public Address addTheAddress(Class<? extends Address> theInterface) {
		return null;
	}

	@Override
	public void addTheAddress(Address newTheAddress) {
		// noop
	}

	@Override
	public void addTheAddress(int index, Address newTheAddress) {
		// noop
	}

	@Override
	public void setTheAddress(int index, Address newTheAddress) {
		// noop
	}

	@Override
	public void setTheAddress(List<Address> objList) {
		// noop
	}

	@Override
	public void removeTheAddress(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAddress(int index) {
		// noop
	}

	@Override
	public void clearThePhone() {
		// noop
	}

	@Override
	public List<Phone> getThePhone() {
		return null;
	}

	@Override
	public Phone getThePhone(String _uniqueId) {
		return null;
	}

	@Override
	public Phone getThePhone(int index) {
		return null;
	}

	@Override
	public Phone addThePhone() {
		return null;
	}

	@Override
	public Phone addThePhone(Class<? extends Phone> theInterface) {
		return null;
	}

	@Override
	public void addThePhone(Phone newThePhone) {
		// noop
	}

	@Override
	public void addThePhone(int index, Phone newThePhone) {
		// noop
	}

	@Override
	public void setThePhone(int index, Phone newThePhone) {
		// noop
	}

	@Override
	public void setThePhone(List<Phone> objList) {
		// noop
	}

	@Override
	public void removeThePhone(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePhone(int index) {
		// noop
	}

	@Override
	public void clearTheEMailAddress() {
		// noop
	}

	@Override
	public List<EMailAddress> getTheEMailAddress() {
		return null;
	}

	@Override
	public EMailAddress getTheEMailAddress(String _uniqueId) {
		return null;
	}

	@Override
	public EMailAddress getTheEMailAddress(int index) {
		return null;
	}

	@Override
	public EMailAddress addTheEMailAddress() {
		return null;
	}

	@Override
	public EMailAddress addTheEMailAddress(Class<? extends EMailAddress> theInterface) {
		return null;
	}

	@Override
	public void addTheEMailAddress(EMailAddress newTheEMailAddress) {
		// noop
	}

	@Override
	public void addTheEMailAddress(int index, EMailAddress newTheEMailAddress) {
		// noop
	}

	@Override
	public void setTheEMailAddress(int index, EMailAddress newTheEMailAddress) {
		// noop
	}

	@Override
	public void setTheEMailAddress(List<EMailAddress> objList) {
		// noop
	}

	@Override
	public void removeTheEMailAddress(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheEMailAddress(int index) {
		// noop
	}

	@Override
	public ProducerRepositoryEntry getTheProducerRepositoryEntry() {
		return null;
	}

	@Override
	public void setTheProducerRepositoryEntry(ProducerRepositoryEntry newTheProducerRepositoryEntry) {
		// noop
	}

	@Override
	public ProducerRepositoryEntry createTheProducerRepositoryEntry() {
		return null;
	}

	@Override
	public ProducerRepositoryEntry createTheProducerRepositoryEntry(Class<? extends ProducerRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public DistributorRepositoryEntry getTheDistributorRepositoryEntry() {
		return null;
	}

	@Override
	public void setTheDistributorRepositoryEntry(DistributorRepositoryEntry newTheDistributorRepositoryEntry) {
		// noop
	}

	@Override
	public DistributorRepositoryEntry createTheDistributorRepositoryEntry() {
		return null;
	}

	@Override
	public DistributorRepositoryEntry createTheDistributorRepositoryEntry(Class<? extends DistributorRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public void clearTheCreditScore() {
		// noop
	}

	@Override
	public List<CreditScore> getTheCreditScore() {
		return null;
	}

	@Override
	public CreditScore getTheCreditScore(String _uniqueId) {
		return null;
	}

	@Override
	public CreditScore getTheCreditScore(int index) {
		return null;
	}

	@Override
	public CreditScore addTheCreditScore() {
		return null;
	}

	@Override
	public CreditScore addTheCreditScore(Class<? extends CreditScore> theInterface) {
		return null;
	}

	@Override
	public void addTheCreditScore(CreditScore newTheCreditScore) {
		// noop
	}

	@Override
	public void addTheCreditScore(int index, CreditScore newTheCreditScore) {
		// noop
	}

	@Override
	public void setTheCreditScore(int index, CreditScore newTheCreditScore) {
		// noop
	}

	@Override
	public void setTheCreditScore(List<CreditScore> objList) {
		// noop
	}

	@Override
	public void removeTheCreditScore(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheCreditScore(int index) {
		// noop
	}

	@Override
	public void clearTheConsent() {
		// noop
	}

	@Override
	public List<Consent> getTheConsent() {
		return null;
	}

	@Override
	public Consent getTheConsent(String _uniqueId) {
		return null;
	}

	@Override
	public Consent getTheConsent(int index) {
		return null;
	}

	@Override
	public Consent addTheConsent() {
		return null;
	}

	@Override
	public Consent addTheConsent(Class<? extends Consent> theInterface) {
		return null;
	}

	@Override
	public void addTheConsent(Consent newTheConsent) {
		// noop
	}

	@Override
	public void addTheConsent(int index, Consent newTheConsent) {
		// noop
	}

	@Override
	public void setTheConsent(int index, Consent newTheConsent) {
		// noop
	}

	@Override
	public void setTheConsent(List<Consent> objList) {
		// noop
	}

	@Override
	public void removeTheConsent(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheConsent(int index) {
		// noop
	}

	@Override
	public DriverComplementInfo getTheDriverComplementInfo() {
		this.testDCI = this.testDCI == null ? new MockDriverComplementInfo() : this.testDCI;
		return this.testDCI;
	}

	@Override
	public void setTheDriverComplementInfo(DriverComplementInfo newTheDriverComplementInfo) {
		// noop
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfo() {
		return null;
	}

	@Override
	public DriverComplementInfo createTheDriverComplementInfo(Class<? extends DriverComplementInfo> theInterface) {
		return null;
	}

	@Override
	public void clearThePartyGroup() {
		// noop
	}

	@Override
	public List<PartyGroup> getThePartyGroup() {
		return null;
	}

	@Override
	public PartyGroup getThePartyGroup(String _uniqueId) {
		return null;
	}

	@Override
	public PartyGroup getThePartyGroup(int index) {
		return null;
	}

	@Override
	public PartyGroup addThePartyGroup() {
		return null;
	}

	@Override
	public PartyGroup addThePartyGroup(Class<? extends PartyGroup> theInterface) {
		return null;
	}

	@Override
	public void addThePartyGroup(PartyGroup newThePartyGroup) {
		// noop
	}

	@Override
	public void addThePartyGroup(int index, PartyGroup newThePartyGroup) {
		// noop
	}

	@Override
	public void setThePartyGroup(int index, PartyGroup newThePartyGroup) {
		// noop
	}

	@Override
	public void setThePartyGroup(List<PartyGroup> objList) {
		// noop
	}

	@Override
	public void removeThePartyGroup(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePartyGroup(int index) {
		// noop
	}

	@Override
	public ManufacturingContext getTheManufacturingContext() {
		return null;
	}

	@Override
	public void setTheManufacturingContext(ManufacturingContext newTheManufacturingContext) {
		// noop
	}

	@Override
	public ManufacturingContext createTheManufacturingContext() {
		return null;
	}

	@Override
	public ManufacturingContext createTheManufacturingContext(Class<? extends ManufacturingContext> theInterface) {
		return null;
	}

	@Override
	public void clearTheCredential() {
		// noop
	}

	@Override
	public List<Credential> getTheCredential() {
		return null;
	}

	@Override
	public Credential getTheCredential(String _uniqueId) {
		return null;
	}

	@Override
	public Credential getTheCredential(int index) {
		return null;
	}

	@Override
	public Credential addTheCredential() {
		return null;
	}

	@Override
	public Credential addTheCredential(Class<? extends Credential> theInterface) {
		return null;
	}

	@Override
	public void addTheCredential(Credential newTheCredential) {
		// noop
	}

	@Override
	public void addTheCredential(int index, Credential newTheCredential) {
		// noop
	}

	@Override
	public void setTheCredential(int index, Credential newTheCredential) {
		// noop
	}

	@Override
	public void setTheCredential(List<Credential> objList) {
		// noop
	}

	@Override
	public void removeTheCredential(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheCredential(int index) {
		// noop
	}

	@Override
	public void clearTheContactHistory() {
		// noop
	}

	@Override
	public List<ContactHistory> getTheContactHistory() {
		return null;
	}

	@Override
	public ContactHistory getTheContactHistory(String _uniqueId) {
		return null;
	}

	@Override
	public ContactHistory getTheContactHistory(int index) {
		return null;
	}

	@Override
	public ContactHistory addTheContactHistory() {
		return null;
	}

	@Override
	public ContactHistory addTheContactHistory(Class<? extends ContactHistory> theInterface) {
		return null;
	}

	@Override
	public void addTheContactHistory(ContactHistory newTheContactHistory) {
		// noop
	}

	@Override
	public void addTheContactHistory(int index, ContactHistory newTheContactHistory) {
		// noop
	}

	@Override
	public void setTheContactHistory(int index, ContactHistory newTheContactHistory) {
		// noop
	}

	@Override
	public void setTheContactHistory(List<ContactHistory> objList) {
		// noop
	}

	@Override
	public void removeTheContactHistory(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheContactHistory(int index) {
		// noop
	}

	@Override
	public PartyCommercialInfo getThePartyCommercialInfo() {
		return null;
	}

	@Override
	public void setThePartyCommercialInfo(PartyCommercialInfo newThePartyCommercialInfo) {
		// noop
	}

	@Override
	public PartyCommercialInfo createThePartyCommercialInfo() {
		return null;
	}

	@Override
	public PartyCommercialInfo createThePartyCommercialInfo(Class<? extends PartyCommercialInfo> theInterface) {
		return null;
	}

	@Override
	public void clearThePriorCarrierDeclinedOrCancelled() {
		// noop
	}

	@Override
	public List<PriorCarrierDeclinedOrCancelled> getThePriorCarrierDeclinedOrCancelled() {
		return null;
	}

	@Override
	public PriorCarrierDeclinedOrCancelled getThePriorCarrierDeclinedOrCancelled(String _uniqueId) {
		return null;
	}

	@Override
	public PriorCarrierDeclinedOrCancelled getThePriorCarrierDeclinedOrCancelled(int index) {
		return null;
	}

	@Override
	public PriorCarrierDeclinedOrCancelled addThePriorCarrierDeclinedOrCancelled() {
		return null;
	}

	@Override
	public PriorCarrierDeclinedOrCancelled addThePriorCarrierDeclinedOrCancelled(Class<? extends PriorCarrierDeclinedOrCancelled> theInterface) {
		return null;
	}

	@Override
	public void addThePriorCarrierDeclinedOrCancelled(PriorCarrierDeclinedOrCancelled newThePriorCarrierDeclinedOrCancelled) {
		// noop
	}

	@Override
	public void addThePriorCarrierDeclinedOrCancelled(int index, PriorCarrierDeclinedOrCancelled newThePriorCarrierDeclinedOrCancelled) {
		// noop
	}

	@Override
	public void setThePriorCarrierDeclinedOrCancelled(int index, PriorCarrierDeclinedOrCancelled newThePriorCarrierDeclinedOrCancelled) {
		// noop
	}

	@Override
	public void setThePriorCarrierDeclinedOrCancelled(List<PriorCarrierDeclinedOrCancelled> objList) {
		// noop
	}

	@Override
	public void removeThePriorCarrierDeclinedOrCancelled(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePriorCarrierDeclinedOrCancelled(int index) {
		// noop
	}

	@Override
	public Party getThePartyPriorTrans() {
		return null;
	}

	@Override
	public void setThePartyPriorTrans(Party newThePartyPriorTrans) {
		// noop
	}

	@Override
	public Party createThePartyPriorTrans() {
		return null;
	}

	@Override
	public Party createThePartyPriorTrans(Class<? extends Party> theInterface) {
		return null;
	}

	@Override
	public void clearThePartyRelationTo() {
		// noop
	}

	@Override
	public List<PartyRelation> getThePartyRelationTo() {
		return null;
	}

	@Override
	public PartyRelation getThePartyRelationTo(String _uniqueId) {
		return null;
	}

	@Override
	public PartyRelation getThePartyRelationTo(int index) {
		return null;
	}

	@Override
	public PartyRelation addThePartyRelationTo() {
		return null;
	}

	@Override
	public PartyRelation addThePartyRelationTo(Class<? extends PartyRelation> theInterface) {
		return null;
	}

	@Override
	public void addThePartyRelationTo(PartyRelation newThePartyRelationTo) {
		// noop
	}

	@Override
	public void addThePartyRelationTo(int index, PartyRelation newThePartyRelationTo) {
		// noop
	}

	@Override
	public void setThePartyRelationTo(int index, PartyRelation newThePartyRelationTo) {
		// noop
	}

	@Override
	public void setThePartyRelationTo(List<PartyRelation> objList) {
		// noop
	}

	@Override
	public void removeThePartyRelationTo(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePartyRelationTo(int index) {
		// noop
	}

	@Override
	public void clearTheInsuranceHistory() {
		// noop
	}

	@Override
	public List<InsuranceHistory> getTheInsuranceHistory() {
		return null;
	}

	@Override
	public InsuranceHistory getTheInsuranceHistory(String _uniqueId) {
		return null;
	}

	@Override
	public InsuranceHistory getTheInsuranceHistory(int index) {
		return null;
	}

	@Override
	public InsuranceHistory addTheInsuranceHistory() {
		return null;
	}

	@Override
	public InsuranceHistory addTheInsuranceHistory(Class<? extends InsuranceHistory> theInterface) {
		return null;
	}

	@Override
	public void addTheInsuranceHistory(InsuranceHistory newTheInsuranceHistory) {
		// noop
	}

	@Override
	public void addTheInsuranceHistory(int index, InsuranceHistory newTheInsuranceHistory) {
		// noop
	}

	@Override
	public void setTheInsuranceHistory(int index, InsuranceHistory newTheInsuranceHistory) {
		// noop
	}

	@Override
	public void setTheInsuranceHistory(List<InsuranceHistory> objList) {
		// noop
	}

	@Override
	public void removeTheInsuranceHistory(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheInsuranceHistory(int index) {
		// noop
	}

	@Override
	public void clearTheDriver() {
		// noop
	}

	@Override
	public List<Driver> getTheDriver() {
		return null;
	}

	@Override
	public Driver getTheDriver(String _uniqueId) {
		return null;
	}

	@Override
	public Driver getTheDriver(int index) {
		return null;
	}

	@Override
	public Driver addTheDriver() {
		return null;
	}

	@Override
	public Driver addTheDriver(Class<? extends Driver> theInterface) {
		return null;
	}

	@Override
	public void addTheDriver(Driver newTheDriver) {
		// noop
	}

	@Override
	public void addTheDriver(int index, Driver newTheDriver) {
		// noop
	}

	@Override
	public void setTheDriver(int index, Driver newTheDriver) {
		// noop
	}

	@Override
	public void setTheDriver(List<Driver> objList) {
		// noop
	}

	@Override
	public void removeTheDriver(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDriver(int index) {
		// noop
	}

	@Override
	public void clearThePolicyHolder() {
		// noop
	}

	@Override
	public List<PolicyHolder> getThePolicyHolder() {
		return null;
	}

	@Override
	public PolicyHolder getThePolicyHolder(String _uniqueId) {
		return null;
	}

	@Override
	public PolicyHolder getThePolicyHolder(int index) {
		return null;
	}

	@Override
	public PolicyHolder addThePolicyHolder() {
		return null;
	}

	@Override
	public PolicyHolder addThePolicyHolder(Class<? extends PolicyHolder> theInterface) {
		return null;
	}

	@Override
	public void addThePolicyHolder(PolicyHolder newThePolicyHolder) {
		// noop
	}

	@Override
	public void addThePolicyHolder(int index, PolicyHolder newThePolicyHolder) {
		// noop
	}

	@Override
	public void setThePolicyHolder(int index, PolicyHolder newThePolicyHolder) {
		// noop
	}

	@Override
	public void setThePolicyHolder(List<PolicyHolder> objList) {
		// noop
	}

	@Override
	public void removeThePolicyHolder(String _uniqueId) {
		// noop
	}

	@Override
	public void removeThePolicyHolder(int index) {
		// noop
	}

	@Override
	public void clearTheManufacturer() {
		// noop
	}

	@Override
	public List<Manufacturer> getTheManufacturer() {
		return null;
	}

	@Override
	public Manufacturer getTheManufacturer(String _uniqueId) {
		return null;
	}

	@Override
	public Manufacturer getTheManufacturer(int index) {
		return null;
	}

	@Override
	public Manufacturer addTheManufacturer() {
		return null;
	}

	@Override
	public Manufacturer addTheManufacturer(Class<? extends Manufacturer> theInterface) {
		return null;
	}

	@Override
	public void addTheManufacturer(Manufacturer newTheManufacturer) {
		// noop
	}

	@Override
	public void addTheManufacturer(int index, Manufacturer newTheManufacturer) {
		// noop
	}

	@Override
	public void setTheManufacturer(int index, Manufacturer newTheManufacturer) {
		// noop
	}

	@Override
	public void setTheManufacturer(List<Manufacturer> objList) {
		// noop
	}

	@Override
	public void removeTheManufacturer(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheManufacturer(int index) {
		// noop
	}

	@Override
	public void clearTheAdditionalInterest() {
		// noop
	}

	@Override
	public List<AdditionalInterest> getTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(String _uniqueId) {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(int index) {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest(Class<? extends AdditionalInterest> theInterface) {
		return null;
	}

	@Override
	public void addTheAdditionalInterest(AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void addTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(List<AdditionalInterest> objList) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(int index) {
		// noop
	}

	@Override
	public void clearTheOwner() {
		// noop
	}

	@Override
	public List<Owner> getTheOwner() {
		return null;
	}

	@Override
	public Owner getTheOwner(String _uniqueId) {
		return null;
	}

	@Override
	public Owner getTheOwner(int index) {
		return null;
	}

	@Override
	public Owner addTheOwner() {
		return null;
	}

	@Override
	public Owner addTheOwner(Class<? extends Owner> theInterface) {
		return null;
	}

	@Override
	public void addTheOwner(Owner newTheOwner) {
		// noop
	}

	@Override
	public void addTheOwner(int index, Owner newTheOwner) {
		// noop
	}

	@Override
	public void setTheOwner(int index, Owner newTheOwner) {
		// noop
	}

	@Override
	public void setTheOwner(List<Owner> objList) {
		// noop
	}

	@Override
	public void removeTheOwner(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheOwner(int index) {
		// noop
	}

	@Override
	public void clearTheInsured() {
		// noop
	}

	@Override
	public List<Insured> getTheInsured() {
		return null;
	}

	@Override
	public Insured getTheInsured(String _uniqueId) {
		return null;
	}

	@Override
	public Insured getTheInsured(int index) {
		return null;
	}

	@Override
	public Insured addTheInsured() {
		return null;
	}

	@Override
	public Insured addTheInsured(Class<? extends Insured> theInterface) {
		return null;
	}

	@Override
	public void addTheInsured(Insured newTheInsured) {
		// noop
	}

	@Override
	public void addTheInsured(int index, Insured newTheInsured) {
		// noop
	}

	@Override
	public void setTheInsured(int index, Insured newTheInsured) {
		// noop
	}

	@Override
	public void setTheInsured(List<Insured> objList) {
		// noop
	}

	@Override
	public void removeTheInsured(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheInsured(int index) {
		// noop
	}

	@Override
	public ReinsurerRepositoryEntry getTheReinsurerRepositoryEntry() {
		return null;
	}

	@Override
	public void setTheReinsurerRepositoryEntry(ReinsurerRepositoryEntry newTheReinsurerRepositoryEntry) {
		// noop
	}

	@Override
	public ReinsurerRepositoryEntry createTheReinsurerRepositoryEntry() {
		return null;
	}

	@Override
	public ReinsurerRepositoryEntry createTheReinsurerRepositoryEntry(Class<? extends ReinsurerRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public void clearTheAlternateReceiver() {
		// noop
	}

	@Override
	public List<AlternateReceiver> getTheAlternateReceiver() {
		return null;
	}

	@Override
	public AlternateReceiver getTheAlternateReceiver(String _uniqueId) {
		return null;
	}

	@Override
	public AlternateReceiver getTheAlternateReceiver(int index) {
		return null;
	}

	@Override
	public AlternateReceiver addTheAlternateReceiver() {
		return null;
	}

	@Override
	public AlternateReceiver addTheAlternateReceiver(Class<? extends AlternateReceiver> theInterface) {
		return null;
	}

	@Override
	public void addTheAlternateReceiver(AlternateReceiver newTheAlternateReceiver) {
		// noop
	}

	@Override
	public void addTheAlternateReceiver(int index, AlternateReceiver newTheAlternateReceiver) {
		// noop
	}

	@Override
	public void setTheAlternateReceiver(int index, AlternateReceiver newTheAlternateReceiver) {
		// noop
	}

	@Override
	public void setTheAlternateReceiver(List<AlternateReceiver> objList) {
		// noop
	}

	@Override
	public void removeTheAlternateReceiver(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheAlternateReceiver(int index) {
		// noop
	}

	@Override
	public void clearTheDriverLicense() {
		// noop
	}

	@Override
	public List<DriverLicense> getTheDriverLicense() {
		return null;
	}

	@Override
	public DriverLicense getTheDriverLicense(String _uniqueId) {
		return null;
	}

	@Override
	public DriverLicense getTheDriverLicense(int index) {
		return null;
	}

	@Override
	public DriverLicense addTheDriverLicense() {
		return null;
	}

	@Override
	public DriverLicense addTheDriverLicense(Class<? extends DriverLicense> theInterface) {
		return null;
	}

	@Override
	public void addTheDriverLicense(DriverLicense newTheDriverLicense) {
		// noop
	}

	@Override
	public void addTheDriverLicense(int index, DriverLicense newTheDriverLicense) {
		// noop
	}

	@Override
	public void setTheDriverLicense(int index, DriverLicense newTheDriverLicense) {
		// noop
	}

	@Override
	public void setTheDriverLicense(List<DriverLicense> objList) {
		// noop
	}

	@Override
	public void removeTheDriverLicense(String _uniqueId) {
		// noop
	}

	@Override
	public void removeTheDriverLicense(int index) {
		// noop
	}

	@Override
	public void clearTheLocationOtherOccupant() {

	}

	@Override
	public List<LocationOtherOccupant> getTheLocationOtherOccupant() {
		return null;
	}

	@Override
	public LocationOtherOccupant getTheLocationOtherOccupant(String uniqueId) {
		return null;
	}

	@Override
	public LocationOtherOccupant getTheLocationOtherOccupant(int index) {
		return null;
	}

	@Override
	public LocationOtherOccupant addTheLocationOtherOccupant() {
		return null;
	}

	@Override
	public LocationOtherOccupant addTheLocationOtherOccupant(Class<? extends LocationOtherOccupant> theInterface) {
		return null;
	}

	@Override
	public void addTheLocationOtherOccupant(LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void addTheLocationOtherOccupant(int index, LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void setTheLocationOtherOccupant(int index, LocationOtherOccupant newTheLocationOtherOccupant) {

	}

	@Override
	public void setTheLocationOtherOccupant(List<LocationOtherOccupant> objList) {

	}

	@Override
	public void removeTheLocationOtherOccupant(String uniqueId) {

	}

	@Override
	public void removeTheLocationOtherOccupant(int index) {

	}

	@Override
	public SubscriberRepositoryEntry getTheSubscriberRepositoryEntry() {
		return null;
	}

	@Override
	public void setTheSubscriberRepositoryEntry(SubscriberRepositoryEntry newTheSubscriberRepositoryEntry) {
		// noop
	}

	@Override
	public SubscriberRepositoryEntry createTheSubscriberRepositoryEntry() {
		return null;
	}

	@Override
	public SubscriberRepositoryEntry createTheSubscriberRepositoryEntry(Class<? extends SubscriberRepositoryEntry> theInterface) {
		return null;
	}

	@Override
	public String getPriorCarrierConsideredInd() {
		return null;
	}

	@Override
	public void setPriorCarrierConsideredInd(String arg0) {
		// noop
	}

	@Override
	public String getCreditBasedCustomerClassification() {
		return null;
	}

	@Override
	public void setCreditBasedCustomerClassification(String newCreditBasedCustomerClassification) {

	}

	@Override
	public String getFraudCancellationWithPriorCarrierInd() {
		return null;
	}

	@Override
	public void setFraudCancellationWithPriorCarrierInd(String newFraudCancellationWithPriorCarrierInd) {

	}

	@Override
	public Integer getOverallQualityScore() {
		return null;
	}

	@Override
	public void setOverallQualityScore(Integer integer) {

	}

	@Override
	public String getSpecialClientsListInd() {
		return null;
	}

	@Override
	public void setSpecialClientsListInd(String s) {

	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public PreferencePerCriteria addThePreferencePerCriteria() {
		return null;
	}

	@Override
	public PreferencePerCriteria addThePreferencePerCriteria(Class<? extends PreferencePerCriteria> arg0) {
		return null;
	}

	@Override
	public void addThePreferencePerCriteria(PreferencePerCriteria arg0) {
		// noop
	}

	@Override
	public void addThePreferencePerCriteria(int arg0, PreferencePerCriteria arg1) {
		// noop
	}

	@Override
	public void clearThePreferencePerCriteria() {
		// noop
	}

	@Override
	public List<PreferencePerCriteria> getThePreferencePerCriteria() {
		return null;
	}

	@Override
	public PreferencePerCriteria getThePreferencePerCriteria(String arg0) {
		return null;
	}

	@Override
	public PreferencePerCriteria getThePreferencePerCriteria(int arg0) {
		return null;
	}

	@Override
	public void removeThePreferencePerCriteria(String arg0) {
		// noop
	}

	@Override
	public void removeThePreferencePerCriteria(int arg0) {
		// noop
	}

	@Override
	public void clearTheProgramEligible() {

	}

	@Override
	public List<Program> getTheProgramEligible() {
		return null;
	}

	@Override
	public Program getTheProgramEligible(String s) {
		return null;
	}

	@Override
	public Program getTheProgramEligible(int i) {
		return null;
	}

	@Override
	public Program addTheProgramEligible() {
		return null;
	}

	@Override
	public Program addTheProgramEligible(Class<? extends Program> aClass) {
		return null;
	}

	@Override
	public void addTheProgramEligible(Program program) {

	}

	@Override
	public void addTheProgramEligible(int i, Program program) {

	}

	@Override
	public void setTheProgramEligible(int i, Program program) {

	}

	@Override
	public void setTheProgramEligible(List<Program> list) {

	}

	@Override
	public void removeTheProgramEligible(String s) {

	}

	@Override
	public void removeTheProgramEligible(int i) {

	}

	@Override public void clearTheProgram() {

	}

	@Override public List<Program> getTheProgram() {
		return null;
	}

	@Override public Program getTheProgram(String s) {
		return null;
	}

	@Override public Program getTheProgram(int i) {
		return null;
	}

	@Override public Program addTheProgram() {
		return null;
	}

	@Override public Program addTheProgram(Class<? extends Program> aClass) {
		return null;
	}

	@Override public void addTheProgram(Program program) {

	}

	@Override public void addTheProgram(int i, Program program) {

	}

	@Override public void setTheProgram(int i, Program program) {

	}

	@Override public void setTheProgram(List<Program> list) {

	}

	@Override public void removeTheProgram(String s) {

	}

	@Override public void removeTheProgram(int i) {

	}

	@Override
	public void setThePreferencePerCriteria(List<PreferencePerCriteria> arg0) {
		// noop
	}

	@Override
	public void setThePreferencePerCriteria(int arg0, PreferencePerCriteria arg1) {
		// noop
	}

	@Override
	public void clearThePartyParsing() {
		// noop	
	}

	@Override
	public List<PartyParsing> getThePartyParsing() {

		return null;
	}

	@Override
	public PartyParsing getThePartyParsing(String uniqueId) {

		return null;
	}

	@Override
	public PartyParsing getThePartyParsing(int index) {

		return null;
	}

	@Override
	public PartyParsing addThePartyParsing() {

		return null;
	}

	@Override
	public PartyParsing addThePartyParsing(Class<? extends PartyParsing> theInterface) {
		return null;
	}

	@Override
	public void addThePartyParsing(PartyParsing newThePartyParsing) {
		// noop
	}

	@Override
	public void addThePartyParsing(int index, PartyParsing newThePartyParsing) {
		// noop
	}

	@Override
	public void setThePartyParsing(int index, PartyParsing newThePartyParsing) {
		// noop
	}

	@Override
	public void setThePartyParsing(List<PartyParsing> objList) {
		// noop
	}

	@Override
	public void removeThePartyParsing(String uniqueId) {
		// noop
	}

	@Override
	public void removeThePartyParsing(int index) {
		// noop
	}

	@Override
	public InsuranceHistory getTheInsuranceHistoryMostRecentAuto() {
		return null;
	}

	@Override
	public void setTheInsuranceHistoryMostRecentAuto(InsuranceHistory insuranceHistory) {

	}

	@Override
	public InsuranceHistory createTheInsuranceHistoryMostRecentAuto() {
		return null;
	}

	@Override
	public InsuranceHistory createTheInsuranceHistoryMostRecentAuto(Class<? extends InsuranceHistory> aClass) {
		return null;
	}

	@Override
	public InsuranceHistory getTheInsuranceHistoryMostRecentRes() {
		return null;
	}

	@Override
	public void setTheInsuranceHistoryMostRecentRes(InsuranceHistory insuranceHistory) {

	}

	@Override
	public InsuranceHistory createTheInsuranceHistoryMostRecentRes() {
		return null;
	}

	@Override
	public InsuranceHistory createTheInsuranceHistoryMostRecentRes(Class<? extends InsuranceHistory> aClass) {
		return null;
	}

	@Override
	public Address getTheAddressCurrent() {
		return null;
	}

	@Override
	public void setTheAddressCurrent(Address address) {

	}

	@Override
	public Address createTheAddressCurrent() {
		return null;
	}

	@Override
	public Address createTheAddressCurrent(Class<? extends Address> aClass) {
		return null;
	}

}
