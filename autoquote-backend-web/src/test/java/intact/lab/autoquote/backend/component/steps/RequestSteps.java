package intact.lab.autoquote.backend.component.steps;

import intact.lab.autoquote.backend.component.context.Context;
import io.cucumber.java.en.When;
import org.springframework.beans.factory.annotation.Autowired;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

public class RequestSteps {

  private static final String ENDPOINT_URL = "http://localhost:8080/greeting";
  @Autowired
  @SuppressWarnings("SpringJavaAutowiredMembersInspection")
  public Context context;

  @When("the user sends a GET request to the endpoint")
  public void sendGetRequest() {

  }

  @When("the user sends a POST request to the endpoint")
  public void sendPostRequest() {
  }
}
