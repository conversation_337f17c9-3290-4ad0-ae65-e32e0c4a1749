package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.impl.BrokerAssignationParameterBean;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.exception.SubBrokerServiceException;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import intact.lab.autoquote.backend.common.exception.BrokerException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BrokerServiceTest {

    @Mock
    private ISubBrokersService subBrokersService;

    @InjectMocks
    private BrokerService brokerService;

    private ComContext comContext;

    @BeforeEach
    void setUp() {
        comContext = new ComContext();
        ComBrokerInfo brokerInfo = new ComBrokerInfo();
        comContext.setBrokerInfo(brokerInfo);
        comContext.setCompany(ComCompanyEnum.INTACT);
        comContext.setProvince(ComProvinceCodeEnum.QUEBEC);
        comContext.setLanguage(ComLanguageCodeEnum.ENGLISH);
    }

    @Test
    void GetBrokerInfo_ShouldReturnBrokerInfo_WhenSubBrokerIsValid() throws SubBrokerServiceException {
        ISubBrokers subBroker = mock(ISubBrokers.class);
        when(subBroker.getSubBrokerId()).thenReturn(1L);
        when(subBrokersService.findBroker(any(BrokerAssignationParameterBean.class))).thenReturn(subBroker);

        ComBrokerInfo result = brokerService.getBrokerInfo(comContext);

        assertNotNull(result);
        verify(subBrokersService).findBroker(any(BrokerAssignationParameterBean.class));
    }

    @Test
    void GetBrokerInfo_ShouldThrowBrokerException_WhenSubBrokerIsInvalid() throws SubBrokerServiceException {
        ISubBrokers subBroker = mock(ISubBrokers.class);
        when(subBroker.getSubBrokerId()).thenReturn(0L);
        when(subBrokersService.findBroker(any(BrokerAssignationParameterBean.class))).thenReturn(subBroker);

        assertThrows(BrokerException.class, () -> brokerService.getBrokerInfo(comContext));
        verify(subBrokersService, times(1)).findBroker(any(BrokerAssignationParameterBean.class));
    }

    @Test
    void GetBrokerInfo_ShouldThrowBrokerException_WhenSubBrokerServiceThrowsException() throws SubBrokerServiceException {
        when(subBrokersService.findBroker(any(BrokerAssignationParameterBean.class)))
                .thenThrow(new SubBrokerServiceException("Error", new Exception()));

        assertThrows(BrokerException.class, () -> brokerService.getBrokerInfo(comContext));
    }

    @Test
    void BuildBroker_ShouldReturnSubBroker_WhenValidParameters() throws SubBrokerServiceException {
        ComContext context = new ComContext();
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);
        context.setLineOfBusiness(ComLineOfBusinessCodeEnum.PERSONAL_LINES);

        ComBrokerInfo brokerInfo = new ComBrokerInfo();
        brokerInfo.setPostalCode("H1H1H1");
        brokerInfo.setSubBrokerNumber("12345");
        brokerInfo.setBrokerWebsiteOrigin(ComBrokerWebSiteOriginEnum.BROKER);
        context.setBrokerInfo(brokerInfo);

        ManufacturingContext mCtxt = new ManufacturingContext();
        mCtxt.setManufacturerCompany(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);

        ISubBrokers expectedSubBroker = mock(ISubBrokers.class);
        when(subBrokersService.findBroker(any(BrokerAssignationParameterBean.class))).thenReturn(expectedSubBroker);

        ISubBrokers result = brokerService.buildBroker(context, mCtxt, brokerInfo);

        assertNotNull(result);
        assertEquals(expectedSubBroker, result);
        verify(subBrokersService).findBroker(any(BrokerAssignationParameterBean.class));
    }

    @Test
    void BuildBroker_ShouldThrowBrokerException_WhenSubBrokerServiceFails() throws SubBrokerServiceException {
        ComContext context = new ComContext();
        context.setLanguage(ComLanguageCodeEnum.ENGLISH);

        ComBrokerInfo brokerInfo = new ComBrokerInfo();
        brokerInfo.setPostalCode("K1A0A6");
        brokerInfo.setSubBrokerNumber("99999");
        context.setBrokerInfo(brokerInfo);

        ManufacturingContext mCtxt = new ManufacturingContext();
        mCtxt.setManufacturerCompany(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);

        when(subBrokersService.findBroker(any(BrokerAssignationParameterBean.class)))
                .thenThrow(new SubBrokerServiceException("Service error", new Exception()));

        assertThrows(BrokerException.class, () ->
                brokerService.buildBroker(context, mCtxt, brokerInfo));
    }
}
