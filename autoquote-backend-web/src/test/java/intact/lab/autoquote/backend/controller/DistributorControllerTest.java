package intact.lab.autoquote.backend.controller;

import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.facade.IDistributorFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class DistributorControllerTest {

    @Mock
    private IDistributorFacade distributorFacade;

    @InjectMocks
    private DistributorController distributorController;

    MockMvc mockMvc;

    @BeforeEach
    public void setUp() {

        this.mockMvc = MockMvcBuilders.standaloneSetup(this.distributorController).build();
    }

    @Test
    void RetrieveBrokerInfo_Should_Return_DistributorDTO() throws Exception {
        String apiKey = "testKey";
        String language = "en";
        String province = "qc";
        String postalCode = "H3Z2Y7";
        String subBrokerNo = "4017";
        String origin = "INTACT";

        DistributorDTO distributorDTO = new DistributorDTO();
        distributorDTO.setName("test");
        distributorDTO.setNumber("4017");
        distributorDTO.setPhoneNumber("1234567890");

        when(distributorFacade.retrieveBrokerInfo(apiKey, language, province,
                postalCode, subBrokerNo, origin)).thenReturn(distributorDTO);

        mockMvc.perform(get("/irca/v2/distributors/" + subBrokerNo)
                        .param("apiKey", apiKey)
                        .param("language", language)
                        .param("province", province)
                        .param("origin", origin)
                        .param("postalCode", postalCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value(distributorDTO.getName()))
                .andExpect(jsonPath("$.number").value(distributorDTO.getNumber()))
                .andExpect(jsonPath("$.phoneNumber").value(distributorDTO.getPhoneNumber()));
    }
}

