package intact.lab.autoquote.backend.component;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.suite.api.ConfigurationParameter;
import org.junit.platform.suite.api.IncludeEngines;
import org.junit.platform.suite.api.SelectClasspathResource;
import org.junit.platform.suite.api.Suite;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static io.cucumber.core.options.Constants.*;

@Suite
@ExtendWith(SpringExtension.class)
@IncludeEngines("cucumber")
@SelectClasspathResource("features/component")
@ConfigurationParameter(key = PLUGIN_PUBLISH_QUIET_PROPERTY_NAME, value = "true")
@ConfigurationParameter(key = GLUE_PROPERTY_NAME, value = "intact.lab.template.component, intact.test.base.glue, intact.test.component.glue")
@ConfigurationParameter(key = PLUGIN_PROPERTY_NAME, value = """
    json:target/cucumber-report/cucumber.json,
    junit:target/cucumber-report/cucumber.xml,
    io.cucumber.core.plugin.PrettyParallelOutput:CONCISE,
    io.cucumber.core.plugin.SummaryReport,
    io.cucumber.core.plugin.ZephyrLink
    """)
@ConfigurationParameter(key = OBJECT_FACTORY_PROPERTY_NAME, value = "io.cucumber.spring.SpringFactory")
@ConfigurationParameter(key = FILTER_TAGS_PROPERTY_NAME, value = "")
public class RunCucumberComponentTest {
}
