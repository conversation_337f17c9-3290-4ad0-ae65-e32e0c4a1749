package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ClaimLossDateCodeEnum;
import com.ing.canada.plp.domain.enums.CommunicationChannelCodeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DriverLicenseTypeCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.PriorGridLevelClientCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.driver.ComDriver;
import com.intact.com.driver.ComDriverClaim;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import com.intact.com.payment.ComPayment;
import com.intact.com.state.ComState;
import com.intact.com.state.enums.ComStateEnum;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.com.vehicle.ComVehicle;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
public class COMtoPLAdapterTest {

  /**
   * Class to be tested.
   */
  @InjectMocks
  private COMtoPLAdapterONIntactCL comToPlAdapter;
  /**
   * Test objects
   */
  private CommunicationObjectModel currentTestCom;
  private PolicyVersion currentTestPV;
  private ComState currentTestState;
  private ComDriver curComDriver;
  private ComVehicle curComVehicle;
  private Vehicle curVehicle;
  private VehicleModel curVehModel;
  /**
   * Mocks
   */
  @Mock
  private IDateManagerService mockCapiPolicyChangeDateService;
  @Mock
  private ICommonBusinessProcess mockCommonBusinessProcess;
  @Mock
  private IDriverBusinessProcess mockDriverBusinessProcess;
  @Mock
  private IMediatorAdvisor mockMediatorAdvisor;
  @Mock
  private IMediatorClaimPlp mockMediatorClaim;
  @Mock
  private IMediatorComPlp mockMediatorComPlp;
  @Mock
  private IMediatorDriverPlp mockMediatorDriverPlp;
  @Mock
  private IMediatorVehiclePlp mockMediatorVehiclePlp;
  @Mock
  private IPartyHelper mockPartyHelper;
  @Mock
  private IInsuranceRiskHelper mockPlpInsuranceRiskHelper;
  @Mock
  private IPolicyVersionHelper mockPlpPolicyVersionHelper;
  @Mock
  private IUsageBusinessProcess mockUsageBusinessProcess;
  @Mock
  private IVehicleBusinessProcess mockVehicleBusinessProcess;
  @Mock
  private IVehicleHelper mockVehicleHelper;
  @Mock
  private MediatorPaymentPlp mockMediatorPayment;

  @BeforeEach
  public void setUp() throws Exception {
    // Policy version setup
    this.currentTestPV = new PolicyVersion(123L);
    InsurancePolicy testInsPolicy = new InsurancePolicy();
    testInsPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE); // Regular quote as default
    this.currentTestPV.setInsurancePolicy(testInsPolicy);

    // Creation of the COM and it's context
    ComContext context = new ComContext();
    context.setApplication(ComApplicationEnum.AUTOQUOTE);
    context.setDistributionChannel(ComDistributionChannelCodeEnum.DIRECT_SELLER);
    context.setLanguage(ComLanguageCodeEnum.ENGLISH);
    context.setProvince(ComProvinceCodeEnum.ALBERTA);
    context.setCompany(ComCompanyEnum.BELAIR);
    context.setTestDataInd(true);

    this.currentTestCom = new CommunicationObjectModel();
    this.currentTestCom.setContext(context);
    this.currentTestCom.setPolicyVersionId(123L);
    this.currentTestState = new ComState();
    this.currentTestState.setCurrentState(ComStateEnum.INITIAL);
    this.currentTestState.setDataChanged(true);
    this.currentTestState.setHasOffer(true);
    this.currentTestCom.setState(this.currentTestState);
  }

  /**
   * Tears down the test fixture.
   */
  @AfterEach
  public void tearDown() throws Exception {
    this.currentTestCom = null;
    this.currentTestPV = null;
    this.currentTestState = null;
    this.curComDriver = null;
    this.curComVehicle = null;
    this.curVehicle = null;
    this.curVehModel = null;
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#convertMarketingConsent(Party, ComDriver)}
   */
  @Test
  public void testConvertMarketingConsent() {
    // Creating the objects to be passed as parameter
    boolean expectedConsentInd = true;
    ComDriver testDriver = new ComDriver();
    testDriver.setMarketingConsent(expectedConsentInd);
    DriverLicenseTypeCodeEnum expectedDriverLicenseType = DriverLicenseTypeCodeEnum.LEARNER_LICENSE;
    testDriver.setDriverLicenseType(expectedDriverLicenseType.getCode());
    Party testParty = new Party();

    // For this case, the party helper needs to return a consent with an indicator different than the driver's
    Consent testConsent = new Consent();
    testConsent.setConsentIndicator(!expectedConsentInd);
    when(this.mockPartyHelper.getConsent(any(Party.class), any(ConsentTypeCodeEnum.class))).thenReturn(testConsent);

    // Executing the tested method and validating the results
    this.comToPlAdapter.convertMarketingConsent(testParty, testDriver);
    assertEquals(expectedConsentInd, testConsent.getConsentIndicator());
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#convertMarketingConsent(Party, ComDriver)}
   * Case for the party's marketing consent to be null.
   */
  @Test
  public void testConvertMarketingConsent_NullPartyConsent() {
    // Creating the objects to be passed as parameter
    boolean expectedConsentInd = true;
    ComDriver testDriver = new ComDriver();
    testDriver.setMarketingConsent(expectedConsentInd);
    DriverLicenseTypeCodeEnum expectedDriverLicenseType = DriverLicenseTypeCodeEnum.LEARNER_LICENSE;
    testDriver.setDriverLicenseType(expectedDriverLicenseType.getCode());
    Party testParty = new Party();

    // Executing the tested method and validating the results
    this.comToPlAdapter.convertMarketingConsent(testParty, testDriver);
    Consent resultConsent = testParty.getConsents().iterator().next(); // There should only be one consent
    assertEquals(CommunicationChannelCodeEnum.INTERNET, resultConsent.getCommunicationChannelCode());
    assertEquals(ConsentTypeCodeEnum.MARKETING_CONSENT, resultConsent.getConsentType());
    assertEquals(expectedConsentInd, resultConsent.getConsentIndicator());
  }


  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be ADD_DRIVER.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_AddDriverEvent() throws Exception {
    // Creating the ADD_DRIVER event to be passed as parameter
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.ADD_DRIVER);

    // Creating the test party with complement info that will be modified
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI that will be reset, for better validation
    testDCI.setPriorGridLevelClient(PriorGridLevelClientCodeEnum.DONT_KNOW);
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should've been set to null.");
    assertNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should've been set to null.");
    assertNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should've been set to null.");
  }


  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be MODIFY_DRIVER.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_ModifyDriverEvent() throws Exception {
    // Creating the MODIFY_DRIVER event to be passed as parameter
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.MODIFY_DRIVER);

    // Creating the test party with complement info that will be modified
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI that will be reset, for better validation
    testDCI.setPriorGridLevelClient(PriorGridLevelClientCodeEnum.NOT_INSURED);
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should've been set to null.");
    assertNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should've been set to null.");
    assertNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should've been set to null.");
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be MODIFY_VEHICLE.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_ModifyVehicleEvent() throws Exception {
    // Creating the MODIFY_DRIVER event to be passed as parameter
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.MODIFY_VEHICLE);

    // Creating the test party with complement info that will be modified
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI that will be reset, for better validation
    testDCI.setPriorGridLevelClient(PriorGridLevelClientCodeEnum.NOT_INSURED);
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should've been set to null.");
    assertNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should've been set to null.");
    assertNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should've been set to null.");
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be SAVE_USAGE.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_SaveUsageEvent() throws Exception {
    // Creating the SAVE_USAGE event to be passed as parameter
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.SAVE_USAGE);

    // Creating the test party with complement info that will be modified
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI that will be reset, for better validation
    testDCI.setPriorGridLevelClient(PriorGridLevelClientCodeEnum.NOT_INSURED);
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should've been set to null.");
    assertNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should've been set to null.");
    assertNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should've been set to null.");
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be SAVE_OFFER.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_SaveOfferEvent() throws Exception {
    // Creating the SAVE_OFFER event to be passed as parameter
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.SAVE_OFFER);

    // Creating the test party with complement info that will be modified
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI that will be reset, for better validation
    testDCI.setPriorGridLevelClient(PriorGridLevelClientCodeEnum.NOT_INSURED);
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should've been set to null.");
    assertNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should've been set to null.");
    assertNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should've been set to null.");
  }

  /**
   * Test method for
   * {@link COMtoPLAdapter#manageGridLevel(ComEvent, Party)}
   * Case for the event code to be another event. In this case, infos should not be reset.
   *
   * @throws Exception
   */
  @Test
  public void testManageGridLevel_OtherEvent() throws Exception {
    // Creating the event to be passed as parameter with a code not corresponding to any case in the switch
    ComEvent testEvent = new ComEvent();
    testEvent.setEventCode(ComEventEnum.PURCHASE);

    // Creating the test party with complement info to be passed pas parameter
    Party testParty = new Party();
    DriverComplementInfo testDCI = new DriverComplementInfo();
    testParty.setDriverComplementInfo(testDCI);

    // Setting the infos in the test DCI for validation
    testDCI.setGridLevelDate(new Date());
    testDCI.setGridLevelQty((short) 42);
    testDCI.setPriorGridLevelQty((short) 42);

    // Executing the tested method and validating the results
    this.comToPlAdapter.manageGridLevel(testEvent, testParty);
    assertNotNull(testDCI.getGridLevelDate(), "The test Driver Complement Info's grid level date should not have been set to null.");
    assertNotNull(testDCI.getGridLevelQty(), "The test Driver Complement Info's grid level qty should not have been set to null.");
    assertNotNull(testDCI.getPriorGridLevelQty(), "The test Driver Complement Info's prior grid level qty should not have been set to null.");
  }

  @Test
  public void testAddComPaymentToPolicyVersion_Should_Convert_When_ComPayment() throws Exception {
    this.currentTestCom.getContext().setApplication(ComApplicationEnum.CHECKOUT);
    this.currentTestCom.setComPayment(new ComPayment());

    this.comToPlAdapter.addComPaymentInformation(this.currentTestCom, this.currentTestPV);

    verify(this.mockMediatorPayment, times(1)).convertCOMToPLP(any(CommunicationObjectModel.class),
        any(PolicyVersion.class));
  }

  /**
   * Private method to do the general setup for tests for the getDateOfLoss method.
   *
   * @param dateCode   The code used to determine the number of months loss
   * @param monthsLoss The number of months loss used to calculate the expected date
   * @param testParty  The party used for the test
   * @param testDriver The ComDriver used for the test.
   * @return The expected date for the test
   * @throws Exception
   */
  private GregorianCalendar setupGetDateOfLossTest(ClaimLossDateCodeEnum dateCode, Integer monthsLoss, Party testParty, ComDriver testDriver) throws Exception {
    // Adding a test claim to the party to validate the cleaning
    Claim testClaim = new Claim();
    testParty.addClaim(testClaim);
    testParty.setPolicyVersion(this.currentTestPV);
    // Setting the claim with the correct loss code and for it to be returned when a new claim is created
    testClaim.setClaimLossDateCode(dateCode);
//    PowerMockito.whenNew(Claim.class).withAnyArguments().thenReturn(testClaim);

    // Adding a com driver claim to the com driver so a new cliam will be added to the party
    List<ComDriverClaim> testDriverClaimsList = new ArrayList<ComDriverClaim>();
    ComDriverClaim testComClaim = new ComDriverClaim();
    testComClaim.setClaimSequence((short) 42);
    testDriverClaimsList.add(testComClaim);
    testDriver.setDriverClaims(testDriverClaimsList);

    // Calculating the expected date for the current loss code
    Date testDate = new Date();
    GregorianCalendar expectedDateOfLoss = new GregorianCalendar();
    expectedDateOfLoss.setTime(testDate);
    expectedDateOfLoss.add(Calendar.MONTH, -monthsLoss);

    // Other stubs setup
    when(this.mockCapiPolicyChangeDateService.getReferenceDate(any(DateHelperEnum.class),
        any(Long.class))).thenReturn(testDate);

    return expectedDateOfLoss;
  }

  /**
   * Private method used to do the general setup for tests calling the addComVehiclesToPolicyVersion method.
   */
  private void setupGeneralAddComVehiclesToPolicyVersionTest() {
    // Creating the ComDriver to be returned by the MediatorUtils
    this.curComDriver = new ComDriver();
    this.curComDriver.setDriverId(75);
    when(MediatorUtils.getComDriver(any(Integer.class), any(CommunicationObjectModel.class))).thenReturn(this.curComDriver);

    // Setting the ComVehicle and Vehicle for the test
    List<ComVehicle> testVehiclesList = new ArrayList<ComVehicle>();
    this.curComVehicle = new ComVehicle();
    this.curComVehicle.setPrincipalDriver(42);
    testVehiclesList.add(this.curComVehicle);
    this.currentTestCom.setVehicles(testVehiclesList);

    this.curVehicle = new Vehicle();
    InsuranceRisk testRisk = new InsuranceRisk();
    this.curVehicle.setInsuranceRisk(testRisk);
    when(this.mockVehicleHelper.initNewVehicle(any(PolicyVersion.class))).thenReturn(this.curVehicle);

    // Vehicle models setup
    this.curVehModel = new VehicleModel();
    when(this.mockVehicleBusinessProcess.getVehicleModelEnglishByModelCode(anyString(), anyString(),
        anyString(), any(DistributionChannelCodeEnum.class), any(InsuranceBusinessCodeEnum.class),
        any(ProvinceCodeEnum.class))).thenReturn(this.curVehModel);
    when(this.mockVehicleBusinessProcess.getVehicleModelFrenchByModelCode(anyString(), anyString(),
        anyString(), any(DistributionChannelCodeEnum.class), any(InsuranceBusinessCodeEnum.class),
        any(ProvinceCodeEnum.class))).thenReturn(this.curVehModel);

    // Other stubs setup
    when(MediatorUtils.getLocale(any(ComContext.class))).thenReturn(new Locale("FR", "CA"));
    when(MediatorUtils.convertContext(any(ComContext.class))).thenReturn(mock(ManufacturingContext.class));
    when(MediatorUtils.driversSize(any(CommunicationObjectModel.class))).thenReturn(42);
    when(this.mockPlpInsuranceRiskHelper.getRiskYear(any(InsuranceRisk.class))).thenReturn("1990");
  }

}
