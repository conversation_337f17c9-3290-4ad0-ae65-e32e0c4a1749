package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;


@ExtendWith(MockitoExtension.class)
public class DateOfBirthValidationRuleTest {

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "party");
	}

	@Test
	public void testValidate_Valid_ShouldPass() {
		LocalDate date = LocalDate.now().minusYears(20);
		DateOfBirthValidationRule.validate(Integer.toString(date.getYear()), Integer.toString(date.getMonthOfYear()),
				Integer.toString(date.getDayOfMonth()), "dateOfBirth", this.errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_TooYoung_ShouldFail() {
		LocalDate date = LocalDate.now().minusYears(16).plusDays(1);
		DateOfBirthValidationRule.validate(Integer.toString(date.getYear()), Integer.toString(date.getMonthOfYear()),
				Integer.toString(date.getDayOfMonth()), "dateOfBirth", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "dateOfBirth", BRulesExceptionEnum.ERR_DRIVER_DATEBIRTHY_BR7934.getErrorCode());
	}

	@Test
	public void testValidate_TooOld_ShouldFail() {
		LocalDate date = LocalDate.now().minusYears(100);
		DateOfBirthValidationRule.validate(Integer.toString(date.getYear()), Integer.toString(date.getMonthOfYear()),
				Integer.toString(date.getDayOfMonth()), "dateOfBirth", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "dateOfBirth", BRulesExceptionEnum.ERR_DRIVER_DATEBIRTHY_BR7934.getErrorCode());
	}
}
