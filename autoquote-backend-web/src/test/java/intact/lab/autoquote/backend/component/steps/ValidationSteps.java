package intact.lab.autoquote.backend.component.steps;

import intact.lab.autoquote.backend.component.context.Context;
import io.cucumber.java.en.Then;
import org.springframework.beans.factory.annotation.Autowired;

import static org.hamcrest.MatcherAssert.assertThat;

public class ValidationSteps {

  @Autowired
  @SuppressWarnings("SpringJavaAutowiredMembersInspection")
  public Context context;

  @Then("the greeting message should be {string}")
  public void validateGreeting(String expected) {
//     assertThat("Invalid greeting", context.getResponseDTO().getData().getMessage(), equalTo(expected));
  }
}
