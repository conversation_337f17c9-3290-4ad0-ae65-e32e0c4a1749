/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.claim.KindOfLoss;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.physicalObject.Vehicle;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

/**
 * 
 * Mock class for a SOM Claim. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Claim}
 * 
 * <AUTHOR>
 * 
 */
public class MockClaim implements Claim {
	List<KindOfLoss> testKindOfLossList;

	String claimAtFaultInd;

	GregorianCalendar dateOfLoss;

	Party testParty;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		return false;
	}

	@Override
	public String getClaimType() {
		return null;
	}

	@Override
	public void setClaimType(String newClaimType) {
		// noop
	}

	@Override
	public String getClaimCategory() {
		return null;
	}

	@Override
	public void setClaimCategory(String newClaimCategory) {
		// noop
	}

	@Override
	public String getClaimCategoryChargeableInd() {
		return null;
	}

	@Override
	public void setClaimCategoryChargeableInd(String newClaimCategoryChargeableInd) {
		// noop
	}

	@Override
	public String getNatureOfClaim() {
		return null;
	}

	@Override
	public void setNatureOfClaim(String newNatureOfClaim) {
		// noop
	}

	@Override
	public String getCauseOfLoss() {
		return null;
	}

	@Override
	public void setCauseOfLoss(String newCauseOfLoss) {
		// noop
	}

	@Override
	public String getClaimNumber() {
		return null;
	}

	@Override
	public void setClaimNumber(String newClaimNumber) {
		// noop
	}

	@Override
	public Integer getClaimSequence() {
		return null;
	}

	@Override
	public void setClaimSequence(Integer newClaimSequence) {
		// noop
	}

	@Override
	public String getStatus() {
		return null;
	}

	@Override
	public void setStatus(String newStatus) {
		// noop
	}

	@Override
	public String getClaimSource() {
		return null;
	}

	@Override
	public void setClaimSource(String newClaimSource) {
		// noop
	}

	@Override
	public GregorianCalendar getDateOfLossSystem() {
		return null;
	}

	@Override
	public void setDateOfLossSystem(GregorianCalendar newDateOfLossSystem) {

	}

	@Override
	public GregorianCalendar getDateOfLossModified() {
		return null;
	}

	@Override
	public void setDateOfLossModified(GregorianCalendar newDateOfLossModified) {

	}

	@Override
	public GregorianCalendar getDateOfLoss() {
		return this.dateOfLoss;
	}

	@Override
	public void setDateOfLoss(GregorianCalendar newDateOfLoss) {
		this.dateOfLoss = newDateOfLoss;
	}

	@Override
	public Double getNumberOfYearsSinceLoss() {
		return null;
	}

	@Override
	public void setNumberOfYearsSinceLoss(Double newNumberOfYearsSinceLoss) {
		// noop
	}

	@Override
	public String getClaimAtFaultInd() {
		return this.claimAtFaultInd;
	}

	@Override
	public void setClaimAtFaultInd(String newClaimAtFaultInd) {
		this.claimAtFaultInd = newClaimAtFaultInd;
	}

	@Override
	public String getForgivenIndSystem() {
		return null;
	}

	@Override
	public void setForgivenIndSystem(String newForgivenIndSystem) {

	}

	@Override
	public String getForgivenIndModified() {
		return null;
	}

	@Override
	public void setForgivenIndModified(String newForgivenIndModified) {

	}

	@Override
	public String getForgivenInd() {
		return null;
	}

	@Override
	public void setForgivenInd(String newForgivenInd) {
		// noop
	}

	@Override
	public String getForgivenReasonSystem() {
		return null;
	}

	@Override
	public void setForgivenReasonSystem(String newForgivenReasonSystem) {

	}

	@Override
	public String getForgivenReasonModified() {
		return null;
	}

	@Override
	public void setForgivenReasonModified(String newForgivenReasonModified) {

	}

	@Override
	public String getForgivenReason() {
		return null;
	}

	@Override
	public void setForgivenReason(String newForgivenReason) {
		// noop
	}

	@Override
	public String getClaimAccommodationInd() {
		return null;
	}

	@Override
	public void setClaimAccommodationInd(String newClaimAccommodationInd) {
		// noop
	}

	@Override
	public GregorianCalendar getDateOfLossWithAccommodation() {
		return null;
	}

	@Override
	public void setDateOfLossWithAccommodation(GregorianCalendar newDateOfLossWithAccommodation) {
		// noop
	}

	@Override
	public String getOtherDriverInd() {
		return null;
	}

	@Override
	public void setOtherDriverInd(String newOtherDriverInd) {
		// noop
	}

	@Override
	public String getDetailsOfLoss() {
		return null;
	}

	@Override
	public void setDetailsOfLoss(String newDetailsOfLoss) {
		// noop
	}

	@Override
	public String getClaimConsideredCode() {
		return null;
	}

	@Override
	public void setClaimConsideredCode(String newClaimConsideredCode) {
		// noop
	}

	@Override
	public String getClaimConsideredCodeGrid() {
		return null;
	}

	@Override
	public void setClaimConsideredCodeGrid(String newClaimConsideredCodeGrid) {

	}

	@Override
	public String getApplicableFromSourceSystemInd() {
		return null;
	}

	@Override
	public void setApplicableFromSourceSystemInd(String newApplicableFromSourceSystemInd) {
		// noop
	}

	@Override
	public String getDisregardInd() {
		return null;
	}

	@Override
	public void setDisregardInd(String newDisregardInd) {
		// noop
	}

	@Override
	public String getDisregardIndGrid() {
		return null;
	}

	@Override
	public void setDisregardIndGrid(String newDisregardIndGrid) {

	}

	@Override
	public String getDisregardReason() {
		return null;
	}

	@Override
	public void setDisregardReason(String newDisregardReason) {
		// noop
	}

	@Override
	public String getDisregardReasonGrid() {
		return null;
	}

	@Override
	public void setDisregardReasonGrid(String newDisregardReasonGrid) {

	}

	@Override
	public String getClaimConsideredInd() {
		return null;
	}

	@Override
	public void setClaimConsideredInd(String newClaimConsideredInd) {
		// noop
	}

	@Override
	public String getResidentialClaimInd() {
		return null;
	}

	@Override
	public void setResidentialClaimInd(String newResidentialClaimInd) {
		// noop
	}

	@Override
	public Integer getPercentageOfLiabilitySystem() {
		return null;
	}

	@Override
	public void setPercentageOfLiabilitySystem(Integer newPercentageOfLiabilitySystem) {

	}

	@Override
	public Integer getPercentageOfLiabilityModified() {
		return null;
	}

	@Override
	public void setPercentageOfLiabilityModified(Integer newPercentageOfLiabilityModified) {

	}

	@Override
	public Integer getPercentageOfLiability() {
		return null;
	}

	@Override
	public void setPercentageOfLiability(Integer newPercentageOfLiability) {
		// noop
	}

	@Override
	public String getCancelledVehicleClass() {
		return null;
	}

	@Override
	public void setCancelledVehicleClass(String newCancelledVehicleClass) {
		// noop
	}

	@Override
	public String getMainKindOfLoss() {
		return null;
	}

	@Override
	public void setMainKindOfLoss(String newMainKindOfLoss) {
		// noop
	}

	@Override
	public Double getAmountIncurred() {
		return null;
	}

	@Override
	public void setAmountIncurred(Double newAmountIncurred) {
		// noop
	}

	@Override
	public Double getAmountPaidSystem() {
		return null;
	}

	@Override
	public void setAmountPaidSystem(Double newAmountPaidSystem) {

	}

	@Override
	public Double getAmountPaidModified() {
		return null;
	}

	@Override
	public void setAmountPaidModified(Double newAmountPaidModified) {

	}

	@Override
	public Double getAmountPaid() {
		return null;
	}

	@Override
	public void setAmountPaid(Double newAmountPaid) {
		// noop
	}

	@Override
	public String getUnassignedInd() {
		return null;
	}

	@Override
	public void setUnassignedInd(String newUnassignedInd) {
		// noop
	}

	@Override
	public String getReassignedInd() {
		return null;
	}

	@Override
	public void setReassignedInd(String newReassignedInd) {
		// noop
	}

	@Override
	public String getRiskAssignmentModifiedByUserInd() {
		return null;
	}

	@Override
	public void setRiskAssignmentModifiedByUserInd(String newRiskAssignmentModifiedByUserInd) {
		// noop
	}

	@Override
	public String getDriverAssignmentModifiedByUserInd() {
		return null;
	}

	@Override
	public void setDriverAssignmentModifiedByUserInd(String newDriverAssignmentModifiedByUserInd) {
		// noop
	}

	@Override
	public void clearTheKindOfLoss() {
		// noop
	}

	@Override
	public List<KindOfLoss> getTheKindOfLoss() {
		/**
		 * For test purposes, if the list is null, create it and add a single test kind of loss before returning the
		 * list.
		 */
		if (this.testKindOfLossList == null) {
			this.testKindOfLossList = new ArrayList<KindOfLoss>();
			MockKindOfLoss testKindOfLoss = new MockKindOfLoss();
			this.testKindOfLossList.add(testKindOfLoss);
		}
		return this.testKindOfLossList;
	}

	@Override
	public KindOfLoss getTheKindOfLoss(String uniqueId) {
		return null;
	}

	@Override
	public KindOfLoss getTheKindOfLoss(int index) {
		return null;
	}

	@Override
	public KindOfLoss addTheKindOfLoss() {
		return null;
	}

	@Override
	public KindOfLoss addTheKindOfLoss(Class<? extends KindOfLoss> theInterface) {
		return null;
	}

	@Override
	public void addTheKindOfLoss(KindOfLoss newTheKindOfLoss) {
		// noop
	}

	@Override
	public void addTheKindOfLoss(int index, KindOfLoss newTheKindOfLoss) {
		// noop
	}

	@Override
	public void setTheKindOfLoss(int index, KindOfLoss newTheKindOfLoss) {
		// noop
	}

	@Override
	public void setTheKindOfLoss(List<KindOfLoss> objList) {
		// noop
	}

	@Override
	public void removeTheKindOfLoss(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheKindOfLoss(int index) {
		// noop
	}

	@Override
	public InsuranceRisk getTheInsuranceRisk() {
		return null;
	}

	@Override
	public void setTheInsuranceRisk(InsuranceRisk newTheInsuranceRisk) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public Party getTheParty() {
		return this.testParty;
	}

	@Override
	public void setTheParty(Party newTheParty) {
		this.testParty = newTheParty;
	}

	@Override
	public Party createTheParty() {
		return null;
	}

	@Override
	public Party createTheParty(Class<? extends Party> theInterface) {
		return null;
	}

	@Override
	public PolicyVersion getThePolicyVersion() {
		return null;
	}

	@Override
	public void setThePolicyVersion(PolicyVersion newThePolicyVersion) {
		// noop
	}

	@Override
	public PolicyVersion createThePolicyVersion() {
		return null;
	}

	@Override
	public PolicyVersion createThePolicyVersion(Class<? extends PolicyVersion> theInterface) {
		return null;
	}

	@Override
	public Claim getTheClaimPriorTrans() {
		return null;
	}

	@Override
	public void setTheClaimPriorTrans(Claim newTheClaimPriorTrans) {
		// noop
	}

	@Override
	public Claim createTheClaimPriorTrans() {
		return null;
	}

	@Override
	public Claim createTheClaimPriorTrans(Class<? extends Claim> theInterface) {
		return null;
	}

	@Override
	public Vehicle getTheOriginalVehicle() {
		return null;
	}

	@Override
	public void setTheOriginalVehicle(Vehicle newTheOriginalVehicle) {
		// noop
	}

	@Override
	public Vehicle createTheOriginalVehicle() {
		return null;
	}

	@Override
	public Vehicle createTheOriginalVehicle(Class<? extends Vehicle> theInterface) {
		return null;
	}

	@Override
	public InsuranceRisk getTheInsuranceRiskOriginal() {
		return null;
	}

	@Override
	public void setTheInsuranceRiskOriginal(InsuranceRisk newTheInsuranceRiskOriginal) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskOriginal() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRiskOriginal(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public Driver getTheDriverOriginal() {
		return null;
	}

	@Override
	public void setTheDriverOriginal(Driver newTheDriverOriginal) {
		// noop
	}

	@Override
	public Driver createTheDriverOriginal() {
		return null;
	}

	@Override
	public Driver createTheDriverOriginal(Class<? extends Driver> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getClaimStatusReason() {
		return null;
	}

	@Override
	public String getUnassignedRiskReasonCode() {
		return null;
	}

	@Override
	public void setClaimStatusReason(String arg0) {
		// noop
	}

	@Override
	public void setUnassignedRiskReasonCode(String arg0) {
		// noop
	}

	@Override
	public String getClaimNotConsideredByRatingReason() {
		return null;
	}

	@Override
	public void setClaimNotConsideredByRatingReason(String newClaimNotConsideredByRatingReason) {
		// noop
	}

	@Override
	public String getClaimNotConsideredByRatingReasonGrid() {
		return null;
	}

	@Override
	public void setClaimNotConsideredByRatingReasonGrid(String newClaimNotConsideredByRatingReasonGrid) {

	}

	@Override
	public String getClaimReassignmentDataTrace() {
		return null;
	}

	@Override
	public void setClaimReassignmentDataTrace(String newClaimReassignmentDataTrace) {
		// noop
	}

	@Override
	public Claim getTheClaimLatest() {
		return null;
	}

	@Override
	public void setTheClaimLatest(Claim newTheClaimLatest) {
		// noop
	}

	@Override
	public Claim createTheClaimLatest() {
		return null;
	}

	@Override
	public Claim createTheClaimLatest(Class<? extends Claim> theInterface) {
		return null;
	}

	@Override
	public String getDisregardDriverWithAtFaultReassignedClaimInd() {
		return null;
	}

	@Override
	public void setDisregardDriverWithAtFaultReassignedClaimInd(String newDisregardDriverWithAtFaultReassignedClaimInd) {
		// noop
	}

	@Override
	public GregorianCalendar getDriverCreationDateRelated() {
		return null;
	}

	@Override
	public void setDriverCreationDateRelated(GregorianCalendar newDriverCreationDateRelated) {

	}

	@Override
	public String getGrandfatheredAssignmentInd() {
		return null;
	}

	@Override
	public void setGrandfatheredAssignmentInd(String s) {

	}

	@Override public Double getReserveAmount() {
		return null;
	}

	@Override public void setReserveAmount(Double aDouble) {

	}

	@Override public GregorianCalendar getDateReported() {
		return null;
	}

	@Override public void setDateReported(GregorianCalendar gregorianCalendar) {

	}

}
