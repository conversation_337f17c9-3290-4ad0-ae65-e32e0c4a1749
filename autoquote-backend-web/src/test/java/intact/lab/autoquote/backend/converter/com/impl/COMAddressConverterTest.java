package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.address.ComAddress;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.converter.impl.COMAddressConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class COMAddressConverterTest {

	@InjectMocks
	public COMAddressConverter comAddressConverter;

	@Test
	public void testToDTO() throws Exception {
		String cityCode = "11111";
		String postalCode = "H7L2X9";
		String province = "QC";
		String country = "CA";

		ComAddress comAddress = new ComAddress();
		comAddress.setPostalCode(postalCode);
		comAddress.setCityCode(cityCode);
		comAddress.setProvince(province);
		comAddress.setCountry(country);

		AddressDTO address = this.comAddressConverter.toDTO(comAddress);
		assertNotNull(address);
        assertEquals(address.getMunicipalityCode(), comAddress.getCityCode());
        assertEquals(address.getPostalCode(), comAddress.getPostalCode());
        assertEquals(address.getProvince(), comAddress.getProvince());
        assertEquals(address.getCountry(), comAddress.getCountry());
	}

	@Test
	public void testToCOM() throws Exception {
		String cityCode = "11111";
		String postalCode = "H7L2X9";
		AddressDTO address = new AddressDTO();
		address.setMunicipalityCode(cityCode);
		address.setPostalCode(postalCode);

		ComAddress comAddressIn = new ComAddress();
		ComAddress comAddress = this.comAddressConverter.toCOM(address, comAddressIn);
		assertNotNull(comAddress);
        assertEquals(address.getMunicipalityCode(), comAddress.getCityCode());
        assertEquals(address.getPostalCode(), comAddress.getPostalCode());
	}
}
