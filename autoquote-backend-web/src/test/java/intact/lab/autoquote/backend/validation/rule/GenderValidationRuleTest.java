package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

public class GenderValidationRuleTest {

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new PartyDTO(), "party");
	}

	@Test
	public void testValidate_ValidDomain_ShouldPass() {
		GenderValidationRule.validate("M", this.errors);
		GenderValidationRule.validate("F", this.errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_Empty_ShouldFail() {
		GenderValidationRule.validate("", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "gender", BRulesExceptionEnum.NotBlank.getErrorCode());
	}

	@Test
	public void testValidate_InvalidDomain_ShouldFail() {
		GenderValidationRule.validate("A", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "gender", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode());
	}
}
