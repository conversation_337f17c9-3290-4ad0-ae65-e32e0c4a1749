package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.offer.ComOffer;
import com.intact.com.vehicle.ComVehicle;
import com.intact.com.vehicle.ComVehicleModel;
import intact.lab.autoquote.backend.common.dto.OfferDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.converter.impl.COMVehicleConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class COMVehicleConverterTest {

	@InjectMocks
	public COMVehicleConverter comVehicleConverter;

	@Mock
	private ICOMConverter<OfferDTO, ComOffer> comOfferConverter;

	@Test
	public void testToDTOWithCompleteComVehicle() throws Exception {
		ComVehicle comVehicle = new ComVehicle();
		comVehicle.setWebMsgId(1);
		comVehicle.setYear("2000");
		comVehicle.setAnnualKmDriven("1500");
		ComVehicleModel model = new ComVehicleModel();
		model.setMake("HONDA");
		model.setModel("CIVIC DX 2DR COUPE");
		model.setCode("025800");
		comVehicle.setVehicleModel(model);
		comVehicle.setTrackingSystem(Boolean.TRUE);
		comVehicle.setNormalRadiusKm(20);
		comVehicle.setGrossVehicleWeightQty(1000d);
		comVehicle.setCommercialUsageCategoryCd("CONTRACT");
		comVehicle.setCommercialUsageCd(null);
		ComOffer comOffer = new ComOffer();
		comOffer.setAnnualPremium(2000);
		comVehicle.setCurrentOffer(comOffer);
		comVehicle.setOfferSet(Set.of(comOffer));
		VehicleDTO vehicleDTO = this.comVehicleConverter.toDTO(comVehicle);
		assertNotNull(vehicleDTO);
        assertEquals(vehicleDTO.getMake(), comVehicle.getVehicleModel().getMake());
        assertEquals(vehicleDTO.getModelCode(), comVehicle.getVehicleModel().getCode());
        assertEquals((int) vehicleDTO.getYear(), Integer.parseInt(comVehicle.getYear()));
        assertEquals((int) vehicleDTO.getKmPerYear(), Integer.parseInt(comVehicle.getAnnualKmDriven()));
        assertEquals(vehicleDTO.getTrackingSystemInd(), comVehicle.getTrackingSystem());
        assertEquals(vehicleDTO.getNormalRadiusKm(), comVehicle.getNormalRadiusKm());
        assertEquals(vehicleDTO.getGrossVehicleWeight(), comVehicle.getGrossVehicleWeightQty());

        assertEquals(vehicleDTO.getCommercialUsageCategoryCd(), comVehicle.getCommercialUsageCategoryCd());
        assertEquals(vehicleDTO.getOffers().size(), comVehicle.getOfferSet().size());
	}

	@Test
	public void testToCOMWithCompleteVehicle() throws Exception {
		ComVehicle initialComVehicle = null;
		VehicleDTO vehicleDTO = new VehicleDTO();
		vehicleDTO.setId(1);
		vehicleDTO.setYear(2000);
		vehicleDTO.setModel("HONDA");
		vehicleDTO.setModelCode("025800");
		vehicleDTO.setTrackingSystemInd(Boolean.TRUE);
		vehicleDTO.setNormalRadiusKm(4000);
		vehicleDTO.setGrossVehicleWeight(3000d);
		vehicleDTO.setCommercialUsageCategoryCd("CONTRACT");
		vehicleDTO.setCommercialUsageCd(null);
		ComVehicle comVehicle = this.comVehicleConverter.toCOM(vehicleDTO, initialComVehicle);
		assertNotNull(comVehicle);
	}

}
