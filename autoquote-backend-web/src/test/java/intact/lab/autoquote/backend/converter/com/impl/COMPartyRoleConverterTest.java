package intact.lab.autoquote.backend.converter.com.impl;

import com.intact.com.CommunicationObjectModel;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.converter.impl.COMPartyRoleConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class COMPartyRoleConverterTest {

	@InjectMocks
	public COMPartyRoleConverter comPartyRoleConverter;

	@Test
	public void testToDTO() throws Exception {
		CommunicationObjectModel com = ConverterTestUtil.buildCom();
		QuoteDTO quoteDTO = ConverterTestUtil.buildQuoteDTO();
		QuoteDTO responseQuoteDTO = this.comPartyRoleConverter.toDTO(com, quoteDTO);
		assertNotNull(responseQuoteDTO);
        assertEquals(2, responseQuoteDTO.getDrivers().size());
        assertEquals(2, responseQuoteDTO.getParties().size());
        assertEquals(1, responseQuoteDTO.getPolicyHolders().size());
        assertEquals(1, responseQuoteDTO.getRisks().size());
	}

	@Test
	public void testToCOM() throws Exception {
		CommunicationObjectModel com = ConverterTestUtil.buildCom();
		QuoteDTO quoteDTO = ConverterTestUtil.buildQuoteDTO();
		CommunicationObjectModel responseCom = this.comPartyRoleConverter.toCOM(quoteDTO, com);
		assertNotNull(responseCom);
        assertEquals(2, responseCom.getDrivers().size());
        assertEquals(1, responseCom.getVehicles().size());
	}

}
