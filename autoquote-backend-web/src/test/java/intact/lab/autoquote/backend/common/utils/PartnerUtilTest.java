package intact.lab.autoquote.backend.common.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class PartnerUtilTest {

    @Test
    void GetEmailWithPartner_Should_ReturnNull_WhenEmailIsBlank() {
        String result = PartnerUtil.getEmailWithPartner("", "partner");
        assertNull(result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnNull_WhenEmailIsNull() {
        String result = PartnerUtil.getEmailWithPartner(null, "partner");
        assertNull(result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnOriginalEmail_WhenPartnerIsBlank() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "");
        assertEquals(email, result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnOriginalEmail_WhenPartnerIsNull() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, null);
        assertEquals(email, result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnPrefixedEmail_WhenPartnerIsSLF() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "SLF");
        assertEquals("<EMAIL>", result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnPrefixedEmail_WhenPartnerIsLowercaseSlf() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "slf");
        assertEquals("<EMAIL>", result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnOriginalEmail_WhenEmailAlreadyHasSlfPrefix() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "SLF");
        assertEquals(email, result);
    }

    @Test
    void GetEmailWithPartner_Should_ReturnOriginalEmail_WhenPartnerIsNotSLF() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "OTHER");
        assertEquals(email, result);
    }

    @Test
    void GetEmailWithPartner_Should_HandleCaseInsensitiveEmailPrefix() {
        String email = "<EMAIL>";
        String result = PartnerUtil.getEmailWithPartner(email, "SLF");
        assertEquals(email, result);
    }
}
