/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.partyRoleInRisk.AdditionalInterest;
import com.ing.canada.som.interfaces.physicalObject.AntiTheftDevice;
import com.ing.canada.som.interfaces.physicalObject.ConstructionMaterial;
import com.ing.canada.som.interfaces.physicalObject.ImprovementDetail;
import com.ing.canada.som.interfaces.physicalObject.Inspection;
import com.ing.canada.som.interfaces.physicalObject.SecurityProtectionDevice;
import com.ing.canada.som.interfaces.physicalObject.Vehicle;
import com.ing.canada.som.interfaces.physicalObject.VehicleDetailSpec;
import com.ing.canada.som.interfaces.physicalObject.VehicleEquipment;
import com.ing.canada.som.interfaces.physicalObject.VehicleModification;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.ArrayList;
import java.util.GregorianCalendar;
import java.util.List;

/**
 *
 * Mock class for a SOM Vehicle. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link Vehicle}
 *
 * <AUTHOR>
 *
 */
public class MockVehicle implements Vehicle {
	private String vehicleUbiEligibilityInd;

	private String useOfVehicle;

	private String ratingTableIdentification;

	private String actionTaken;

	private List<AntiTheftDevice> antiTheftDeviceList;

	private Integer annualBusinessKms;

	private Integer odometerReading;

	private Integer purchaseOdometerReading;

	@Override
	public Integer getNumberOfUnits() {
		return null;
	}

	@Override
	public void setNumberOfUnits(Integer newNumberOfUnits) {
		// noop
	}

	@Override
	public void clearTheImprovementDetail() {
		// noop
	}

	@Override
	public List<ImprovementDetail> getTheImprovementDetail() {
		return null;
	}

	@Override
	public ImprovementDetail getTheImprovementDetail(String uniqueId) {
		return null;
	}

	@Override
	public ImprovementDetail getTheImprovementDetail(int index) {
		return null;
	}

	@Override
	public ImprovementDetail addTheImprovementDetail() {
		return null;
	}

	@Override
	public ImprovementDetail addTheImprovementDetail(Class<? extends ImprovementDetail> theInterface) {
		return null;
	}

	@Override
	public void addTheImprovementDetail(ImprovementDetail newTheImprovementDetail) {
		// noop
	}

	@Override
	public void addTheImprovementDetail(int index, ImprovementDetail newTheImprovementDetail) {
		// noop
	}

	@Override
	public void setTheImprovementDetail(int index, ImprovementDetail newTheImprovementDetail) {
		// noop
	}

	@Override
	public void setTheImprovementDetail(List<ImprovementDetail> objList) {
		// noop
	}

	@Override
	public void removeTheImprovementDetail(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheImprovementDetail(int index) {
		// noop
	}

	@Override
	public void clearTheSecurityProtectionDevice() {
		// noop
	}

	@Override
	public List<SecurityProtectionDevice> getTheSecurityProtectionDevice() {
		return null;
	}

	@Override
	public SecurityProtectionDevice getTheSecurityProtectionDevice(String uniqueId) {
		return null;
	}

	@Override
	public SecurityProtectionDevice getTheSecurityProtectionDevice(int index) {
		return null;
	}

	@Override
	public SecurityProtectionDevice addTheSecurityProtectionDevice() {
		return null;
	}

	@Override
	public SecurityProtectionDevice addTheSecurityProtectionDevice(Class<? extends SecurityProtectionDevice> theInterface) {
		return null;
	}

	@Override
	public void addTheSecurityProtectionDevice(SecurityProtectionDevice newTheSecurityProtectionDevice) {
		// noop
	}

	@Override
	public void addTheSecurityProtectionDevice(int index, SecurityProtectionDevice newTheSecurityProtectionDevice) {
		// noop
	}

	@Override
	public void setTheSecurityProtectionDevice(int index, SecurityProtectionDevice newTheSecurityProtectionDevice) {
		// noop
	}

	@Override
	public void setTheSecurityProtectionDevice(List<SecurityProtectionDevice> objList) {
		// noop
	}

	@Override
	public void removeTheSecurityProtectionDevice(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheSecurityProtectionDevice(int index) {
		// noop
	}

	@Override
	public void clearTheConstructionMaterial() {
		// noop
	}

	@Override
	public List<ConstructionMaterial> getTheConstructionMaterial() {
		return null;
	}

	@Override
	public ConstructionMaterial getTheConstructionMaterial(String uniqueId) {
		return null;
	}

	@Override
	public ConstructionMaterial getTheConstructionMaterial(int index) {
		return null;
	}

	@Override
	public ConstructionMaterial addTheConstructionMaterial() {
		return null;
	}

	@Override
	public ConstructionMaterial addTheConstructionMaterial(Class<? extends ConstructionMaterial> theInterface) {
		return null;
	}

	@Override
	public void addTheConstructionMaterial(ConstructionMaterial newTheConstructionMaterial) {
		// noop
	}

	@Override
	public void addTheConstructionMaterial(int index, ConstructionMaterial newTheConstructionMaterial) {
		// noop
	}

	@Override
	public void setTheConstructionMaterial(int index, ConstructionMaterial newTheConstructionMaterial) {
		// noop
	}

	@Override
	public void setTheConstructionMaterial(List<ConstructionMaterial> objList) {
		// noop
	}

	@Override
	public void removeTheConstructionMaterial(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheConstructionMaterial(int index) {
		// noop
	}

	@Override
	public String getActionTaken() {
		return this.actionTaken;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		this.actionTaken = newActionTaken;
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {
		return false;
	}

	@Override
	public String getFuelUsedByVehicle() {
		return null;
	}

	@Override
	public void setFuelUsedByVehicle(String newFuelUsedByVehicle) {
		// noop
	}

	@Override
	public Integer getListPriceNew() {
		return null;
	}

	@Override
	public void setListPriceNew(Integer newListPriceNew) {
		// noop
	}

	@Override
	public Integer getValuationAmount() {
		return null;
	}

	@Override
	public void setValuationAmount(Integer newValuationAmount) {
		// noop
	}

	@Override
	public String getUnrepairedDamageInd() {
		return null;
	}

	@Override
	public void setUnrepairedDamageInd(String newUnrepairedDamageInd) {
		// noop
	}

	@Override
	public String getUnrepairedWithOnlyGlassDamageInd() {
		return null;
	}

	@Override
	public void setUnrepairedWithOnlyGlassDamageInd(String newUnrepairedWithOnlyGlassDamageInd) {

	}

	@Override
	public String getVehicleBodyType() {
		return null;
	}

	@Override
	public void setVehicleBodyType(String newVehicleBodyType) {
		// noop
	}

	@Override
	public String getVehicleBodyTypeLegacy() {
		return null;
	}

	@Override
	public void setVehicleBodyTypeLegacy(String newVehicleBodyTypeLegacy) {
		// noop
	}

	@Override
	public String getVehicleBodyTypeDescription() {
		return null;
	}

	@Override
	public void setVehicleBodyTypeDescription(String newVehicleBodyTypeDescription) {
		// noop
	}

	@Override
	public String getVehicleBodyCode() {
		return null;
	}

	@Override
	public void setVehicleBodyCode(String newVehicleBodyCode) {
		// noop
	}

	@Override
	public String getVehicleBodyCodeAdditional() {
		return null;
	}

	@Override
	public void setVehicleBodyCodeAdditional(String newVehicleBodyCodeAdditional) {
		// noop
	}

	@Override
	public String getBodyStyle() {
		return null;
	}

	@Override
	public void setBodyStyle(String newBodyStyle) {
		// noop
	}

	@Override
	public String getCustomBuiltType() {
		return null;
	}

	@Override
	public void setCustomBuiltType(String newCustomBuiltType) {
		// noop
	}

	@Override
	public String getTypeOfMachineryEquipmentCode() {
		return null;
	}

	@Override
	public void setTypeOfMachineryEquipmentCode(String newTypeOfMachineryEquipmentCode) {
		// noop
	}

	@Override
	public String getTypeOfMachineryEquipmentCodeAdditional() {
		return null;
	}

	@Override
	public void setTypeOfMachineryEquipmentCodeAdditional(String newTypeOfMachineryEquipmentCodeAdditional) {
		// noop
	}

	@Override
	public Integer getTotalEquipmentValue() {
		return null;
	}

	@Override
	public void setTotalEquipmentValue(Integer newTotalEquipmentValue) {
		// noop
	}

	@Override
	public Integer getEngineCapacity() {
		return null;
	}

	@Override
	public void setEngineCapacity(Integer newEngineCapacity) {
		// noop
	}

	@Override
	public Integer getVehicleNetWeight() {
		return null;
	}

	@Override
	public void setVehicleNetWeight(Integer newVehicleNetWeight) {
		// noop
	}

	@Override
	public Integer getGrossVehicleWeight() {
		return null;
	}

	@Override
	public void setGrossVehicleWeight(Integer newGrossVehicleWeight) {
		// noop
	}

	@Override
	public Double getGrossVehicleWeightCommercial() {
		return null;
	}

	@Override
	public void setGrossVehicleWeightCommercial(Double newGrossVehicleWeightCommercial) {
		// noop
	}

	@Override
	public Integer getVehicleLength() {
		return null;
	}

	@Override
	public void setVehicleLength(Integer newVehicleLength) {
		// noop
	}

	@Override
	public String getVehicleIdentificationNumber() {
		return null;
	}

	@Override
	public void setVehicleIdentificationNumber(String newVehicleIdentificationNumber) {
		// noop
	}

	@Override
	public GregorianCalendar getVehiclePurchaseDate() {
		return null;
	}

	@Override
	public void setVehiclePurchaseDate(GregorianCalendar newVehiclePurchaseDate) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceVehiclePurchase() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceVehiclePurchase(Integer newNumberOfMonthsSinceVehiclePurchase) {
		// noop
	}

	@Override
	public Integer getNumberOfMonthsSinceMotorcyclePurchase() {
		return null;
	}

	@Override
	public void setNumberOfMonthsSinceMotorcyclePurchase(Integer newNumberOfMonthsSinceMotorcyclePurchase) {
		// noop
	}

	@Override
	public Integer getVehiclePurchasePrice() {
		return null;
	}

	@Override
	public void setVehiclePurchasePrice(Integer newVehiclePurchasePrice) {
		// noop
	}

	@Override
	public String getSubstituteVehicleInd() {
		return null;
	}

	@Override
	public void setSubstituteVehicleInd(String newSubstituteVehicleInd) {
		// noop
	}

	@Override
	public Integer getPercentageUseOutsideOfProvince() {
		return null;
	}

	@Override
	public void setPercentageUseOutsideOfProvince(Integer newPercentageUseOutsideOfProvince) {
		// noop
	}

	@Override
	public String getNumberOfKilometersUseOutsideProvinceCode() {
		return null;
	}

	@Override
	public void setNumberOfKilometersUseOutsideProvinceCode(String newNumberOfKilometersUseOutsideProvinceCode) {
		// noop
	}

	@Override
	public String getNumberOfKilometersUseOutsideProvinceForPleasureOrBusCode() {
		return null;
	}

	@Override
	public void setNumberOfKilometersUseOutsideProvinceForPleasureOrBusCode(String newNumberOfKilometersUseOutsideProvinceForPleasureOrBusCode) {
		// noop
	}

	@Override
	public String getOtherProvinceOfUse() {
		return null;
	}

	@Override
	public void setOtherProvinceOfUse(String newOtherProvinceOfUse) {
		// noop
	}

	@Override
	public String getProvinceOfUse() {
		return null;
	}

	@Override
	public void setProvinceOfUse(String newProvinceOfUse) {
		// noop
	}

	@Override
	public String getPurposeUseOutsideOfProvince() {
		return null;
	}

	@Override
	public void setPurposeUseOutsideOfProvince(String newPurposeUseOutsideOfProvince) {
		// noop
	}

	@Override
	public Integer getNumberOfWeeksOutsideOfProvince() {
		return null;
	}

	@Override
	public void setNumberOfWeeksOutsideOfProvince(Integer newNumberOfWeeksOutsideOfProvince) {
		// noop
	}

	@Override
	public String getUseOfVehicle() {
		return this.useOfVehicle;
	}

	@Override
	public void setUseOfVehicle(String newUseOfVehicle) {
		this.useOfVehicle = newUseOfVehicle;
	}

	@Override
	public String getVehicleUsageDescription() {
		return null;
	}

	@Override
	public void setVehicleUsageDescription(String newVehicleUsageDescription) {
		// noop
	}

	@Override
	public String getConditionOfVehicleWhenBought() {
		return null;
	}

	@Override
	public void setConditionOfVehicleWhenBought(String newConditionOfVehicleWhenBought) {
		// noop
	}

	@Override
	public String getVehicleConditionNoteCode() {
		return null;
	}

	@Override
	public void setVehicleConditionNoteCode(String newVehicleConditionNoteCode) {
		// noop
	}

	@Override
	public Integer getNumberOfKilometersToDriveToWork() {
		return null;
	}

	@Override
	public void setNumberOfKilometersToDriveToWork(Integer newNumberOfKilometersToDriveToWork) {
		// noop
	}

	@Override
	public Integer getAnnualKilometers() {
		return null;
	}

	@Override
	public void setAnnualKilometers(Integer newAnnualKilometers) {
		// noop
	}

	@Override
	public Integer getAnnualBusinessKms() {
		return this.annualBusinessKms;
	}

	@Override
	public void setAnnualBusinessKms(Integer newAnnualBusinessKms) {
		this.annualBusinessKms = newAnnualBusinessKms;
	}

	@Override
	public Integer getEstimatedIncidentalMidHaulNumberOfDaysPerMonth() {
		return null;
	}

	@Override
	public void setEstimatedIncidentalMidHaulNumberOfDaysPerMonth(Integer integer) {

	}

	@Override
	public Integer getOdometerReading() {
		return this.odometerReading;
	}

	@Override
	public void setOdometerReading(Integer newOdometerReading) {
		this.odometerReading = newOdometerReading;
	}

	@Override
	public Integer getPurchaseOdometerReading() {
		return this.purchaseOdometerReading;
	}

	@Override
	public void setPurchaseOdometerReading(Integer newPurchaseOdometerReading) {
		this.purchaseOdometerReading = newPurchaseOdometerReading;
	}

	@Override
	public String getIndicatorOfAdditionalEquipOrModif() {
		return null;
	}

	@Override
	public void setIndicatorOfAdditionalEquipOrModif(String newIndicatorOfAdditionalEquipOrModif) {
		// noop
	}

	@Override
	public Integer getValueOfVehicleModification() {
		return null;
	}

	@Override
	public void setValueOfVehicleModification(Integer newValueOfVehicleModification) {
		// noop
	}

	@Override
	public String getVehicleModificationCode() {
		return null;
	}

	@Override
	public void setVehicleModificationCode(String newVehicleModificationCode) {
		// noop
	}

	@Override
	public String getVehicleRebuiltInd() {
		return null;
	}

	@Override
	public void setVehicleRebuiltInd(String newVehicleRebuiltInd) {
		// noop
	}

	@Override
	public String getVehicleChangeRestrictionInd() {
		return null;
	}

	@Override
	public void setVehicleChangeRestrictionInd(String newVehicleChangeRestrictionInd) {
		// noop
	}

	@Override
	public Integer getValueOfCustomPaintJob() {
		return null;
	}

	@Override
	public void setValueOfCustomPaintJob(Integer newValueOfCustomPaintJob) {
		// noop
	}

	@Override
	public String getHighValueVehicleModificationInd() {
		return null;
	}

	@Override
	public void setHighValueVehicleModificationInd(String newHighValueVehicleModificationInd) {
		// noop
	}

	@Override
	public String getPerformanceVehicleModificationInd() {
		return null;
	}

	@Override
	public void setPerformanceVehicleModificationInd(String newPerformanceVehicleModificationInd) {
		// noop
	}

	@Override
	public String getGoodsForCompensationInd() {
		return null;
	}

	@Override
	public void setGoodsForCompensationInd(String newGoodsForCompensationInd) {
		// noop
	}

	@Override
	public String getGoodsCarriedDescription() {
		return null;
	}

	@Override
	public void setGoodsCarriedDescription(String newGoodsCarriedDescription) {
		// noop
	}

	@Override
	public Double getGoodsCarriedValue() {
		return null;
	}

	@Override
	public void setGoodsCarriedValue(Double newGoodsCarriedValue) {
		// noop
	}

	@Override
	public Integer getNormalRadiusOfOperationForVehicleKms() {
		return null;
	}

	@Override
	public void setNormalRadiusOfOperationForVehicleKms(Integer newNormalRadiusOfOperationForVehicleKms) {
		// noop
	}

	@Override
	public String getCarryPassengerForCompensationInd() {
		return null;
	}

	@Override
	public void setCarryPassengerForCompensationInd(String newCarryPassengerForCompensationInd) {
		// noop
	}

	@Override
	public String getCarryExplosiveRadioactiveInd() {
		return null;
	}

	@Override
	public void setCarryExplosiveRadioactiveInd(String newCarryExplosiveRadioactiveInd) {
		// noop
	}

	@Override
	public String getCarPoolInd() {
		return null;
	}

	@Override
	public void setCarPoolInd(String newCarPoolInd) {
		// noop
	}

	@Override
	public String getAntiTheftBarInd() {
		return null;
	}

	@Override
	public void setAntiTheftBarInd(String newAntiTheftBarInd) {
		// noop
	}

	@Override
	public String getRemoteTrackingSystemInd() {
		return null;
	}

	@Override
	public void setRemoteTrackingSystemInd(String newRemoteTrackingSystemInd) {
		// noop
	}

	@Override
	public String getCutOffSwitchInd() {
		return null;
	}

	@Override
	public void setCutOffSwitchInd(String newCutOffSwitchInd) {
		// noop
	}

	@Override
	public String getEngravingInd() {
		return null;
	}

	@Override
	public void setEngravingInd(String newEngravingInd) {
		// noop
	}

	@Override
	public String getIntensiveEngravingInd() {
		return null;
	}

	@Override
	public void setIntensiveEngravingInd(String newIntensiveEngravingInd) {
		// noop
	}

	@Override
	public String getFlashingLightInd() {
		return null;
	}

	@Override
	public void setFlashingLightInd(String newFlashingLightInd) {
		// noop
	}

	@Override
	public String getAlarmSystemInd() {
		return null;
	}

	@Override
	public void setAlarmSystemInd(String newAlarmSystemInd) {
		// noop
	}

	@Override
	public String getAntiTheftDeviceCodeLegacy() {
		return null;
	}

	@Override
	public void setAntiTheftDeviceCodeLegacy(String newAntiTheftDeviceCodeLegacy) {
		// noop
	}

	@Override
	public Integer getVehicleAge() {
		return null;
	}

	@Override
	public void setVehicleAge(Integer newVehicleAge) {
		// noop
	}

	@Override
	public Integer getVehicleAgeInMonths() {
		return null;
	}

	@Override
	public void setVehicleAgeInMonths(Integer newVehicleAgeInMonths) {
		// noop
	}

	@Override
	public String getLeasedVehicleIndSystem() {
		return null;
	}

	@Override
	public void setLeasedVehicleIndSystem(String s) {

	}

	@Override
	public String getLeasedVehicleIndModified() {
		return null;
	}

	@Override
	public void setLeasedVehicleIndModified(String s) {

	}

	@Override
	public String getLeasedVehicleInd() {
		return null;
	}

	@Override
	public void setLeasedVehicleInd(String newLeasedVehicleInd) {
		// noop
	}

	@Override
	public String getLienholderInd() {
		return null;
	}

	@Override
	public void setLienholderInd(String newLienholderInd) {
		// noop
	}

	@Override
	public String getReplacementCostProofInd() {
		return null;
	}

	@Override
	public void setReplacementCostProofInd(String newReplacementCostProofInd) {
		// noop
	}

	@Override
	public String getRecreationalMotorcycleDiscountInd() {
		return null;
	}

	@Override
	public void setRecreationalMotorcycleDiscountInd(String newRecreationalMotorcycleDiscountInd) {
		// noop
	}

	@Override
	public String getVehicleRateGroupByValueInd() {
		return null;
	}

	@Override
	public void setVehicleRateGroupByValueInd(String newVehicleRateGroupByValueInd) {
		// noop
	}

	@Override
	public String getVehicleRateGroup() {
		return null;
	}

	@Override
	public void setVehicleRateGroup(String newVehicleRateGroup) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityBodilyInjury() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityBodilyInjury(String newVehicleRateGroupLiabilityBodilyInjury) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityBodilyInjuryAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityBodilyInjuryAdjustment(String newVehicleRateGroupLiabilityBodilyInjuryAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityBodilyInjuryAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityBodilyInjuryAdjusted(String newVehicleRateGroupLiabilityBodilyInjuryAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityPropertyDamage() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityPropertyDamage(String newVehicleRateGroupLiabilityPropertyDamage) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityPropertyDamageAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityPropertyDamageAdjustment(String newVehicleRateGroupLiabilityPropertyDamageAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityPropertyDamageAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityPropertyDamageAdjusted(String newVehicleRateGroupLiabilityPropertyDamageAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAccidentBenefit() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAccidentBenefit(String newVehicleRateGroupAccidentBenefit) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAccidentBenefitAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAccidentBenefitAdjustment(String newVehicleRateGroupAccidentBenefitAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAccidentBenefitAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAccidentBenefitAdjusted(String newVehicleRateGroupAccidentBenefitAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAllPerils() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAllPerils(String newVehicleRateGroupAllPerils) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAllPerilsAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAllPerilsAdjustment(String newVehicleRateGroupAllPerilsAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAllPerilsAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAllPerilsAdjusted(String newVehicleRateGroupAllPerilsAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupAllPerilsClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupAllPerilsClear(String newVehicleRateGroupAllPerilsClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupCollision() {
		return null;
	}

	@Override
	public void setVehicleRateGroupCollision(String newVehicleRateGroupCollision) {
		// noop
	}

	@Override
	public String getVehicleRateGroupCollisionAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupCollisionAdjustment(String newVehicleRateGroupCollisionAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupCollisionAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupCollisionAdjusted(String newVehicleRateGroupCollisionAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupCollisionClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupCollisionClear(String newVehicleRateGroupCollisionClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupComprehensive() {
		return null;
	}

	@Override
	public void setVehicleRateGroupComprehensive(String newVehicleRateGroupComprehensive) {
		// noop
	}

	@Override
	public String getVehicleRateGroupComprehensiveAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupComprehensiveAdjustment(String newVehicleRateGroupComprehensiveAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupComprehensiveAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupComprehensiveAdjusted(String newVehicleRateGroupComprehensiveAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupComprehensiveClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupComprehensiveClear(String newVehicleRateGroupComprehensiveClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupSpecifiedPerils() {
		return null;
	}

	@Override
	public void setVehicleRateGroupSpecifiedPerils(String newVehicleRateGroupSpecifiedPerils) {
		// noop
	}

	@Override
	public String getVehicleRateGroupSpecifiedPerilsAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupSpecifiedPerilsAdjustment(String newVehicleRateGroupSpecifiedPerilsAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupSpecifiedPerilsAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupSpecifiedPerilsAdjusted(String newVehicleRateGroupSpecifiedPerilsAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupSpecifiedPerilsClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupSpecifiedPerilsClear(String newVehicleRateGroupSpecifiedPerilsClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiability() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiability(String newVehicleRateGroupLiability) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityAdjustment(String newVehicleRateGroupLiabilityAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityAdjusted(String newVehicleRateGroupLiabilityAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupLiabilityClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupLiabilityClear(String newVehicleRateGroupLiabilityClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupMedicalExpenses() {
		return null;
	}

	@Override
	public void setVehicleRateGroupMedicalExpenses(String newVehicleRateGroupMedicalExpenses) {
		// noop
	}

	@Override
	public String getVehicleRateGroupMedicalExpensesAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupMedicalExpensesAdjustment(String newVehicleRateGroupMedicalExpensesAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupMedicalExpensesAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupMedicalExpensesAdjusted(String newVehicleRateGroupMedicalExpensesAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupMedicalExpensesClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupMedicalExpensesClear(String newVehicleRateGroupMedicalExpensesClear) {
		// noop
	}

	@Override
	public String getVehicleRateGroupTotalDisability() {
		return null;
	}

	@Override
	public void setVehicleRateGroupTotalDisability(String newVehicleRateGroupTotalDisability) {
		// noop
	}

	@Override
	public String getVehicleRateGroupTotalDisabilityAdjustment() {
		return null;
	}

	@Override
	public void setVehicleRateGroupTotalDisabilityAdjustment(String newVehicleRateGroupTotalDisabilityAdjustment) {
		// noop
	}

	@Override
	public String getVehicleRateGroupTotalDisabilityAdjusted() {
		return null;
	}

	@Override
	public void setVehicleRateGroupTotalDisabilityAdjusted(String newVehicleRateGroupTotalDisabilityAdjusted) {
		// noop
	}

	@Override
	public String getVehicleRateGroupTotalDisabilityClear() {
		return null;
	}

	@Override
	public void setVehicleRateGroupTotalDisabilityClear(String newVehicleRateGroupTotalDisabilityClear) {
		// noop
	}

	@Override
	public String getRatingTableIdentification() {
		return this.ratingTableIdentification;
	}

	@Override
	public void setRatingTableIdentification(String newRatingTableIdentification) {
		this.ratingTableIdentification = newRatingTableIdentification;
	}

	@Override
	public String getParkingType() {
		return null;
	}

	@Override
	public void setParkingType(String newParkingType) {
		// noop
	}

	@Override
	public String getSnowClubMemberInd() {
		return null;
	}

	@Override
	public void setSnowClubMemberInd(String newSnowClubMemberInd) {
		// noop
	}

	@Override
	public String getMotorcycleClubMemberInd() {
		return null;
	}

	@Override
	public void setMotorcycleClubMemberInd(String newMotorcycleClubMemberInd) {
		// noop
	}

	@Override
	public String getMotorcycleCategory() {
		return null;
	}

	@Override
	public void setMotorcycleCategory(String newMotorcycleCategory) {
		// noop
	}

	@Override
	public String getMotorcycleSubCategory() {
		return null;
	}

	@Override
	public void setMotorcycleSubCategory(String newMotorcycleSubCategory) {
		// noop
	}

	@Override
	public String getMotorcycleType() {
		return null;
	}

	@Override
	public void setMotorcycleType(String newMotorcycleType) {
		// noop
	}

	@Override
	public String getVehicleTheftDuringLastTermInd() {
		return null;
	}

	@Override
	public void setVehicleTheftDuringLastTermInd(String newVehicleTheftDuringLastTermInd) {
		// noop
	}

	@Override
	public String getVehicleCodeOverride() {
		return null;
	}

	@Override
	public void setVehicleCodeOverride(String newVehicleCodeOverride) {
		// noop
	}

	@Override
	public String getUbiEligibilityInd() {
		return this.vehicleUbiEligibilityInd;
	}

	@Override
	public void setUbiEligibilityInd(String newUbiEligibilityInd) {
		this.vehicleUbiEligibilityInd = newUbiEligibilityInd;
	}

	@Override
	public String getRentedToThirdPartyInd() {
		return null;
	}

	@Override
	public void setRentedToThirdPartyInd(String newRentedToThirdPartyInd) {
		// noop
	}

	@Override
	public String getSourceOfVehicleSpecification() {
		return null;
	}

	@Override
	public void setSourceOfVehicleSpecification(String newSourceOfVehicleSpecification) {
		// noop
	}

	@Override
	public String getInvalidSerialNumberInd() {
		return null;
	}

	@Override
	public void setInvalidSerialNumberInd(String newInvalidSerialNumberInd) {
		// noop
	}

	@Override
	public String getVehicleInsuredReferenceNumber() {
		return null;
	}

	@Override
	public void setVehicleInsuredReferenceNumber(String newVehicleInsuredReferenceNumber) {
		// noop
	}

	@Override
	public String getTypeOfMachineryEquipmentDescriptionEng() {
		return null;
	}

	@Override
	public void setTypeOfMachineryEquipmentDescriptionEng(String newTypeOfMachineryEquipmentDescriptionEng) {
		// noop
	}

	@Override
	public String getVehicleBusinessUse() {
		return null;
	}

	@Override
	public void setVehicleBusinessUse(String newVehicleBusinessUse) {
		// noop
	}

	@Override
	public String getWinterTiresInstalledInd() {
		return null;
	}

	@Override
	public void setWinterTiresInstalledInd(String newWinterTiresInstalledInd) {
		// noop
	}

	@Override
	public String getVehiclePhotosReceivedInd() {
		return null;
	}

	@Override
	public void setVehiclePhotosReceivedInd(String newVehiclePhotosReceivedInd) {
		// noop
	}

	@Override
	public Integer getMaximumRadiusOfOperationForVehicleKms() {
		return null;
	}

	@Override
	public void setMaximumRadiusOfOperationForVehicleKms(Integer newMaximumRadiusOfOperationForVehicleKms) {
		// noop
	}

	@Override
	public Integer getNumberOfPassengerSeats() {
		return null;
	}

	@Override
	public void setNumberOfPassengerSeats(Integer newNumberOfPassengerSeats) {
		// noop
	}

	@Override
	public void clearTheAdditionalInterest() {
		// noop
	}

	@Override
	public List<AdditionalInterest> getTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(String uniqueId) {
		return null;
	}

	@Override
	public AdditionalInterest getTheAdditionalInterest(int index) {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest() {
		return null;
	}

	@Override
	public AdditionalInterest addTheAdditionalInterest(Class<? extends AdditionalInterest> theInterface) {
		return null;
	}

	@Override
	public void addTheAdditionalInterest(AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void addTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(int index, AdditionalInterest newTheAdditionalInterest) {
		// noop
	}

	@Override
	public void setTheAdditionalInterest(List<AdditionalInterest> objList) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAdditionalInterest(int index) {
		// noop
	}

	@Override
	public InsuranceRisk getTheInsuranceRisk() {
		return null;
	}

	@Override
	public void setTheInsuranceRisk(InsuranceRisk newTheInsuranceRisk) {
		// noop
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk() {
		return null;
	}

	@Override
	public InsuranceRisk createTheInsuranceRisk(Class<? extends InsuranceRisk> theInterface) {
		return null;
	}

	@Override
	public VehicleDetailSpec getTheVehicleDetailSpec() {
		return null;
	}

	@Override
	public void setTheVehicleDetailSpec(VehicleDetailSpec newTheVehicleDetailSpec) {
		// noop
	}

	@Override
	public VehicleDetailSpec createTheVehicleDetailSpec() {
		return null;
	}

	@Override
	public VehicleDetailSpec createTheVehicleDetailSpec(Class<? extends VehicleDetailSpec> theInterface) {
		return null;
	}

	@Override
	public Inspection getTheInspection() {
		return null;
	}

	@Override
	public void setTheInspection(Inspection newTheInspection) {
		// noop
	}

	@Override
	public Inspection createTheInspection() {
		return null;
	}

	@Override
	public Inspection createTheInspection(Class<? extends Inspection> theInterface) {
		return null;
	}

	@Override
	public void clearTheVehicleEquipment() {
		// noop
	}

	@Override
	public List<VehicleEquipment> getTheVehicleEquipment() {
		return null;
	}

	@Override
	public VehicleEquipment getTheVehicleEquipment(String uniqueId) {
		return null;
	}

	@Override
	public VehicleEquipment getTheVehicleEquipment(int index) {
		return null;
	}

	@Override
	public VehicleEquipment addTheVehicleEquipment() {
		return null;
	}

	@Override
	public VehicleEquipment addTheVehicleEquipment(Class<? extends VehicleEquipment> theInterface) {
		return null;
	}

	@Override
	public void addTheVehicleEquipment(VehicleEquipment newTheVehicleEquipment) {
		// noop
	}

	@Override
	public void addTheVehicleEquipment(int index, VehicleEquipment newTheVehicleEquipment) {
		// noop
	}

	@Override
	public void setTheVehicleEquipment(int index, VehicleEquipment newTheVehicleEquipment) {
		// noop
	}

	@Override
	public void setTheVehicleEquipment(List<VehicleEquipment> objList) {
		// noop
	}

	@Override
	public void removeTheVehicleEquipment(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheVehicleEquipment(int index) {
		// noop
	}

	@Override
	public void clearTheAntiTheftDevice() {
		// noop
	}

	@Override
	public List<AntiTheftDevice> getTheAntiTheftDevice() {
		this.antiTheftDeviceList = this.antiTheftDeviceList == null ? new ArrayList<AntiTheftDevice>() : this.antiTheftDeviceList;
		return this.antiTheftDeviceList;
	}

	@Override
	public AntiTheftDevice getTheAntiTheftDevice(String uniqueId) {
		return null;
	}

	@Override
	public AntiTheftDevice getTheAntiTheftDevice(int index) {
		return null;
	}

	@Override
	public AntiTheftDevice addTheAntiTheftDevice() {
		return null;
	}

	@Override
	public AntiTheftDevice addTheAntiTheftDevice(Class<? extends AntiTheftDevice> theInterface) {
		return null;
	}

	@Override
	public void addTheAntiTheftDevice(AntiTheftDevice newTheAntiTheftDevice) {
		// Creating the list if it doesn't exist
		this.antiTheftDeviceList = this.antiTheftDeviceList == null ? new ArrayList<AntiTheftDevice>() : this.antiTheftDeviceList;

		// Adding the new element to the list
		this.antiTheftDeviceList.add(newTheAntiTheftDevice);
	}

	@Override
	public void addTheAntiTheftDevice(int index, AntiTheftDevice newTheAntiTheftDevice) {
		// noop
	}

	@Override
	public void setTheAntiTheftDevice(int index, AntiTheftDevice newTheAntiTheftDevice) {
		// noop
	}

	@Override
	public void setTheAntiTheftDevice(List<AntiTheftDevice> objList) {
		// noop
	}

	@Override
	public void removeTheAntiTheftDevice(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAntiTheftDevice(int index) {
		// noop
	}

	@Override
	public Vehicle getTheVehiclePriorTrans() {
		return null;
	}

	@Override
	public void setTheVehiclePriorTrans(Vehicle newTheVehiclePriorTrans) {
		// noop
	}

	@Override
	public Vehicle createTheVehiclePriorTrans() {
		return null;
	}

	@Override
	public Vehicle createTheVehiclePriorTrans(Class<? extends Vehicle> theInterface) {
		return null;
	}

	@Override
	public void clearTheVehicleModification() {
		// noop
	}

	@Override
	public List<VehicleModification> getTheVehicleModification() {
		return null;
	}

	@Override
	public VehicleModification getTheVehicleModification(String uniqueId) {
		return null;
	}

	@Override
	public VehicleModification getTheVehicleModification(int index) {
		return null;
	}

	@Override
	public VehicleModification addTheVehicleModification() {
		return null;
	}

	@Override
	public VehicleModification addTheVehicleModification(Class<? extends VehicleModification> theInterface) {
		return null;
	}

	@Override
	public void addTheVehicleModification(VehicleModification newTheVehicleModification) {
		// noop
	}

	@Override
	public void addTheVehicleModification(int index, VehicleModification newTheVehicleModification) {
		// noop
	}

	@Override
	public void setTheVehicleModification(int index, VehicleModification newTheVehicleModification) {
		// noop
	}

	@Override
	public void setTheVehicleModification(List<VehicleModification> objList) {
		// noop
	}

	@Override
	public void removeTheVehicleModification(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheVehicleModification(int index) {
		// noop
	}

	@Override
	public String getVehicleYear() {
		return null;
	}

	@Override
	public void setVehicleYear(String newVehicleYear) {
		// noop
	}

	@Override
	public String getVehicleCategory() {
		return null;
	}

	@Override
	public void setVehicleCategory(String newVehicleCategory) {
		// noop
	}

	@Override
	public String getRestrictedVehicleInd() {
		return null;
	}

	@Override
	public void setRestrictedVehicleInd(String newRestrictedVehicleInd) {
		// noop
	}

	@Override
	public Integer getHorsepower() {
		return null;
	}

	@Override
	public void setHorsepower(Integer newHorsepower) {
		// noop
	}

	@Override
	public String getEngineHybrid() {
		return null;
	}

	@Override
	public void setEngineHybrid(String newEngineHybrid) {
		// noop
	}

	@Override
	public String getReplacementCostInd() {
		return null;
	}

	@Override
	public void setReplacementCostInd(String newReplacementCostInd) {
		// noop
	}

	@Override
	public Integer getRetailPriceWithGst() {
		return null;
	}

	@Override
	public void setRetailPriceWithGst(Integer newRetailPriceWithGst) {
		// noop
	}

	@Override
	public String getCommercialClassEligibilityInd() {
		return null;
	}

	@Override
	public void setCommercialClassEligibilityInd(String newCommercialClassEligibilityInd) {
		// noop
	}

	@Override
	public Integer getRspAdjustmentScenarioVehicleNewBusiness() {
		return null;
	}

	@Override
	public void setRspAdjustmentScenarioVehicleNewBusiness(Integer newRspAdjustmentScenarioVehicleNewBusiness) {
		// noop
	}

	@Override
	public Integer getRspAdjustmentScenarioVehicleStandard() {
		return null;
	}

	@Override
	public void setRspAdjustmentScenarioVehicleStandard(Integer newRspAdjustmentScenarioVehicleStandard) {
		// noop
	}

	@Override
	public Integer getFacilityRiskSharingPoolScoring() {
		return null;
	}

	@Override
	public void setFacilityRiskSharingPoolScoring(Integer newFacilityRiskSharingPoolScoring) {
		// noop
	}

	@Override
	public String getRateGroupClearLiability() {
		return null;
	}

	@Override
	public void setRateGroupClearLiability(String newRateGroupClearLiability) {
		// noop
	}

	@Override
	public String getRateGroupClearAccidentBenefit() {
		return null;
	}

	@Override
	public void setRateGroupClearAccidentBenefit(String newRateGroupClearAccidentBenefit) {
		// noop
	}

	@Override
	public String getRateGroupClearCollision() {
		return null;
	}

	@Override
	public void setRateGroupClearCollision(String newRateGroupClearCollision) {
		// noop
	}

	@Override
	public String getRateGroupClearComprehensive() {
		return null;
	}

	@Override
	public void setRateGroupClearComprehensive(String newRateGroupClearComprehensive) {
		// noop
	}

	@Override
	public String getRateGroupClearDcpd() {
		return null;
	}

	@Override
	public void setRateGroupClearDcpd(String newRateGroupClearDcpd) {
		// noop
	}

	@Override
	public String getRateGroupMsrp() {
		return null;
	}

	@Override
	public void setRateGroupMsrp(String newRateGroupMsrp) {
		// noop
	}

	@Override
	public String getRateGroupClearCombined() {
		return null;
	}

	@Override
	public void setRateGroupClearCombined(String newRateGroupClearCombined) {
		// noop
	}

	@Override
	public String getRateGroupModClearCombined() {
		return null;
	}

	@Override
	public void setRateGroupModClearCombined(String newRateGroupModClearCombined) {
		// noop
	}

	@Override
	public String getRateGroupModClearCombinedNewBusiness() {
		return null;
	}

	@Override
	public void setRateGroupModClearCombinedNewBusiness(String newRateGroupModClearCombinedNewBusiness) {
		// noop
	}

	@Override
	public String getAntiTheftDeviceRequired() {
		return null;
	}

	@Override
	public void setAntiTheftDeviceRequired(String newAntiTheftDeviceRequired) {
		// noop
	}

	@Override
	public Integer getNumberOfCylinders() {
		return null;
	}

	@Override
	public void setNumberOfCylinders(Integer newNumberOfCylinders) {
		// noop
	}

	@Override
	public String getMinimumDeductibleMandatoryIndB3B4() {
		return null;
	}

	@Override
	public void setMinimumDeductibleMandatoryIndB3B4(String newMinimumDeductibleMandatoryIndB3B4) {
		// noop
	}

	@Override
	public String getMinimumDeductibleMandatoryIndC2C3() {
		return null;
	}

	@Override
	public void setMinimumDeductibleMandatoryIndC2C3(String newMinimumDeductibleMandatoryIndC2C3) {
		// noop
	}

	@Override
	public String getVehicleInspectionInd() {
		return null;
	}

	@Override
	public void setVehicleInspectionInd(String newVehicleInspectionInd) {
		// noop
	}

	@Override
	public GregorianCalendar getEffectiveDate() {
		return null;
	}

	@Override
	public void setEffectiveDate(GregorianCalendar newEffectiveDate) {
		// noop
	}

	@Override
	public GregorianCalendar getExpiryDate() {
		return null;
	}

	@Override
	public void setExpiryDate(GregorianCalendar newExpiryDate) {
		// noop
	}

	@Override
	public String getVehicleMake() {
		return null;
	}

	@Override
	public void setVehicleMake(String newVehicleMake) {
		// noop
	}

	@Override
	public String getVehicleModel() {
		return null;
	}

	@Override
	public void setVehicleModel(String newVehicleModel) {
		// noop
	}

	@Override
	public String getVehicleCode() {
		return null;
	}

	@Override
	public void setVehicleCode(String newVehicleCode) {
		// noop
	}

	@Override
	public String getVehicleMakeAndModelAbbreviation() {
		return null;
	}

	@Override
	public void setVehicleMakeAndModelAbbreviation(String newVehicleMakeAndModelAbbreviation) {
		// noop
	}

	/**
	 * @see Vehicle#getProhibitedVehicleInd()
	 */
	@Override
	public String getProhibitedVehicleInd() {
		return null;
	}

	/**
	 * @see Vehicle#setProhibitedVehicleInd(String)
	 */
	@Override
	public void setProhibitedVehicleInd(String newProhibitedVehicleInd) {
		// noop
	}

	/**
	 * @see Vehicle#getVehicleSurchargeInd()
	 */
	@Override
	public String getVehicleSurchargeInd() {
		return null;
	}

	/**
	 * @see Vehicle#setVehicleSurchargeInd(String)
	 */
	@Override
	public void setVehicleSurchargeInd(String newVehicleSurchargeInd) {
		// noop
	}

	@Override
	public String getRateGroupAccidentBenefitOverride() {
		return null;
	}

	@Override
	public void setRateGroupAccidentBenefitOverride(String newRateGroupAccidentBenefitOverride) {
		// noop
	}

	@Override
	public String getRateGroupLiabilityPropertyDamageOverride() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityPropertyDamageOverride(String newRateGroupLiabilityPropertyDamageOverride) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupCollisionOverride()
	 */
	@Override
	public String getRateGroupCollisionOverride() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupCollisionOverride(String)
	 */
	@Override
	public void setRateGroupCollisionOverride(String newRateGroupCollisionOverride) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupComprehensiveOverride()
	 */
	@Override
	public String getRateGroupComprehensiveOverride() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupComprehensiveOverride(String)
	 */
	@Override
	public void setRateGroupComprehensiveOverride(String newRateGroupComprehensiveOverride) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupAccidentBenefitVicc()
	 */
	@Override
	public String getRateGroupAccidentBenefitVicc() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupAccidentBenefitVicc(String)
	 */
	@Override
	public void setRateGroupAccidentBenefitVicc(String newRateGroupAccidentBenefitVicc) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupLiabilityPropertyDamageVicc()
	 */
	@Override
	public String getRateGroupLiabilityPropertyDamageVicc() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupLiabilityPropertyDamageVicc(String)
	 */
	@Override
	public void setRateGroupLiabilityPropertyDamageVicc(String newRateGroupLiabilityPropertyDamageVicc) {
		// noop
	}

	@Override
	public String getRateGroupLiabilityPropertyDamageViccPure() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityPropertyDamageViccPure(String s) {

	}

	/**
	 * @see Vehicle#getRateGroupCollisionVicc()
	 */
	@Override
	public String getRateGroupCollisionVicc() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupCollisionVicc(String)
	 */
	@Override
	public void setRateGroupCollisionVicc(String newRateGroupCollisionVicc) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupComprehensiveVicc()
	 */
	@Override
	public String getRateGroupComprehensiveVicc() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupComprehensiveVicc(String)
	 */
	@Override
	public void setRateGroupComprehensiveVicc(String newRateGroupComprehensiveVicc) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupAccidentBenefitScoring()
	 */
	@Override
	public String getRateGroupAccidentBenefitScoring() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupAccidentBenefitScoring(String)
	 */
	@Override
	public void setRateGroupAccidentBenefitScoring(String newRateGroupAccidentBenefitScoring) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupLiabilityPropertyDamageScoring()
	 */
	@Override
	public String getRateGroupLiabilityPropertyDamageScoring() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupLiabilityPropertyDamageScoring(String)
	 */
	@Override
	public void setRateGroupLiabilityPropertyDamageScoring(String newRateGroupLiabilityPropertyDamageScoring) {
		// noop
	}

	/**
	 * @see Vehicle#getRateGroupCollisionScoring()
	 */
	@Override
	public String getRateGroupCollisionScoring() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupCollisionScoring(String)
	 */
	@Override
	public void setRateGroupCollisionScoring(String newRateGroupCollisionScoring) {
		// noop
	}

	@Override
	public String getRateGroupCollisionViccPure() {
		return null;
	}

	@Override
	public void setRateGroupCollisionViccPure(String s) {

	}

	/**
	 * @see Vehicle#getRateGroupComprehensiveScoring()
	 */
	@Override
	public String getRateGroupComprehensiveScoring() {
		return null;
	}

	/**
	 * @see Vehicle#setRateGroupComprehensiveScoring(String)
	 */
	@Override
	public void setRateGroupComprehensiveScoring(String newRateGroupComprehensiveScoring) {
		// noop
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getAutomaticEmergencyBrakingSystemInd() {
		return null;
	}

	@Override
	public String getEngineStroke() {
		return null;
	}

	@Override
	public String getExtendedVehicleCode() {
		return null;
	}

	@Override
	public Integer getNumberOfWheels() {
		return null;
	}

	@Override
	public String getRateGroupAccidentBenefitByValue() {
		return null;
	}

	@Override
	public String getRateGroupCollisionByValue() {
		return null;
	}

	@Override
	public String getRateGroupComprehensiveByValue() {
		return null;
	}

	@Override
	public String getRateGroupLiabilityPropertyDamageByValue() {
		return null;
	}

	@Override
	public GregorianCalendar getVehiclePhotosReceivedDate() {
		return null;
	}

	@Override
	public void setAutomaticEmergencyBrakingSystemInd(String arg0) {
		// noop
	}

	@Override
	public void setEngineStroke(String arg0) {
		// noop
	}

	@Override
	public void setExtendedVehicleCode(String arg0) {
		// noop
	}

	@Override
	public void setNumberOfWheels(Integer arg0) {
		// noop
	}

	@Override
	public void setRateGroupAccidentBenefitByValue(String arg0) {
		// noop
	}

	@Override
	public void setRateGroupCollisionByValue(String arg0) {
		// noop
	}

	@Override
	public void setRateGroupComprehensiveByValue(String arg0) {
		// noop
	}

	@Override
	public String getRateGroupLiabilityOverride() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityOverride(String s) {

	}

	@Override
	public String getRateGroupLiabilityVicc() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityVicc(String s) {

	}

	@Override
	public String getRateGroupLiabilityScoring() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityScoring(String s) {

	}

	@Override
	public String getRateGroupLiabilityByValue() {
		return null;
	}

	@Override
	public void setRateGroupLiabilityByValue(String s) {

	}

	@Override
	public void setRateGroupLiabilityPropertyDamageByValue(String arg0) {
		// noop
	}

	@Override
	public void setVehiclePhotosReceivedDate(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public Integer getWheelbase() {
		return null;
	}

	@Override
	public void setWheelbase(Integer newWheelbase) {
		//noop
	}

	@Override
	public String getAirbags() {
		return null;
	}

	@Override
	public void setAirbags(String newAirbags) {
		//noop
	}

	@Override
	public String getAbsBrakes() {
		return null;
	}

	@Override
	public void setAbsBrakes(String newAbsBrakes) {
		//noop
	}

	@Override
	public String getAudibleAlarm() {
		return null;
	}

	@Override
	public void setAudibleAlarm(String newAudibleAlarm) {
		//noop
	}

	@Override
	public String getCutOffSystem() {
		return null;
	}

	@Override
	public void setCutOffSystem(String newCutOffSystem) {
		//noop
	}

	@Override
	public String getSecurityKeySystem() {
		return null;
	}

	@Override
	public void setSecurityKeySystem(String newSecurityKeySystem) {
		//noop
	}

	@Override
	public String getIbcApproved() {
		return null;
	}

	@Override
	public void setIbcApproved(String newIbcApproved) {
		//noop
	}

	@Override
	public String getEngineCylinder() {
		return null;
	}

	@Override
	public void setEngineCylinder(String newEngineCylinder) {
		//noop
	}

	@Override
	public String getIbcMarket() {
		return null;
	}

	@Override
	public void setIbcMarket(String newIbcMarket) {
		//noop
	}

	@Override
	public String getVehicleSize() {
		return null;
	}

	@Override
	public void setVehicleSize(String newVehicleSize) {
		//noop
	}

	@Override
	public String getVehicleGeneration() {
		return null;
	}

	@Override
	public void setVehicleGeneration(String newVehicleGeneration) {
		//noop
	}

	@Override
	public String getEngineForceInduction() {
		return null;
	}

	@Override
	public void setEngineForceInduction(String newEngineForceInduction) {
		//noop
	}

	@Override
	public String getTractionControl() {
		return null;
	}

	@Override
	public void setTractionControl(String newTractionControl) {
		//noop
	}

	@Override
	public String getStabilityControl() {
		return null;
	}

	@Override
	public void setStabilityControl(String newStabilityControl) {
		//noop
	}

	@Override
	public String getDriveTrain() {
		return null;
	}

	@Override
	public void setDriveTrain(String newDriveTrain) {
		//noop
	}

	@Override public String getBrakeAssist() {
		return null;
	}

	@Override public void setBrakeAssist(String s) {

	}

	@Override public Integer getRetailPriceWithGstDepreciated() {
		return null;
	}

	@Override public void setRetailPriceWithGstDepreciated(Integer integer) {

	}

	@Override public String getForwardCollisionMitigationLowSpeed() {
		return null;
	}

	@Override public void setForwardCollisionMitigationLowSpeed(String s) {

	}

	@Override public String getForwardCollisionMitigationAllSpeed() {
		return null;
	}

	@Override public void setForwardCollisionMitigationAllSpeed(String s) {

	}

	@Override
	public String getGoodStatus() {
		return null;
	}

	@Override
	public void setGoodStatus(String newGoodStatus) {
		//noop
	}

	@Override
	public GregorianCalendar getLocationOccupiedSince() {
		return null;
	}

	@Override
	public void setLocationOccupiedSince(GregorianCalendar newLocationOccupiedSince) {

	}

	@Override
	public String getAmphibiousVehicleInd() {
		return null;
	}

	@Override
	public void setAmphibiousVehicleInd(String newAmphibiousVehicleInd) {

	}

	@Override
	public String getMotorhomeClass() {
		return null;
	}

	@Override
	public void setMotorhomeClass(String s) {

	}

	@Override public Integer getTheftExposureScore() {
		return null;
	}

	@Override public void setTheftExposureScore(Integer integer) {

	}

	@Override public Integer getVehiclePurchasePriceDepreciated() {
		return null;
	}

	@Override public void setVehiclePurchasePriceDepreciated(Integer integer) {

	}
}
