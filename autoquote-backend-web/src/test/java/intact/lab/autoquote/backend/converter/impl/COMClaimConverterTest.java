package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.driver.ComDriverClaim;
import intact.lab.autoquote.backend.common.dto.ClaimDTO;
import intact.lab.autoquote.backend.common.enums.ClaimNatureEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class COMClaimConverterTest {

	@InjectMocks
	private COMClaimConverter converter;

	@Test
	public void testToDTO() throws Exception {
		ComDriverClaim com = new ComDriverClaim();
		com.setClaimSequence((short) 0);
		com.setClaimNatureYear("00");
		com.setClaimNature("AA");

		ClaimDTO dto = this.converter.toDTO(com);

		assertEquals("00", dto.getDateOfLoss());
		assertEquals(ClaimNatureEnum.AT_FAULT_ACCIDENT, dto.getNature());
	}

	@Test
	public void testToCOM() throws Exception {
		ClaimDTO dtoClaim = new ClaimDTO();
		dtoClaim.setNature(ClaimNatureEnum.AT_FAULT_ACCIDENT);
		dtoClaim.setDateOfLoss("00");

		ComDriverClaim comClaim = this.converter.toCOM(dtoClaim, null);

		assertEquals("AA", comClaim.getClaimNature());
		assertEquals("00", comClaim.getClaimNatureYear());
	}


}
