package org.apache.commons.lang;

/**
 * Bridge class to provide compatibility with commons-lang NotImplementedException
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 NotImplementedException to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class NotImplementedException extends org.apache.commons.lang3.NotImplementedException {

    private static final long serialVersionUID = 1L;

    /**
     * Constructs a new NotImplementedException with no detail message.
     */
    public NotImplementedException() {
        super();
    }

    /**
     * Constructs a new NotImplementedException with the specified detail message.
     *
     * @param message the detail message
     */
    public NotImplementedException(String message) {
        super(message);
    }

    /**
     * Constructs a new NotImplementedException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause   the cause
     */
    public NotImplementedException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new NotImplementedException with the specified cause.
     *
     * @param cause the cause
     */
    public NotImplementedException(Throwable cause) {
        super(cause);
    }
}
