package org.apache.commons.lang.time;

import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.Calendar;

/**
 * Bridge class to provide compatibility with commons-lang DateFormatUtils
 * while using commons-lang3 internally.
 * 
 * This class delegates all method calls to the commons-lang3 DateFormatUtils
 * to maintain compatibility with legacy code that expects the old commons-lang package.
 */
public class DateFormatUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private DateFormatUtils() {
        // Utility class
    }

    // Common date format patterns - delegate to commons-lang3 constants
    public static final String ISO_DATETIME_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern();
    public static final String ISO_DATETIME_TIME_ZONE_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.getPattern();
    public static final String ISO_DATE_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.getPattern();
    public static final String ISO_DATE_TIME_ZONE_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.getPattern();
    public static final String ISO_TIME_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_TIME_FORMAT.getPattern();
    public static final String ISO_TIME_TIME_ZONE_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_TIME_TIME_ZONE_FORMAT.getPattern();
    public static final String ISO_TIME_NO_T_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_TIME_FORMAT.getPattern();
    public static final String ISO_TIME_NO_T_TIME_ZONE_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_TIME_TIME_ZONE_FORMAT.getPattern();
    public static final String SMTP_DATETIME_FORMAT = org.apache.commons.lang3.time.DateFormatUtils.SMTP_DATETIME_FORMAT.getPattern();

    // Delegate all commonly used methods to commons-lang3

    public static String formatUTC(long millis, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(millis, pattern);
    }

    public static String formatUTC(Date date, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(date, pattern);
    }

    public static String formatUTC(long millis, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(millis, pattern, locale);
    }

    public static String formatUTC(Date date, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(date, pattern, locale);
    }

    public static String format(long millis, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(millis, pattern);
    }

    public static String format(Date date, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, pattern);
    }

    public static String format(Calendar calendar, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(calendar, pattern);
    }

    public static String format(long millis, String pattern, TimeZone timeZone) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(millis, pattern, timeZone);
    }

    public static String format(Date date, String pattern, TimeZone timeZone) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, pattern, timeZone);
    }

    public static String format(Calendar calendar, String pattern, TimeZone timeZone) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(calendar, pattern, timeZone);
    }

    public static String format(long millis, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(millis, pattern, locale);
    }

    public static String format(Date date, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, pattern, locale);
    }

    public static String format(Calendar calendar, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(calendar, pattern, locale);
    }

    public static String format(long millis, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(millis, pattern, timeZone, locale);
    }

    public static String format(Date date, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, pattern, timeZone, locale);
    }

    public static String format(Calendar calendar, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(calendar, pattern, timeZone, locale);
    }

    // ISO format methods
    public static String formatISO(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(date);
    }

    public static String formatISO(long millis) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(millis);
    }

    public static String formatISO(Calendar calendar) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(calendar);
    }

    // SMTP format methods
    public static String formatSMTP(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.SMTP_DATETIME_FORMAT.format(date);
    }

    public static String formatSMTP(long millis) {
        return org.apache.commons.lang3.time.DateFormatUtils.SMTP_DATETIME_FORMAT.format(millis);
    }

    public static String formatSMTP(Calendar calendar) {
        return org.apache.commons.lang3.time.DateFormatUtils.SMTP_DATETIME_FORMAT.format(calendar);
    }

    // Additional utility methods for backward compatibility
    public static String formatUTC(Date date, String pattern, String timeZone) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(date, pattern);
    }

    public static String formatUTC(long millis, String pattern, String timeZone) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(millis, pattern);
    }

    // Common date formatting shortcuts
    public static String formatDate(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, "yyyy-MM-dd");
    }

    public static String formatTime(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, "HH:mm:ss");
    }

    public static String formatDateTime(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static String formatDateTimeISO(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.format(date);
    }

    public static String formatDateISO(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(date);
    }

    public static String formatTimeISO(Date date) {
        return org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_TIME_FORMAT.format(date);
    }

    // Backward compatibility methods that might have been used in commons-lang
    public static String formatUTC(Date date, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(date, pattern, locale);
    }

    public static String formatUTC(long millis, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(millis, pattern, locale);
    }

    public static String formatUTC(Calendar calendar, String pattern) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(calendar.getTime(), pattern);
    }

    public static String formatUTC(Calendar calendar, String pattern, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(calendar.getTime(), pattern, locale);
    }

    public static String formatUTC(Calendar calendar, String pattern, TimeZone timeZone, Locale locale) {
        return org.apache.commons.lang3.time.DateFormatUtils.formatUTC(calendar.getTime(), pattern, locale);
    }
}
