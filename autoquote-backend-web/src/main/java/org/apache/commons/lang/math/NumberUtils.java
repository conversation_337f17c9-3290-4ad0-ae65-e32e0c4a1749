package org.apache.commons.lang.math;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * Bridge class to provide compatibility with commons-lang NumberUtils
 * while using commons-lang3 internally.
 * 
 * This class delegates all method calls to the commons-lang3 NumberUtils
 * to maintain compatibility with legacy code that expects the old commons-lang package.
 */
public class NumberUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private NumberUtils() {
        // Utility class
    }

    // Delegate all commonly used methods to commons-lang3

    public static int toInt(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toInt(str);
    }

    public static int toInt(String str, int defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toInt(str, defaultValue);
    }

    public static long toLong(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toLong(str);
    }

    public static long toLong(String str, long defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toLong(str, defaultValue);
    }

    public static float toFloat(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toFloat(str);
    }

    public static float toFloat(String str, float defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toFloat(str, defaultValue);
    }

    public static double toDouble(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toDouble(str);
    }

    public static double toDouble(String str, double defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toDouble(str, defaultValue);
    }

    public static byte toByte(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toByte(str);
    }

    public static byte toByte(String str, byte defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toByte(str, defaultValue);
    }

    public static short toShort(String str) {
        return org.apache.commons.lang3.math.NumberUtils.toShort(str);
    }

    public static short toShort(String str, short defaultValue) {
        return org.apache.commons.lang3.math.NumberUtils.toShort(str, defaultValue);
    }

    public static Number createNumber(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createNumber(str);
    }

    public static Float createFloat(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createFloat(str);
    }

    public static Double createDouble(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createDouble(str);
    }

    public static Integer createInteger(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createInteger(str);
    }

    public static Long createLong(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createLong(str);
    }

    public static BigInteger createBigInteger(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createBigInteger(str);
    }

    public static BigDecimal createBigDecimal(String str) {
        return org.apache.commons.lang3.math.NumberUtils.createBigDecimal(str);
    }

    public static long min(long[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static int min(int[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static short min(short[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static byte min(byte[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static double min(double[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static float min(float[] array) {
        return org.apache.commons.lang3.math.NumberUtils.min(array);
    }

    public static long max(long[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static int max(int[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static short max(short[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static byte max(byte[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static double max(double[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static float max(float[] array) {
        return org.apache.commons.lang3.math.NumberUtils.max(array);
    }

    public static long min(long a, long b, long c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static int min(int a, int b, int c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static short min(short a, short b, short c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static byte min(byte a, byte b, byte c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static double min(double a, double b, double c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static float min(float a, float b, float c) {
        return org.apache.commons.lang3.math.NumberUtils.min(a, b, c);
    }

    public static long max(long a, long b, long c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static int max(int a, int b, int c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static short max(short a, short b, short c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static byte max(byte a, byte b, byte c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static double max(double a, double b, double c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static float max(float a, float b, float c) {
        return org.apache.commons.lang3.math.NumberUtils.max(a, b, c);
    }

    public static boolean isDigits(String str) {
        return org.apache.commons.lang3.math.NumberUtils.isDigits(str);
    }

    public static boolean isNumber(String str) {
        return org.apache.commons.lang3.math.NumberUtils.isNumber(str);
    }

    public static boolean isParsable(String str) {
        return org.apache.commons.lang3.math.NumberUtils.isParsable(str);
    }

    public static int compare(int x, int y) {
        return org.apache.commons.lang3.math.NumberUtils.compare(x, y);
    }

    public static int compare(long x, long y) {
        return org.apache.commons.lang3.math.NumberUtils.compare(x, y);
    }

    public static int compare(short x, short y) {
        return org.apache.commons.lang3.math.NumberUtils.compare(x, y);
    }

    public static int compare(byte x, byte y) {
        return org.apache.commons.lang3.math.NumberUtils.compare(x, y);
    }

    public static int compare(double x, double y) {
        return Double.compare(x, y);
    }

    public static int compare(float x, float y) {
        return Float.compare(x, y);
    }

    // Constants from commons-lang3
    public static final Long LONG_ZERO = org.apache.commons.lang3.math.NumberUtils.LONG_ZERO;
    public static final Long LONG_ONE = org.apache.commons.lang3.math.NumberUtils.LONG_ONE;
    public static final Long LONG_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.LONG_MINUS_ONE;
    public static final Integer INTEGER_ZERO = org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO;
    public static final Integer INTEGER_ONE = org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE;
    public static final Integer INTEGER_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.INTEGER_MINUS_ONE;
    public static final Short SHORT_ZERO = org.apache.commons.lang3.math.NumberUtils.SHORT_ZERO;
    public static final Short SHORT_ONE = org.apache.commons.lang3.math.NumberUtils.SHORT_ONE;
    public static final Short SHORT_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.SHORT_MINUS_ONE;
    public static final Byte BYTE_ZERO = org.apache.commons.lang3.math.NumberUtils.BYTE_ZERO;
    public static final Byte BYTE_ONE = org.apache.commons.lang3.math.NumberUtils.BYTE_ONE;
    public static final Byte BYTE_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.BYTE_MINUS_ONE;
    public static final Double DOUBLE_ZERO = org.apache.commons.lang3.math.NumberUtils.DOUBLE_ZERO;
    public static final Double DOUBLE_ONE = org.apache.commons.lang3.math.NumberUtils.DOUBLE_ONE;
    public static final Double DOUBLE_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.DOUBLE_MINUS_ONE;
    public static final Float FLOAT_ZERO = org.apache.commons.lang3.math.NumberUtils.FLOAT_ZERO;
    public static final Float FLOAT_ONE = org.apache.commons.lang3.math.NumberUtils.FLOAT_ONE;
    public static final Float FLOAT_MINUS_ONE = org.apache.commons.lang3.math.NumberUtils.FLOAT_MINUS_ONE;
}
