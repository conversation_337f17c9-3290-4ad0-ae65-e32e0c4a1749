package org.apache.commons.lang;

/**
 * Bridge class to provide compatibility with commons-lang ObjectUtils
 * while using commons-lang3 internally.
 * 
 * This class delegates all method calls to the commons-lang3 ObjectUtils
 * to maintain compatibility with legacy code that expects the old commons-lang package.
 */
public class ObjectUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private ObjectUtils() {
        // Utility class
    }

    // Delegate all commonly used methods to commons-lang3

    public static String toString(Object obj) {
        return org.apache.commons.lang3.ObjectUtils.toString(obj);
    }

    public static String toString(Object obj, String nullStr) {
        return org.apache.commons.lang3.ObjectUtils.toString(obj, nullStr);
    }

    public static <T> T defaultIfNull(T object, T defaultValue) {
        return org.apache.commons.lang3.ObjectUtils.defaultIfNull(object, defaultValue);
    }

    public static boolean equals(Object object1, Object object2) {
        return org.apache.commons.lang3.ObjectUtils.equals(object1, object2);
    }

    public static boolean notEqual(Object object1, Object object2) {
        return org.apache.commons.lang3.ObjectUtils.notEqual(object1, object2);
    }

    public static int hashCode(Object obj) {
        return org.apache.commons.lang3.ObjectUtils.hashCode(obj);
    }

    public static int hashCodeMulti(Object... objects) {
        return org.apache.commons.lang3.ObjectUtils.hashCodeMulti(objects);
    }

    public static String identityToString(Object object) {
        return org.apache.commons.lang3.ObjectUtils.identityToString(object);
    }

    public static void identityToString(StringBuffer buffer, Object object) {
        org.apache.commons.lang3.ObjectUtils.identityToString(buffer, object);
    }

    public static void identityToString(StringBuilder builder, Object object) {
        org.apache.commons.lang3.ObjectUtils.identityToString(builder, object);
    }

    public static <T> T clone(T obj) {
        return org.apache.commons.lang3.ObjectUtils.clone(obj);
    }

    public static <T> T cloneIfPossible(T obj) {
        return org.apache.commons.lang3.ObjectUtils.cloneIfPossible(obj);
    }

    public static boolean isEmpty(Object object) {
        return org.apache.commons.lang3.ObjectUtils.isEmpty(object);
    }

    public static boolean isNotEmpty(Object object) {
        return org.apache.commons.lang3.ObjectUtils.isNotEmpty(object);
    }

    @SuppressWarnings("unchecked")
    public static <T> T firstNonNull(T... values) {
        return org.apache.commons.lang3.ObjectUtils.firstNonNull(values);
    }

    public static boolean anyNotNull(Object... values) {
        return org.apache.commons.lang3.ObjectUtils.anyNotNull(values);
    }

    public static boolean allNotNull(Object... values) {
        return org.apache.commons.lang3.ObjectUtils.allNotNull(values);
    }

    public static boolean anyNull(Object... values) {
        return org.apache.commons.lang3.ObjectUtils.anyNull(values);
    }

    public static boolean allNull(Object... values) {
        return org.apache.commons.lang3.ObjectUtils.allNull(values);
    }

    @SuppressWarnings("unchecked")
    public static <T extends Comparable<? super T>> T min(T... values) {
        return org.apache.commons.lang3.ObjectUtils.min(values);
    }

    @SuppressWarnings("unchecked")
    public static <T extends Comparable<? super T>> T max(T... values) {
        return org.apache.commons.lang3.ObjectUtils.max(values);
    }

    @SuppressWarnings("unchecked")
    public static <T extends Comparable<? super T>> int compare(T c1, T c2) {
        return org.apache.commons.lang3.ObjectUtils.compare(c1, c2);
    }

    @SuppressWarnings("unchecked")
    public static <T extends Comparable<? super T>> int compare(T c1, T c2, boolean nullGreater) {
        return org.apache.commons.lang3.ObjectUtils.compare(c1, c2, nullGreater);
    }

    @SuppressWarnings("unchecked")
    public static <T extends Comparable<? super T>> T median(T... items) {
        return org.apache.commons.lang3.ObjectUtils.median(items);
    }

    @SuppressWarnings("unchecked")
    public static <T> T mode(T... items) {
        return org.apache.commons.lang3.ObjectUtils.mode(items);
    }

    // Constants from commons-lang3
    public static final Object NULL = org.apache.commons.lang3.ObjectUtils.NULL;

    // Additional utility methods for backward compatibility

    /**
     * Gets the toString that would be produced by Object if a class did not override toString itself.
     *
     * @param object the Object to create a toString for
     * @return the default toString text, or null if null passed in
     */
    public static String getToString(Object object) {
        if (object == null) {
            return null;
        }
        return object.getClass().getName() + "@" + Integer.toHexString(System.identityHashCode(object));
    }

    /**
     * Null safe comparison of Comparables.
     *
     * @param c1 the first comparable, may be null
     * @param c2 the second comparable, may be null
     * @return a negative value if c1 < c2, zero if c1 = c2 and a positive value if c1 > c2
     */
    public static <T extends Comparable<? super T>> int nullSafeCompare(T c1, T c2) {
        return compare(c1, c2);
    }

    /**
     * Checks if the object is null.
     *
     * @param object the Object to test, may be null
     * @return true if the object is null
     */
    public static boolean isNull(Object object) {
        return object == null;
    }

    /**
     * Checks if the object is not null.
     *
     * @param object the Object to test, may be null
     * @return true if the object is not null
     */
    public static boolean isNotNull(Object object) {
        return object != null;
    }

    // Note: defaultIfNull method with Object parameters removed to avoid name clash
    // with generic version. Use the generic version: defaultIfNull(T object, T defaultValue)

    /**
     * Compares two objects for equality, where either one or both objects may be null.
     *
     * @param object1 the first object, may be null
     * @param object2 the second object, may be null
     * @return true if the values of both objects are the same
     */
    public static boolean nullSafeEquals(Object object1, Object object2) {
        return equals(object1, object2);
    }

    /**
     * Gets the hash code of an object returning zero when the object is null.
     *
     * @param obj the object to obtain the hash code of, may be null
     * @return the hash code of the object, or zero if null
     */
    public static int nullSafeHashCode(Object obj) {
        return hashCode(obj);
    }

    /**
     * Gets the toString of an Object returning an empty string ("") if null input.
     *
     * @param obj the Object to toString, may be null
     * @return the passed in Object's toString, or nullStr if null input
     */
    public static String nullSafeToString(Object obj) {
        return toString(obj, "");
    }

    /**
     * Gets the toString of an Object returning a specified text if null input.
     *
     * @param obj the Object to toString, may be null
     * @param nullStr the String to return if null input, may be null
     * @return the passed in Object's toString, or nullStr if null input
     */
    public static String nullSafeToString(Object obj, String nullStr) {
        return toString(obj, nullStr);
    }
}
