package org.apache.commons.lang;

/**
 * Bridge class to provide compatibility with commons-lang BooleanUtils
 * while using commons-lang3 internally.
 * 
 * This class delegates all method calls to the commons-lang3 BooleanUtils
 * to maintain compatibility with legacy code that expects the old commons-lang package.
 */
public class BooleanUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private BooleanUtils() {
        // Utility class
    }

    // Delegate all commonly used methods to commons-lang3

    public static boolean isTrue(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.isTrue(bool);
    }

    public static boolean isNotTrue(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.isNotTrue(bool);
    }

    public static boolean isFalse(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.isFalse(bool);
    }

    public static boolean isNotFalse(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.isNotFalse(bool);
    }

    public static Boolean toBooleanObject(String str) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(str);
    }

    public static Boolean toBooleanObject(String str, String trueString, String falseString, String nullString) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(str, trueString, falseString, nullString);
    }

    public static boolean toBoolean(String str) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(str);
    }

    public static boolean toBoolean(String str, String trueString, String falseString) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(str, trueString, falseString);
    }

    public static Boolean toBooleanObject(int value) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(value);
    }

    public static Boolean toBooleanObject(Integer value) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(value);
    }

    public static Boolean toBooleanObject(int value, int trueValue, int falseValue, int nullValue) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(value, trueValue, falseValue, nullValue);
    }

    public static Boolean toBooleanObject(Integer value, Integer trueValue, Integer falseValue, Integer nullValue) {
        return org.apache.commons.lang3.BooleanUtils.toBooleanObject(value, trueValue, falseValue, nullValue);
    }

    public static boolean toBoolean(int value) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(value);
    }

    public static boolean toBoolean(Integer value) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(value);
    }

    public static boolean toBoolean(int value, int trueValue, int falseValue) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(value, trueValue, falseValue);
    }

    public static boolean toBoolean(Integer value, Integer trueValue, Integer falseValue) {
        return org.apache.commons.lang3.BooleanUtils.toBoolean(value, trueValue, falseValue);
    }

    public static int toInteger(boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toInteger(bool);
    }

    public static Integer toIntegerObject(boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toIntegerObject(bool);
    }

    public static Integer toIntegerObject(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toIntegerObject(bool);
    }

    public static int toInteger(boolean bool, int trueValue, int falseValue) {
        return org.apache.commons.lang3.BooleanUtils.toInteger(bool, trueValue, falseValue);
    }

    public static int toInteger(Boolean bool, int trueValue, int falseValue, int nullValue) {
        return org.apache.commons.lang3.BooleanUtils.toInteger(bool, trueValue, falseValue, nullValue);
    }

    public static Integer toIntegerObject(boolean bool, Integer trueValue, Integer falseValue) {
        return org.apache.commons.lang3.BooleanUtils.toIntegerObject(bool, trueValue, falseValue);
    }

    public static Integer toIntegerObject(Boolean bool, Integer trueValue, Integer falseValue, Integer nullValue) {
        return org.apache.commons.lang3.BooleanUtils.toIntegerObject(bool, trueValue, falseValue, nullValue);
    }

    public static String toString(boolean bool, String trueString, String falseString) {
        return org.apache.commons.lang3.BooleanUtils.toString(bool, trueString, falseString);
    }

    public static String toString(Boolean bool, String trueString, String falseString, String nullString) {
        return org.apache.commons.lang3.BooleanUtils.toString(bool, trueString, falseString, nullString);
    }

    public static String toStringTrueFalse(boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringTrueFalse(bool);
    }

    public static String toStringTrueFalse(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringTrueFalse(bool);
    }

    public static String toStringOnOff(boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringOnOff(bool);
    }

    public static String toStringOnOff(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringOnOff(bool);
    }

    public static String toStringYesNo(boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringYesNo(bool);
    }

    public static String toStringYesNo(Boolean bool) {
        return org.apache.commons.lang3.BooleanUtils.toStringYesNo(bool);
    }

    public static boolean and(boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.and(array);
    }

    public static Boolean and(Boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.and(array);
    }

    public static boolean or(boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.or(array);
    }

    public static Boolean or(Boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.or(array);
    }

    public static boolean xor(boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.xor(array);
    }

    public static Boolean xor(Boolean... array) {
        return org.apache.commons.lang3.BooleanUtils.xor(array);
    }

    public static int compare(boolean x, boolean y) {
        return org.apache.commons.lang3.BooleanUtils.compare(x, y);
    }
}
