package org.apache.commons.lang.builder;

/**
 * Bridge class to provide compatibility with commons-lang HashCodeBuilder
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 HashCodeBuilder to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class Hash<PERSON>odeBuilder extends org.apache.commons.lang3.builder.HashCodeBuilder {

    /**
     * Constructs a HashCodeBuilder with default initial value and multiplier.
     */
    public HashCodeBuilder() {
        super();
    }

    /**
     * Constructs a HashCodeBuilder with the specified initial value and multiplier.
     *
     * @param initialNonZeroOddNumber a non-zero, odd number used as the initial value
     * @param multiplierNonZeroOddNumber a non-zero, odd number used as the multiplier
     */
    public HashCodeBuilder(int initialNonZeroOddNumber, int multiplierNonZeroOddNumber) {
        super(initialNonZeroOddNumber, multiplierNonZeroOddNumber);
    }

    // Override methods to return the bridge type for method chaining

    @Override
    public HashCodeBuilder append(boolean value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(boolean[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(byte value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(byte[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(char value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(char[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(double value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(double[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(float value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(float[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(int value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(int[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(long value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(long[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(Object object) {
        super.append(object);
        return this;
    }

    @Override
    public HashCodeBuilder append(Object[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder append(short value) {
        super.append(value);
        return this;
    }

    @Override
    public HashCodeBuilder append(short[] array) {
        super.append(array);
        return this;
    }

    @Override
    public HashCodeBuilder appendSuper(int superHashCode) {
        super.appendSuper(superHashCode);
        return this;
    }

    // Static utility methods for convenience

    /**
     * Uses reflection to build a valid hash code from the fields of object.
     *
     * @param object the Object to create a hash code for
     * @return hash code
     */
    public static int reflectionHashCode(Object object) {
        return org.apache.commons.lang3.builder.HashCodeBuilder.reflectionHashCode(object);
    }

    /**
     * Uses reflection to build a valid hash code from the fields of object.
     *
     * @param object the Object to create a hash code for
     * @param testTransients whether to include transient fields
     * @return hash code
     */
    public static int reflectionHashCode(Object object, boolean testTransients) {
        return org.apache.commons.lang3.builder.HashCodeBuilder.reflectionHashCode(object, testTransients);
    }

    /**
     * Uses reflection to build a valid hash code from the fields of object.
     *
     * @param initialNonZeroOddNumber a non-zero, odd number used as the initial value
     * @param multiplierNonZeroOddNumber a non-zero, odd number used as the multiplier
     * @param object the Object to create a hash code for
     * @return hash code
     */
    public static int reflectionHashCode(int initialNonZeroOddNumber, int multiplierNonZeroOddNumber, Object object) {
        return org.apache.commons.lang3.builder.HashCodeBuilder.reflectionHashCode(initialNonZeroOddNumber, multiplierNonZeroOddNumber, object);
    }

    /**
     * Uses reflection to build a valid hash code from the fields of object.
     *
     * @param initialNonZeroOddNumber a non-zero, odd number used as the initial value
     * @param multiplierNonZeroOddNumber a non-zero, odd number used as the multiplier
     * @param object the Object to create a hash code for
     * @param testTransients whether to include transient fields
     * @return hash code
     */
    public static int reflectionHashCode(int initialNonZeroOddNumber, int multiplierNonZeroOddNumber, Object object, boolean testTransients) {
        return org.apache.commons.lang3.builder.HashCodeBuilder.reflectionHashCode(initialNonZeroOddNumber, multiplierNonZeroOddNumber, object, testTransients);
    }

    /**
     * Uses reflection to build a valid hash code from the fields of object.
     *
     * @param initialNonZeroOddNumber a non-zero, odd number used as the initial value
     * @param multiplierNonZeroOddNumber a non-zero, odd number used as the multiplier
     * @param object the Object to create a hash code for
     * @param testTransients whether to include transient fields
     * @param reflectUpToClass the superclass to reflect up to (inclusive), may be null
     * @return hash code
     */
    @SuppressWarnings("unchecked")
    public static int reflectionHashCode(int initialNonZeroOddNumber, int multiplierNonZeroOddNumber, Object object, boolean testTransients, Class<?> reflectUpToClass) {
        return org.apache.commons.lang3.builder.HashCodeBuilder.reflectionHashCode(initialNonZeroOddNumber, multiplierNonZeroOddNumber, object, testTransients, (Class) reflectUpToClass);
    }

    // Note: The method with excludeFields parameter is omitted to avoid method signature clash
    // with the generic method in commons-lang3. The functionality is still available through
    // the parent class methods.
}
