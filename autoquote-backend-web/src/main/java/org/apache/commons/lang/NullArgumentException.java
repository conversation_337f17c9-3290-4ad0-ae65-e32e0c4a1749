package org.apache.commons.lang;

/**
 * Bridge class to provide compatibility with commons-lang NullArgumentException
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 NullArgumentException to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 * 
 * Note: In commons-lang3, NullArgumentException was replaced with IllegalArgumentException
 * in most cases, but we maintain the original class for backward compatibility.
 */
public class NullArgumentException extends IllegalArgumentException {

    private static final long serialVersionUID = 1L;

    /**
     * Constructs a new NullArgumentException with no detail message.
     */
    public NullArgumentException() {
        super("Argument cannot be null");
    }

    /**
     * Constructs a new NullArgumentException with the specified detail message.
     *
     * @param message the detail message
     */
    public NullArgumentException(String message) {
        super(message != null ? message : "Argument cannot be null");
    }

    /**
     * Constructs a new NullArgumentException with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause   the cause
     */
    public NullArgumentException(String message, Throwable cause) {
        super(message != null ? message : "Argument cannot be null", cause);
    }

    /**
     * Constructs a new NullArgumentException with the specified cause.
     *
     * @param cause the cause
     */
    public NullArgumentException(Throwable cause) {
        super("Argument cannot be null", cause);
    }
}
