package org.apache.commons.lang.time;

import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.Locale;
import java.util.TimeZone;

/**
 * Bridge class to provide compatibility with commons-lang DateUtils
 * while using commons-lang3 internally.
 * 
 * This class delegates all method calls to the commons-lang3 DateUtils
 * to maintain compatibility with legacy code that expects the old commons-lang package.
 */
public class DateUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private DateUtils() {
        // Utility class
    }

    // Calendar field constants - delegate to commons-lang3
    public static final int MILLIS_PER_SECOND = (int) org.apache.commons.lang3.time.DateUtils.MILLIS_PER_SECOND;
    public static final int MILLIS_PER_MINUTE = (int) org.apache.commons.lang3.time.DateUtils.MILLIS_PER_MINUTE;
    public static final int MILLIS_PER_HOUR = (int) org.apache.commons.lang3.time.DateUtils.MILLIS_PER_HOUR;
    public static final int MILLIS_PER_DAY = (int) org.apache.commons.lang3.time.DateUtils.MILLIS_PER_DAY;

    // Range constants
    public static final int RANGE_WEEK_SUNDAY = org.apache.commons.lang3.time.DateUtils.RANGE_WEEK_SUNDAY;
    public static final int RANGE_WEEK_MONDAY = org.apache.commons.lang3.time.DateUtils.RANGE_WEEK_MONDAY;
    public static final int RANGE_WEEK_RELATIVE = org.apache.commons.lang3.time.DateUtils.RANGE_WEEK_RELATIVE;
    public static final int RANGE_WEEK_CENTER = org.apache.commons.lang3.time.DateUtils.RANGE_WEEK_CENTER;
    public static final int RANGE_MONTH_SUNDAY = org.apache.commons.lang3.time.DateUtils.RANGE_MONTH_SUNDAY;
    public static final int RANGE_MONTH_MONDAY = org.apache.commons.lang3.time.DateUtils.RANGE_MONTH_MONDAY;

    // Delegate all commonly used methods to commons-lang3

    public static boolean isSameDay(Date date1, Date date2) {
        return org.apache.commons.lang3.time.DateUtils.isSameDay(date1, date2);
    }

    public static boolean isSameDay(Calendar cal1, Calendar cal2) {
        return org.apache.commons.lang3.time.DateUtils.isSameDay(cal1, cal2);
    }

    public static boolean isSameInstant(Date date1, Date date2) {
        return org.apache.commons.lang3.time.DateUtils.isSameInstant(date1, date2);
    }

    public static boolean isSameInstant(Calendar cal1, Calendar cal2) {
        return org.apache.commons.lang3.time.DateUtils.isSameInstant(cal1, cal2);
    }

    public static boolean isSameLocalTime(Calendar cal1, Calendar cal2) {
        return org.apache.commons.lang3.time.DateUtils.isSameLocalTime(cal1, cal2);
    }

    public static Date parseDate(String str, String[] parsePatterns) throws java.text.ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDate(str, parsePatterns);
    }

    public static Date parseDate(String str, Locale locale, String[] parsePatterns) throws java.text.ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDate(str, locale, parsePatterns);
    }

    public static Date parseDateStrictly(String str, String[] parsePatterns) throws java.text.ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(str, parsePatterns);
    }

    public static Date parseDateStrictly(String str, Locale locale, String[] parsePatterns) throws java.text.ParseException {
        return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(str, locale, parsePatterns);
    }

    public static Date addYears(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addYears(date, amount);
    }

    public static Date addMonths(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addMonths(date, amount);
    }

    public static Date addWeeks(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addWeeks(date, amount);
    }

    public static Date addDays(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addDays(date, amount);
    }

    public static Date addHours(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addHours(date, amount);
    }

    public static Date addMinutes(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addMinutes(date, amount);
    }

    public static Date addSeconds(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addSeconds(date, amount);
    }

    public static Date addMilliseconds(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.addMilliseconds(date, amount);
    }

    public static Date setYears(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setYears(date, amount);
    }

    public static Date setMonths(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setMonths(date, amount);
    }

    public static Date setDays(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setDays(date, amount);
    }

    public static Date setHours(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setHours(date, amount);
    }

    public static Date setMinutes(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setMinutes(date, amount);
    }

    public static Date setSeconds(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setSeconds(date, amount);
    }

    public static Date setMilliseconds(Date date, int amount) {
        return org.apache.commons.lang3.time.DateUtils.setMilliseconds(date, amount);
    }

    public static Calendar toCalendar(Date date) {
        return org.apache.commons.lang3.time.DateUtils.toCalendar(date);
    }

    public static Calendar toCalendar(Date date, TimeZone tz) {
        return org.apache.commons.lang3.time.DateUtils.toCalendar(date, tz);
    }

    public static Date round(Date date, int field) {
        return org.apache.commons.lang3.time.DateUtils.round(date, field);
    }

    public static Calendar round(Calendar date, int field) {
        return org.apache.commons.lang3.time.DateUtils.round(date, field);
    }

    public static Date round(Object date, int field) {
        return org.apache.commons.lang3.time.DateUtils.round(date, field);
    }

    public static Date truncate(Date date, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncate(date, field);
    }

    public static Calendar truncate(Calendar date, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncate(date, field);
    }

    public static Date truncate(Object date, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncate(date, field);
    }

    public static Date ceiling(Date date, int field) {
        return org.apache.commons.lang3.time.DateUtils.ceiling(date, field);
    }

    public static Calendar ceiling(Calendar date, int field) {
        return org.apache.commons.lang3.time.DateUtils.ceiling(date, field);
    }

    public static Date ceiling(Object date, int field) {
        return org.apache.commons.lang3.time.DateUtils.ceiling(date, field);
    }

    @SuppressWarnings("unchecked")
    public static Iterator<Calendar> iterator(Date focus, int rangeStyle) {
        return (Iterator<Calendar>) org.apache.commons.lang3.time.DateUtils.iterator(focus, rangeStyle);
    }

    @SuppressWarnings("unchecked")
    public static Iterator<Calendar> iterator(Calendar focus, int rangeStyle) {
        return (Iterator<Calendar>) org.apache.commons.lang3.time.DateUtils.iterator(focus, rangeStyle);
    }

    @SuppressWarnings("unchecked")
    public static Iterator<Calendar> iterator(Object focus, int rangeStyle) {
        return (Iterator<Calendar>) org.apache.commons.lang3.time.DateUtils.iterator(focus, rangeStyle);
    }

    public static long getFragmentInMilliseconds(Date date, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInMilliseconds(date, fragment);
    }

    public static long getFragmentInSeconds(Date date, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInSeconds(date, fragment);
    }

    public static long getFragmentInMinutes(Date date, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInMinutes(date, fragment);
    }

    public static long getFragmentInHours(Date date, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInHours(date, fragment);
    }

    public static long getFragmentInDays(Date date, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInDays(date, fragment);
    }

    public static long getFragmentInMilliseconds(Calendar calendar, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInMilliseconds(calendar, fragment);
    }

    public static long getFragmentInSeconds(Calendar calendar, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInSeconds(calendar, fragment);
    }

    public static long getFragmentInMinutes(Calendar calendar, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInMinutes(calendar, fragment);
    }

    public static long getFragmentInHours(Calendar calendar, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInHours(calendar, fragment);
    }

    public static long getFragmentInDays(Calendar calendar, int fragment) {
        return org.apache.commons.lang3.time.DateUtils.getFragmentInDays(calendar, fragment);
    }

    public static boolean truncatedEquals(Calendar cal1, Calendar cal2, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncatedEquals(cal1, cal2, field);
    }

    public static boolean truncatedEquals(Date date1, Date date2, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncatedEquals(date1, date2, field);
    }

    public static int truncatedCompareTo(Calendar cal1, Calendar cal2, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncatedCompareTo(cal1, cal2, field);
    }

    public static int truncatedCompareTo(Date date1, Date date2, int field) {
        return org.apache.commons.lang3.time.DateUtils.truncatedCompareTo(date1, date2, field);
    }
}
