package org.apache.commons.lang.time;

/**
 * Bridge class to provide compatibility with commons-lang StopWatch
 * while using commons-lang3 internally.
 *
 * This class delegates to the commons-lang3 StopWatch to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class StopWatch {

    private final org.apache.commons.lang3.time.StopWatch delegate;

    /**
     * Constructs a new StopWatch.
     */
    public StopWatch() {
        this.delegate = new org.apache.commons.lang3.time.StopWatch();
    }

    /**
     * Constructs a new StopWatch with a message.
     *
     * @param message the message for the StopWatch
     */
    public StopWatch(String message) {
        this.delegate = new org.apache.commons.lang3.time.StopWatch(message);
    }

    // Delegate methods matching original commons-lang API (void return types)

    public void start() {
        delegate.start();
    }

    public void stop() {
        delegate.stop();
    }

    public void reset() {
        delegate.reset();
    }

    public void split() {
        delegate.split();
    }

    public void unsplit() {
        delegate.unsplit();
    }

    public void suspend() {
        delegate.suspend();
    }

    public void resume() {
        delegate.resume();
    }

    // Static factory methods for convenience (matching commons-lang behavior)

    /**
     * Creates a started StopWatch.
     *
     * @return a started StopWatch
     */
    public static StopWatch createStarted() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        return stopWatch;
    }

    /**
     * Creates a started StopWatch with a message.
     *
     * @param message the message for the StopWatch
     * @return a started StopWatch
     */
    public static StopWatch createStarted(String message) {
        StopWatch stopWatch = new StopWatch(message);
        stopWatch.start();
        return stopWatch;
    }

    // Additional utility methods for backward compatibility

    /**
     * Gets the time this stopwatch was started.
     *
     * @return the start time in milliseconds
     */
    public long getStartTime() {
        return delegate.getStartTime();
    }

    /**
     * Gets the time on the stopwatch.
     *
     * @return the time in milliseconds
     */
    public long getTime() {
        return delegate.getTime();
    }

    /**
     * Gets the time on the stopwatch in nanoseconds.
     *
     * @return the time in nanoseconds
     */
    public long getNanoTime() {
        return delegate.getNanoTime();
    }

    /**
     * Gets the split time on the stopwatch.
     *
     * @return the split time in milliseconds
     */
    public long getSplitTime() {
        return delegate.getSplitTime();
    }

    /**
     * Gets the split time on the stopwatch in nanoseconds.
     *
     * @return the split time in nanoseconds
     */
    public long getSplitNanoTime() {
        return delegate.getSplitNanoTime();
    }

    /**
     * Returns whether the StopWatch is started.
     *
     * @return true if the StopWatch is started
     */
    public boolean isStarted() {
        return delegate.isStarted();
    }

    /**
     * Returns whether the StopWatch is stopped.
     *
     * @return true if the StopWatch is stopped
     */
    public boolean isStopped() {
        return delegate.isStopped();
    }

    /**
     * Returns whether the StopWatch is suspended.
     *
     * @return true if the StopWatch is suspended
     */
    public boolean isSuspended() {
        return delegate.isSuspended();
    }

    /**
     * Returns whether a split time has been taken.
     *
     * @return true if a split time has been taken
     */
    public boolean isSplit() {
        // commons-lang3 doesn't have isSplit(), so we'll implement a simple check
        try {
            delegate.getSplitTime();
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }

    /**
     * Gets a summary of the time that the stopwatch recorded as a string.
     *
     * @return the time as a string
     */
    @Override
    public String toString() {
        return delegate.toString();
    }

    /**
     * Gets a summary of the split time that the stopwatch recorded as a string.
     *
     * @return the split time as a string
     */
    public String toSplitString() {
        return delegate.toSplitString();
    }

    /**
     * Formats the time gap as a string.
     *
     * @param startTime the start time
     * @param stopTime the stop time
     * @return the formatted time gap
     */
    public static String formatTime(long startTime, long stopTime) {
        long time = stopTime - startTime;
        return formatTime(time);
    }

    /**
     * Formats the time as a string.
     *
     * @param time the time in milliseconds
     * @return the formatted time
     */
    public static String formatTime(long time) {
        // Simple formatting similar to commons-lang behavior
        if (time < 1000) {
            return time + "ms";
        } else if (time < 60000) {
            return String.format("%.3fs", time / 1000.0);
        } else if (time < 3600000) {
            long minutes = time / 60000;
            long seconds = (time % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        } else {
            long hours = time / 3600000;
            long minutes = (time % 3600000) / 60000;
            long seconds = (time % 60000) / 1000;
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        }
    }

    // Backward compatibility methods that might have been used in commons-lang

    /**
     * Gets the message associated with this StopWatch.
     *
     * @return the message
     */
    public String getMessage() {
        // commons-lang3 doesn't expose message directly, so we'll return a default
        return delegate.toString();
    }

    /**
     * Validates that the StopWatch is in the expected state.
     *
     * @param expectedState the expected state
     * @param message the error message
     */
    private void validateState(String expectedState, String message) {
        // Simple state validation - this is for backward compatibility
        // The actual validation is handled by the parent class
    }
}
