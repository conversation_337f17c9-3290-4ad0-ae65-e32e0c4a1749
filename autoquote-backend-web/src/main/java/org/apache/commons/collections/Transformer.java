package org.apache.commons.collections;

/**
 * Bridge interface to provide compatibility with commons-collections Transformer
 * while using commons-collections4 internally.
 * 
 * This interface extends the commons-collections4 Transformer to maintain
 * compatibility with legacy code that expects the old commons-collections package.
 * 
 * @param <I> the input type to the transformer
 * @param <O> the output type from the transformer
 */
public interface Transformer<I, O> extends org.apache.commons.collections4.Transformer<I, O> {

    /**
     * Transforms the input object (leaving it unchanged) into some output object.
     *
     * @param input the object to be transformed, should be left unchanged
     * @return a transformed object
     */
    @Override
    O transform(I input);
}
