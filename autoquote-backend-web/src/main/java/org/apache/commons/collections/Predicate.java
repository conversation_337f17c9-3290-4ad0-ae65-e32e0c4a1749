package org.apache.commons.collections;

/**
 * Bridge interface to provide compatibility with commons-collections Predicate
 * while using commons-collections4 internally.
 * 
 * This interface extends the commons-collections4 Predicate to maintain
 * compatibility with legacy code that expects the old commons-collections package.
 * 
 * @param <T> the type of the input to the predicate
 */
public interface Predicate<T> extends org.apache.commons.collections4.Predicate<T> {

    /**
     * Evaluates this predicate on the given argument.
     *
     * @param object the input argument
     * @return {@code true} if the input argument matches the predicate,
     * otherwise {@code false}
     */
    @Override
    boolean evaluate(T object);
}
