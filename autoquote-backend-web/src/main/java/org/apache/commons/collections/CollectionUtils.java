package org.apache.commons.collections;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Bridge class to provide compatibility with commons-collections CollectionUtils
 * while using commons-collections4 internally.
 *
 * This class delegates all method calls to the commons-collections4 CollectionUtils
 * to maintain compatibility with legacy code that expects the old commons-collections package.
 */
public class CollectionUtils {

    /**
     * Private constructor to prevent instantiation.
     */
    private CollectionUtils() {
        // Utility class
    }

    // Delegate all commonly used methods to commons-collections4

    public static boolean isEmpty(Collection<?> coll) {
        return org.apache.commons.collections4.CollectionUtils.isEmpty(coll);
    }

    public static boolean isNotEmpty(Collection<?> coll) {
        return org.apache.commons.collections4.CollectionUtils.isNotEmpty(coll);
    }

    public static boolean isEqualCollection(Collection<?> a, Collection<?> b) {
        return org.apache.commons.collections4.CollectionUtils.isEqualCollection(a, b);
    }

    public static <T> Collection<T> union(Collection<? extends T> a, Collection<? extends T> b) {
        return org.apache.commons.collections4.CollectionUtils.union(a, b);
    }

    public static <T> Collection<T> intersection(Collection<? extends T> a, Collection<? extends T> b) {
        return org.apache.commons.collections4.CollectionUtils.intersection(a, b);
    }

    public static <T> Collection<T> disjunction(Collection<? extends T> a, Collection<? extends T> b) {
        return org.apache.commons.collections4.CollectionUtils.disjunction(a, b);
    }

    @SuppressWarnings("unchecked")
    public static <O> Collection<O> subtract(Collection<O> a, Collection<?> b) {
        // Use raw types to avoid generic type conflicts
        return (Collection<O>) org.apache.commons.collections4.CollectionUtils.subtract((Collection) a, (Collection) b);
    }

    public static boolean containsAny(Collection<?> coll1, Collection<?> coll2) {
        return org.apache.commons.collections4.CollectionUtils.containsAny(coll1, coll2);
    }

    public static <T> boolean addIgnoreNull(Collection<T> collection, T object) {
        return org.apache.commons.collections4.CollectionUtils.addIgnoreNull(collection, object);
    }

    public static <T> void addAll(Collection<T> collection, T[] elements) {
        org.apache.commons.collections4.CollectionUtils.addAll(collection, elements);
    }

    public static <T> void addAll(Collection<T> collection, Iterable<? extends T> iterable) {
        org.apache.commons.collections4.CollectionUtils.addAll(collection, iterable);
    }

    public static int size(Object object) {
        return org.apache.commons.collections4.CollectionUtils.size(object);
    }

    public static boolean sizeIsEmpty(Object object) {
        return org.apache.commons.collections4.CollectionUtils.sizeIsEmpty(object);
    }

    public static <T> void reverseArray(T[] array) {
        org.apache.commons.collections4.CollectionUtils.reverseArray(array);
    }

    public static boolean isFull(Collection<? extends Object> coll) {
        return org.apache.commons.collections4.CollectionUtils.isFull(coll);
    }

    public static int maxSize(Collection<? extends Object> coll) {
        return org.apache.commons.collections4.CollectionUtils.maxSize(coll);
    }

    @SuppressWarnings("unchecked")
    public static <E> Collection<E> retainAll(Collection<E> collection, Collection<?> retain) {
        return (Collection<E>) org.apache.commons.collections4.CollectionUtils.retainAll((Collection) collection, (Collection) retain);
    }

    @SuppressWarnings("unchecked")
    public static <E> Collection<E> removeAll(Collection<E> collection, Collection<?> remove) {
        return (Collection<E>) org.apache.commons.collections4.CollectionUtils.removeAll((Collection) collection, (Collection) remove);
    }

    @SuppressWarnings("unchecked")
    public static <T> Collection<T> synchronizedCollection(Collection<T> collection) {
        return java.util.Collections.synchronizedCollection(collection);
    }

    @SuppressWarnings("unchecked")
    public static <T> Collection<T> unmodifiableCollection(Collection<? extends T> collection) {
        return (Collection<T>) java.util.Collections.unmodifiableCollection(collection);
    }

    @SuppressWarnings("unchecked")
    public static <T> Collection<T> predicatedCollection(Collection<T> collection, Predicate<? super T> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate =
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.predicatedCollection(collection, newPredicate);
    }

    @SuppressWarnings("unchecked")
    public static <T> Collection<T> typedCollection(Collection<T> collection, Class<T> type) {
        // Use a simple wrapper since checkedCollection doesn't exist in commons-collections4
        return collection; // For compatibility, just return the original collection
    }

    // Original commons-collections signature that external libraries expect
    @SuppressWarnings("unchecked")
    public static Collection transformedCollection(Collection collection, Transformer transformer) {
        // Convert old Transformer to new Transformer if needed
        org.apache.commons.collections4.Transformer newTransformer =
            (org.apache.commons.collections4.Transformer) transformer;
        return org.apache.commons.collections4.CollectionUtils.transformingCollection(collection, newTransformer);
    }

    @SuppressWarnings("unchecked")
    public static <T> Collection<T> transformedCollection(Collection<T> collection,
            org.apache.commons.collections4.Transformer<? super T, ? extends T> transformer) {
        return org.apache.commons.collections4.CollectionUtils.transformingCollection(collection, transformer);
    }

    @SuppressWarnings("unchecked")
    public static <T> T get(Object object, int index) {
        return (T) org.apache.commons.collections4.CollectionUtils.get(object, index);
    }

    // Original commons-collections signature that external libraries expect
    @SuppressWarnings("unchecked")
    public static void filter(Collection collection, Predicate predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate newPredicate =
            (org.apache.commons.collections4.Predicate) predicate;
        org.apache.commons.collections4.CollectionUtils.filter(collection, newPredicate);
    }

    // Original commons-collections signature that external libraries expect
    @SuppressWarnings("unchecked")
    public static void transform(Collection collection, Transformer transformer) {
        // Convert old Transformer to new Transformer if needed
        org.apache.commons.collections4.Transformer newTransformer =
            (org.apache.commons.collections4.Transformer) transformer;
        org.apache.commons.collections4.CollectionUtils.transform(collection, newTransformer);
    }

    @SuppressWarnings("unchecked")
    public static <T> void transform(Collection<T> collection,
            org.apache.commons.collections4.Transformer<? super T, ? extends T> transformer) {
        org.apache.commons.collections4.CollectionUtils.transform(collection, transformer);
    }

    public static int countMatches(Iterable<?> input, Predicate<? super Object> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super Object> newPredicate = 
            (org.apache.commons.collections4.Predicate<? super Object>) predicate;
        return org.apache.commons.collections4.CollectionUtils.countMatches(input, newPredicate);
    }

    public static <T> boolean exists(Iterable<T> collection, Predicate<? super T> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate = 
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.exists(collection, newPredicate);
    }

    public static <T> T find(Iterable<T> collection, Predicate<? super T> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate = 
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.find(collection, newPredicate);
    }

    public static <T> void forAllDo(Iterable<T> collection, 
            org.apache.commons.collections4.Closure<? super T> closure) {
        org.apache.commons.collections4.CollectionUtils.forAllDo(collection, closure);
    }

    public static <T> void forAllButLastDo(Iterable<T> collection, 
            org.apache.commons.collections4.Closure<? super T> closure) {
        org.apache.commons.collections4.CollectionUtils.forAllButLastDo(collection, closure);
    }

    public static <T> void forAllButLastDo(T[] array,
            org.apache.commons.collections4.Closure<? super T> closure) {
        if (array != null && array.length > 0) {
            for (int i = 0; i < array.length - 1; i++) {
                closure.execute(array[i]);
            }
        }
    }

    public static <T> boolean isSubCollection(Collection<T> a, Collection<T> b) {
        return org.apache.commons.collections4.CollectionUtils.isSubCollection(a, b);
    }

    public static <T> boolean isProperSubCollection(Collection<T> a, Collection<T> b) {
        return org.apache.commons.collections4.CollectionUtils.isProperSubCollection(a, b);
    }

    // Original commons-collections signature that external libraries expect
    @SuppressWarnings("unchecked")
    public static Collection collect(Collection inputCollection, Transformer transformer) {
        // Convert old Transformer to new Transformer if needed
        org.apache.commons.collections4.Transformer newTransformer =
            (org.apache.commons.collections4.Transformer) transformer;
        return org.apache.commons.collections4.CollectionUtils.collect(inputCollection, newTransformer);
    }

    public static <T> Collection<T> collect(Iterable<?> inputCollection,
            org.apache.commons.collections4.Transformer<? super Object, ? extends T> transformer) {
        return org.apache.commons.collections4.CollectionUtils.collect(inputCollection, transformer);
    }

    public static <T, R> Collection<R> collect(Iterable<? extends T> inputCollection, 
            org.apache.commons.collections4.Transformer<? super T, ? extends R> transformer, 
            Collection<R> outputCollection) {
        return org.apache.commons.collections4.CollectionUtils.collect(inputCollection, transformer, outputCollection);
    }

    // Original commons-collections signature that external libraries expect
    @SuppressWarnings("unchecked")
    public static Collection select(Collection inputCollection, Predicate predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate newPredicate =
            (org.apache.commons.collections4.Predicate) predicate;
        return org.apache.commons.collections4.CollectionUtils.select(inputCollection, newPredicate);
    }

    public static <T> Collection<T> select(Iterable<? extends T> inputCollection,
            Predicate<? super T> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate =
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.select(inputCollection, newPredicate);
    }

    public static <T> Collection<T> select(Iterable<? extends T> inputCollection,
            Predicate<? super T> predicate, Collection<T> outputCollection) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate =
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.select(inputCollection, newPredicate, outputCollection);
    }

    public static <T> Collection<T> selectRejected(Iterable<? extends T> inputCollection, 
            Predicate<? super T> predicate) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate = 
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.selectRejected(inputCollection, newPredicate);
    }

    public static <T> Collection<T> selectRejected(Iterable<? extends T> inputCollection,
            Predicate<? super T> predicate, Collection<T> outputCollection) {
        // Convert old Predicate to new Predicate if needed
        org.apache.commons.collections4.Predicate<? super T> newPredicate =
            (org.apache.commons.collections4.Predicate<? super T>) predicate;
        return org.apache.commons.collections4.CollectionUtils.selectRejected(inputCollection, newPredicate, outputCollection);
    }

    public static String toString(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Collection) {
            return obj.toString();
        }
        return obj.toString();
    }
}
