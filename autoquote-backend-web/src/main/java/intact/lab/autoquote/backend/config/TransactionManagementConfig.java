package intact.lab.autoquote.backend.config;

import com.atomikos.icatch.config.UserTransactionServiceImp;
import com.atomikos.icatch.jta.UserTransactionManager;
import com.atomikos.spring.AtomikosProperties;
import jakarta.transaction.TransactionManager;
import jakarta.transaction.UserTransaction;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.jta.JtaTransactionManager;

import java.util.Properties;

/**
 * Configuration for transaction management.
 * <p>
 * Current provider is <b>Atomikos</b>. For some reason, using Atomikos autoconfiguration does not work, so we have to
 * configure it manually.
 * <p>
 * Some beans have been copied over from {@link com.atomikos.spring.AtomikosAutoConfiguration}. Especially, this class
 * re-enables support for Atomikos properties defined in application.yml (instead of using {@code jta.properties} file)
 * inside the project.
 */
@Configuration
@EnableTransactionManagement
@EnableConfigurationProperties(AtomikosProperties.class)
public class TransactionManagementConfig {

  @Bean(initMethod = "init", destroyMethod = "shutdownWait")
  UserTransactionServiceImp userTransactionService(AtomikosProperties atomikosProperties) {
    Properties properties = new Properties();
    properties.putAll(atomikosProperties.asProperties());
    return new UserTransactionServiceImp(properties);
  }

  @Bean(name = "atomikosTransactionManager")
  public UserTransactionManager atomikosTransactionManager() throws Throwable {
    UserTransactionManager atomikosTransactionManager = new UserTransactionManager();
    atomikosTransactionManager.setTransactionTimeout(700000);
    atomikosTransactionManager.setForceShutdown(false);
    atomikosTransactionManager.init();
    return atomikosTransactionManager;
  }

  @Bean(name = "atomikosUserTransaction")
  public UserTransaction atomikosUserTransaction() throws Throwable {
    UserTransaction atomikosUserTransaction = new com.atomikos.icatch.jta.UserTransactionImp();
    atomikosUserTransaction.setTransactionTimeout(7000000);
    return atomikosUserTransaction;
  }

  @Bean(name = "transactionManager")
  public PlatformTransactionManager transactionManager(TransactionManager atomikosTransactionManager) throws Throwable {
    JtaTransactionManager tManager = new JtaTransactionManager(atomikosTransactionManager);
    tManager.setTransactionManager(atomikosTransactionManager());
    tManager.setUserTransaction(atomikosUserTransaction());
    tManager.setAllowCustomIsolationLevels(true);
    tManager.setAutodetectUserTransaction(false);
    tManager.setAutodetectTransactionManager(false);
    tManager.setDefaultTimeout(300); // 5 minutes timeout
    tManager.setRollbackOnCommitFailure(true);
    return tManager;
  }
}
