/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.singleid.accessmanager.domain.SecureDomain;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 *
 */
public final class SecureDomainUtil {

	private SecureDomainUtil(){
		// private constructor
	}

	/**
	 * Get Secure Domain from the policyVersion's context.
	 *
	 * @param policyVersion {@link PolicyVersion}
	 * @return {@link SecureDomain}
	 */
	public static SecureDomain getSecureDomain(final PolicyVersion policyVersion) {
		SecureDomain sd = null;
		if (policyVersion != null) {
			// check manufacturing context
			DistributionChannelCodeEnum distributionChannel = null;
			if (policyVersion.getInsurancePolicy() != null) {
				ManufacturingContext manufacturingContext = policyVersion.getInsurancePolicy().getManufacturingContext();
				if (manufacturingContext != null){
					distributionChannel = manufacturingContext.getDistributionChannel();
				}
			}
			if (distributionChannel != null){
				switch(distributionChannel){
				// when distibution channel is a direct seller then verify the distributor
				case DIRECT_SELLER:
					DistributorCodeEnum dist = policyVersion.getDistributorCode();
					if (dist != null) {
						switch (dist) {
						case BEL:
							sd = SecureDomain.BELAIRDIRECT;
							break;
						case BNA:
							sd = SecureDomain.BNA;
							break;
						// Default for backward compatibility, will contact the old secure domain.
						case DEFAULT:
						default:
							sd = SecureDomain.DEFAULT;
							break;
						}
					}else{
						// when distributor is not defined then set it to default
						sd = SecureDomain.DEFAULT;
					}
					break;

				// when distribution channel is brokers then we will set to Intat
				case THROUGH_BROKERS:
					sd = SecureDomain.INTACT;
					break;
				default:
					// no assignment SecureDomain should be null, this should not occur
					break;
				}
			}
		}
		return sd;
	}
}
