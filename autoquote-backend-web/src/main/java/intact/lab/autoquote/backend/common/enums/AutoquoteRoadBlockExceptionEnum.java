/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.enums;

import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

/**
 * The Enum AutoquoteRoadBlockExceptionEnum.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public enum AutoquoteRoadBlockExceptionEnum {

	// VEHICLE
	/** BR232 ONE_VEHICLE_IS_TOO_OLD. */
	BR232("MSG69"),
	/** BR331 PERFORMANCE_MODIF_MORE_5000. */
	BR331("MSG87"),
	/** BR1260 MODIFICATION_OVER_10K. */
	BR1260("MSG311"),
	/** BR673 RATE_GROUP_OVER_50. */
	BR673("MSG177"),

	/** BR7682_VehicleModifiedForPerformance */
	BR7682("MSG87"),

	/** BR7649_VehicleIsTooOld */
	BR7649("BR7649"),
	/** BR7683_VehicleModificationsExceedLimit */
	BR7683("BR7683"),
	/** BR7685_VehicleUsedToTransportGoodsMaterialsTools */
	BR7685("BR7685"),
	/** BR231 ONE_VEHICLE_LEASED_OLDER_4_YEAR. */
	BR231("MSG68"),
	/** BR1527 NO_VEHICLE_DETAIL_IN_CLASSIC */
	BR1527("MSG413"),

	/** BR7946_ProhibitedPostalCode */
	BR7946("BR7946"),

	/** BR8001_CancellationReasonOtherThanNonPayment */
	BR8001("BR8001"),

	/** BR7997_TooManyClaims3years */
	BR7997("BR7997"),

	/** BR7995_TooManyAtFaultClaims3years */
	BR7995("BR7995"),

	/** BR7936_RelationshipToPolicyholderOtherNotLivingUnderSameRoof */
	BR7936("BR7936"),

	/** BR7967_DriverNotHavingQuebecLicense */
	BR7967("BR7967"),

	/** BR7937_RelationshipToPolicyholderOtherLivingUnderSameRoof */
	BR7937("BR7937"),

	/** BR7934_DriverAgeNotValid */
	BR7934("BR7934"),

	/** BR8719_FourOrMoreMinorInfractions3Years */
	BR8719("BR8719"),

	/** BR16797_ToManyMinorInfractions */
	BR16797("BR16797"),

	/** BR8716_ThreeOrMoreClaimsExceptGlassRepair3Years */
	BR8716("BR8716"),

	/** BR8715_TwoOrMoreClaimsAtFault3Years */
	BR8715("BR8715"),

	/** BR0080_PolicyholderOwnVehicle */
	BR0080("BR0080"),

	/** BR8713_TheftClaimOverTenThousandNoAntiTheft */
	BR8713("BR8713"),

	/** BR7684_AnnualKMExceedThreshold */
	BR7684("BR7684"),

	/** BR8000_CancelledPolicy */
	BR8000("BR8000"),

	/** BR7996_TooManyAtFaultClaims6years */
	BR7996("BR7996"),

	/** BR7983_TooManyConvictions */
	BR7983("BR7983"),

	/** BR7972_DriverNotHavingOntarioLicense */
	BR7972("BR7972)"),

	/** BR7998_TooManyClaims2years */
	BR7998("BR7998"),

	/** BR8720_FourOrMoreMinorInfractions6Years */
	BR8720("BR8720"),

	/** BR8714_TwoOrMoreClaimsAtFault6Years */
	BR8714("BR8714"),

	/** BR8725_TwoDifferentOwnersNotSpouse */
	BR8725("BR8725"),

	/** BR8723_PrincipalDriverG1License */
	BR8723("BR8723"),

	/** BR8722_TwoDriversUnder25YearsOld */
	BR8722("BR8722"),

	/** BR8718_ThreeOrMoreGlassRepairClaim2Years */
	BR8718("BR8718"),

	/** BR8717_ThreeOrMoreClaimsExceptGlassRepair2Years */
	BR8717("BR8717"),

	// USAGE POST BR EXCEPTION
	/** Usage type is commercial/delivery */
	BR367("MSG91"),
	/** Vehicle is used outside of Canada - ON */
	BR369("MSG91"),
	/** When the named insured is not assigned as owner on at least one (1) vehicle */
	BR370("MSG92"),
	/** One of the vehice have no party assign as owner. (when 'other' is choosed in the UI) */
	BR372("MSG93"),
	/** Two underage drivers. */
	BR374("MSG94"),
	/** The number of responsible claims exceeded. */
	BR377("MSG95"),
	/** The total nb claims exceeded. */
	BR475("MSG95"),
	/** Two different registed owners and no one have a SPOUSE relationship with the named insured */
	BR476("MSG107"),
	/** Principal driver with a G1 license class - ON */
	BR671("MSG94"),
	/** Refer the user due to limit of yearly mileage */
	BR1893("MSG411"),

	/** TRACKING_SYSTEM_REQUIRED */
	BR300("MSG84"),
	/** NO_BIND_SIMULATION_QUOTE */
	BR298("MSG82"),
	/** EXPERIENCE_LETTER_REQUIRED */
	BR299("MSG83"),
	/** ELIGIBILITY_FOR_BIND_QC */
	BR255("MSG88"),
	/** ELIGIBILITY_FOR_BIND_ON */
	BR256_1("MSG89"),
	/** LEASED VEHICLE WITHTOUT B2 AND/OR B3 */
	BR1857("MSG406"),
	/** User refuses to give is consent for credit score */
	BR1997("MSG424"),

	/** Driver DRIVER_REVOKED */
	BR462("MSG22"),
	/** Driver DRIVER_CLAIMS */
	BR463("MSG290"),
	/** Driver DRIVER_CLAIMS */
	BR464("MSG290"),
	/** Driver DRIVER_TOTAL_CONVICTIONS */
	BR466("MSG380"),
	/** Driver CONVICTIONS */
	BR759("MSG380"),
	/** Driver CONVICTIONS */
	BR760("MSG380"),
	/** Driver NAME_INSURED_RELATIONSHIP */
	BR1781("MSG381"),
	/** Driver LICENCE_TYPE_OTHER */
	BR457_1("MSG382"),
	/** Driver LICENCE_TYPE_OTHER */
	BR1782("MSG383"),
	/** Driver NO_LICENCE */
	BR458("MSG384"),
	/** Driver VALIDATE_PROFIL_CONSENT */
	BR2253("MSG106"),
	/** Driver INSURANCE_CANCELLED */
	BR460_1("MSG385"),
	/** Driver INSURANCE_CANCELLED */
	BR461_1("MSG385"),
	/** Driver PROHIBITED_POSTAL_CODE */
	BR611("MSG402"),
	/** Vehicle validation INVALID_VIN */
	BR916("MSG225"),
	/** Vehicle validation SECOND_REGISTER_OWNER_OTHER */
	BR486("MSG112"),
	/** Vehicle validation SECOND_REGISTER_OWNER_NOT_SPOUSE */
	BR487("MSG113"),
	/** Vehicle validation ALL_OTHER_OWN_INSURANCE */
	BR6408("MSG7654"),

	/**
	 * Single Id RoadBlockExceptionEnum
	 */

	// NEW QUOTE 1st Validation (Name/Postal/Civic#)
	/** BR551 */
	BR551("MSG203"),
	/** BR550 */
	BR550("MSG202"),
	/** BR732_CLIENT */
	BR732_CLIENT("MSG201"),
	/** BR732_AGENT */
	BR732_AGENT("MSG116"),

	/** BR2359 */
	BR2359("MSG465"),
	/** BR2506 */
	BR2506("MSG532"),
	/** BR2565 */
	BR2565("MSG531"),

	/** BR5003 - Email already used **/
	BR5003("BR5003"),
	/** BR2260 - User has already bought a policy **/
	BR2260("BR2260"),

	// NEW QUOTE 2nd Validation (TAM account/email)
	/** BR734 */
	BR734("MSG206"),
	/** BR735 */
	BR735("MSG206"),
	/** BR736_CLIENT */
	BR736_CLIENT("MSG204"),
	/** BR736_AGENT */
	BR736_AGENT("MSG205"),

	// Retrieve Quote
	/** BR569_CLIENT */
	BR569_CLIENT("MSG135"),
	/** BR569_AGENT */
	BR569_AGENT("MSG136"),
	/** BR570 */
	BR570("MSG137"),
	/** BR571.1 */
	BR571("MSG138"),
	/** BR572.1 */
	BR572("MSG139"),
	/** BR573 */
	BR573("MSG140"),
	/** BR574 */
	BR574("MSG141"),
	/** BR575 */
	BR8475("BR8475"),

	// Intact Specific BR's
	/** BRxxxx */
	BR_ON_VEUT_PAS_TAVOIR_COMME_CLIENT("XXXX"),
	/** BR2437 */
	BR2437("MSG486"),
	/** BR2438 */
	BR2438("MSG485"),
	/** BR2439 */
	BR2439("MSG485"),
	/** BR2510 */
	BR2510("MSG497"),
	/** BR2526 */
	BR2526("MSG515"),
	/** BR2527 */
	BR2527("MSG516"),
	/** BR2678 */
	BR2678("MSG541"),
	/** BR2684 */
	BR2684("MSG542"),
	/** BR3162 */
	BR3162_001("msgMasterError"),
	/** BR3162 */
	BR3162_002("msgSubError"), 
	
	BR15548("BR15548"),

	//WebQuote BR and messages
	BR16060("MSG53011"),
	BR16061("MSG53011"),
	BR16062("MSG53011"),
	BR16063("MSG53012"),
	BR16064("MSG53011"),
	BR16065("MSG53013"),
	BR16066("MSG53015"),
	BR16067("MSG53017"),
	BR16071("MSG53020");

	private static final Logger LOG = ESAPI.getLogger(AutoquoteRoadBlockExceptionEnum.class);

	/**
	 * Instantiates a new policy change road block exception enum.
	 *
	 * @param aCode the a code
	 */
	private AutoquoteRoadBlockExceptionEnum(String aCode) {
		this.code = aCode;
	}

	/** The code. */
	private String code = null;

	/**
	 * Gets the code.
	 *
	 * @return the code
	 */
	public String getCode() {
		return this.code;
	}

	/**
	 * Value of code.
	 *
	 * @param value the value
	 *
	 * @return the policy change road block exception enum
	 */
	public static AutoquoteRoadBlockExceptionEnum valueOfCode(String value) {

		if (StringUtils.isEmpty(value)) {
			return null;
		}

		for (AutoquoteRoadBlockExceptionEnum v : values()) {
			if (v.code.equals(value)) {
				if (LOG.isTraceEnabled()) {
					LOG.trace(Logger.EVENT_SUCCESS, new StringBuilder("returning enum constant ").append(v.name()).append(" for value : ")
							.append(value).toString());
				}
				return v;
			}

		}

		throw new IllegalArgumentException("no enum value found for code: " + value);

	}

}
