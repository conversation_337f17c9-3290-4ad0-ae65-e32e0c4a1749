package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;

public interface IExecuteService {

	/**
	 * Determine Quote Informations
	 * 
	 * @param context {@link ManufacturingContext}
	 * @param policyVersion {@link PolicyVersion}
	 * @return the related som {@link PolicyVersion}
	 * @throws BaseException
	 */
	PolicyVersion determineQuoteInfos(ManufacturingContext context, PolicyVersion policyVersion) throws BaseException;
	
}
