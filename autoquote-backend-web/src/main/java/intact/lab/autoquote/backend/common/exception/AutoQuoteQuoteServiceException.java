package intact.lab.autoquote.backend.common.exception;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteQuoteServiceException extends AutoQuoteException {

	public static final String DEFAULT_EXCEPTION_CODE = "exec.quickquote.quote.default.error";
	public static final String EXEC_SAVE_QUOTE_ERROR_CODE = "exec.quickquote.service.quote.save.quote.error";
	public static final String EXEC_RETRIEVE_QUOTE_ERROR_CODE = "exec.quickquote.service.quote.retrieve.quote.error";
	public static final String EXEC_CREATE_QUOTE_ERROR_CODE = "exec.quickquote.service.quote.create.quote.error";
	public static final String PARAM_COM_NULL_ERROR_CODE = "param.quickquote.quote.service.com.null.error";
	public static final String PARAM_QUOTE_DTO_NULL_ERROR_CODE = "param.quickquote.quote.service.quote.dto.null.error";
	public static final String PARAM_COM_CONTEXT_NULL_ERROR_CODE = "param.quickquote.quote.service.com.context.null.error";
	public static final String PARAM_POLICY_NUMBER_NULL_ERROR_CODE = "param.quickquote.quote.service.policy.number.null.error";
	public static final String PARAM_UUID_NULL_ERROR_CODE = "param.quickquote.quote.service.uuid.null.error";
	public static final String PARAM_ORIGIN_NULL_ERROR_CODE = "param.quickquote.quote.service.origin.null.error";
	private static final long serialVersionUID = 2031937736930433740L;
	private static Map<String, String> messages = null;

	public AutoQuoteQuoteServiceException(String exceptionCode, Object... parameters) {
		super(exceptionCode, parameters);
	}

	public AutoQuoteQuoteServiceException(String exceptionCode, Throwable cause, Object... parameters) {
		super(exceptionCode, cause, parameters);
	}

	public AutoQuoteQuoteServiceException(String message, Throwable cause) {
		super(message, cause);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<>();

		messages.put(
				AutoQuoteQuoteServiceException.DEFAULT_EXCEPTION_CODE,
				"An unknown error occurred while peforming one of the QuickQuoteQuoteService function.  The cause is {0}");

		messages.put(
				AutoQuoteQuoteServiceException.PARAM_COM_NULL_ERROR_CODE,
				"The com provided as a parameter is null. Please provide a none null parameter");
		messages.put(
				AutoQuoteQuoteServiceException.PARAM_COM_CONTEXT_NULL_ERROR_CODE,
				"The ComContext provided as a parameter is null. Please provide a none null parameter");
		messages.put(
				AutoQuoteQuoteServiceException.PARAM_POLICY_NUMBER_NULL_ERROR_CODE,
				"The policyNumber provided as a parameter is null. Please provide a none null parameter");
		messages.put(
				AutoQuoteQuoteServiceException.PARAM_ORIGIN_NULL_ERROR_CODE,
				"The origin provided as a parameter is null. Please provide a none null parameter");

		messages.put(
				AutoQuoteQuoteServiceException.EXEC_SAVE_QUOTE_ERROR_CODE,
				"An error occured while trying to save the quote with this object {0}.  The cause is {1}");

		messages.put(
				AutoQuoteQuoteServiceException.EXEC_RETRIEVE_QUOTE_ERROR_CODE,
				"An error occured while trying to retreive the quote with comContext {0}, policyNumber{1}, origin{2}.  The cause is {3}");

		messages.put(
				AutoQuoteQuoteServiceException.EXEC_CREATE_QUOTE_ERROR_CODE,
				"An error occured while trying to save the quote with province {0}, language {1}, ipAddress {2}, subBroker {3}, origin {4}.  The cause is {0}");
		messages.put(
				AutoQuoteQuoteServiceException.PARAM_UUID_NULL_ERROR_CODE,
				"The uuid provided as a parameter is null. Please provide a none null parameter");
		messages.put(
				AutoQuoteQuoteServiceException.PARAM_QUOTE_DTO_NULL_ERROR_CODE,
				"The QuoteDTO provided as a parameter is null. Please provide a none null parameter");

		AutoQuoteQuoteServiceException.setMessages(messages);

	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteQuoteServiceException.getMessages() == null) {
			AutoQuoteQuoteServiceException.initMessages();
		}

		String messageFormat = AutoQuoteQuoteServiceException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteQuoteServiceException.getMessages().get(AutoQuoteQuoteServiceException.DEFAULT_EXCEPTION_CODE);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteQuoteServiceException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteQuoteServiceException.messages = messages;
	}

}
