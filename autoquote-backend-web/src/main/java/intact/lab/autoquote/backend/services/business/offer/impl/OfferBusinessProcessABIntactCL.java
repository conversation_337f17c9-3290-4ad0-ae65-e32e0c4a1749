/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.business.rules.exception.BusinessRuleException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.impl.ExecuteServiceABIntact;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.ALBERTA;

/**
 * Business process specific to Intact Alberta CL. Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 */
@ComponentLocal(province = ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class OfferBusinessProcessABIntactCL extends OfferBusinessProcess {

    @Autowired
    private ExecuteServiceABIntact executeService;

    @AutowiredLocal
    protected IRateManagerService rateManagerService;

    @AutowiredLocal
    protected IRatingService ratingService;

    @Autowired
    private IVehicleHelper vehicleHelper;

    @Override
    protected void determineQuoteInfosPostProcess(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException {
        // We must retrieve the use of vehicle category since Pega only returns the usage code.
        Vehicle aPlVehicle = this.policyVersionHelper.retrieveVehicle(aPlPolicyVersion,1);
        if (aPlVehicle.getUseOfVehicleCategory() == null && aPlVehicle.getVehicleUsage() != null) {
            aPlVehicle.setUseOfVehicleCategory(vehicleHelper.retrieveUseOfVehicleCategory(aPlVehicle.getVehicleUsage()));
        }
    }

    @Override
    protected IExecuteService getExecuteService() {
        return this.executeService;
    }

    @Override
    public IRateManagerService getRateManagerService() {
        return this.rateManagerService;
    }

    @Override
    public IRatingService getRatingService() {
        return this.ratingService;
    }

    @Override
    public void manageUbiStatus(PolicyVersion aPolicyVersion) {
        // no more UBI AB
    }

    @Override
    public void selectOffer(final PolicyVersion policyVersion, Boolean isUbiEnabled) throws AutoquoteRatingException, BusinessRuleException {
        //super.validatePostBR(policyVersion);
        // RANB-155 all roadblocks in litebox
        this.validatePostBR(policyVersion);
        super.selectOffer(policyVersion, isUbiEnabled);
    }

    @Override
    public void validatePostBR(PolicyVersion policyVersion) throws BusinessRuleException {
        // no applicable roadblocks
    }
}
