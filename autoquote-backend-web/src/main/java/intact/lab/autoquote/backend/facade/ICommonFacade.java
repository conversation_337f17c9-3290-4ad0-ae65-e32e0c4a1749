package intact.lab.autoquote.backend.facade;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;

public interface ICommonFacade {

    /**
     * Initializes the quote process by creating a brand new policy version.
     *
     * @param aComContext {@link ComContext}
     * @return {@link CommunicationObjectModel}
     * @throws AutoQuoteQuoteServiceException
     */
    CommunicationObjectModel createQuote(ComContext aComContext) throws AutoQuoteQuoteServiceException;

    /**
     * Retrieve policy by UUID.
     *
     * @param context the context
     * @param uuid the uuid of policy
     * @return the communication object model
     * @throws AutoQuoteQuoteServiceException the autoquote facade exception
     */
    CommunicationObjectModel retrievePolicyByUUID(ComContext context, String uuid) throws AutoQuoteQuoteServiceException;

    /**
     * Mediates the provided CommunicationObjectModel instance into a PolicyVersion instance and saves the latter in the
     * database.
     *
     * When a quote is modified but has already been rated, the current policy version is copied (cloned) and it is that
     * copy that will be returned in the COM. Previously generated offers are wiped from the cloned version.
     *
     * @param aCom {@link CommunicationObjectModel}
     * @param force Indicates whether or not the 'mediate and save on data change' mechanism should be ignored and<br>
     *            proceed with those operations eventhough the COM's data was not flagged as being changed.<br>
     *            Has no effect on the clone.
     *
     * @return {@link CommunicationObjectModel}
     * @throws AutoQuoteQuoteServiceException the autoquote facade exception
     */
    CommunicationObjectModel save(CommunicationObjectModel aCom, boolean force) throws AutoQuoteQuoteServiceException, AutoquoteFacadeException;

    /**
     * Calls the credit score service. When a 'No Hit' is returned from the service, a flag is updated in the ComState
     * of the returned CommunicationObjectModel instance to indicate that the 'no Hit' page should be displayed to the
     * user.
     *
     * @param com {@link CommunicationObjectModel}
     * @return {@link CommunicationObjectModel}
     * @throws AutoquoteFacadeException
     * */
    CommunicationObjectModel callCreditScore(CommunicationObjectModel com) throws AutoquoteFacadeException;

    /**
     * Performs roadblock validation on the policy version.
     *
     * A CommunicationObjectModel instance can be considered valid if both 'ValidationErrors' and 'RoadBlock'
     * collections are empty.
     *
     * Does not perform basic UI validation.
     *
     * @param aCom {@link CommunicationObjectModel}
     * @throws Exception
     */
    void validateRoadblocks(CommunicationObjectModel aCom) throws Exception;
}
