package intact.lab.autoquote.backend.facade;

import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteOfferException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;

public interface IOfferFacade {

    /**
     * Calculate.
     *
     * @param quoteDTO the quote DTO
     * @param apiKey   the api key
     * @param province the province
     * @param language the language
     * @return the response DTO
     * @throws AutoQuoteQuoteServiceException the autoquote exception
     * @throws AutoQuoteOfferException         the auto quote offer exception
     */
    ResponseDTO calculate(QuoteDTO quoteDTO, String apiKey, String province, String language, String subBroker,
                          String organizationSource) throws AutoQuoteQuoteServiceException, AutoQuoteOfferException;

    /**
     * Recalculate.
     *
     * @param quoteDTO the quote DTO
     * @param uuid  the quote id
     * @param apiKey   the api key
     * @param province the province
     * @param language the language
     */
    ResponseDTO recalculate(QuoteD<PERSON> quoteDTO, String uuid, String apiKey, String province, String language, String subBroker, String organizationSource);

}
