package intact.lab.autoquote.backend.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Getter
@RequiredArgsConstructor
public enum ConvictionTypeEnum {

    MAJOR("MAJ"),
    MINOR("MIN"),
    DISTRACTED("DIS");

    private final String code;

    public static ConvictionTypeEnum valueOfCode(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        for (ConvictionTypeEnum v : values()) {
            if (v.code.equals(value)) {
                return v;
            }
        }

        throw new IllegalArgumentException("No enum value found for code: " + value);
    }

    @JsonValue
    public String getCode() {
        return code;
    }
}
