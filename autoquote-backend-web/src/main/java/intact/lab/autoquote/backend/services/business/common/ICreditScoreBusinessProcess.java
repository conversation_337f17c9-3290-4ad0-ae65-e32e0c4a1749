/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;

/**
 * Defines a CreditScoreBusinessProcess
 */
public interface ICreditScoreBusinessProcess {

	/**
	 * Call the credit score.
	 * 
	 * @param policyVersion the policy version
	 * 
	 * @throws AutoquoteBusinessException the business exception
	 */
	void callTheCreditScore(PolicyVersion policyVersion) throws AutoquoteBusinessException;
}
