package intact.lab.autoquote.backend.datamediator;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public final class DMConstants {
    private static final String COVERAGE_CD = "COVERAGE_CD";
    private static final String COVERAGE_TYPE_CD = "COVERAGE_TYPE_CD";
    private static final String COVERAGE_TYPE = "coverageType";
    private static final String COVERAGE_CODE = "coverageCode";
    private static final String INSURED_GROUP = "insuredGroup";
    private static final String ACTION_TAKEN = "actionTaken";
    private static final String ACTION_TAKEN_CD = "ACTION_TAKEN_CD";
    private static final String EFFECTIVE_DATE = "effectiveDate";
    private static final String EFFECTIVE_DT = "EFFECTIVE_DT";
    private static final String EXPIRY_DATE = "expiryDate";
    private static final String EXPIRY_DT = "EXPIRY_DT";
    private static final String RATING_DATE = "ratingDate";
    private static final String RATING_DT = "RATING_DT";
    private static final String PROVINCE_CD = "PROVINCE_CD";
    private static final String PROVINCE = "province";
    private static final String FACILITY_RISK_SHARING_POOL_SCORING = "facilityRiskSharingPoolScoring";
    private static final String CREATION_TS = "CREATION_TS";
    private static final String RSP_SCORING_QTY = "RSP_SCORING_QTY";
    private static final String COMPET_SCORE_WITHOUT_CR_QTY = "COMPET_SCORE_WITHOUT_CR_QTY";
    private static final String RET_SCORE_PURE_WITHOUT_CR_QTY = "RET_SCORE_PURE_WITHOUT_CR_QTY";
    private static final String CVI_PURE_WITHOUT_CREDIT_QTY = "CVI_PURE_WITHOUT_CREDIT_QTY";
    private static final String QUALITY_RISK_LEVEL_CD = "QUALITY_RISK_LEVEL_CD";
    private static final String CVI_PURE_FOR_UNDRWTR_REG_QTY = "CVI_PURE_FOR_UNDRWTR_REG_QTY";
    private static final String FULL_TERM_PREMIUM_AMT = "FULL_TERM_PREMIUM_AMT";
    private static final String CVI_PURE_FOR_UNDRWTR_PREF_QTY = "CVI_PURE_FOR_UNDRWTR_PREF_QTY";
    private static final String CVI_PURE_WITH_CREDIT_QTY = "CVI_PURE_WITH_CREDIT_QTY";
    private static final String CVI_ADJD_WITH_CREDIT_QTY = "CVI_ADJD_WITH_CREDIT_QTY";
    private static final String ADDITIONAL_RETURN_PREMIUM_AMT = "ADDITIONAL_RETURN_PREMIUM_AMT";
    private static final String RET_SCORE_PURE_WITH_CR_QTY = "RET_SCORE_PURE_WITH_CR_QTY";
    private static final String COMPET_SCORE_WITH_CR_QTY = "COMPET_SCORE_WITH_CR_QTY";
    private static final String CUSTOMER_VALUE_INDEX_PURE_WITH_CREDIT = "customerValueIndexPureWithCredit";
    private static final String CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_REGULAR = "customerValueIndexPureForUnderwritingRegular";
    private static final String CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_PREFERRED = "customerValueIndexPureForUnderwritingPreferred";
    private static final String ADDITIONAL_RETURN_PREMIUM = "additionalReturnPremium";
    private static final String CUSTOMER_VALUE_INDEX_ADJUSTED_WITH_CREDIT = "customerValueIndexAdjustedWithCredit";
    private static final String CUSTOMER_VALUE_INDEX_PURE_WITHOUT_CREDIT = "customerValueIndexPureWithoutCredit";
    private static final String FULL_TERM_PREMIUM = "fullTermPremium";
    private static final String COMPETITIVITY_SCORE_WITH_CREDIT = "competitivityScoreWithCredit";
    private static final String COMPETITIVITY_SCORE_WITHOUT_CREDIT = "competitivityScoreWithoutCredit";
    private static final String RETENTION_SCORE_PURE_WITHOUT_CREDIT = "retentionScorePureWithoutCredit";
    private static final String RETENTION_SCORE_PURE_WITH_CREDIT = "retentionScorePureWithCredit";
    private static final String QUALITY_RISK_LEVEL = "qualityRiskLevel";
    private static final String ANNUAL_PREMIUM_AMT = "ANNUAL_PREMIUM_AMT";
    private static final String CLAIMS_FREE_DISCOUNT_CD = "CLAIMS_FREE_DISCOUNT_CD";
    private static final String CLAIMS_FREE_DISCOUNT_SYS_CD = "CLAIMS_FREE_DISCOUNT_SYS_CD";
    private static final String DRIVING_RECORD_MOD_CD = "DRIVING_RECORD_MOD_CD";
    private static final String DRIVING_RECORD_SYS_CD = "DRIVING_RECORD_SYS_CD";
    private static final String DRIVING_RECORD_PR_TERM_CD = "DRIVING_RECORD_PR_TERM_CD";
    private static final String DRIVING_RECORD_CD = "DRIVING_RECORD_CD";
    private static final String RATING_CLASS_CD = "RATING_CLASS_CD";
    private static final String CLAIMS_FREE_DISCOUNT_MOD_CD = "CLAIMS_FREE_DISCOUNT_MOD_CD";
    private static final String DRIVING_RECORD_MODIFIED = "drivingRecordModified";
    private static final String CLAIMS_FREE_DISCOUNT = "claimsFreeDiscount";
    private static final String RATING_CLASS = "ratingClass";
    private static final String CLAIMS_FREE_DISCOUNT_SYSTEM = "claimsFreeDiscountSystem";
    private static final String CLAIMS_FREE_DISCOUNT_MODIFIED = "claimsFreeDiscountModified";
    private static final String DRIVING_RECORD_SYSTEM = "drivingRecordSystem";
    private static final String DRIVING_RECORD = "drivingRecord";
    private static final String DEDUCTIBLE_AMT = "DEDUCTIBLE_AMT";
    private static final String WEEKLY_BENEFITS_AMT = "WEEKLY_BENEFITS_AMT";
    private static final String COVERAGE_ELIGIBLE_IND = "COVERAGE_ELIGIBLE_IND";
    private static final String LMT_MED_EXPENSES_PER_PRSN_AMT = "LMT_MED_EXPENSES_PER_PRSN_AMT";
    private static final String VEH_RATE_GROUP_ADJUSTMENT_QTY = "VEH_RATE_GROUP_ADJUSTMENT_QTY";
    private static final String LIMIT_OF_INSURANCE_AMT = "LIMIT_OF_INSURANCE_AMT";
    private static final String LMT_MUTLTN_DEATH_INDEMNITY_AMT = "LMT_MUTLTN_DEATH_INDEMNITY_AMT";
    private static final String VEHICLE_RATE_GROUP_ADJUSTMENT = "vehicleRateGroupAdjustment";
    private static final String LIMIT_MUTILATION_AND_DEATH_INDEMNITY = "limitMutilationAndDeathIndemnity";
    private static final String WEEKLY_BENEFITS = "weeklyBenefits";
    private static final String DEDUCTIBLE_AMOUNT = "deductibleAmount";
    private static final String LIMIT_MEDICAL_EXPENSES_PER_PERSON = "limitMedicalExpensesPerPerson";
    private static final String LIMIT_OF_INSURANCE = "limitOfInsurance";
    private static final String COVERAGE_ELIGIBLE_IND2 = "coverageEligibleInd";

    public static final String[] plAdditionalInterestRoleAttributeNames = new String[]{"ADDITIONAL_INTEREST_TYPE_CD"};
    public static final String[] somAdditionalInterestRoleAttributeNames = new String[]{"typeOfAdditionalInterests"};
    public static final String[] plKindOfLossAttributeNames = new String[]{"KIND_OF_LOSS_CD", COVERAGE_TYPE_CD, COVERAGE_CD, "PAID_AMT"};
    public static final String[] somKindOfLossAttributeNames = new String[]{"kindOfLoss", COVERAGE_TYPE, COVERAGE_CODE, "amountPaid"};
    public static final String[] plClaimAttributeNames = new String[]{"CLAIM_TYPE_CD", "NATURE_OF_CLAIM_CD", "CLAIM_NBR", "CLAIM_STATUS_CD", "LOSS_DT", "CLAIM_AT_FAULT_IND", "CLAIM_CONSIDERED_CD", "LIABILITY_PCT", "CANCELLED_VEHICLE_CLASS_CD", "CLAIM_CATEGORY_CD", "CLAIM_CATEGORY_CHARGEABLE_IND"};
    public static final String[] somClaimAttributeNames = new String[]{"claimType", "natureOfClaim", "claimNumber", "status", "dateOfLoss", "claimAtFaultInd", "claimConsideredCode", "percentageOfLiability", "cancelledVehicleClass", "claimCategory", "claimCategoryChargeableInd"};
    public static final String[] plProducerRepositoryEntryAttributeNames = new String[]{"AGENT_NBR", "BRANCH_CD", "UNDERWRITING_TEAM_NBR", "MARKETING_TERRITORY_CD"};
    public static final String[] somProducerRepositoryEntryAttributeNames = new String[]{"agentNumber", "branch", "underwritingTeam", "marketingTerritory"};
    public static final String[] plGroupRepositoryEntryAttributeNames = new String[]{"PARTY_GROUP_TYPE_CD", "PARTY_GROUP_CD", "PARTY_SUB_GROUP_TYPE_CD", "PARTY_SUB_GROUP_CD", "INSURED_GROUP_CD"};
    public static final String[] somGroupRepositoryEntryAttributeNames = new String[]{"partyGroupType", "partyGroupCode", "partySubGroupType", "partySubGroupCode", INSURED_GROUP};
    public static final String[] plPartyGroupAttributeNames = new String[]{"MEMBER_TYPE_CD", "DIVISION_NBR", "MEMBER_NBR", "INTERNAL_BILLING_IND"};
    public static final String[] somPartyGroupAttributeNames = new String[]{"memberType", "divisionNumber", "memberNumber", "internalBillingInd"};
    public static final String[] plPartyRoleInRiskAttributeNames = new String[]{ACTION_TAKEN_CD, "DRIVER_TYPE_CD", "CREATION_DT", "AGE_QTY", "DRIVER_HAS_OWNED_VEHICLE_DT", "PRINCIPAL_DRIVER_SINCE_DT", "DRIVER_PERCENTAGE_USE_QTY"};
    public static final String[] somDriverAttributeNames = new String[]{ACTION_TAKEN, "typeOfDriver", "driverCreationDate", "age", "dateDriverHasOwnedVehicle", "principalDriverSince", "driverPercentageUse"};
    public static final String[] plPartyRoleInRiskOnOwnerAttributeNames = new String[]{ACTION_TAKEN_CD, "OWNER_TYPE_CD"};
    public static final String[] somOwnerAttributeNames = new String[]{ACTION_TAKEN, "ownerType"};
    public static final String[] plMunicipalityRepositoryEntryAttributeNames = new String[]{"MUNICIPAL_CD", "MUNICIPALITY_TXT", "MUNICIPALITY_STATUS_CD", PROVINCE_CD, "COUNTRY_CD"};
    public static final String[] somMunicipalityRepositoryEntryAttributeNames = new String[]{"municipalCode", "municipality", "municipalityStatus", PROVINCE, "country"};
    public static final String[] plMunicipalityDetailSpecificationAttributeNames = new String[]{"FCLTY_RSK_SHR_POOL_SCORING_QTY", "AUTOMOBILE_TERRITORY_STAT_CD", "MUNICIPAL_ADJUSTMENT_AUTO_CD", "AUTOMOBILE_TERRITORY_RATING_CD", "PROHIBITED_MNCPL_AUTO_IND", EFFECTIVE_DT, EXPIRY_DT, "AUT_TERR_LIAB_SCR_CD", "AUT_TERR_ACC_BNF_SCR_CD", "AUT_TERR_COLL_SCR_CD", "AUT_TERR_COMP_SCR_CD", "AUT_TERR_DIR_COMP_SCR_CD", "AUT_TERR_SCR_CD"};
    public static final String[] somMunicipalityDetailSpecAttributeNames = new String[]{FACILITY_RISK_SHARING_POOL_SCORING, "automobileTerritoryStat", "municipalAdjustmentAuto", "automobileTerritoryRating", "prohibitedMunicipalityIndAuto", EFFECTIVE_DATE, EXPIRY_DATE, "automobileTerritoryLiabilityScoring", "automobileTerritoryAccidentBenefitsScoring", "automobileTerritoryCollisionScoring", "automobileTerritoryComprehensiveScoring", "automobileTerritoryDirectCompensationScoring", "automobileTerritoryScoring"};
    public static final String[] plAddressAttributeNames = new String[]{ACTION_TAKEN_CD, "ADDRESS_TYPE_CD", "ADDRESS_USAGE_CD", "UNIT_TYPE_CD", "UNIT_NBR", "CIVIC_NBR", "STREET_NAME_TXT", "MUNICIPALITY_TXT", "UNFORMATTED_ADDRESS1_TXT", "UNFORMATTED_ADDRESS2_TXT", "UNFORMATTED_ADDRESS3_TXT", "POSTAL_CD", PROVINCE_CD, "COUNTRY_CD", "PROHIBITED_POSTAL_CODE_IND", "MUNICIPAL_SYS_CD", "MUNICIPAL_MOD_CD", "MUNICIPAL_CD", EFFECTIVE_DT, EXPIRY_DT, "ADDRESS_DELIVERY_MODE_CD", "ADDRESS_DELIVERY_MODE_NBR"};
    public static final String[] somAddressAttributeNames = new String[]{ACTION_TAKEN, "addressType", "addressUsage", "addressUnitType", "addressUnitNumber", "civicNumber", "streetName", "municipality", "unformattedAddress1", "unformattedAddress2", "unformattedAddress3", "postalCode", PROVINCE, "country", "prohibitedPostalCodeIndicator", "municipalCodeSystem", "municipalCodeModified", "municipalCode", EFFECTIVE_DATE, EXPIRY_DATE, "addressDeliveryMode", "addressDeliveryModeNumber"};
    public static final String[] plGeographicalAssessmentNames = new String[]{"CLM_ACC_BENEF_NGH_2500M_AMT", "AVG_DAILY_TEMP_DG_C_QTY", "AVG_NB_DY_FZ_THAW_0D_NV_MR_QTY", "AVG_NB_DY_FZ_THAW_2D_NV_MR_QTY", "AVG_NB_DY_LG_EV_EX_CLD_20D_QTY", "AVG_NB_DY_EV_EX_CLD_20D_QTY", "AVG_NB_EV_PR_YR_EX_CLD_20D_QTY", "CLM_BDLY_INJRY_NGH_2500M_AMT", "CLM_COLLISION_NGH_2500M_AMT", "CLM_COMP_NGH_2500M_AMT", "CRIM_AGNST_PERS_OR_PRP_SCR_QTY", "CLM_DIRECT_COMP_NGH_2500M_AMT", "MEDIAN_HOUSEHOLD_ANL_INCOM_QTY", "PERSON_MALES_PCT", "BREAK_AND_ENTER_QTY", "CLM_ACC_BENEF_NGH_2500M_QTY", "CLM_BDLY_INJRY_NGH_2500M_QTY", "CLM_COLLISION_NGH_2500M_QTY", "CLM_COMP_NGH_2500M_QTY", "CLM_DIRECT_COMP_NGH_2500M_QTY", "NB_YR_RAIN_RTRN_25MM_1HR_QTY", "PERSON_AGE_15_TO_24_PCT", "POPULATION_DENSITY_QTY", "POST_CD_CENTROID_LATITUDE_QTY", "POST_CD_CENTROID_LONGITUDE_QTY", "PRECIPIT_AVG_MM_BY_YEAR_QTY", "RAIN_INTENS_5_YR_RTRN_1HR_QTY", "RAINFALL_AVG_MM_BY_YEAR_QTY", "PERSON_SINGLE_PCT", "SNOW_INTENS_2_YR_RTRN_3HR_QTY", "SNOWFALL_AVG_CM_BY_YEAR_QTY", "THEFT_VEHICLE_SCORE_QTY", "VIOLENT_CRIMES_SCORE_QTY", "WORKFORCE_EMPLOYMENT_PCT"};
    public static final String[] somGeographicalAssessmentNames = new String[]{"accidentBenefitNeighborhood2500MetersClaimAmount", "averageDailyTemperatureDegreeCelsius", "averageNumberOfDaysFreezeAndThaw0CFromNovToMarch", "averageNumberOfDaysFreezeAndThaw2CFromNovToMarch", "averageNumberOfDaysOfLongEventExtraColdMinus20C", "averageNumberOfDaysPerEventExtraColdMinus20C", "averageNumberOfEventsPerYearExtraColdMinus20C", "bodilyInjuryNeighborhood2500MetersClaimAmount", "collisionNeighborhood2500MetersClaimAmount", "comprehensiveNeighborhood2500MetersClaimAmount", "crimesAgainstPropertyOrPersonScore", "directCompensationNeighborhood2500MetersClaimAmount", "householdAnnualIncomeMedian", "malePercentage", "numberOfBreakAndEnter", "numberOfClaimsAccidentBenefitNeighborhood2500Meters", "numberOfClaimsBodilyInjuryNeighborhood2500Meters", "numberOfClaimsCollisionNeighborhood2500Meters", "numberOfClaimsComprehensiveNeighborhood2500Meters", "numberOfClaimsDirectCompensationNeighborhood2500Meters", "numberOfYearsRainReturn25mmFor1Hour", "peopleAged15To24Percentage", "populationDensity", "postalCodeCentroidLatitude", "postalCodeCentroidLongitude", "precipitationAverageByYear", "rainIntensityPerHourWith5YearsReturnFor1Hour", "rainfallAverageByYear", "singlePeoplePercentage", "snowIntensityPerHourWith2YearsReturnFor3Hours", "snowfallAverageByYear", "vehicleTheftScore", "violentCrimesScore", "workForceEmploymentRate"};
    public static final String[] plPhoneAttributeNames = new String[]{"PHONE_TYPE_CD", "AREA_CODE_NBR", "PHONE_NBR", "EXTENSION_NBR", "PREFERRED_PHONE_IND", EFFECTIVE_DT, EXPIRY_DT};
    public static final String[] somPhoneAttributeNames = new String[]{"phoneType", "phoneAreaCode", "phoneNumber", "phoneExtension", "preferredPhoneInd", EFFECTIVE_DATE, EXPIRY_DATE};
    public static final String[] plCreditScoreAttributeNames = new String[]{"CREDIT_SCORE_QTY", "CREDIT_SCORE_DT", "REFERENCE_DT", "CREDIT_SCORE_TYPE_CD", "REPORT_SEARCH_TYPE_CD", "SERVICE_STATUS_CD", "USER_ID", "LAST_MOVE_DT", "CREDIT_SCORE_REGULAR_IND"};
    public static final String[] somCreditScoreAttributeNames = new String[]{"creditScore", "creditScoreDate", "referenceDate", "creditScoreType", "creditScoreReportSearchType", "creditScoreServiceStatus", "userId", "dateOfLastMove", "creditScoreRegularInd"};
    public static final String[] plPartyRelationAttributeNames = new String[]{"NATURE_TO_CD", "NATURE_FROM_CD"};
    public static final String[] somPartyRelationAttributeNames = new String[]{"natureTo", "natureFrom"};
    public static final String[] plDriverLicenceClassAttributeNames = new String[]{"DRIVER_LICENSE_CLASS_CD", EFFECTIVE_DT};
    public static final String[] somDriverLicenceClassAttributeNames = new String[]{"driverLicenseClass", EFFECTIVE_DATE};
    public static final String[] plConvictionAttributeNames = new String[]{"CONVICTION_SEQ", "CONVICTION_CD", "CONVICTION_TYPE_CD", "CONVICTION_TYPE_RSP_CD", CREATION_TS, "CONVICTION_DT", "SUSPENSION_END_DT", "CONVICTION_CHARGEABILITY_IND"};
    public static final String[] somConvictionAttributeNames = new String[]{"convictionSequence", "convictionCode", "convictionType", "convictionTypeRsp", "creationDate", "convictionDate", "suspensionEndDate", "convictionChargeabilityInd"};
    public static final String[] plDriverComplementInfoAttributeNames = new String[]{"DRIVER_SEQ", EFFECTIVE_DT, "LAST_REQUEST_TO_MVRSAAQ_DT", "SUBMIT_REQUEST_TO_MVRSAAQ_IND", "RELATIONSHIP_TO_NAMED_INSD_CD", "RESIDE_WITH_PARENTS_IND", "DRIVER_TRAINING_IND", "TRAINING_SCHOOL_NAME_TXT", "LICENSE_JURISDICTION_CD", "LICENSE_NBR", "DRVR_LICENSE_OBTAINED_DT", "INSURED_WITH_COMPANY_DT", "NBR_KM_TO_GO_TO_SCHOOL_QTY", "NBR_MONTHS_ANLY_STDY_AWAY_QTY", "MVR_SAAQ_AUTHORIZATION_CD", "NBR_MINOR_CNVCTN_3YRS_QTY", "NBR_MAJOR_CNVCTN_3YRS_QTY", "NBR_SEVERE_CNVCTN_3YRS_QTY", "PARENTS_INSRD_WITH_COMPANY_IND", ACTION_TAKEN_CD, "DRIVER_LICENSE_TYPE_CD", "SCHOOL_MORE_100KMS_PAR_RES_IND", "OBT_G2_OR_G_CLASS_12_MTH_IND", "GRID_LEVEL_QTY", "GRID_LEVEL_DT", "PRIOR_GRID_LEVEL_QTY", "GRID_LEVEL_SYS_QTY", "GRID_LEVEL_MOD_QTY", "DRIVING_DURING_WKEND_ONLY_IND", "CONTINUOUSLY_INSURED_SINCE_DT", "UBI_STATUS_CD", "INTERESTED_BY_UBI_IND", "UBI_DISCOUNT_SYS_PCT", "UBI_DISCOUNT_MOD_PCT", "UBI_DISCOUNT_PCT", "UBI_SERVICE_PROVIDER_CD", "UBI_PROGRAM_VERSION_CD"};
    public static final String[] somDriverComplementInfoAttributeNames = new String[]{"driverSequence",EFFECTIVE_DATE, "dateOfLastRequestToMVRSAAQ", "submitRequestToMVRSAAQ", "relationshipToNamedInsured", "resideWithParentsInd", "driverTrainingInd", "trainingSchoolName", "licenseJurisdiction", "licenseNumber", "dateDriverLicenseObtained", "dateInsuredWithCompany", "numberOfKmToGoToSchool", "numberOfmonthsAnnuallyStudyingAway", "mvrsaaqAuthorizationCode", "numberOfMinorConvictions3Years", "numberOfMajorConvictions3Years", "numberOfSevereConvictions3Years", "parentsInsuredWithCompanyInd", ACTION_TAKEN, "driverLicenseType", "distantStudentInd", "obtainedG2orGclassInLast12MonthsInd", "gridLevel", "gridLevelDate", "priorGridLevel", "gridLevelSystem", "gridLevelModified", "drivingDuringWeekendOnlyInd", "continuouslyInsuredSince", "ubiStatus", "interestedByUbiInd", "ubiDiscountPercentageSystem", "ubiDiscountPercentageModified", "ubiDiscountPercentage", "ubiServiceProviderCode", "ubiProgramVersionCode"};
    public static final String[] plConsentAttributeNames = new String[]{"COMMUNICATION_CHANNEL_CD", "CONSENT_TYPE_CD", "CONSENT_IND", CREATION_TS, EFFECTIVE_DT, EXPIRY_DT};
    public static final String[] somConsentAttributeNames = new String[]{"communicationChannel", "consentType", "consentInd", "creationDateTime", EFFECTIVE_DATE, EXPIRY_DATE};
    public static final String[] plPartyAttributeNames = new String[]{ACTION_TAKEN_CD, "PARTY_TYPE_CD", "LANGUAGE_OF_COMMUNICATION_CD", "LAST_MOVE_DT", "CUR_STDY_CDN_COLLG_UNVRSTY_IND", "SOCIAL_INSURANCE_NBR", "SEX_CD", "OCCUPATION_CD", "MARITAL_STATUS_CD", "BIRTH_DT", "DISABILITY_DESC", "DISABILITY_DT", "RETIRED_IND", "FIRST_NAME_TXT", "LAST_NAME_TXT", "MIDDLE_NAME_TXT", "SUFFIX_NAME_TXT", "UNSTRUCTURED_NAME_TXT", "CLIENT_ELIGIBILITY_LEVEL_CD", "CREDIT_SCR_CLIENT_ELGBLTY_IND", "ADDITIONAL_INTEREST_SEQ", "ING_STAFF_IND", "EMPLOYEE_NBR", "HOLDER_DIPLOMA_CDN_UNVRSTY_IND", "STUDENT_GRADE_CD", "NBR_OF_INSUF_FUNDS_TO_DATE_QTY", "CRIMINAL_RECORD_IND"};
    public static final String[] somPartyAttributeNames = new String[]{ACTION_TAKEN, "partyType", "languageOfCommunication", "dateOfLastMove", "currentlyStudyCanadianCollegeOrUniversityInd", "socialInsuranceNumber", "sex", "occupationCode", "maritalStatus", "dateOfBirth", "disabilityDescription", "disabilityDate", "retiredInd", "firstName", "lastName", "middleName", "suffixName", "unstructuredName", "clientEligibilityLevel", "creditScoreClientEligibilityInd", "additionalInterestSequence", "ingStaffInd", "employeeNumber", "holderOfDiplomaFromCanadianUniversityInd", "studentGradeCode", "numberOfInsufficientFundsToDate", "criminalRecordStatus"};
    public static final String[] plPolicyHolderAttributeNames = new String[]{"POLICY_HOLDER_TYPE_CD", "NBR_STABILITY_MONTHS_QTY"};
    public static final String[] somPolicyHolderAttributeNames = new String[]{"policyHolderType", "numberOfStabilityMonths"};
    public static final String[] plAccountAttributeNames = new String[]{"FINANCIAL_INSTITUTION_NBR", "ROUTING_NBR", "ACCOUNT_NBR", "BANK_ACCOUNT_HOLDER_NAME_TXT", "CREDIT_CARD_COMPANY_CD", "CREDIT_CARD_ACCOUNT_NBR", "CREDIT_CARD_EXPIRY_DT", "CREDIT_CARD_HOLDER_NAME_TXT", "CREDIT_CARD_TOKEN_ID", "CREDIT_CARD_ACCOUNT_MASK_NBR"};
    public static final String[] somAccountAttributeNames = new String[]{"financialInstitutionNumber", "routingNumber", "bankAccountNumber", "bankAccountHolderName", "creditCardCompanyCode", "creditCardAccountNumber", "creditCardExpiryDate", "creditCardHolderName", "creditCardTokenId", "creditCardAccountNumberMask"};
    public static final String[] plPaymentScheduleAttributeNames = new String[]{"PAYMENT_DT", "PAYMENT_AMT", "PAYMENT_SCHEDULE_SEQ"};
    public static final String[] somPaymentScheduleAttributeNames = new String[]{"paymentDate", "paymentAmount", "paymentScheduleSequence"};
    public static final String[] plBillingAttributeNames = new String[]{"BILLING_METHOD_CD", "BILLING_PLAN_CD", "METHOD_OF_PAYMENT_CD", "NBR_OF_PAYMENT_INSTLM_QTY", "PAYMENT_PLAN_CD", "SERVICE_CHARGE_AMT", "PROVINCIAL_SALES_TAX_AMT", "PAYMENT_DEPOSIT_AMT", "REMAINING_PAYMENT_AMT", "DAY_OF_PAYMENT_NBR"};
    public static final String[] somBillingAttributeNames = new String[]{"billingMethod", "billingPlan", "methodOfPayment", "numberOfPaymentInstallments", "paymentPlan", "serviceCharge", "provincialSalesTax", "paymentDeposit", "remainingPaymentAmount", "dayOfPayment"};
    public static final String[] plTrailerAttributeNames = new String[]{"TRAILER_YR", "TRAILER_MAKE_TXT", "TRAILER_MODEL_TXT", "TRAILER_CD"};
    public static final String[] somTrailerAttributeNames = new String[]{"trailerYear", "trailerMake", "trailerModel", "trailerCode"};
    public static final String[] plVehicleRepositoryEntryAttributeNames = new String[]{"VEHICLE_CD", "VEHICLE_CD", "VEHICLE_MAKE_ENG_TXT", "VEHICLE_MAKE_FRE_TXT", "VEHICLE_MODEL_ENG_TXT", "VEHICLE_MODEL_FRE_TXT", "VEH_MAKE_MODEL_ABRVTN_ENG_TXT", "VEH_MAKE_MODEL_ABRVTN_FRE_TXT"};
    public static final String[] somVehicleRepositoryEntryAttributeNames = new String[]{"vehicleCode", "extendedVehicleCode", "vehicleMake", "vehicleMake", "vehicleModel", "vehicleModel", "vehicleMakeAndModelAbbreviation", "vehicleMakeAndModelAbbreviation"};
    public static final Map<String, String> som2plVehicleDetails2VehDetSpecRepEntry;
    public static final String[] plVehicleDetailSpecificationRepositoryEntryAttributeNames;
    public static final String[] somVehicleDetailSpecificationRepositoryEntryAttributeNames;
    public static final String[] plAntiTheftDevicesAttributeNames;
    public static final String[] somAntiTheftDevicesAttributeNames;
    public static final String[] plVehicleEquipmentAttributeNames;
    public static final String[] somVehicleEquipmentAttributeNames;
    public static final String[] plVehicleAttributeNames;
    public static final String[] somVehicleAttributeNames;
    public static final String[] plInsuranceRiskAttributeNames;
    public static final String[] somInsuranceRiskAttributeNames;
    public static final String[] plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames;
    public static final String[] somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames;
    public static final String[] plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames;
    public static final String[] somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames;
    public static final String[] plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
    public static final String[] somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
    public static final String[] plInsuranceRiskOfferOverridesOnVehicleAttributeNames;
    public static final String[] somInsuranceRiskOfferOverridesOnVehicleAttributeNames;
    public static final String[] plRatingRiskAttributeNames;
    public static final String[] somRatingRiskAttributeNames;
    public static final String[] plRatingRiskOfferAttributeNames;
    public static final String[] somRatingRiskOfferAttributeNames;
    public static final String[] plRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames;
    public static final String[] somRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames;
    public static final String[] plGroupRepositoryEntryOnRatingRiskAttributeNames;
    public static final String[] somGroupRepositoryEntryOnRatingRiskAttributeNames;
    public static final String[] plGroupRepositoryEntryRootOverridesRatingRiskAttributeNames;
    public static final String[] somGroupRepositoryEntryRootOverridesRatingRiskAttributeNames;
    public static final String[] plCoverageAttributeNames;
    public static final String[] somCoverageAttributeNames;
    public static final String[] plCoverageOfferAttributeNames;
    public static final String[] somCoverageOfferAttributeNames;
    public static final String[] plCoverageOfferReferenceOverridesOnCoverageOffersAttributeNames;
    public static final String[] somCoverageOfferReferenceOverridesOnCoverageOffersAttributeNames;
    public static final String[] plCoveragePremiumAttributeNames;
    public static final String[] somCoveragePremiumAttributeNames;
    public static final String[] plCoveragePremiumOfferAttributeNames;
    public static final String[] somCoveragePremiumOfferAttributeNames;
    public static final String[] plSubCoveragePremiumOfferAttributeNames;
    public static final String[] somSubCoveragePremiumOfferAttributeNames;
    public static final String[] plCoverageRepositoryEntryFromCoverageAttributeNames;
    public static final String[] somCoverageRepositoryEntryFromCoverageAttributeNames;
    public static final String[] plCoverageRepositoryEntryAttributeNames;
    public static final String[] somCoverageRepositoryEntryAttributeNames;
    public static final String[] plPolicyAdditionalCoverageAttributeNames;
    public static final String[] somPolicyAdditionalCoverageAttributeNames;
    public static final String[] plPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames;
    public static final String[] somPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames;
    public static final String[] plPriorCarrierPolicyInfoAttributeNames;
    public static final String[] somPriorCarrierPolicyInfoAttributeNames;
    public static final String[] plMessageRepositoryEntryAttributeNames;
    public static final String[] somMessageRepositoryEntryAttributeNames;
    public static final String[] plTransactionalMessageElementAttributeNames;
    public static final String[] somTransactionalMessageElementAttributeNames;
    public static final String[] plTransactionalMessageAttributeNames;
    public static final String[] somTransactionalMessageAttributeNames;
    public static final String[] plCredentialAttributeNames;
    public static final String[] somCredentialAttributeNames;
    public static final String[] plBusinessTransactionActivityAttributeNames;
    public static final String[] somBusinessTransactionActivityAttributeNames;
    public static final String[] plBusinessTransactionAttributeNames;
    public static final String[] somBusinessTransactionAttributeNames;
    public static final String[] plRelatedInsurancePolicyAttributeNames;
    public static final String[] somRelatedInsurancePolicyAttributeNames;
    public static final String[] plManufacturingContextAttributeNames;
    public static final String[] somManufacturingContextAttributeNames;
    public static final String[] plInsurancePolicyAttributeNames;
    public static final String[] somInsurancePolicyAttributeNames;
    public static final String[] plPolicyVersionAttributeNames;
    public static final String[] somPolicyVersionAttributeNames;
    public static final String[] plReferenceDateAttributeNames;
    public static final String[] somReferenceDateAttributeNames;
    public static final String[] plMultiplicativeRatingFactorFromBasicCoverageAttributeNames;
    public static final String[] somMultRatingFactorFromBasicCoverageAttributeNames;
    public static final String[] plMultiplicativeRatingFactorFromNonBasicCoverageAttributeNames;
    public static final String[] somMultRatingFactorFromNonBasicCoverageAttributeNames;
    public static final String[] plAffinityGroupRepositoryEntryAttributeNames;
    public static final String[] somAffinityGroupRepositoryEntryAttributeNames;
    public static final String[] plDiagnosticAutomatedAdviceAttributeNames;
    public static final String[] somDiagnosticAutomatedAdviceAttributeNames;
    public static final String[] plDiagnosticAutomatedAdviceRepositoryEntryAttributeNames;
    public static final String[] somDiagnosticAutomatedAdviceRepositoryEntryAttributeNames;
    public static final String[] plMarketSegmentAttributeNames;
    public static final String[] somMarketSegmentAttributeNames;
    public static final String[] plPartnershipAttributeNames;
    public static final String[] somPartnershipAttributeNames;
    public static final String[] som2somWhiteList;
    public static final String[] plCoverageOptionAttributeNames;
    public static final String[] somCoverageOptionAttributeNames;
    public static final String[] plLegacyRatingInfoByPostalCodes;
    public static final String[] somLegacyRatingInfoByPostalCodes;

    private DMConstants() {
    }

    static {
        Map<String, String> map = new HashMap();
        map.put("rateGroupAccidentBenefitVicc", "RATE_GROUP_ACDNT_BNFT_VICC_CD");
        map.put("rateGroupCollisionVicc", "RATE_GROUP_COLLISION_VICC_CD");
        map.put("rateGroupComprehensiveVicc", "RATE_GROUP_COMP_VICC_CD");
        map.put("rateGroupLiabilityPropertyDamageVicc", "RATE_GROUP_LIA_PRP_DAM_VICC_CD");
        map.put("rateGroupCollisionViccPure", "RATE_GROUP_COLLISION_VICC_P_CD");
        map.put("rateGroupLiabilityPropertyDamageViccPure", "RATE_GROUP_LIA_PRP_DAM_VI_P_CD");
        som2plVehicleDetails2VehDetSpecRepEntry = Collections.unmodifiableMap(map);
        plVehicleDetailSpecificationRepositoryEntryAttributeNames = new String[]{"VEHICLE_YR", "VEHICLE_CATEGORY_CD", "RESTRICTED_VEHICLE_IND", "REPLACEMENT_COST_IND", "COMMERCIAL_CLASS_ELGBLTY_IND", "FACILITY_RSP_SCORING_QTY", "RSP_ADJ_SCENARIO_VEH_NEW_B_QTY", "RSP_ADJ_SCENARIO_VEH_STD_QTY", "RATE_GROUP_CLR_LIABILITY_CD", "RATE_GROUP_CLR_ACDNT_BNFT_CD", "RATE_GROUP_CLR_COLLISION_CD", "RATE_GROUP_CLR_COMBINED_CD", "RATE_GROUP_CLR_COMP_CD", "RATE_GROUP_CLR_DCPD_CD", "RATE_GROUP_MSRP_CD", "RATE_GROUP_MOD_CLR_COMBINED_CD", "RATE_GROUP_MOD_CLR_CMBND_NB_CD", "ANTI_THEFT_DEVICE_REQUIRED_CD", "NBR_OF_CYLINDERS_QTY", "MIN_DEDUCTIBLE_MNDTRY_B3B4_IND", "MIN_DEDUCTIBLE_MNDTRY_C2C3_IND", "VEHICLE_INSPECTION_IND", EFFECTIVE_DT, EXPIRY_DT, "HORSEPOWER_QTY", "RETAIL_PRICE_WITH_GST_AMT", "WHEELBASE_QTY", "AIRBAGS_CD", "ABS_BRAKES_CD", "AUDIBLE_ALARM_CD", "CUT_OFF_SYSTEM_CD", "SECURITY_KEY_SYSTEM_CD", "IBC_APPROVED_CD", "ENGINE_CYLINDER_CD", "IBC_MARKET_CD", "VEHICLE_SIZE_CD", "VEHICLE_GENERATION_CD", "FUEL_USED_BY_VEHICLE_CD", "ENGINE_FORCE_INDUCTION_CD", "ENGINE_HYBRID_CD", "TRACTION_CONTROL_CD", "STABILITY_CONTROL_CD", "DRIVE_TRAIN_CD", "RATE_GROUP_ACDNT_BNFT_VICC_CD", "RATE_GROUP_COLLISION_VICC_CD", "RATE_GROUP_COMP_VICC_CD", "RATE_GROUP_LIA_PRP_DAM_VICC_CD", "RATE_GROUP_COLLISION_VICC_P_CD", "RATE_GROUP_LIA_PRP_DAM_VI_P_CD", "FRWRD_COLL_MTGTN_LW_SPEED_CD", "FRWRD_COLL_MTGTN_ALL_SPEED_CD"};
        somVehicleDetailSpecificationRepositoryEntryAttributeNames = new String[]{"vehicleYear", "vehicleCategory", "restrictedVehicleInd", "replacementCostInd", "commercialClassEligibilityInd", FACILITY_RISK_SHARING_POOL_SCORING, "rspAdjustmentScenarioVehicleNewBusiness", "rspAdjustmentScenarioVehicleStandard", "rateGroupClearLiability", "rateGroupClearAccidentBenefit", "rateGroupClearCollision", "rateGroupClearCombined", "rateGroupClearComprehensive", "rateGroupClearDcpd", "rateGroupMsrp", "rateGroupModClearCombined", "rateGroupModClearCombinedNewBusiness", "antiTheftDeviceRequired", "numberOfCylinders", "minimumDeductibleMandatoryIndB3B4", "minimumDeductibleMandatoryIndC2C3", "vehicleInspectionInd", EFFECTIVE_DATE, EXPIRY_DATE, "horsepower", "retailPriceWithGst", "wheelbase", "airbags", "absBrakes", "audibleAlarm", "cutOffSystem", "securityKeySystem", "ibcApproved", "engineCylinder", "ibcMarket", "vehicleSize", "vehicleGeneration", "fuelUsedByVehicle", "engineForceInduction", "engineHybrid", "tractionControl", "stabilityControl", "driveTrain", "rateGroupAccidentBenefitVicc", "rateGroupCollisionVicc", "rateGroupComprehensiveVicc", "rateGroupLiabilityPropertyDamageVicc", "rateGroupCollisionViccPure", "rateGroupLiabilityPropertyDamageViccPure", "forwardCollisionMitigationLowSpeed", "forwardCollisionMitigationAllSpeed"};
        plAntiTheftDevicesAttributeNames = new String[]{"DEVICE_CATEGORY_CD", "DEVICE_MODEL_CD", ACTION_TAKEN_CD};
        somAntiTheftDevicesAttributeNames = new String[]{"deviceCategory", "deviceModel", ACTION_TAKEN};
        plVehicleEquipmentAttributeNames = new String[]{"EQUIPMENT_TYPE_CD", "EQUIPMENT_VALUE_AMT"};
        somVehicleEquipmentAttributeNames = new String[]{"equipmentType", "equipmentValue"};
        plVehicleAttributeNames = new String[]{"LIST_PRICE_NEW_AMT", "VEHICLE_BODY_TYPE_CD", "TYPE_MACHINERY_EQUIP_CD", "TYPE_MACHINERY_EQUIP_ADD_CD", "VEHICLE_IDENTIFICATION_NBR", "VEHICLE_PURCHASE_DT", "VEHICLE_PURCHASE_PRICE_AMT", "USE_OF_VEHICLE_CD", "VEHICLE_USAGE_DESC", "OTHER_PROVINCE_OF_USE_CD", "USE_OUTSIDE_OF_PROVINCE_PCT", "PURPOSE_USE_OUTSIDE_OF_PV_CD", "CONDITION_VEH_WHEN_BOUGHT_CD", "NBR_OF_KM_DRIVE_TO_WORK_QTY", "ANNUAL_KM_QTY", "ANNUAL_BUSINESS_KM_QTY", "ODOMETER_READING_QTY", "FUEL_USED_BY_VEHICLE_CD", "ADDL_EQPMNT_OR_MDFCTN_IND", "VALUE_OF_VEH_MODIFICATION_AMT", "VALUE_OF_CUSTOM_PAINT_JOB_AMT", "VEH_MODIFICATION_RESTRICT_IND", "VEHICLE_AGE_QTY", "LEASED_VEHICLE_IND", "REPLACEMENT_COST_PROOF_IND", "UNREPAIRED_DAMAGE_IND", "RCRTNL_MOTORCYCLE_DISCOUNT_IND", "CARRY_EXPL_RADIOACTIVE_IND", "ALARM_SYSTEM_IND", "CUT_OFF_SWITCH_IND", "INTENSIVE_ENGRAVING_IND", "REMOTE_TRACKING_SYSTEM_IND", "VEHICLE_RATE_GROUP_CD", "VEH_RT_GRP_LBLTY_BDLY_INJRY_CD", "VEH_RT_GRP_LBLTY_PRPTY_DMD_CD", "VEH_RT_GRP_ACDNT_BNFT_CD", "VEH_RT_GRP_ALL_PERILS_CD", "VEH_RT_GRP_COLLISION_CD", "VEH_RT_GRP_COMPREHENSIVE_CD", "VEH_RT_GRP_SPECIFIED_PERILS_CD", "VEH_RT_GRP_LBLTY_CD", "VEH_RT_GRP_MEDICAL_EXPENSES_CD", "VEH_RT_GRP_TOTAL_DISABILITY_CD", "NBR_KM_USE_OUTSIDE_PV_CD", "NBR_KM_USE_OUTSIDE_PV_PL_BS_CD", "HIGH_VALUE_VEHICLE_MOD_IND", "PERFORMANCE_VEHICLE_MOD_IND", "PARKING_TYPE_CD", "INTENSIVE_ENGRAVING_IND", "VEHICLE_NET_WEIGHT_QTY", "UBI_ELIGIBILITY_IND", "NORMAL_RADIUS_OF_OPERATION_QTY", "GROSS_VEHICLE_WEIGHT_QTY", "VEH_RT_GRP_BY_VALUE_IND", "EST_MD_HAUL_NB_DAY_PR_MTH_QTY", "MAX_RADIUS_OF_OPERATION_QTY", "VEHICLE_BUSINESS_USE_CD"};
        somVehicleAttributeNames = new String[]{"listPriceNew", "vehicleBodyType", "typeOfMachineryEquipmentCode", "typeOfMachineryEquipmentCodeAdditional", "vehicleIdentificationNumber", "vehiclePurchaseDate", "vehiclePurchasePrice", "useOfVehicle", "vehicleUsageDescription", "otherProvinceOfUse", "percentageUseOutsideOfProvince", "purposeUseOutsideOfProvince", "conditionOfVehicleWhenBought", "numberOfKilometersToDriveToWork", "annualKilometers", "annualBusinessKms", "odometerReading", "fuelUsedByVehicle", "indicatorOfAdditionalEquipOrModif", "valueOfVehicleModification", "valueOfCustomPaintJob", "vehicleChangeRestrictionInd", "vehicleAge", "leasedVehicleInd", "replacementCostProofInd", "unrepairedDamageInd", "recreationalMotorcycleDiscountInd", "carryExplosiveRadioactiveInd", "alarmSystemInd", "cutOffSwitchInd", "intensiveEngravingInd", "remoteTrackingSystemInd", "vehicleRateGroup", "vehicleRateGroupLiabilityBodilyInjury", "vehicleRateGroupLiabilityPropertyDamage", "vehicleRateGroupAccidentBenefit", "vehicleRateGroupAllPerils", "vehicleRateGroupCollision", "vehicleRateGroupComprehensive", "vehicleRateGroupSpecifiedPerils", "vehicleRateGroupLiability", "vehicleRateGroupMedicalExpenses", "vehicleRateGroupTotalDisability", "numberOfKilometersUseOutsideProvinceCode", "numberOfKilometersUseOutsideProvinceForPleasureOrBusCode", "highValueVehicleModificationInd", "performanceVehicleModificationInd", "parkingType", "engravingInd", "vehicleNetWeight", "ubiEligibilityInd", "normalRadiusOfOperationForVehicleKms", "grossVehicleWeightCommercial", "vehicleRateGroupByValueInd", "estimatedIncidentalMidHaulNumberOfDaysPerMonth", "maximumRadiusOfOperationForVehicleKms", "vehicleBusinessUse"};
        plInsuranceRiskAttributeNames = new String[]{ACTION_TAKEN_CD, "RISK_TYPE_CD", "RISK_SUBTYPE_CD", "INSURANCE_RISK_SEQ", "PROOF_DRIVING_RECORD5_IND", "PROOF_DRIVING_RECORD5_OCC_IND", "GOOD_DRIVER_CD", "GOOD_DRIVER_MOD_CD", "CREATION_DT", EFFECTIVE_DT, EXPIRY_DT, RATING_DT, "FLEX_PCT", "NBR_STABILITY_MONTHS_QTY", "STABILITY_DISCOUNT_SYS_IND", "STABILITY_DISCOUNT_MOD_IND", "STABILITY_DISCOUNT_IND", "TERRITORY_STAT_SYS_CD", "TERRITORY_STAT_MOD_CD", "TERRITORY_STAT_CD", "MUNICIPAL_ADJUSTMENT_SYS_CD", "MUNICIPAL_ADJUSTMENT_MOD_CD", "MUNICIPAL_ADJUSTMENT_CD", "DEDUCTIBLE_DISCOUNT_SYS_AMT", "DEDUCTIBLE_DISCOUNT_MOD_AMT", "DEDUCTIBLE_DISCOUNT_AMT", "CAPPING_PCT", QUALITY_RISK_LEVEL_CD, FULL_TERM_PREMIUM_AMT, ADDITIONAL_RETURN_PREMIUM_AMT, "RSN_ABS_THIRD_PRTY_LBL_CVRG_CD", "RSP_TRANSFER_TYPE_CD", "RSP_DECISION_ORIGIN_CD", "RSP_MENTION_AT_RENEWAL_CD", "RSP_REASON_TXT", "COMPETITIVITY_ADJUSTMENT_PCT", "RETENTION_BAND_CD", RET_SCORE_PURE_WITH_CR_QTY, RET_SCORE_PURE_WITHOUT_CR_QTY, CVI_PURE_WITH_CREDIT_QTY, CVI_PURE_WITHOUT_CREDIT_QTY, "VEHICLE_CLASS_SYS_CD", "VEHICLE_CLASS_MOD_CD", "VEHICLE_CLASS_CD", "VEHICLE_CLASS_OCCASIONAL_CD", "LICENSE_SUSPENSION_PRINC_IND", COMPET_SCORE_WITH_CR_QTY, COMPET_SCORE_WITHOUT_CR_QTY, RSP_SCORING_QTY, CVI_PURE_FOR_UNDRWTR_PREF_QTY, CVI_PURE_FOR_UNDRWTR_REG_QTY, "PRINC_DRVR_HIGH_RATED_VEH_IND", CVI_ADJD_WITH_CREDIT_QTY, "RATING_TERRITORY_CD", "MULT_VEH_DSCNT_ELIGIBILITY_IND", "CODING_CLASS_CD", "VALUATION_AMT"};
        somInsuranceRiskAttributeNames = new String[]{ACTION_TAKEN, "riskType", "riskSubtype", "insuranceRiskSequence", "indicatorProofDrivingRecord5", "indicatorProofDrivingRecord5Occasional", "goodDriverInd", "goodDriverIndModified", "creationDate", EFFECTIVE_DATE, EXPIRY_DATE, RATING_DATE, "flexPercentage", "numberOfStabilityMonths", "stabilityDiscountIndSystem", "stabilityDiscountIndModified", "stabilityDiscountInd", "territoryStatSystem", "territoryStatModified", "territoryStat", "municipalAdjustmentSystem", "municipalAdjustmentModified", "municipalAdjustment", "deductibleDiscountAmountSystem", "deductibleDiscountAmountModified", "deductibleDiscountAmount", "cappingPercentage", QUALITY_RISK_LEVEL, FULL_TERM_PREMIUM, ADDITIONAL_RETURN_PREMIUM, "reasonForAbsenceOfThirdPartyLiabilityCoverage", "rspTransfertType", "rspDecisionOrigin", "rspMentionAtRenewal", "rspReason", "competitivityAdjustmentPercentage", "retentionBand", RETENTION_SCORE_PURE_WITH_CREDIT, RETENTION_SCORE_PURE_WITHOUT_CREDIT, CUSTOMER_VALUE_INDEX_PURE_WITH_CREDIT, CUSTOMER_VALUE_INDEX_PURE_WITHOUT_CREDIT, "vehicleClassSystem", "vehicleClassModified", "vehicleClass", "vehicleClassOccasional", "licenseSuspensionIndPrincipal", COMPETITIVITY_SCORE_WITH_CREDIT, COMPETITIVITY_SCORE_WITHOUT_CREDIT, FACILITY_RISK_SHARING_POOL_SCORING, CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_PREFERRED, CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_REGULAR, "principalDriverHighestRatedVehicleInd", CUSTOMER_VALUE_INDEX_ADJUSTED_WITH_CREDIT, "ratingTerritory", "multiVehicleDiscountEligibilityInd", "codingClass", "valuationAmount"};
        plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames = new String[]{"CAPPING_PCT", "SCORING_ADJUSTMENT_PCT", "COMPETITIVITY_ADJUSTMENT_PCT", "FLEX_PCT", "RETENTION_BAND_CD"};
        somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames = new String[]{"cappingPercentage", "scoringAdjustmentPercentage", "competitivityAdjustmentPercentage", "flexPercentage", "retentionBand"};
        plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames = new String[]{CVI_PURE_WITH_CREDIT_QTY, CVI_PURE_WITHOUT_CREDIT_QTY, QUALITY_RISK_LEVEL_CD, RSP_SCORING_QTY, RET_SCORE_PURE_WITH_CR_QTY, RET_SCORE_PURE_WITHOUT_CR_QTY, COMPET_SCORE_WITH_CR_QTY, COMPET_SCORE_WITHOUT_CR_QTY, CVI_PURE_FOR_UNDRWTR_PREF_QTY, CVI_PURE_FOR_UNDRWTR_REG_QTY, CVI_ADJD_WITH_CREDIT_QTY};
        somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames = new String[]{CUSTOMER_VALUE_INDEX_PURE_WITH_CREDIT, CUSTOMER_VALUE_INDEX_PURE_WITHOUT_CREDIT, QUALITY_RISK_LEVEL, FACILITY_RISK_SHARING_POOL_SCORING, RETENTION_SCORE_PURE_WITH_CREDIT, RETENTION_SCORE_PURE_WITHOUT_CREDIT, COMPETITIVITY_SCORE_WITH_CREDIT, COMPETITIVITY_SCORE_WITHOUT_CREDIT, CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_PREFERRED, CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_REGULAR, CUSTOMER_VALUE_INDEX_ADJUSTED_WITH_CREDIT};
        plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames = new String[]{ADDITIONAL_RETURN_PREMIUM_AMT, ANNUAL_PREMIUM_AMT, CVI_PURE_WITH_CREDIT_QTY, CVI_PURE_WITHOUT_CREDIT_QTY, FULL_TERM_PREMIUM_AMT, "GOOD_DRIVER_CD", "OFFER_TYPE_CD", QUALITY_RISK_LEVEL_CD, RET_SCORE_PURE_WITH_CR_QTY, RET_SCORE_PURE_WITHOUT_CR_QTY, COMPET_SCORE_WITH_CR_QTY, COMPET_SCORE_WITHOUT_CR_QTY, RSP_SCORING_QTY, "SELECTED_FOR_POL_OFFER_RAT_IND", "INTERNAL_TECH_OFFER_TYPE_CD", CVI_PURE_FOR_UNDRWTR_PREF_QTY, CVI_PURE_FOR_UNDRWTR_REG_QTY, CVI_ADJD_WITH_CREDIT_QTY};
        somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames = new String[]{ADDITIONAL_RETURN_PREMIUM, "annualPremiumDiscountedSurcharged", CUSTOMER_VALUE_INDEX_PURE_WITH_CREDIT, CUSTOMER_VALUE_INDEX_PURE_WITHOUT_CREDIT, FULL_TERM_PREMIUM, "goodDriverInd", "offerType", QUALITY_RISK_LEVEL, RETENTION_SCORE_PURE_WITH_CREDIT, RETENTION_SCORE_PURE_WITHOUT_CREDIT, COMPETITIVITY_SCORE_WITH_CREDIT, COMPETITIVITY_SCORE_WITHOUT_CREDIT, FACILITY_RISK_SHARING_POOL_SCORING, "riskSelectedInd", "internalTechnicalOfferType", CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_PREFERRED, CUSTOMER_VALUE_INDEX_PURE_FOR_UNDERWRITING_REGULAR, CUSTOMER_VALUE_INDEX_ADJUSTED_WITH_CREDIT};
        plInsuranceRiskOfferOverridesOnVehicleAttributeNames = new String[]{"VEH_RT_GRP_ALL_PERILS_CD", "VEH_RT_GRP_LBLTY_BDLY_INJRY_CD", "VEH_RT_GRP_LBLTY_CD", "VEH_RT_GRP_MEDICAL_EXPENSES_CD", "VEH_RT_GRP_SPECIFIED_PERILS_CD", "VEH_RT_GRP_TOTAL_DISABILITY_CD", "VEHICLE_RATE_GROUP_CD"};
        somInsuranceRiskOfferOverridesOnVehicleAttributeNames = new String[]{"vehicleRateGroupAllPerils", "vehicleRateGroupLiabilityBodilyInjury", "vehicleRateGroupLiability", "vehicleRateGroupMedicalExpenses", "vehicleRateGroupSpecifiedPerils", "vehicleRateGroupTotalDisability", "vehicleRateGroup"};
        plRatingRiskAttributeNames = new String[]{"RATING_RISK_TYPE_CD", DRIVING_RECORD_SYS_CD, DRIVING_RECORD_MOD_CD, DRIVING_RECORD_CD, DRIVING_RECORD_PR_TERM_CD, CLAIMS_FREE_DISCOUNT_SYS_CD, CLAIMS_FREE_DISCOUNT_MOD_CD, CLAIMS_FREE_DISCOUNT_CD, RATING_CLASS_CD};
        somRatingRiskAttributeNames = new String[]{"ratingRiskType", DRIVING_RECORD_SYSTEM, DRIVING_RECORD_MODIFIED, DRIVING_RECORD, CLAIMS_FREE_DISCOUNT_SYSTEM, CLAIMS_FREE_DISCOUNT_MODIFIED, CLAIMS_FREE_DISCOUNT, RATING_CLASS};
        plRatingRiskOfferAttributeNames = new String[]{"RATING_RISK_TYPE_CD", DRIVING_RECORD_SYS_CD, DRIVING_RECORD_MOD_CD, DRIVING_RECORD_CD, DRIVING_RECORD_PR_TERM_CD, CLAIMS_FREE_DISCOUNT_SYS_CD, CLAIMS_FREE_DISCOUNT_MOD_CD, CLAIMS_FREE_DISCOUNT_CD, RATING_CLASS_CD};
        somRatingRiskOfferAttributeNames = new String[]{"ratingRiskType", DRIVING_RECORD_SYSTEM, DRIVING_RECORD_MODIFIED, DRIVING_RECORD, CLAIMS_FREE_DISCOUNT_SYSTEM, CLAIMS_FREE_DISCOUNT_MODIFIED, CLAIMS_FREE_DISCOUNT, RATING_CLASS};
        plRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames = new String[]{DRIVING_RECORD_SYS_CD, DRIVING_RECORD_MOD_CD, DRIVING_RECORD_CD, DRIVING_RECORD_PR_TERM_CD, CLAIMS_FREE_DISCOUNT_SYS_CD, CLAIMS_FREE_DISCOUNT_MOD_CD, CLAIMS_FREE_DISCOUNT_CD, RATING_CLASS_CD};
        somRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames = new String[]{DRIVING_RECORD_SYSTEM, DRIVING_RECORD_MODIFIED, DRIVING_RECORD, CLAIMS_FREE_DISCOUNT_SYSTEM, CLAIMS_FREE_DISCOUNT_MODIFIED, CLAIMS_FREE_DISCOUNT, RATING_CLASS};
        plGroupRepositoryEntryOnRatingRiskAttributeNames = new String[]{"INSURED_GROUP_RATED_CD", "INSURED_GROUP_DSCNT_RATED_PCT"};
        somGroupRepositoryEntryOnRatingRiskAttributeNames = new String[]{INSURED_GROUP, "insuredGroupDiscount"};
        plGroupRepositoryEntryRootOverridesRatingRiskAttributeNames = new String[]{"INSURED_GROUP_RATED_CD", "INSURED_GROUP_DSCNT_RATED_PCT"};
        somGroupRepositoryEntryRootOverridesRatingRiskAttributeNames = new String[]{INSURED_GROUP, "insuredGroupDiscount"};
        plCoverageAttributeNames = new String[]{EFFECTIVE_DT, EXPIRY_DT, RATING_DT, "COVERAGE_SELECTABLE_IND", "COVERAGE_SELECTED_IND", "COVERAGE_SELECTED_TYPE_CD", COVERAGE_ELIGIBLE_IND, DEDUCTIBLE_AMT, "REDUCED_DEDUCTIBLE_AMT", LIMIT_OF_INSURANCE_AMT, LMT_MED_EXPENSES_PER_PRSN_AMT, LMT_MUTLTN_DEATH_INDEMNITY_AMT, WEEKLY_BENEFITS_AMT, VEH_RATE_GROUP_ADJUSTMENT_QTY};
        somCoverageAttributeNames = new String[]{EFFECTIVE_DATE, EXPIRY_DATE, RATING_DATE, "coverageSelectableInd", "coverageSelectedInd", "coverageSelectedType", COVERAGE_ELIGIBLE_IND2, DEDUCTIBLE_AMOUNT, "reducedDeductibleAmount", LIMIT_OF_INSURANCE, LIMIT_MEDICAL_EXPENSES_PER_PERSON, LIMIT_MUTILATION_AND_DEATH_INDEMNITY, WEEKLY_BENEFITS, VEHICLE_RATE_GROUP_ADJUSTMENT};
        plCoverageOfferAttributeNames = new String[]{EFFECTIVE_DT, EXPIRY_DT, RATING_DT, "COVERAGE_SELECTABLE_IND", "COVERAGE_SELECTED_IND", "COVERAGE_SELECTED_TYPE_CD", COVERAGE_ELIGIBLE_IND, DEDUCTIBLE_AMT, "REDUCED_DEDUCTIBLE_AMT", LIMIT_OF_INSURANCE_AMT, LMT_MED_EXPENSES_PER_PRSN_AMT, LMT_MUTLTN_DEATH_INDEMNITY_AMT, WEEKLY_BENEFITS_AMT, VEH_RATE_GROUP_ADJUSTMENT_QTY, ACTION_TAKEN_CD, "MOST_RECENT_USR_ACT_TAKEN_CD"};
        somCoverageOfferAttributeNames = new String[]{EFFECTIVE_DATE, EXPIRY_DATE, RATING_DATE, "coverageSelectableInd", "coverageSelectedInd", "coverageSelectedType", COVERAGE_ELIGIBLE_IND2, DEDUCTIBLE_AMOUNT, "reducedDeductibleAmount", LIMIT_OF_INSURANCE, LIMIT_MEDICAL_EXPENSES_PER_PERSON, LIMIT_MUTILATION_AND_DEATH_INDEMNITY, WEEKLY_BENEFITS, VEHICLE_RATE_GROUP_ADJUSTMENT, ACTION_TAKEN, "mostRecentUserActionTaken"};
        plCoverageOfferReferenceOverridesOnCoverageOffersAttributeNames = new String[]{VEH_RATE_GROUP_ADJUSTMENT_QTY};
        somCoverageOfferReferenceOverridesOnCoverageOffersAttributeNames = new String[]{VEHICLE_RATE_GROUP_ADJUSTMENT};
        plCoveragePremiumAttributeNames = new String[]{ANNUAL_PREMIUM_AMT, "ANNUAL_PREMIUM_MOD_AMT", "ANNUAL_PREMIUM_SYS_AMT", FULL_TERM_PREMIUM_AMT, ADDITIONAL_RETURN_PREMIUM_AMT};
        somCoveragePremiumAttributeNames = new String[]{"annualPremium", "annualPremiumModified", "annualPremiumSystem", FULL_TERM_PREMIUM, ADDITIONAL_RETURN_PREMIUM};
        plCoveragePremiumOfferAttributeNames = new String[]{ANNUAL_PREMIUM_AMT, FULL_TERM_PREMIUM_AMT, ADDITIONAL_RETURN_PREMIUM_AMT};
        somCoveragePremiumOfferAttributeNames = new String[]{"annualPremiumSystem", FULL_TERM_PREMIUM, ADDITIONAL_RETURN_PREMIUM};
        plSubCoveragePremiumOfferAttributeNames = new String[]{"ANL_PREM_LBLTY_BDLY_INJRY_AMT", "ANL_PREM_LBLTY_PRPTY_DMD_AMT", "ANL_PREM_ACDNT_BNFT_AMT", "ANL_PREM_ALL_PERILS_AMT", "ANL_PREM_COLLISION_AMT", "ANL_PREM_COMPREHENSIVE_AMT", "ANL_PREM_SPECIFIED_PERILS_AMT", "ANL_PREM_LBLTY_AMT", "ANL_PREM_MEDICAL_EXPENSES_AMT", "ANL_PREM_TOTAL_DISABILITY_AMT"};
        somSubCoveragePremiumOfferAttributeNames = new String[]{"annualPremiumLiabilityBodilyInjured", "annualPremiumLiabilityPropertyDamage", "annualPremiumAccidentBenefit", "annualPremiumAllPerils", "annualPremiumCollision", "annualPremiumComprehensive", "annualPremiumSpecifiedPerils", "annualPremiumLiability", "annualPremiumMedicalExpenses", "annualPremiumTotalDisability"};
        plCoverageRepositoryEntryFromCoverageAttributeNames = new String[]{"COVERAGE_DESC"};
        somCoverageRepositoryEntryFromCoverageAttributeNames = new String[]{"coverageDescription"};
        plCoverageRepositoryEntryAttributeNames = new String[]{"BROKER_AUTH_ENDORSEMENT_CD", COVERAGE_CD, "COVERAGE_PRINT_ENG_DESC", "COVERAGE_PRINT_FRE_DESC", "COVERAGE_SHORT_ENG_DESC", "COVERAGE_SHORT_FRE_DESC", COVERAGE_TYPE_CD, "CREATION_FORM_CD", EFFECTIVE_DT, "ENDORSEMENT_APLCBL_TYPE_CD", "ENDORSEMENT_METHOD_CALCUL_CD", "ENDORSEMENT_PRCSNG_TYPE_CD", EXPIRY_DT, "LINE_OF_INSURANCE_CD", "PREMIUM_OR_PERCENTAGE_CD", "RAT_FACT_ACDNT_BNFT_AMT", "RAT_FACT_ALL_PERILS_AMT", "RAT_FACT_COLLISION_AMT", "RAT_FACT_COMPREHENSIVE_AMT", "RAT_FACT_LBLTY_AMT", "RAT_FACT_LBLTY_BDLY_INJRY_AMT", "RAT_FACT_LBLTY_PRPTY_DMD_AMT", "RAT_FACT_MEDICAL_EXPENSES_AMT", "RAT_FACT_SPECIFIED_PERILS_AMT", "RAT_FACT_TOTAL_DISABILITY_AMT", "RATING_RISK_TYPE_APPLY_CD", "RENEWAL_ACTION_CD", "SURCHARGE_DISCOUNT_CD"};
        somCoverageRepositoryEntryAttributeNames = new String[]{"brokerAuthorizationEndorsement", COVERAGE_CODE, "coverageDescriptionPrintEng", "coverageDescriptionPrintFre", "coverageDescriptionShortEng", "coverageDescriptionShortFre", COVERAGE_TYPE, "creationFormInd", EFFECTIVE_DATE, "endorsementApplicabilityType", "endorsementMethodOfCalculation", "endorsementProcessingType", EXPIRY_DATE, "lineOfInsurance", "premiumOrPercentageInd", "ratingFactorAccidentBenefit", "ratingFactorAllPerils", "ratingFactorCollision", "ratingFactorComprehensive", "ratingFactorLiability", "ratingFactorLiabilityBodilyInjury", "ratingFactorLiabilityPropertyDamage", "ratingFactorMedicalExpenses", "ratingFactorSpecifiedPerils", "ratingFactorTotalDisability", "ratingRiskTypeApply", "renewalActionCode", "surchargeDiscountInd"};
        plPolicyAdditionalCoverageAttributeNames = new String[]{"COVERAGE_DESC", EFFECTIVE_DT, ACTION_TAKEN_CD, COVERAGE_ELIGIBLE_IND, "MOST_RECENT_USR_ACT_TAKEN_CD"};
        somPolicyAdditionalCoverageAttributeNames = new String[]{"coverageDescription", EFFECTIVE_DATE, ACTION_TAKEN, COVERAGE_ELIGIBLE_IND2, "mostRecentUserActionTaken"};
        plPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames = new String[]{COVERAGE_CD, COVERAGE_TYPE_CD};
        somPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames = new String[]{COVERAGE_CODE, COVERAGE_TYPE};
        plPriorCarrierPolicyInfoAttributeNames = new String[]{"PRIOR_CARRIER_POLICY_NBR", "CARRIER_CD", "PRIOR_CARRIER_NAME_TXT", "PRIOR_POLICY_EXPIRY_DT", "NBR_YRS_CONT_INSRD_PR_CARR_QTY", "PRIOR_CARR_DCLN_CNCL_NAME_TXT", "PRIOR_CARR_DCLN_CNCL_DT", "REASON_DECLINED_OR_CNCL_CD"};
        somPriorCarrierPolicyInfoAttributeNames = new String[]{"priorCarrierPolicyNumber", "carrierCode", "priorCarrierName", "priorPolicyExpiryDate", "numberOfYearsContinuouslyInsuredWithPriorCarrier", "priorCarrierDeclinedOrCancelledName", "priorCarrierDeclinedOrCancelledDate", "reasonDeclinedOrCancelled"};
        plMessageRepositoryEntryAttributeNames = new String[]{"LANGUAGE_CD", "MESSAGE_NBR", "MESSAGE_NATURE_CD", "MESSAGE_DESC", "MSG_ACCEPTATION_LEVEL_TYPE_CD", "LVL_OF_AUTHORITY_BROKER_QTY", "LVL_OF_AUTHORITY_IN_HOUSE_QTY"};
        somMessageRepositoryEntryAttributeNames = new String[]{"language", "messageNumber", "messageNature", "messageDescription", "messageAcceptationLevelType", "levelOfAuthorityBroker", "levelOfAuthorityInHouse"};
        plTransactionalMessageElementAttributeNames = new String[]{"ELEMENT_VALUE_TXT", "ELEMENT_NAME_TXT", "ELEMENT_DESC", "MESSAGE_ELEMENT_ID"};
        somTransactionalMessageElementAttributeNames = new String[]{"elementValue", "elementName", "elementDescription", "messageElementIdentification"};
        plTransactionalMessageAttributeNames = new String[]{"ACCEPTING_TS", "ACCEPTING_USER_ID", "UNDERWRITING_MESSAGE_STATUS_CD", "CONTEXT_KEY_NBR"};
        somTransactionalMessageAttributeNames = new String[]{"acceptingDateTime", "acceptingUserId", "underwritingMessageStatus", "contextKey"};
        plCredentialAttributeNames = new String[]{"USER_ID", "MAINFRAME_USER_ID"};
        somCredentialAttributeNames = new String[]{"userId", "userIdMainframe"};
        plBusinessTransactionActivityAttributeNames = new String[]{"APPLICATION_ID"};
        somBusinessTransactionActivityAttributeNames = new String[]{"applicationIdentification"};
        plBusinessTransactionAttributeNames = new String[]{"TRANSACTION_CD", "TRANSACTION_STATUS_CD", CREATION_TS, "TRANSACTION_SEQ", "TRANSACTION_EFFECTIVE_TS", "PROCESSING_TS", "LAST_RATING_SEQ", "TRANSACTIONAL_PAPER_IND"};
        somBusinessTransactionAttributeNames = new String[]{"transactionCode", "transactionStatus", "creationDateTime", "transactionSequence", "transactionEffectiveDateTime", "processingDateTime", "lastRatingSequence", "transactionalPaperInd"};
        plRelatedInsurancePolicyAttributeNames = new String[]{"RELATED_AGREEMENT_NBR"};
        somRelatedInsurancePolicyAttributeNames = new String[]{"agreementNumber"};
        plManufacturingContextAttributeNames = new String[]{"DISTRIBUTION_CHANNEL_CD", "INSURANCE_BUSINESS_CD", PROVINCE_CD};
        somManufacturingContextAttributeNames = new String[]{"distributionChannel", "insuranceBusiness", PROVINCE};
        plInsurancePolicyAttributeNames = new String[]{"AGREEMENT_TYPE_CD", "AGREEMENT_NBR", "AGREEMENT_STATUS_CD", "ORIGINAL_INCEPTION_DT", "SPF_CD", "RATING_BASIS_CD", "LINE_OF_BUSINESS_CD", "TEST_DATA_IND", "QUO_VALD_PRD_IN_DAYS_QTY", "QUOTATION_VALIDITY_EXPIRY_DT", "APPLICATION_MODE_CD"};
        somInsurancePolicyAttributeNames = new String[]{"agreementType", "agreementNumber", "agreementStatus", "originalInceptionDate", "spfCode", "ratingBasis", "lineOfBusiness", "testDataInd", "quotationValidityPeriodInDays", "quotationValidityExpiryDate", "applicationMode"};
        plPolicyVersionAttributeNames = new String[]{"POLICY_VERSION_TYPE_CD", "CONVERTED_POLICY_CD", "BUSINESS_SOURCE_CD", "LANGUAGE_OF_COMMUNICATION_CD", "INITIAL_TRANSACTION_CD", "POLICY_INCEPTION_DT", "POLICY_EXPIRY_DT", "POLICY_TERM_IN_MONTHS_QTY", RATING_DT, "CLIENT_OF_BROKER_SINCE_AUTO_DT", "HOLDER_AUTO_INSURANCE_SINCE_DT", "COMBINED_POLICY_CD", "COMBINED_POLICY_SCENARIO_CD", "POLICY_DISCOUNT_TYPE_CD", "LOYALTY_DISCOUNT_IND", "MVRSAAQ_AUTHORIZATION_IND", ANNUAL_PREMIUM_AMT, FULL_TERM_PREMIUM_AMT, ADDITIONAL_RETURN_PREMIUM_AMT, "UNDERWRITING_COMPANY_CD", "MARKETING_PROMOTION_CD", "AFFINITY_GROUP_CD", "OTH_DRVR_WITH_COMP_IN_HHLD_IND", "UNDERWRITING_ACTION_CD", "BILLING_PLAN_FOR_UNDWTR_ACT_CD", "POL_CHG_PRM_WAR_PRD_IN_DAY_QTY", "POL_CHG_PRM_WAR_EXP_DT", "CLIENT_TARGET_REASON_CD", "NBR_OF_QU_LAST_24HR_FOR_IP_QTY", "NBR_OF_QU_LAST_HR_FOR_IP_QTY"};
        somPolicyVersionAttributeNames = new String[]{"policyVersionType", "convertedPolicyCode", "businessSource", "languageOfCommunication", "initialTransactionCode", "policyInceptionDate", "policyExpiryDate", "policyTermInMonths", RATING_DATE, "clientOfBrokerSinceAuto", "holderOfAutoInsuranceSince", "combinedPolicyCode", "combinedPolicyScenarioCode", "policyDiscountType", "loyaltyDiscountInd", "mvrsaaqAuthorizationInd", "annualPremium", FULL_TERM_PREMIUM, ADDITIONAL_RETURN_PREMIUM, "underwritingCompany", "marketingPromotionCode", "affinityGroupCode", "otherDriverWithCompanyinHouseholdInd", "underwritingActionCode", "billingPlanUseForUnderwritingAction", "policyChangePremiumWarrantyPeriodInDays", "policyChangePremiumWarrantyExpiryDate", "clientTargetReasonCode", "numberOfQuotationsLast24HrsForIpAddress", "numberOfQuotationsLastHrForIpAddress"};
        plReferenceDateAttributeNames = new String[]{"CLAIM_REF_DT", "CREDIT_SCORE_REF_DT", "DRIVER_AGE_REF_DT", "LICENSE_OBTAINED_PERIOD_REF_DT", "TRX_CREATION_REF_DT", "TRX_FIRST_RATING_REF_DT", "VEHICLE_AGE_REF_DT"};
        somReferenceDateAttributeNames = new String[]{"claimReferenceDate", "creditScoreReferenceDate", "driverAgeReferenceDate", "licenseObtainedPeriodReferenceDate", "transactionCreationDate", "transactionFirstRatingReferenceDate", "vehicleAgeReferenceDate"};
        plMultiplicativeRatingFactorFromBasicCoverageAttributeNames = new String[]{"BASIC_COVERAGE_CD", LIMIT_OF_INSURANCE_AMT, DEDUCTIBLE_AMT, LMT_MED_EXPENSES_PER_PRSN_AMT, LMT_MUTLTN_DEATH_INDEMNITY_AMT, WEEKLY_BENEFITS_AMT, "MULT_RATING_FACTOR_QTY", "MAX_PRM_VAR_ALLOW_W_RT_FCT_AMT", "RATING_FACTOR_TYPE_CD", "METHOD_OF_CALCULATION_CD", "RATING_FACTOR_APPLY_COND_CD"};
        somMultRatingFactorFromBasicCoverageAttributeNames = new String[]{"basicCoverageCode", LIMIT_OF_INSURANCE, DEDUCTIBLE_AMOUNT, LIMIT_MEDICAL_EXPENSES_PER_PERSON, LIMIT_MUTILATION_AND_DEATH_INDEMNITY, WEEKLY_BENEFITS, "multRatingFactor", "maximumPremiumVariationAllowWithFactor", "ratingFactorType", "methodOfCalculation", "ratingFactorApplyCondition"};
        plMultiplicativeRatingFactorFromNonBasicCoverageAttributeNames = new String[]{"CONDITION_TYPE_CD", "CONDITION_CD", "RATING_FACTOR_APPLY_COND_CD", "ML_RT_FCT_LBLTY_BDLY_INJRY_QTY", "ML_RT_FCT_LBLTY_PRPTY_DMD_QTY", "ML_RT_FCT_ACDNT_BNFT__QTY", "ML_RT_FCT_ALL_PERILS_QTY", "ML_RT_FCT_COLLISION_QTY", "ML_RT_FCT_COMPREHENSIVE_QTY", "ML_RT_FCT_SPECIFIED_PERILS_QTY", "ML_RT_FCT_LBLTY_QTY", "ML_RT_FCT_MEDICAL_EXPENSES_QTY", "ML_RT_FCT_TOTAL_DISABILITY_QTY", "FIXED_AMT", "RATING_FACTOR_TYPE_CD", "METHOD_OF_CALCULATION_CD"};
        somMultRatingFactorFromNonBasicCoverageAttributeNames = new String[]{"conditionType", "conditionCode", "ratingFactorApplyCondition", "multRatingFactorLiabilityBodilyInjury", "multRatingFactorLiabilityPropertyDamage", "multRatingFactorAccidentBenefit", "multRatingFactorAllPerils", "multRatingFactorCollision", "multRatingFactorComprehensive", "multRatingFactorSpecifiedPerils", "multRatingFactorLiability", "multRatingFactorMedicalExpenses", "multRatingFactorTotalDisability", "fixedAmount", "ratingFactorType", "methodOfCalculation"};
        plAffinityGroupRepositoryEntryAttributeNames = new String[]{"AFFINITY_GROUP_CD"};
        somAffinityGroupRepositoryEntryAttributeNames = new String[]{"affinityGroupCode"};
        plDiagnosticAutomatedAdviceAttributeNames = new String[]{"ADVICE_SELECTED_IND", "ADVICE_SELECTED_TYPE_CD", "ADVICE_ELIGIBLE_IND", "ADVICE_APPLICABLE_IND"};
        somDiagnosticAutomatedAdviceAttributeNames = new String[]{"adviceSelectedInd", "adviceSelectedType", "adviceEligibleInd", "adviceApplicableInd"};
        plDiagnosticAutomatedAdviceRepositoryEntryAttributeNames = new String[]{"ADVICE_TYPE_CD", "ADVICE_CD", "ADVICE_GROUPED_IND", "ADVICE_SELECTABLE_IND", "ADVICE_DISPLAY_IND", EFFECTIVE_DT, EXPIRY_DT, "ADVICE_ROAD_BLOCK_IND"};
        somDiagnosticAutomatedAdviceRepositoryEntryAttributeNames = new String[]{"adviceType", "adviceCode", "adviceGroupedInd", "adviceSelectableInd", "adviceDisplayInd", EFFECTIVE_DATE, EXPIRY_DATE, "adviceRoadblockInd"};
        plMarketSegmentAttributeNames = new String[]{"MARKET_SEGMENT_NAME_TXT", "MARKET_SEGMENT_ELIGIBLE_IND", "MARKET_SEGMENT_PERMANENT_IND", "MARKET_SEGMENT_TYPE_CD"};
        somMarketSegmentAttributeNames = new String[]{"marketSegmentName", "marketSegmentEligibleInd", "marketSegmentPermanentInd", "marketSegmentType"};
        plPartnershipAttributeNames = new String[]{"PARTNERSHIP_CD", "GROUP_NBR", "PROMO_SOURCE_NBR"};
        somPartnershipAttributeNames = new String[]{"partnershipCode", "groupNumber", "promoSource"};
        som2somWhiteList = new String[]{"Coverage.actionTaken", "Coverage.annualPremium", "Coverage.coverageEligibleInd", "Coverage.coverageSelectableInd", "Coverage.coverageSelectedInd", "Coverage.coverageSelectedType", "Coverage.coverageTermInMonths", "CoveragePremium.actionTaken", "CoveragePremium.additionalPremium", "CoveragePremium.additionalReturnPremium", "CoveragePremium.annualPremium", "CoveragePremium.annualPremiumCapped", "CoveragePremium.annualPremiumDiscountedSurcharged", "CoveragePremium.annualPremiumDiscountedSurchargedCapped", "CoveragePremium.fullTermPremium", "CoveragePremium.fullTermPremiumProRated", "CoverageProduct.ratingMethod", "CoverageRepositoryEntry.coverageType", "CreditScore.creditScore", "CreditScore.creditScoreCategory", "CreditScore.creditScoreDate", "CreditScore.creditScoreServiceStatus", "CreditScore.dateOfLastMove", "Driver.age", "Driver.numberOfMajorConvictions3YearsAdjusted", "Driver.numberOfMinorConvictions3YearsAdjusted", "Driver.numberOfSevereConvictions3YearsAdjusted", "Driver.responsibleDriverGuaranteeEligibilityInd", "DriverComplementInfo.graduatedLicenseDiscountInd", "DriverComplementInfo.licenseSuspensionPeriodInMonths", "DriverComplementInfo.numberOfClaimsAtFault6Years", "DriverComplementInfo.numberOfClaimsNonLiable5Years", "DriverComplementInfo.numberOfForgivenClaims7Years", "DriverComplementInfo.numberOfLiableClaims5Years", "DriverComplementInfo.numberOfLiableClaims5YearsSystem", "DriverComplementInfo.numberOfLiableClaims7Years", "DriverComplementInfo.numberOfLiableClaims9Years", "DriverComplementInfo.numberOfMajorConvictions3Years", "DriverComplementInfo.numberOfMinorConvictions3Years", "DriverComplementInfo.numberOfMinorConvictions3YearsSystem", "DriverComplementInfo.numberOfMonthsContinuouslyInsured", "DriverComplementInfo.numberOfMonthsDriverHasHeldCurrentLicenseClass", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwriting", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingSystem", "DriverComplementInfo.numberOfMonthsSinceLastLiableClaim", "DriverComplementInfo.numberOfMonthsSinceLastLiableClaimSystem", "DriverComplementInfo.numberOfMonthsSinceLastMinorConviction", "DriverComplementInfo.numberOfMonthsSinceLastMinorConvictionSystem", "DriverComplementInfo.numberOfMonthsSinceLastSuspension", "DriverComplementInfo.numberOfSevereConvictions3Years", "DriverComplementInfo.numberOfYearsDrivingExperience", "DriverComplementInfo.ageDriverLicenseObtained", "DriverComplementInfo.drivingExperience", "DriverComplementInfo.numberOfAlcoholConvictions3Years", "DriverComplementInfo.numberOfSevereConvictionsExcludingAlcohol3Years", "DriverComplementInfo.mostRecentDriverLicenseClassOnPpv", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingForGraduated", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingForIntl", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingForLearner", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingForProbation", "DriverComplementInfo.numberOfMonthsDriverLicensedUnderwritingForUs", "DriverComplementInfo.numberOfMonthsDriverLicensedSuspensionForGraduated", "DriverComplementInfo.numberOfMonthsDriverLicensedSuspensionForIntl", "DriverComplementInfo.numberOfMonthsDriverLicensedSuspensionForLearner", "DriverComplementInfo.numberOfMonthsDriverLicensedSuspensionForProbation", "DriverComplementInfo.numberOfMonthsDriverLicensedSuspensionForUs", "DriverComplementInfo.numberOfMonthsSinceLastConviction", "DriverComplementInfo.numberOfLiableClaims3Years", "DriverComplementInfo.numberOfClaimsLiable6Years", "DriverComplementInfo.numberOfLiableClaimsOnPpvAsPrincipalDriver7Years", "DriverComplementInfo.numberOfClaimsDisregarded3Years", "DriverComplementInfo.numberOfClaimsDisregarded6Years", "DriverComplementInfo.numberOfNonPaymentCancellationsIn3Years", "DriverComplementInfo.graduatedDriverDiscountEligibilityInd", "DriverComplementInfo.ubiEligibilityInd", "DriverComplementInfo.ubiServiceProviderCode", "DriverComplementInfo.interestedByUbiInd", "InsuranceRisk.annualPremiumDiscountedSurcharged", "InsuranceRisk.annualPremiumDiscountedSurchargedCapped", "InsuranceRisk.additionalReturnPremium", "InsuranceRisk.coverageLevelOnRisk", "InsuranceRisk.customerValueIndexBand", "InsuranceRisk.customerValueIndexPureWithCredit", "InsuranceRisk.customerValueIndexPureWithoutCredit", "InsuranceRisk.customerValueIndexPureForUnderwritingRegular", "InsuranceRisk.customerValueIndexPureForUnderwritingPreferred", "InsuranceRisk.customerValueIndexPureWithCreditRegular", "InsuranceRisk.customerValueIndexPureWithCreditPreferred", "InsuranceRisk.customerValueIndexPureWithoutCreditRegular", "InsuranceRisk.customerValueIndexPureWithoutCreditPreferred", "InsuranceRisk.fullTermPremium", "InsuranceRisk.gridTerritory", "InsuranceRisk.multiVehicleDiscountType", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver5Years", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver6Years", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver7Years", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver9Years", "InsuranceRisk.numberOfClaimsCollisionAndComprehensive3Years", "InsuranceRisk.numberOfClaimsFire3Years", "InsuranceRisk.numberOfClaimsGlassRepair3Years", "InsuranceRisk.numberOfClaimsGlassReplacement3Years", "InsuranceRisk.numberOfClaims3Years", "InsuranceRisk.numberOfClaims6Years", "InsuranceRisk.numberOfForgivenClaimsPrincAndNonRatedDrvr7Years", "InsuranceRisk.numberOfMonthsSinceLastLiableClaimPrincAndNonRatedDrvr", "InsuranceRisk.numberOfMonthsSinceLastMinorConvPrincAndNonRatedDrvr", "InsuranceRisk.pipDiscountEligibilityInd", "InsuranceRisk.ratingDate", "InsuranceRisk.ratingTerritory", "InsuranceRisk.retentionBand", "InsuranceRisk.retentionScorePureWithCredit", "InsuranceRisk.riskTermInDays", "InsuranceRisk.territoryCategory", "InsuranceRisk.territoryStatSystem", "InsuranceRisk.underwritingIndexBand", "InsuranceRisk.underwritingIndexScore", "InsuranceRisk.vehicleClass", "InsuranceRisk.vehicleClassOccasional", "InsuranceRisk.vehicleClassSystem", "InsuranceRisk.competitivityScoreWithCredit", "InsuranceRisk.competitivityScoreWithCreditPreferred", "InsuranceRisk.competitivityScoreWithCreditRegular", "InsuranceRisk.competitivityScoreWithoutCredit", "InsuranceRisk.competitivityScoreWithoutCreditPreferred", "InsuranceRisk.competitivityScoreWithoutCreditRegular", "InsuranceRisk.facilityRiskSharingPoolCessionScoringPreferred", "InsuranceRisk.facilityRiskSharingPoolCessionScoringRegular", "InsuranceRisk.facilityRiskSharingPoolScoringPreferred", "InsuranceRisk.facilityRiskSharingPoolScoringRegular", "InsuranceRisk.numberOfDrivers", "InsuranceRisk.highestNumberOfClaimsLiable3Years", "InsuranceRisk.numberOfClaimsLiableInternal6Years", "InsuranceRisk.numberOfClaimsLiableInternal9Years", "InsuranceRisk.highestNumberOfClaimsLiable6Years", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver1Year", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver2Years", "InsuranceRisk.numberOfClaimsLiablePrincipalAndNonRatedDriver3Years", "InsuranceRisk.numberOfClaimsLiableOccasionalDriver9Years", "InsuranceRisk.numberOfClaimsNonLiableDcpd2Years", "InsuranceRisk.numberOfClaimsLiableCollision3Years", "InsuranceRisk.numberOfClaimsNonLiableCollision2Years", "InsuranceRisk.numberOfClaimsNonLiableCollision3Year", "InsuranceRisk.numberOfClaimsNonLiablePrincipalAndNonRatedDriver5Years", "InsuranceRisk.numberOfClaimsComprehensiveSpecifiedPerils2Years", "InsuranceRisk.numberOfClaimsComprehensiveSpecifiedPerils3Years", "InsuranceRisk.numberOfClaimsTheft3Years", "InsuranceRisk.numberOfClaimsLiable1Year", "InsuranceRisk.numberOfClaimsLiable2Years", "InsuranceRisk.numberOfClaimsLiable3Years", "InsuranceRisk.numberOfClaimsLiable4Years", "InsuranceRisk.numberOfClaimsLiable5Years", "InsuranceRisk.numberOfClaimsLiable6Years", "InsuranceRisk.numberOfClaimsLiable9Years", "InsuranceRisk.numberOfSevereConvictions3Years", "InsuranceRisk.numberOfAlcoholConvictions3Years", "InsuranceRisk.numberOfSevereConvictionsExcludingAlcohol3Years", "InsuranceRisk.numberOfMinorConvictions3Years", "InsuranceRisk.numberOfMajorConvictions3Years", "InsuranceRisk.numberOfClaimsNonLiable6Years", "InsuranceRisk.numberOfClaimsDisregarded3Years", "InsuranceRisk.numberOfClaimsDisregarded6Years", "InsuranceRisk.highestNumberOfAlcoholConvictions3Years", "InsuranceRisk.highestNumberOfConvictions3Years", "InsuranceRisk.highestNumberOfMajorConvictions3Years", "InsuranceRisk.highestNumberOfMinorConvictions3Years", "InsuranceRisk.highestNumberOfSevereConvictions3Years", "InsuranceRisk.maximumNumberOfNonPaymentCancellations3Years", "InsuranceRisk.minorConvictionProtectorOccDrvrCovEligInd", "InsuranceRisk.minorConvictionProtectorPrincOrNonRatedDrvrCovEligInd", "InsuranceRisk.numberOfAdditionalNamedInsured", "InsuranceRisk.numberOfConvictionsOccasionalDriver3Years", "InsuranceRisk.numberOfConvictionsPrincipalAndNonRatedDriver3Years", "InsuranceRisk.numberOfNamedInsured", "InsuranceRisk.numberOfNonRatedDriversUnder25Years", "InsuranceRisk.numberOfOccasionalDriversUnder25Years", "InsuranceRisk.highestNumberOfClaimsDisregarded3Years", "InsuranceRisk.highestNumberOfClaimsDisregarded6Years", "InsuranceRisk.highestNumberOfClaimsLiableWithAccommodation9Years", "InsuranceRisk.numberOfMonthsSinceLastConvPrincAndNonRatedDrvr", "InsuranceRisk.numberOfConvictions3Years", "InsuranceRisk.winterTireCoverageInd", "ManufacturingContext.province", "Party.ingStaffInd", "Party.numberOfWeeksSinceRetirement", "PolicyHolder.age", "PolicyHolder.ageAdjusted", "PolicyVersion.numberOfDriversPerVehicleOccasional", "PolicyVersion.numberOfDriversPerVehicleOccasionalSystem", "PolicyVersion.numberOfDriversPerVehiclePrincipal", "PolicyVersion.numberOfDriversPerVehiclePrincipalSystem", "PolicyVersion.numberOfPpvRisksConsideredOnPolicy", "PolicyVersion.numberOfPpvRisksWithCoverageSuspension", "PolicyVersion.numberOfPpvRisksWithOccasionalDriver", "PolicyVersion.numberOfRisksConsideredOnPolicy", "PolicyVersion.numberOfYearsInsuredWithIngOrAffilliate", "PolicyVersion.numberOfYearsInsuredWithIngOrAffilliateSystem", "PolicyVersion.numberOfRisksOnPolicyAnt", "PolicyVersion.numberOfRisksOnPolicyCla", "PolicyVersion.numberOfRisksOnPolicyMcy", "PolicyVersion.numberOfRisksOnPolicyMho", "PolicyVersion.numberOfRisksOnPolicyPpa", "PolicyVersion.numberOfRisksOnPolicyPpaConsidered", "PolicyVersion.numberOfRisksOnPolicyPpaForPipDiscount", "PolicyVersion.numberOfRisksOnPolicyHom", "PolicyVersion.numberOfRisksOnPolicyTnt", "PolicyVersion.numberOfRisksOnPolicyCon", "PolicyVersion.numberOfRisksOnPolicyMob", "PolicyVersion.numberOfRisksOnPolicySea", "PolicyVersion.numberOfRisksOnPolicyFec", "PolicyVersion.numberOfPpvRisksWithCollisionCoverage", "PolicyVersion.ltvBand", "PolicyVersion.ltvScore", "PolicyVersion.policyDaysLeft", "PolicyVersion.policyDaysPast", "PolicyVersion.policyTermInDays", "PolicyVersion.policyTermInMonths", "PolicyVersion.priorCarrierConsideredInd", "PolicyVersion.termAdjustmentPercentage", "PolicyVersion.termBalancePercentageProRate", "PolicyVersion.numberOfDriversOnPpv", "PolicyVersion.numberOfPrincipalDriverOnPpv", "PolicyVersion.numberOfOccasionalDriverOnPpv", "PolicyVersion.numberOfNonRatedDriverOnPpv", "PolicyVersion.numberOfUnrelatedDrivers", "PolicyVersion.numberOfActiveDriversOnPolicy", "RatingRisk.annualPremiumCapped", "RatingRisk.convictionSurchargePercentage", "RatingRisk.drivingRecordCollision", "RatingRisk.drivingRecordCollisionActual", "RatingRisk.drivingRecordLiability", "RatingRisk.drivingRecordLiabilityActual", "RatingRisk.drivingRecordDcpd", "RatingRisk.drivingRecordDcpdActual", "RatingRisk.drivingRecordAccidentBenefit", "RatingRisk.drivingRecordAccidentBenefitActual", "RatingRisk.pricingIndexScore", "SubCoveragePremium.actionTaken", "SubCoveragePremium.additionalPremium", "SubCoveragePremium.additionalReturnPremium", "SubCoveragePremium.annualPremium", "SubCoveragePremium.basicCoverageCode", "SubCoveragePremium.fullTermPremium", "SubCoveragePremium.fullTermPremiumProRated", "Vehicle.substituteVehicleInd", "Vehicle.vehicleAge", "Vehicle.vehicleRateGroupAccidentBenefit", "Vehicle.vehicleRateGroupCollision", "Vehicle.vehicleRateGroupComprehensive", "Vehicle.vehicleRateGroupLiability", "Vehicle.vehicleRateGroupLiabilityPropertyDamage", "Vehicle.vehicleRateGroupByValueInd", "Vehicle.valueOfVehicleModification", "Vehicle.automaticEmergencyBrakingSystemInd", "Vehicle.ubiEligibilityInd", "VehicleDetailSpec.rateGroupAccidentBenefitVicc", "VehicleDetailSpec.rateGroupComprehensiveVicc", "VehicleDetailSpec.rateGroupCollisionVicc", "VehicleDetailSpec.rateGroupLiabilityPropertyDamageVicc", "VehicleDetailSpec.rateGroupCollisionViccPure", "VehicleDetailSpec.rateGroupLiabilityPropertyDamageViccPure", "MunicipalityDetailSpec.automobileTerritoryLiabilityScoring", "MunicipalityDetailSpec.automobileTerritoryAccidentBenefitsScoring", "MunicipalityDetailSpec.automobileTerritoryComprehensiveScoring", "MunicipalityDetailSpec.automobileTerritoryCollisionScoring", "MunicipalityDetailSpec.automobileTerritoryDirectCompensationScoring", "MunicipalityDetailSpec.automobileTerritoryScoring", "MunicipalityDetailSpec.automobileTerritoryRating"};
        plCoverageOptionAttributeNames = new String[]{DEDUCTIBLE_AMT, LIMIT_OF_INSURANCE_AMT, "PROFILE_BUYING_PCT", "PROFILE_MOST_POPULAR_IND", "RECOMMENDED_IND", "RECOMMENDATION_CD"};
        somCoverageOptionAttributeNames = new String[]{DEDUCTIBLE_AMOUNT, LIMIT_OF_INSURANCE, "profileBuyingPercentage", "profileMostPopularInd", "recommendedInd", "recommendationCode"};
        plLegacyRatingInfoByPostalCodes = new String[]{"AUT_TERR_COLLISION_BY_PC_CD", "AUT_TERR_COLL_SCR_BY_PC_CD", "AUT_TERR_COMP_BY_PC_CD", "AUT_TERR_COMP_SCR_BY_PC_CD", "AUT_TERR_LIABILITY_BY_PC_CD", "AUT_TERR_LIAB_SCR_BY_PC_CD", "AUT_TERR_ACC_BNF_BY_PC_CD", "AUT_TERR_ACC_BNF_SCR_BY_PC_CD", "AUT_TERR_DIR_COMP_BY_PC_CD", "AUT_TERR_DIR_COMP_SCR_BY_PC_CD", "AUT_TERR_SPEC_PER_BY_PC_CD", "AUT_TERR_SPEC_PER_SCR_BY_PC_CD", "AUT_TERR_GLASS_BREAK_BY_PC_CD", "AUT_TERR_RATING_BY_PC_CD", "AUT_TERR_ALL_PERL_BY_PC_CD", "AUT_TERR_ALL_PERL_SCR_BY_PC_CD", "AUT_TERR_LB_BD_IJ_BY_PC_CD", "AUT_TERR_LB_BD_IJ_SCR_BY_PC_CD", "AUT_TERR_LB_PR_DM_BY_PC_CD", "AUT_TERR_LB_PR_DM_SCR_BY_PC_CD", "AUT_TERR_MED_EXP_BY_PC_CD", "AUT_TERR_MED_EXP_SCR_BY_PC_CD", "AUT_TERR_TOT_DISB_BY_PC_CD", "AUT_TERR_TOT_DISB_SCR_BY_PC_CD"};
        somLegacyRatingInfoByPostalCodes = new String[]{"automobileTerritoryCollision", "automobileTerritoryCollisionScoring", "automobileTerritoryComprehensive", "automobileTerritoryComprehensiveScoring", "automobileTerritoryLiability", "automobileTerritoryLiabilityScoring", "automobileTerritoryAccidentBenefits", "automobileTerritoryAccidentBenefitsScoring", "automobileTerritoryDirectCompensation", "automobileTerritoryDirectCompensationScoring", "automobileTerritorySpecifiedPerils", "automobileTerritorySpecifiedPerilsScoring", "automobileTerritoryGlassBreakage", "automobileTerritoryRating", "automobileTerritoryAllPerils", "automobileTerritoryAllPerilsScoring", "automobileTerritoryLiabilityBodilyInjury", "automobileTerritoryLiabilityBodilyInjuryScoring", "automobileTerritoryLiabilityPropertyDamage", "automobileTerritoryLiabilityPropertyDamageScoring", "automobileTerritoryMedicalExpenses", "automobileTerritoryMedicalExpensesScoring", "automobileTerritoryTotalDisability", "automobileTerritoryTotalDisabilityScoring"};
    }
}
