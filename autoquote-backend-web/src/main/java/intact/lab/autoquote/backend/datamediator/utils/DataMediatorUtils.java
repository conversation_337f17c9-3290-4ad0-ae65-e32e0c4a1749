package intact.lab.autoquote.backend.datamediator.utils;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.plp.domain.AssociationsHelper;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.usertype.BaseEntity;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.businessModel.BusinessModel;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import commonj.sdo.ChangeSummary;
import commonj.sdo.DataObject;
import commonj.sdo.Property;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.Column;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.LinkedList;
import java.util.List;

import static intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOM.getTranslatedValueFromPLtoSOM;

@Slf4j
public class DataMediatorUtils {

    public static String getRealClassName(Object object) {
        return AssociationsHelper.unproxy(object).getClass().getName();
    }

    public static Class<?> getRealClass(Object object) {
        return AssociationsHelper.unproxy(object).getClass();
    }

    public static String getSOMObjectName(DataObject dataObject) {
        return (String) getRealSimpleName(dataObject).subSequence(0, getRealSimpleName(dataObject).length() - "BOImpl".length());
    }

    public static String getRealSimpleName(Object object) {
        return AssociationsHelper.unproxy(object).getClass().getSimpleName();
    }

    public static String accessorMethodName(String prefix, String fieldName) {
        return String.format("%s%s", prefix, StringUtils.capitalize(fieldName));
    }

    public static void setAttributesByReflectionFromPLtoSOM(String[] plAttributeNames, String[] somAttributeNames, BaseEntity plBean, BusinessModel somBean, Boolean... ignorePersistenceUniqueId) {
        if (ignorePersistenceUniqueId.length == 0) {
            somBean.setPersistenceUniqueId(plBean.getId().toString());
        }

        List<String> plList = Arrays.asList(plAttributeNames);
        List<String> somList = Arrays.asList(somAttributeNames);
        String className = getRealClassName(plBean);
        Class<?> plBeanRealClass = getRealClass(plBean);
        Field[] fields = plBeanRealClass.getDeclaredFields();
        List<String> fieldNameList = new ArrayList<>();
        List<Field> fieldList = new ArrayList<>();

        for(Field field : fields) {
            Column column = field.getAnnotation(Column.class);
            if (column != null && plList.contains(column.name())) {
                fieldNameList.add(column.name());
                fieldList.add(field);
            }
        }

        if (plBeanRealClass.getSuperclass() != BaseEntity.class) {
            Field[] superclassFields = plBeanRealClass.getSuperclass().getDeclaredFields();

            for(Field field : superclassFields) {
                Column column = field.getAnnotation(Column.class);
                if (column != null && plList.contains(column.name())) {
                    fieldNameList.add(column.name());
                    fieldList.add(field);
                }
            }
        }

        if (plBeanRealClass != InsuranceRiskOffer.class && !fieldNameList.containsAll(plList)) {
            throw new SystemException("Could not find all the attributes configured for the conversion from PL to SOM");
        } else {
            for(Field field : fieldList) {
                Method plGetterMethod;
                try {
                    plGetterMethod = plBeanRealClass.getMethod("get" + StringUtils.capitalize(field.getName()));
                } catch (NoSuchMethodException nsme) {
                    log.error("Could not find the approriate getter for the field conversion from PL to SOM. Make sure the getter exists!", nsme);
                    throw new SystemException("Could not find the approriate getter for the field " + StringUtils.capitalize(field.getName()) + " conversion from PL to SOM. Make sure the getter exists!", nsme);
                }

                String somFieldName = null;

                try {
                    Object value = plGetterMethod.invoke(plBean);
                    if (value != null) {
                        Column column = (Column)field.getAnnotation(Column.class);
                        String plFieldName = column.name();
                        int index = plList.indexOf(plFieldName);
                        somFieldName = (String)somList.get(index);
                        Method somGetterMethod = somBean.getClass().getMethod(accessorMethodName("get", somFieldName));
                        Method somSetterMethod = somBean.getClass().getMethod(accessorMethodName("set", somFieldName), somGetterMethod.getReturnType());
                        somSetterMethod.invoke(somBean, getTranslatedValueFromPLtoSOM(value, somGetterMethod.getReturnType()));
                    }
                } catch (NoSuchMethodException nsme) {
                    String message = MessageFormat.format("Could not find the approriate getter for the field [ {0} ] conversion from PL to SOM. Make sure the getter exists!", somFieldName);
                    log.error(message, nsme);
                    throw new SystemException(message, nsme);
                } catch (InvocationTargetException ex) {
                    log.error("Error while invoking the getter/setter method", ex);
                    throw new SystemException("Error while invoking the getter/setter method", ex);
                } catch (IllegalAccessException ex) {
                    log.error("Access error on method", ex);
                    throw new SystemException("Access error on method", ex);
                }
            }

        }
    }

    public static void setAttributesByReflectionFromSOMtoPL(String[] plAttributeNames, String[] somAttributeNames, BaseEntity plBean, DataObject somDataObject) {
        try {
            if (somDataObject != null && plBean != null && plAttributeNames != null && somAttributeNames != null) {
                List<ChangeSummary.Setting> oldSettingsList = somDataObject.getDataGraph().getChangeSummary().getOldValues(somDataObject);
                if (oldSettingsList != null) {
                    for(ChangeSummary.Setting oldSetting : oldSettingsList) {
                        List<String> plList = Arrays.asList(plAttributeNames);
                        List<String> somList = Arrays.asList(somAttributeNames);
                        String className = getRealClassName(plBean);
                        Class<?> nonLazyLoadedPLClass = Class.forName(className);
                        Field[] fields = nonLazyLoadedPLClass.getDeclaredFields();
                        Property property = oldSetting.getProperty();
                        Object newValue = somDataObject.get(property);
                        if (somList.contains(property.getName())) {
                            String plPropertyColumnName = (String)plList.get(somList.indexOf(property.getName()));
                            Field plField = null;
                            plField = getFieldForPLProperty(fields, plPropertyColumnName);
                            if (plField == null) {
                                if (nonLazyLoadedPLClass != BaseEntity.class) {
                                    plField = getFieldForPLProperty(nonLazyLoadedPLClass.getSuperclass().getDeclaredFields(), plPropertyColumnName);
                                }

                                if (plField == null) {
                                    log.error("Could not find the field for the given PL column name : {}", plPropertyColumnName);
                                    throw new SystemException("Could not find the field for the given PL column name : " + plPropertyColumnName);
                                }
                            }

                            List<Method> plSetterMethodList = new ArrayList();

                            for(Method aMethod : plBean.getClass().getMethods()) {
                                if (StringUtils.equalsIgnoreCase(aMethod.getName(), "set" + StringUtils.capitalize(plField.getName()))) {
                                    plSetterMethodList.add(aMethod);
                                }
                            }

                            if (plSetterMethodList.size() == 0) {
                                log.error("Did not find a setter method in PL matching the SOM property: {} of SOM object: {}", property.getName(), getRealSimpleName(somDataObject));
                                throw new SystemException("Did not find a setter method in PL matching the SOM property: " + property.getName() + " of SOM object: " + getRealSimpleName(somDataObject));
                            }

                            if (plSetterMethodList.size() > 1) {
                                log.error("Found more than one method in PL matching the SOM property: {} of SOM object: {}", property.getName(), getRealSimpleName(somDataObject));
                                throw new SystemException("Found more than one method in PL matching the SOM property: " + property.getName() + " of SOM object: " + getRealSimpleName(somDataObject));
                            }

                            Object valueToSet = null;
                            if (newValue == null) {
                                plSetterMethodList.get(0).invoke(plBean, valueToSet);
                            } else {
                                Method plGetterMethod = plBean.getClass().getMethod("get" + StringUtils.capitalize(plField.getName()));
                                plSetterMethodList.get(0).invoke(plBean, getTranslatedValueFromSOMtoPL(newValue, plGetterMethod.getReturnType()));
                            }
                        } else {
                            log.debug(String.format("the {%s} property is unmapped... it might be a derived value modified in the SOM that we ignore.", property.getName()));
                        }
                    }
                }
            }

        } catch (ClassNotFoundException e) {
            throw new SystemException("ClassNotFoundException in DataMediatorToPL", e);
        } catch (IllegalArgumentException e) {
            throw new SystemException("IllegalArgumentException in DataMediatorToPL", e);
        } catch (IllegalAccessException e) {
            throw new SystemException("IllegalAccessException in DataMediatorToPL", e);
        } catch (InvocationTargetException e) {
            throw new SystemException("InvocationTargetException in DataMediatorToPL", e);
        } catch (SecurityException e) {
            throw new SystemException("SecurityException in DataMediatorToPL", e);
        } catch (NoSuchMethodException e) {
            throw new SystemException("NoSuchMethodException in DataMediatorToPL", e);
        }
    }

    private static Field getFieldForPLProperty(Field[] fields, String plPropertyColumnName) {
        Field plField = null;

        for(Field field : fields) {
            Column column = field.getAnnotation(Column.class);
            if (column != null && column.name().equals(plPropertyColumnName)) {
                plField = field;
                break;
            }
        }

        return plField;
    }

    private static Object getTranslatedValueFromSOMtoPL(Object aValue, Class<?> plFieldClass) {
        Object value = aValue;
        Object objectToSet = null;
        boolean translationError = false;

        try {
            if (plFieldClass.equals(String.class)) {
                if (value instanceof String) {
                    objectToSet = value;
                } else if (value instanceof GregorianCalendar) {
                    objectToSet = value.toString();
                } else {
                    translationError = true;
                }
            } else if (plFieldClass.equals(Date.class)) {
                objectToSet = new Date((Long)value);
            } else if (plFieldClass.equals(Boolean.class)) {
                if ("Y".equals(value)) {
                    objectToSet = Boolean.TRUE;
                } else if ("N".equals(value)) {
                    objectToSet = Boolean.FALSE;
                }
            } else if (plFieldClass.equals(Integer.class)) {
                if (value instanceof Integer) {
                    objectToSet = value;
                } else if (value instanceof String) {
                    objectToSet = Integer.parseInt((String)value);
                } else if (value instanceof Double) {
                    objectToSet = ((Double)value).intValue();
                } else {
                    translationError = true;
                }
            } else if (plFieldClass.equals(Byte.class)) {
                objectToSet = ((Integer)value).byteValue();
            } else if (plFieldClass.equals(Short.class)) {
                objectToSet = ((Integer)value).shortValue();
            } else if (plFieldClass.equals(BigDecimal.class)) {
                objectToSet = BigDecimal.valueOf((Double)value);
            } else if (plFieldClass.equals(Double.class)) {
                objectToSet = value;
            } else if (Short.TYPE.equals(plFieldClass)) {
                objectToSet = ((Integer)value).shortValue();
            } else if (Integer.TYPE.equals(plFieldClass)) {
                objectToSet = (Integer)value;
            } else if (plFieldClass.isEnum()) {
                Method enumValueOfCodeMethod = null;
                if (value instanceof String) {
                    enumValueOfCodeMethod = plFieldClass.getMethod("valueOfCode", String.class);
                } else if (value instanceof Integer) {
                    if (plFieldClass.equals(PolicyTermInMonthsEnum.class)) {
                        enumValueOfCodeMethod = plFieldClass.getMethod("valueOfCode", Integer.class);
                    } else {
                        translationError = true;
                    }
                } else {
                    translationError = true;
                }

                objectToSet = enumValueOfCodeMethod != null ? enumValueOfCodeMethod.invoke((Object)null, value) : null;
            } else {
                translationError = true;
            }

            if (translationError) {
                log.error("No appropriate conversion has been found for the {SOM field class, PL field class} pair. SOM Object: {} / PL Object: {}", value.getClass().getName(), plFieldClass.getName());
                throw new SystemException("No appropriate conversion has been found for the {SOM field class, PL field class} pair. SOM Object: " + value.getClass().getName() + " / PL Object: " + plFieldClass.getName());
            } else {
                return objectToSet;
            }
        } catch (SecurityException e) {
            throw new SystemException("SecurityException in DataMediatorToPL", e);
        } catch (NoSuchMethodException e) {
            throw new SystemException("NoSuchMethodException in DataMediatorToPL", e);
        } catch (IllegalArgumentException e) {
            throw new SystemException("IllegalArgumentException in DataMediatorToPL", e);
        } catch (IllegalAccessException e) {
            throw new SystemException("IllegalAccessException in DataMediatorToPL", e);
        } catch (InvocationTargetException e) {
            throw new SystemException("InvocationTargetException in DataMediatorToPL", e);
        }
    }

    public static String logCoveragesOnPolicyVersion(PolicyVersion policyVersion, boolean isEis) {
        return isEis ? logEISCoveragesOnPolicyVersionForOfferType(policyVersion, null) : logCoveragesOnPolicyVersionForOfferType(policyVersion, null);
    }

    public static String logEISCoveragesOnPolicyVersionForOfferType(PolicyVersion policyVersion, OfferTypeCodeEnum offerTypeCodeEnum) {
        List<InsuranceRisk> irList = new LinkedList<>(policyVersion.getTheInsuranceRisk());

        for(PolicyVersion policyVersionOffer : policyVersion.getThePolicyVersionOffer()) {
            if (policyVersionOffer.getTheInsuranceRisk() != null) {
                irList.addAll(policyVersionOffer.getTheInsuranceRisk());
            }
        }

        String logInfo = new String();

        for(InsuranceRisk insuranceRisk : irList) {
            if (insuranceRisk != null && (offerTypeCodeEnum == null || offerTypeCodeEnum.getCode().equals(insuranceRisk.getOfferType()))) {
                List<Coverage> coList = insuranceRisk.getTheCoverage();
                Integer sequence = insuranceRisk.getInsuranceRiskSequence();
                logInfo = logInfo.concat("List of coverage codes on insurance risk #" + sequence + " with offerType " + insuranceRisk.getOfferType() + "\n");

                for(Coverage coverage : coList) {
                    logInfo = logInfo.concat(coverage.getCoverageCode() + ", " + coverage.getCoverageType() + "\n");
                }
            }
        }

        return logInfo;
    }

    public static String logCoveragesOnPolicyVersionForOfferType(PolicyVersion policyVersion, OfferTypeCodeEnum offerTypeCodeEnum) {
        List<InsuranceRisk> irList = new LinkedList<>(policyVersion.getTheInsuranceRisk());

        for(PolicyVersion policyVersionOffer : policyVersion.getThePolicyVersionOffer()) {
            if (policyVersionOffer.getTheInsuranceRisk() != null) {
                irList.addAll(policyVersionOffer.getTheInsuranceRisk());
            }
        }

        String logInfo = new String();

        for(InsuranceRisk insuranceRisk : irList) {
            if (insuranceRisk != null && (offerTypeCodeEnum == null || offerTypeCodeEnum.getCode().equals(insuranceRisk.getOfferType()))) {
                List<Coverage> coList = insuranceRisk.getTheCoverage();
                Integer sequence = insuranceRisk.getInsuranceRiskSequence();
                logInfo = logInfo.concat("List of coverage codes on insurance risk #" + sequence + " with offerType " + insuranceRisk.getOfferType() + "\n");

                for(Coverage coverage : coList) {
                    logInfo = logInfo.concat(coverage.getCoverageCode() + ", " + coverage.getCoverageType() + ", " + coverage.getAnnualPremium() + ", " + coverage.getCoverageSelectedInd() + "\n");
                }
            }
        }

        return logInfo;
    }
}
