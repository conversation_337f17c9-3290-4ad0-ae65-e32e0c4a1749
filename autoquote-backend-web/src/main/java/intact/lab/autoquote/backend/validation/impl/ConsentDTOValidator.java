package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.enums.ConsentTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IConsentDTOValidator;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("ConsentDTOValidator")
public class ConsentDTOValidator implements IConsentDTOValidator {

	@Override
	public void validate(ConsentDTO consentDTO, Errors errors) {
		validateConsentInd(consentDTO.getConsentInd(), errors);
		validateConsentType(consentDTO.getConsentType(), errors);
	}

	private void validateConsentInd(Boolean consentInd, Errors errors) {
		if (null == consentInd) {
			errors.rejectValue("consentInd", BRulesExceptionEnum.NotBlank.getErrorCode(), "[consentInd]");
		}
	}

	private void validateConsentType(ConsentTypeEnum consentType, Errors errors) {
		if (null == consentType) {
			errors.rejectValue("consentType", BRulesExceptionEnum.NotBlank.getErrorCode(), "[consentType]");
		}
	}

}
