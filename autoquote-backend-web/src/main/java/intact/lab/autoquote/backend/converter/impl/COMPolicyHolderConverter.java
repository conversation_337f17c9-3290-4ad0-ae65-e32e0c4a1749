package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.address.ComAddress;
import com.intact.com.driver.ComDriver;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component("comPolicyHolderConverter")
public class COMPolicyHolderConverter implements ICOMConverter<PolicyHolderDTO, ComDriver> {

	@Resource(name = "comAddressConverter")
	private ICOMConverter<AddressDTO, ComAddress> comAddressConverter;

	@Override
	public PolicyHolderDTO toDTO(ComDriver driver) {
		PolicyHolderDTO policyHolder = new PolicyHolderDTO();

		if (driver != null) {
			if (!driver.getIsDriver()) {
				policyHolder.setPartyId(driver.getWebMsgId());
			}
			if (!StringUtils.isEmpty(driver.getDurationOfInsuranceTerm())) {
				policyHolder.setNumberOfYearsWithCurrentInsurer(Integer.parseInt(driver.getDurationOfInsuranceTerm()));
			}
		}

		return policyHolder;
	}

	@Override
	public ComDriver toCOM(PolicyHolderDTO policyHolder, ComDriver initialComDriver) {
		ComDriver driver = initialComDriver == null ? new ComDriver() : initialComDriver;

		driver.setDurationOfInsuranceTerm(policyHolder.getNumberOfYearsWithCurrentInsurer() != null ? policyHolder.getNumberOfYearsWithCurrentInsurer().toString() : null);
		driver.setPolicyRefusedInd(Boolean.FALSE);
		driver.setFullTermInsuranceInd(Boolean.FALSE);
		driver.setHolderAutoInsuranceIndicator(Boolean.FALSE);
		driver.setMarketingConsent(Boolean.FALSE);
		driver.setCreateProfileConsentInd(Boolean.TRUE);
		return driver;
	}

	public ICOMConverter<AddressDTO, ComAddress> getComAddressConverter() {
		return comAddressConverter;
	}

	public void setComAddressConverter(
			ICOMConverter<AddressDTO, ComAddress> comAddressConverter) {
		this.comAddressConverter = comAddressConverter;
	}
}
