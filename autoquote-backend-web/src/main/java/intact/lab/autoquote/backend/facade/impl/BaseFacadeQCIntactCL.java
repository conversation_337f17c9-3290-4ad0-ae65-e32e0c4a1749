package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.com.context.ComContext;

import java.util.Arrays;
import java.util.List;

/**
 * Base Facade Quebec Intact
 * 
 * <AUTHOR>
 *
 */
@ComponentLocal(province = ProvinceCodeEnum.QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class BaseFacadeQCIntactCL extends BaseFacadeIntact {

}
