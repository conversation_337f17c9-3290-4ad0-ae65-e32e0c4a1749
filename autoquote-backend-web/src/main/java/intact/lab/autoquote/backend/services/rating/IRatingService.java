package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import org.springframework.util.StopWatch;

import java.util.Map;

public interface IRatingService {

    /**
     * Rate offer.
     *
     * @param aPolicyVersion the a policy version
     * @param isAgent the is agent flag
     *
     * @return the policy version
     *
     * @throws AutoquoteRatingException the rating exception
     */
    PolicyVersion rateOffer(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled) throws AutoquoteRatingException;

    /**
     * Add or remove the UBI endorsement
     * @param aPlPolicyVersion
     */
    void manageUbi(PolicyVersion aPlPolicyVersion, Map<Integer, Boolean> ubiSelectedByRiskSeq,
                   StopWatch performanceWatch) throws AutoquoteRatingException;

    OfferTypeCodeEnum getReferenceOfferType(String applicationMode) throws AutoquoteRatingException;

    void manageUbiStatus(PolicyVersion aPlPolicyVersion);

    /**
     * Gets the ammount that as no taxes on a policyOffer.
     *
     * @param currentPolicyOfferRating the current policy offer rating
     *
     * @return the untaxable amount
     */
    double getUntaxableAmount(PolicyOfferRating currentPolicyOfferRating);
}
