package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.ManufacturingContext;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import org.springframework.util.StopWatch;

public interface ICreditScoreService {

	/**
	 * Call the credit score.
	 * 
	 * @param somPolicyVersion the som policy version
	 * @param aCtxt the a ctxt
	 * @param performanceWatch
	 * @throws AutoquoteRatingException the rating exception
	 */
	void callTheCreditScore(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
			ManufacturingContext aCtxt, StopWatch performanceWatch) throws AutoquoteRatingException;

	/**
	 * Call the credit score.
	 * 
	 * @param somPolicyVersion the som policy version
	 * @param aCtxt the a ctxt
	 * @param performanceWatch {@link StopWatch}
	 * @throws AutoquoteRatingException the rating exception
	 */
	void callTheCreditScoreWithoutClientEligibility(
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, ManufacturingContext aCtxt,
			StopWatch performanceWatch) throws AutoquoteRatingException;

}
