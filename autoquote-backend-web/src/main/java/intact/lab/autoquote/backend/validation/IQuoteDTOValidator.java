package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.springframework.validation.Errors;

public interface IQuoteDTOValidator {

	void validateQuote(QuoteDTO quoteDTO, Errors errors) throws AutoQuoteApiParametersException;

	void setContext(ValidationContext context);

	void setDriverDTOValidator(IDriverDTOValidator driverDTOValidator);

	void setPartyDTOValidator(IPartyDTOValidator partyDTOValidator);

	void setPolicyHolderDTOValidator(IPolicyHolderDTOValidator policyHolderDTOValidator);

	void setVehicleDTOValidator(IVehicleDTOValidator vehicleDTOValidator);


}
