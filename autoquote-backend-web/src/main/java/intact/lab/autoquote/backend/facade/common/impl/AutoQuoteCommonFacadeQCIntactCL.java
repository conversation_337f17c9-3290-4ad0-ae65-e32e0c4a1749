package intact.lab.autoquote.backend.facade.common.impl;

import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.domain.VehicleDetail;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.vehicle.IVehicleDetailService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.converter.impl.COMQuoteConverter;
import intact.lab.autoquote.backend.facade.impl.AutoQuoteCommonFacade;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Locale;

@ComponentLocal(province = ProvinceCodeEnum.QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AutoQuoteCommonFacadeQCIntactCL extends AutoQuoteCommonFacade {

	private final IVehicleDetailService vehicleDetailService;

	AutoQuoteCommonFacadeQCIntactCL(@Qualifier("cifSubBrokersService") ISubBrokersService subBrokersService,
									ICommonBusinessProcess commonBusinessProcess, COMQuoteConverter comQuoteConverter,
									RatingConfig ratingConfig, IVehicleDetailService vehicleDetailService,
									IInsurancePolicyService insurancePolicyService, VehicleHelper vehicleHelper,
									BloomMQHandlerService bloomMQHandlerService, BrokerService brokerService) {
		super(subBrokersService, commonBusinessProcess, comQuoteConverter, ratingConfig, insurancePolicyService, vehicleHelper, bloomMQHandlerService, brokerService);
		this.vehicleDetailService = vehicleDetailService;
	}

	@Override
	protected void ajustPrincipalDriverAndRegisterOwner(
			CommunicationObjectModel quote, ComVehicle vehicle) {
		// no implementation required here
	}

	@Override
	public void assignDriverToVehicle(CommunicationObjectModel quote) throws AutoquoteFacadeException {
		super.assignDriverToVehicle(quote);
		this.callCreditScore(quote);
	}

	// 46231 We need to validate before saving / rating if the detailed spec are
	// available for the vehicle and if the group is not 69 and over.
	@Override
	public boolean validateVehiclesDEFAULTRB(CommunicationObjectModel aCom, Locale locale) {

		// Can we get detailed vehicle specifications ?
		Language language = Language.fromLocale(locale);
		Province province = Province.fromLocale(locale);

		VehicleDetail details = null;

		if(  CollectionUtils.isEmpty(aCom.getVehicles())){
			return false;
		}

		try {
			details = this.vehicleDetailService.getVehicleDetailService(province, language,
					aCom.getFirstRatingDate().toDate(),
					aCom.getVehicle(0).getVehicleModel().getCode(),
					aCom.getVehicle(0).getYear(),
					DistributionChannelCodeEnum.THROUGH_BROKERS,
					InsuranceBusinessCodeEnum.REGULAR);

		}catch (Exception e) {
			LOG.error(Logger.EVENT_FAILURE, String.format("an exception occured in the getting during getVehicleDetailService: %s", e.getMessage()));
		}

		if (details==null) {
			return true;
		}

		// Is vehicle group 69 or higher
		int rateGroupNum = 0;
		String rateGroupClearCollision = details.getVehicleRateGroupClearCollision();

		if (StringUtils.isBlank(rateGroupClearCollision)) {
			return true;
		}

		// make sure that the value is numerical or null
		if (StringUtils.isNotBlank(rateGroupClearCollision) && !StringUtils.isNumeric(rateGroupClearCollision)) {
			return true;
		} else if (!StringUtils.isBlank(rateGroupClearCollision) && StringUtils.isNumeric(rateGroupClearCollision)) {
			rateGroupNum = Integer.parseInt(StringUtils.trim(rateGroupClearCollision));
		}

		if (rateGroupNum>= 69){
			return true;
		}

		// ok, we have detailed spec and vehicle group is under 69
		return false;

	}

	@Override
	protected PolicyVersion clonePolicyVersion(CommunicationObjectModel aCom, PolicyVersion policyVersion) {
		PolicyVersion returnPV;

		if (this.hasOffer(policyVersion)) {
			String cifClientIdStr = null;
			if (aCom.getCifClientId() != null) {
				cifClientIdStr = Long.toString(aCom.getCifClientId());
			}
			returnPV = this.cloneService.clone(policyVersion, cifClientIdStr, aCom.getContext().getMarketingPromotionCode());
			aCom.setPolicyVersionId(returnPV.getId());
			return returnPV;
		} else {
			return policyVersion;
		}
	}

	private Boolean hasOffer(PolicyVersion aPolicyVersion) {
		// insuranceRisk.getInsuranceRiskOffer !=null
		boolean hasOffer = Boolean.FALSE;
		for (InsuranceRisk ir : aPolicyVersion.getInsuranceRisks()) {
			if (ir != null) {
				// has offer when found riskOffers and has at least one risk offer
				hasOffer = (ir.getInsuranceRiskOffers() != null && ir.getInsuranceRiskOffers().size() > 0);
				if (hasOffer) {
					break; // end loop;
				}
			}
		}
		return hasOffer;
	}
}
