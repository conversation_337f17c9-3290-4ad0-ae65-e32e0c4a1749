package intact.lab.autoquote.backend.datamediator.utils;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.plp.crypto.IIngCipher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class CipherUtils {


    private final IIngCipher ingCipher;

    public CipherUtils(@Qualifier("plp-ingAesCipher") IIngCipher ingCipher) {
        this.ingCipher = ingCipher;
    }

    public String getFinancialInstitutionNumber(String financialInstitutionNumber) {
        if (this.isFinancialInstitutionNumberIsInVormetricFormat(financialInstitutionNumber)) {
            return financialInstitutionNumber;
        } else {
            try {
                return this.ingCipher.decryptToString(financialInstitutionNumber);
            } catch (Exception var3) {
                throw new SystemException("Failed to decrypt the financial institution number with cipher");
            }
        }
    }

    private boolean isFinancialInstitutionNumberIsInVormetricFormat(String financialInstitutionNumber) {
        return financialInstitutionNumber.length() == 3;
    }

    public String getRoutingNumber(String routingNumber) {
        if (this.isRoutingNumberIsInVormetricFormat(routingNumber)) {
            return routingNumber;
        } else {
            try {
                return this.ingCipher.decryptToString(routingNumber);
            } catch (Exception var3) {
                throw new SystemException("Failed to decrypt the routing number with cipher");
            }
        }
    }

    private boolean isRoutingNumberIsInVormetricFormat(String routingNumber) {
        return routingNumber.length() == 5;
    }

    public String getBankAccountNumber(String bankAccountNumber) {
        if (this.isBankAccountNumberIsInVormetricFormat(bankAccountNumber)) {
            return bankAccountNumber;
        } else {
            try {
                return this.ingCipher.decryptToString(bankAccountNumber);
            } catch (Exception var3) {
                throw new SystemException("Failed to decrypt the bank account number with cipher");
            }
        }
    }

    private boolean isBankAccountNumberIsInVormetricFormat(String bankAccountNumber) {
        return bankAccountNumber.length() <= 12 && bankAccountNumber.length() >= 7;
    }

    public String getCreditCardAccountNumber(String creditCardAccountNumber) {
        try {
            return this.ingCipher.decryptToString(creditCardAccountNumber);
        } catch (Exception var3) {
            throw new SystemException("Failed to decrypt the bank account number with cipher");
        }
    }

    public String getCreditCardExpriryDate(String creditCardExpiryDate) {
        try {
            return this.ingCipher.decryptToString(creditCardExpiryDate);
        } catch (Exception var3) {
            throw new SystemException("Failed to decrypt the CreditCardExpiryDate");
        }
    }

    public String getCreditCardHolderName(String creditCardHolderName) {
        try {
            return this.ingCipher.decryptToString(creditCardHolderName);
        } catch (Exception var3) {
            throw new SystemException("Failed to decrypt the CreditCardHolderName");
        }
    }
}

