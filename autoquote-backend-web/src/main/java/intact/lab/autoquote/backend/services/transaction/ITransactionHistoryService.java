/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2015, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.transaction;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.state.enums.ComStateEnum;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.com.transaction.activity.enums.ComEventEnum;

/**
 * Interface for Transaction History.
 * <AUTHOR>
 *
 */
public interface ITransactionHistoryService {

	/**
	 * Update the transaction history
	 *
	 * @param event The event
	 * @param aPolicyVersion Contains the details of the policy
	 */
	void updateTransactionHistory(ComEvent event, PolicyVersion aPolicyVersion);

	/**
	 * Update the transaction history
	 *
	 * @param eventEnum The event enum
	 * @param aPolicyVersion Contains the details of the policy
	 */
	void updateTransactionHistory(ComEventEnum eventEnum, PolicyVersion aPolicyVersion);

	/**
	 * Update transaction history on Roadblock
	 *
	 * @param roadblockEx {@link RoadBlockException}
	 * @param currentState {@link ComStateEnum}
	 * @param policyVersion {@link PolicyVersion}
	 */
	void updateTransHistoryOnRoadBlock(RoadBlockException roadblockEx, ComStateEnum currentState, PolicyVersion policyVersion);

}
