/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;

/**
 * 
 * <AUTHOR>
 */
public interface IAssignDriverService {

	/**
	 * This function is used to assign the occasionnal and the not-rated driver on the policy. This function will
	 * reassign all the driver except those that are mark as principal driver
	 * 
	 * @param aSomPolicyVersion the a som policy version
	 * @param aContext the a context
	 * 
	 * @return the policy version
	 */
	PolicyVersion assignDrivers(PolicyVersion aSomPolicyVersion, ManufacturingContext aContext);
}
