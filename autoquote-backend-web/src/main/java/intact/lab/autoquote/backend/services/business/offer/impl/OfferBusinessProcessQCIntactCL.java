/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.NumberStabilityMonthsCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.business.rules.enums.RoadBlockExceptionEnum;
import com.intact.business.rules.exception.BusinessRuleException;
import com.intact.business.rules.offer.BR2496_IsHomeAssumedInsuredWithCompany;
import com.intact.business.rules.offer.BR2497_CreditScoreUnderThresholdOrNoConsent;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.impl.ExecuteServiceQCIntact;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Calendar;
import java.util.Date;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class OfferBusinessProcessQCIntactCL extends OfferBusinessProcess {

    @Autowired
    protected BR2497_CreditScoreUnderThresholdOrNoConsent br2497;

    @Autowired
    protected IVehicleHelper vehicleHelper;

    @AutowiredLocal
    protected IRateManagerService rateManagerService;

    @Autowired
    @Qualifier("executeServiceQCIntact")
    private ExecuteServiceQCIntact executeService;

    @Autowired
    private BR2496_IsHomeAssumedInsuredWithCompany br2496;

    @AutowiredLocal
    protected IRatingService ratingService;

    /**
     * Determine quote info post process for Intact.
     *
     * @param aPlPolicyVersion {@link PolicyVersion}
     * @throws AutoquoteBusinessException
     */
    @Override
    protected void determineQuoteInfosPostProcess(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException {
        PolicyHolder policyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(aPlPolicyVersion);

        if (aPlPolicyVersion.getCombinedPolicyCode() != null && CombinedPolicyCodeEnum.COMBO_POLICY.equals(aPlPolicyVersion.getCombinedPolicyCode())) {
            aPlPolicyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.AUTOMOBILE_AND_RESIDENTIAL_PRODUCT_INCLUDED);
        }

        if (policyHolder != null && policyHolder.getNumberStabilityMonths() != null) {
            // Pega only set the NumberStabilityMonths. Convert the value and set the NumberStabilityMonthsCode
            switch (policyHolder.getNumberStabilityMonths()) {
                case 6:
                    policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.LESS_THAN_ONE);
                    break;
                case 18:
                    policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.BETWEEN_TWO_AND_THREE);
                    break;
                case 42:
                    policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.BETWEEN_THREE_AND_FOUR);
                    break;
                case 54:
                    policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.BETWEEN_FOUR_AND_FIVE);
                    break;
                case 66:
                    policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.FIVE_OR_MORE);
                    break;
                default:
                    throw new AutoquoteBusinessException("NumberStabilityMonths with value (" + policyHolder.getNumberStabilityMonths() + ") cannot be used for Intact.");
            }
        }
        // set the dateOfLastMove
        NumberStabilityMonthsCodeEnum stabilMonthCode = policyHolder != null ? policyHolder.getNumberStabilityMonthsCode() : null;
        if (stabilMonthCode != null) {
            Calendar dateOfLastMove = Calendar.getInstance();
            dateOfLastMove.setTime(this.capiPolicyChangeDateService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, aPlPolicyVersion.getId()));
            dateOfLastMove.add(Calendar.MONTH, -policyHolder.getNumberStabilityMonths());
            policyHolder.getParty().setDateOfLastMove(dateOfLastMove.getTime());
        }

        // post process for Claims
        DriverComplementInfo driverComplementInfo = policyHolder.getParty().getDriverComplementInfo();
        if (driverComplementInfo != null) {
            if (CollectionUtils.isEmpty(aPlPolicyVersion.getClaims())) {
                driverComplementInfo.setRelevantClaimIndicator(Boolean.FALSE);
            } else {
                driverComplementInfo.setRelevantClaimIndicator(Boolean.TRUE);
            }
        }

        // principalDriver
        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk = aPlPolicyVersion.getInsuranceRisks().iterator().next();
        PartyRoleInRisk principalDriver = this.vehicleHelper.getPrincipalDriver(insuranceRisk);

        if (principalDriver != null) {
            Date principalDriverSinceDate = principalDriver.getPrincipalDriverSince();
            Date principalDateDriverHasOwnedVehicle = principalDriver.getDateDriverHasOwnedVehicle();

            if (principalDriverSinceDate != null) {
                // manipulation (convert date to code)
                if (LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(aPlPolicyVersion.getInsurancePolicy().getLineOfBusiness())) {
                    principalDriver.setPrincipalDriverSinceCode(this.policyVersionHelper.convertDateToDriverSinceCodeEnum(principalDateDriverHasOwnedVehicle));
                } else {
                    principalDriver.setPrincipalDriverSinceCode(this.policyVersionHelper.convertDateToDriverSinceCodeEnum(principalDriverSinceDate));
                }
                principalDriver.setAlreadyBeenPrincipalDriverIndicator(Boolean.TRUE);
            }
            this.policyVersionHelper.definePrincipalDriverRelatedInformation(aPlPolicyVersion);

        }
    }

    @Override
    public IRateManagerService getRateManagerService() {
        return this.rateManagerService;
    }

    @Override
    protected IExecuteService getExecuteService() {
        return this.executeService;
    }

    @Override
    public IRatingService getRatingService() {
        return this.ratingService;
    }

    @Override
    public void validatePostBR(PolicyVersion aPolicyVersion) throws BusinessRuleException {
        if (!this.br2497.validate(aPolicyVersion)) {
            throw new BusinessRuleException(RoadBlockExceptionEnum.BR2497, RoadBlockExceptionContextEnum.OFFER);
        }
    }

    @Override
    public void selectOffer(final PolicyVersion policyVersion, Boolean isUbiEnabled) throws AutoquoteRatingException, BusinessRuleException {

        if (!this.br2496.validate(policyVersion)) {
            throw new BusinessRuleException(RoadBlockExceptionEnum.BR2496, RoadBlockExceptionContextEnum.OFFER);
        }

        super.selectOffer(policyVersion, isUbiEnabled);
    }
}
