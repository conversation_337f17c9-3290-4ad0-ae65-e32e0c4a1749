package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.plp.dao.diagnostics.IDiagnosticAutomatedAdviceDAO;
import com.ing.canada.plp.dao.party.IGroupRepositoryEntryDAO;
import com.ing.canada.plp.dao.policyversion.IPolicyAdditionalCoverageDAO;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.billing.Account;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.billing.PaymentSchedule;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.MessageRepositoryEntry;
import com.ing.canada.plp.domain.businesstransaction.TransactionalMessage;
import com.ing.canada.plp.domain.businesstransaction.TransactionalMessageElement;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.diagnostics.DiagnosticAutomatedAdvice;
import com.ing.canada.plp.domain.diagnostics.DiagnosticAutomatedAdviceRepositoryEntry;
import com.ing.canada.plp.domain.driver.Conviction;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.InternalTechnicalOfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartySubGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.RatingFactorCalculationMethodEnum;
import com.ing.canada.plp.domain.enums.RatingFactorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.RatingRiskTypeCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRole;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromBasicCoverage;
import com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromNonBasicCoverage;
import com.ing.canada.plp.domain.insurancerisk.Trailer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOfferPackageComp;
import com.ing.canada.plp.domain.insuranceriskoffer.CoveragePremiumOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.RatingRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.SubCoveragePremiumOffer;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.party.GeographicalAssessment;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyGroup;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.MarketSegment;
import com.ing.canada.plp.domain.policyversion.PolicyAdditionalCoverage;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PriorCarrierPolicyInfo;
import com.ing.canada.plp.domain.policyversion.Producer;
import com.ing.canada.plp.domain.policyversion.ProducerRepositoryEntry;
import com.ing.canada.plp.domain.policyversion.ReferenceDate;
import com.ing.canada.plp.domain.usertype.BaseEntity;
import com.ing.canada.plp.domain.vehicle.AntiTheftDevice;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleEquipment;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.ing.canada.plp.service.ICoverageService;
import com.ing.canada.plp.service.IInsuranceRiskOfferService;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IRatingRiskOfferService;
import com.ing.canada.plp.service.IVehicleService;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.moneyProvisionPremium.MultRatingFactorFromBasicCoverage;
import com.ing.canada.som.interfaces.moneyProvisionPremium.MultRatingFactorFromNonBasicCoverage;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.RatingRisk;
import com.ing.canada.som.sdo.agreement.DiagnosticAutomatedAdviceBO;
import com.ing.canada.som.sdo.agreement.DiagnosticAutomatedAdviceRepositoryEntryBO;
import com.ing.canada.som.sdo.agreement.PolicyAdditionalCoverageBO;
import com.ing.canada.som.sdo.agreement.PolicyVersionBO;
import com.ing.canada.som.sdo.businessmodel.impl.ObjectContainerBOImpl;
import com.ing.canada.som.sdo.businesstransaction.TransactionalMessageBO;
import com.ing.canada.som.sdo.claim.ClaimBO;
import com.ing.canada.som.sdo.moneyprovisionpremium.CoveragePremiumBO;
import com.ing.canada.som.sdo.moneyprovisionpremium.SubCoveragePremiumBO;
import com.ing.canada.som.sdo.moneyprovisionpremium.impl.SubCoveragePremiumBOImpl;
import com.ing.canada.som.sdo.party.CreditScoreBO;
import com.ing.canada.som.sdo.party.GroupRepositoryEntryBO;
import com.ing.canada.som.sdo.party.PartyBO;
import com.ing.canada.som.sdo.party.PartyGroupBO;
import com.ing.canada.som.sdo.partyroleinrisk.DriverBO;
import com.ing.canada.som.sdo.physicalobject.VehicleBO;
import com.ing.canada.som.sdo.physicalobject.VehicleEquipmentBO;
import com.ing.canada.som.sdo.physicalobject.impl.VehicleEquipmentBOImpl;
import com.ing.canada.som.sdo.product.CoverageRepositoryEntryBO;
import com.ing.canada.som.sdo.registration.ConvictionBO;
import com.ing.canada.som.sdo.risk.CoverageBO;
import com.ing.canada.som.sdo.risk.InsuranceRiskBO;
import com.ing.canada.som.sdo.risk.RatingRiskBO;
import com.ing.canada.sombase.IGenericRootObject;
import com.ing.canada.sombase.ModelFactory;
import commonj.sdo.ChangeSummary;
import commonj.sdo.DataGraph;
import commonj.sdo.DataObject;
import commonj.sdo.Property;
import intact.lab.autoquote.backend.datamediator.DMConstants;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorCoverageAdvisor;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPlLegacyRatingInfo;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Months;
import org.owasp.esapi.ESAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Column;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Transactional
@Component
public class DataMediatorToPL implements IDataMediatorToPL {

    @Setter
    private OfferTypeCodeEnum referenceOffer = null;
    private boolean isKeepNonEligibleCoverages = false;
    private static final OfferTypeCodeEnum DEFAULT_REFERENCE_OFFER;
    private static final Class<?>[] PL_CLASSES;
    private static final String[] SOM_OBJECT_NAMES;
    ArrayList<String> ignoredAdditions = new ArrayList<>();

    @Getter
    @Setter
    private Map<String, Field> somMediators = null;

    @Getter
    @Setter
    private Map<String, Field> plpMediators = null;

    private IBaseEntityDAO baseEntityDAO;
    private IPolicyAdditionalCoverageDAO policyCoverageDAO;
    private IDiagnosticAutomatedAdviceDAO diagnosticAutomatedAdviceDAO;
    private IInsuranceRiskOfferHelper insuranceRiskOfferHelper;
    private IInsuranceRiskOfferService insuranceRiskOfferService;
    private IDataMediatorToPlLegacyRatingInfo mediatorLegacyRatingInfoByPostalCodeToPL;
    private ICoverageService coverageService;
    private IInsuranceRiskService insuranceRiskService;
    private IRatingRiskOfferService ratingRiskOfferService;
    private IGroupRepositoryEntryDAO groupRepositoryEntryDAO;
    private IVehicleService vehicleService;
    private IDataMediatorCoverageAdvisor coverageOptionMediator;

    private static final String POLICY_ADDITIONAL_COVERAGE = "PolicyAdditionalCoverage";
    private static final String POLICY_VERSION = "PolicyVersion";
    private static final String DIAGNOSTIC_AUTOMATED_ADVICE = "DiagnosticAutomatedAdvice";
    private static final String ANTI_THEFT_DEVICE = "AntiTheftDevice";
    private static final String REFERENCE_DATE = "ReferenceDate";
    private static final String CONVICTION = "Conviction";
    private static final String VEHICLE_EQUIPMENT = "VehicleEquipment";
    private static final String TRANSACTIONAL_MESSAGE = "TransactionalMessage";
    private static final String COVERAGE_PREMIUM = "CoveragePremium";
    private static final String INSURANCE_RISK = "InsuranceRisk";
    public static final String MESSAGE_REPOSITORY_ENTRY = "MessageRepositoryEntry";
    public static final String TRANSACTIONAL_MESSAGE_ELEMENT = "TransactionalMessageElement";
    public static final String DRIVER = "Driver";
    public static final String VEHICLE = "Vehicle";
    public static final String COVERAGE = "Coverage";
    public static final String SUB_COVERAGE_PREMIUM = "SubCoveragePremium";
    public static final String LEGACY_RATING_INFO_BY_POSTAL_CODE = "LegacyRatingInfoByPostalCode";
    public static final String THE_INSURANCE_RISK_BO = "theInsuranceRiskBO";
    public static final String ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE = "IllegalArgumentException in DataMediatorToPL";
    public static final String ILLEGAL_ACCESS_EXCEPTION_MESSAGE = "IllegalAccessException in DataMediatorToPL";

    @Autowired
    public DataMediatorToPL(IBaseEntityDAO baseEntityDAO, IPolicyAdditionalCoverageDAO policyCoverageDAO,
                            IDiagnosticAutomatedAdviceDAO diagnosticAutomatedAdviceDAO,
                            @Lazy IInsuranceRiskOfferHelper insuranceRiskOfferHelper,
                            @Lazy IInsuranceRiskOfferService insuranceRiskOfferService,
                            IDataMediatorToPlLegacyRatingInfo mediatorLegacyRatingInfoByPostalCodeToPL,
                            ICoverageService coverageService, IInsuranceRiskService insuranceRiskService,
                            IRatingRiskOfferService ratingRiskOfferService, IGroupRepositoryEntryDAO groupRepositoryEntryDAO,
                            IVehicleService vehicleService, IDataMediatorCoverageAdvisor coverageOptionMediator) {
        this();
        this.baseEntityDAO = baseEntityDAO;
        this.policyCoverageDAO = policyCoverageDAO;
        this.diagnosticAutomatedAdviceDAO = diagnosticAutomatedAdviceDAO;
        this.insuranceRiskOfferHelper = insuranceRiskOfferHelper;
        this.insuranceRiskOfferService = insuranceRiskOfferService;
        this.mediatorLegacyRatingInfoByPostalCodeToPL = mediatorLegacyRatingInfoByPostalCodeToPL;
        this.coverageService = coverageService;
        this.insuranceRiskService = insuranceRiskService;
        this.ratingRiskOfferService = ratingRiskOfferService;
        this.groupRepositoryEntryDAO = groupRepositoryEntryDAO;
        this.vehicleService = vehicleService;
        this.coverageOptionMediator = coverageOptionMediator;
    }

    static {
        DEFAULT_REFERENCE_OFFER = OfferTypeCodeEnum.RECOMMENDED;
        PL_CLASSES = new Class[]{DiagnosticAutomatedAdvice.class, DiagnosticAutomatedAdviceRepositoryEntry.class, Account.class, AdditionalInterestRole.class, Address.class, AntiTheftDevice.class, Billing.class, BusinessTransaction.class, BusinessTransactionActivity.class, BusinessTransactionActivity.class, Claim.class, Consent.class, Conviction.class, CoverageOffer.class, CoveragePremiumOffer.class, CreditScore.class, PartyRoleInRisk.class, PartyRoleInRisk.class, DriverComplementInfo.class, DriverLicenseClass.class, InsurancePolicy.class, com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, KindOfLoss.class, Party.class, PartyGroup.class, PaymentSchedule.class, Phone.class, PolicyAdditionalCoverage.class, PolicyHolder.class, com.ing.canada.plp.domain.policyversion.PolicyVersion.class, PriorCarrierPolicyInfo.class, Producer.class, RatingRiskOffer.class, ReferenceDate.class, SubCoveragePremiumOffer.class, Trailer.class, TransactionalMessage.class, TransactionalMessageElement.class, Vehicle.class, VehicleEquipment.class, GroupRepositoryEntry.class, ManufacturingContext.class, MunicipalityDetailSpecification.class, MunicipalityRepositoryEntry.class, ProducerRepositoryEntry.class, VehicleDetailSpecificationRepositoryEntry.class, VehicleRepositoryEntry.class, null, null, null, MarketSegment.class, null, GeographicalAssessment.class};
        SOM_OBJECT_NAMES = new String[]{DIAGNOSTIC_AUTOMATED_ADVICE, "DiagnosticAutomatedAdviceRepositoryEntry", "Account", "AdditionalInterest", "Address", ANTI_THEFT_DEVICE, "Billing", "BusinessTransaction", "BusinessTransactionActivity", "Credential", "Claim", "Consent", CONVICTION, COVERAGE, COVERAGE_PREMIUM, "CreditScore", DRIVER, "Owner", "DriverComplementInfo", "DriverLicenseClass", "InsurancePolicy", INSURANCE_RISK, "KindOfLoss", "Party", "PartyGroup", "PaymentSchedule", "Phone", POLICY_ADDITIONAL_COVERAGE, "PolicyHolder", POLICY_VERSION, "PriorCarrierPolicyInfo", "Producer", "RatingRisk", REFERENCE_DATE, SUB_COVERAGE_PREMIUM, "Trailer", TRANSACTIONAL_MESSAGE, TRANSACTIONAL_MESSAGE_ELEMENT, VEHICLE, VEHICLE_EQUIPMENT, "GroupRepositoryEntry", "ManufacturingContext", "MunicipalityDetailSpec", "MunicipalityRepositoryEntry", "ProducerRepositoryEntry", "VehicleDetailSpec", "CoverageRepositoryEntry", MESSAGE_REPOSITORY_ENTRY, REFERENCE_DATE, "MarketSegment", LEGACY_RATING_INFO_BY_POSTAL_CODE, "GeographicalAssessment"};
    }

    public DataMediatorToPL() {
        this.ignoredAdditions.add("CoverageProduct");
        this.ignoredAdditions.add("CoverageRepositoryEntry");
        this.ignoredAdditions.add(SUB_COVERAGE_PREMIUM);
        this.ignoredAdditions.add("GroupRepositoryEntry");
        this.ignoredAdditions.add("DiagnosticAutomatedAdviceRepositoryEntry");
        this.ignoredAdditions.add(CONVICTION);
        this.ignoredAdditions.add("EMailAddress");
        this.ignoredAdditions.add("Note");
        this.ignoredAdditions.add("Address");
        this.ignoredAdditions.add("Producer");
        this.ignoredAdditions.add(POLICY_VERSION);
        this.ignoredAdditions.add("Distributor");
        this.ignoredAdditions.add(ANTI_THEFT_DEVICE);
        this.ignoredAdditions.add("InsuranceRiskDerivedInd");
        this.ignoredAdditions.add("DistributorRepositoryEntry");
        this.ignoredAdditions.add("DriverComplementInfo");
        this.ignoredAdditions.add(VEHICLE);
        this.ignoredAdditions.add("VehicleRepositoryEntry");
        this.ignoredAdditions.add("PolicyVersionDerivedInd");
        this.ignoredAdditions.add(MESSAGE_REPOSITORY_ENTRY);
        this.ignoredAdditions.add(INSURANCE_RISK);
        this.ignoredAdditions.add("RatingRisk");
        this.ignoredAdditions.add("CommercialUsage");
        this.ignoredAdditions.add("EnvironmentContext");
        this.ignoredAdditions.add("ClaimDerivedInfo");
        this.ignoredAdditions.add("PartyCommercialInfo");
        this.ignoredAdditions.add("MunicipalityDetailSpec");
        this.ignoredAdditions.add(DRIVER);
        this.ignoredAdditions.add("Party");
        this.ignoredAdditions.add("Insured");
        this.ignoredAdditions.add("Manufacturer");
        this.ignoredAdditions.add("DriverLicense");
        this.ignoredAdditions.add("Billing");
        this.ignoredAdditions.add("MunicipalityRepositoryEntry");
        this.setPlpMediators(this.buildMediators("pl"));
        this.setSomMediators(this.buildMediators("som"));
    }

    protected Map<String, Field> buildMediators(String prefix) {
        Map<String, Field> newMediators = new HashMap<>();

        for(Field field : DMConstants.class.getFields()) {
            if (field.getName().startsWith(prefix) && field.getName().endsWith("AttributeNames")) {
                newMediators.put(field.getName().substring(prefix.length(), field.getName().length() - "AttributeNames".length()), field);
            }
        }

        return newMediators;
    }

    @Override
    public com.ing.canada.plp.domain.policyversion.PolicyVersion convertTo_PL(PolicyVersion somPolicyVersion, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, boolean applyInsuranceRiskOverrides) {
        return this.convertTo_PL(somPolicyVersion, plPolicyVersion, applyInsuranceRiskOverrides, false);
    }

    @Override
    public com.ing.canada.plp.domain.policyversion.PolicyVersion convertTo_PL(PolicyVersion somPolicyVersion, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, boolean applyInsuranceRiskOverrides, boolean isUseEISCoverageCodes) {
        long dateStartTime = (new Date()).getTime();

        com.ing.canada.plp.domain.policyversion.PolicyVersion var34;
        try {
            if (log.isDebugEnabled()) {
                log.debug("Java:convertToPL");
            }

            IGenericRootObject gro = ModelFactory.getInstance().getGenericRootObjectForModelObject(somPolicyVersion);
            gro.endLogging();
            DataGraph dataGraph = gro.getDataGraph();
            ChangeSummary cs = dataGraph.getChangeSummary();
            List<DataObject> changedDataObjectsList = cs.getChangedDataObjects();
            List<DataObject> additionsList = new ArrayList<>();
            List<DataObject> notAdditionsList = new ArrayList<>();

            for(DataObject changedDataObject : changedDataObjectsList) {
                if (!(changedDataObject instanceof ObjectContainerBOImpl)) {
                    String somObjectName = DataMediatorUtils.getSOMObjectName(changedDataObject);
                    int somObjectIndex = Arrays.asList(SOM_OBJECT_NAMES).indexOf(somObjectName);
                    String persistenceUniqueId = (String)changedDataObject.get("persistenceUniqueId");
                    if (log.isDebugEnabled()) {
                        log.debug(String.format(">> somObjectName=[%s], SOM_OBJECT_NAMES.indexOf=[%d], persistenceUniqueId=[%s]", somObjectName, somObjectIndex, persistenceUniqueId));
                    }

                    if (persistenceUniqueId == null) {
                        additionsList.add(changedDataObject);
                    } else if (somObjectIndex != -1) {
                        Long uniqueId = Long.valueOf(persistenceUniqueId);
                        Class<?> plBeanClass = Arrays.asList(PL_CLASSES).get(somObjectIndex);
                        if (plBeanClass != null) {
                            try {
                                BaseEntity plBean = this.getBeanFromPLGraphWithCriteria(uniqueId, plBeanClass, plPolicyVersion);
                                this.mapSOMtoPL(changedDataObject, plBean, somObjectName, applyInsuranceRiskOverrides, notAdditionsList);
                                this.updateModifiedAssociations(changedDataObject, somObjectName, plPolicyVersion);
                            } catch (Exception ex) {
                                String plBeanClassName = plBeanClass.getName() != null ? plBeanClass.getName() : "null";
                                log.warn("an exception occured mapping som:" + somObjectName + " to pl:" + plBeanClassName + ". message:" + ex.getMessage());
                            }
                        }

                        if (LEGACY_RATING_INFO_BY_POSTAL_CODE.equals(somObjectName)) {
                            this.mediatorLegacyRatingInfoByPostalCodeToPL.processLegacyRatingInfoByPostalCodeBO(plPolicyVersion, changedDataObject);
                        }
                    }
                }
            }

            for(DataObject object : notAdditionsList) {
                additionsList.remove(object);
            }

            additionsList.sort(new AdditionsComparator());
            this.processAdditions(additionsList, plPolicyVersion, applyInsuranceRiskOverrides, isUseEISCoverageCodes);
            if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
                this.updateVehicleRateGroupAdjustment(plPolicyVersion);

                for(InsuranceRisk somInsuranceRisk : somPolicyVersion.getTheInsuranceRisk()) {
                    RatingRisk somRatingRiskPrincipal = somInsuranceRisk.getTheRatingRiskPrincipal();
                    RatingRisk somRatingRiskOccasional = somInsuranceRisk.getTheRatingRiskOccasional();
                    PolicyHolder policyHolder = plPolicyVersion.getPolicyHolder(PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED);
                    com.ing.canada.plp.domain.insurancerisk.RatingRisk plRatingRiskPrincipal = null;
                    com.ing.canada.plp.domain.insurancerisk.RatingRisk plRatingRiskOccasional = null;

                    for(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
                        if (plInsuranceRisk.getInsuranceRiskSequence() == somInsuranceRisk.getInsuranceRiskSequence().shortValue()) {
                            for(com.ing.canada.plp.domain.insurancerisk.RatingRisk plRatingRisk : plInsuranceRisk.getRatingRisks()) {
                                if (RatingRiskTypeCodeEnum.PRINCIPAL_RISK.equals(plRatingRisk.getRatingRiskType())) {
                                    plRatingRiskPrincipal = plRatingRisk;
                                } else {
                                    plRatingRiskOccasional = plRatingRisk;
                                }
                            }
                        }
                    }

                    if (somRatingRiskPrincipal != null && somRatingRiskPrincipal.getTheMultRatingFactorFromBasicCoverage() != null) {
                        for(MultRatingFactorFromBasicCoverage factor : somRatingRiskPrincipal.getTheMultRatingFactorFromBasicCoverage()) {
                            MultiplicativeRatingFactorFromBasicCoverage plFactor = new MultiplicativeRatingFactorFromBasicCoverage();
                            if (plRatingRiskPrincipal != null) {
                                plRatingRiskPrincipal.addMultiplicativeRatingFactorFromBasicCoverage(plFactor);
                            }

                            plFactor.setBasicCoverageCd(BasicCoverageCodeEnum.valueOfCode(factor.getBasicCoverageCode()));
                            if (factor.getDeductibleAmount() != null) {
                                plFactor.setDeductibleAmount(factor.getDeductibleAmount().longValue());
                            }

                            if (factor.getLimitMedicalExpensesPerPerson() != null) {
                                plFactor.setLimitMedicalExpensesPerPersonAmount(factor.getLimitMedicalExpensesPerPerson().longValue());
                            }

                            if (factor.getLimitMutilationAndDeathIndemnity() != null) {
                                plFactor.setLimitMutilationDeathIndemnityAmount(factor.getLimitMutilationAndDeathIndemnity().longValue());
                            }

                            if (factor.getLimitOfInsurance() != null) {
                                plFactor.setLimitOfInsuranceAmount(factor.getLimitOfInsurance().longValue());
                            }

                            plFactor.setMultiplicativeRatingFactor(factor.getMultRatingFactor());
                            if (factor.getWeeklyBenefits() != null) {
                                plFactor.setWeeklyBenefitsAmount(factor.getWeeklyBenefits().longValue());
                            }

                            if (factor.getConditionType() != null) {
                                plFactor.setConditionType(factor.getConditionType());
                            }

                            if (factor.getMethodOfCalculation() != null) {
                                plFactor.setRatingFactorCalculationMethod(RatingFactorCalculationMethodEnum.valueOfCode(factor.getMethodOfCalculation()));
                            }

                            if (factor.getRatingFactorType() != null) {
                                plFactor.setRatingFactorType(RatingFactorTypeCodeEnum.valueOfCode(factor.getRatingFactorType()));
                            }

                            plFactor.setMaximumPremiumVariationAllowWithFactor(factor.getMaximumPremiumVariationAllowWithFactor());
                            plFactor.setRatingFactorApplyCondition(factor.getRatingFactorApplyCondition());
                        }
                    }

                    if (somRatingRiskOccasional != null && somRatingRiskOccasional.getTheMultRatingFactorFromBasicCoverage() != null) {
                        for(MultRatingFactorFromBasicCoverage factor : somRatingRiskOccasional.getTheMultRatingFactorFromBasicCoverage()) {
                            MultiplicativeRatingFactorFromBasicCoverage plFactor = new MultiplicativeRatingFactorFromBasicCoverage();
                            if (plRatingRiskOccasional != null) {
                                plRatingRiskOccasional.addMultiplicativeRatingFactorFromBasicCoverage(plFactor);
                            }

                            plFactor.setBasicCoverageCd(BasicCoverageCodeEnum.valueOfCode(factor.getBasicCoverageCode()));
                            if (factor.getDeductibleAmount() != null) {
                                plFactor.setDeductibleAmount(factor.getDeductibleAmount().longValue());
                            }

                            if (factor.getLimitMedicalExpensesPerPerson() != null) {
                                plFactor.setLimitMedicalExpensesPerPersonAmount(factor.getLimitMedicalExpensesPerPerson().longValue());
                            }

                            if (factor.getLimitMutilationAndDeathIndemnity() != null) {
                                plFactor.setLimitMutilationDeathIndemnityAmount(factor.getLimitMutilationAndDeathIndemnity().longValue());
                            }

                            if (factor.getLimitOfInsurance() != null) {
                                plFactor.setLimitOfInsuranceAmount(factor.getLimitOfInsurance().longValue());
                            }

                            plFactor.setMultiplicativeRatingFactor(factor.getMultRatingFactor());
                            if (factor.getWeeklyBenefits() != null) {
                                plFactor.setWeeklyBenefitsAmount(factor.getWeeklyBenefits().longValue());
                            }

                            if (factor.getConditionType() != null) {
                                plFactor.setConditionType(factor.getConditionType());
                            }

                            if (factor.getMethodOfCalculation() != null) {
                                plFactor.setRatingFactorCalculationMethod(RatingFactorCalculationMethodEnum.valueOfCode(factor.getMethodOfCalculation()));
                            }

                            if (factor.getRatingFactorType() != null) {
                                plFactor.setRatingFactorType(RatingFactorTypeCodeEnum.valueOfCode(factor.getRatingFactorType()));
                            }

                            plFactor.setMaximumPremiumVariationAllowWithFactor(factor.getMaximumPremiumVariationAllowWithFactor());
                            plFactor.setRatingFactorApplyCondition(factor.getRatingFactorApplyCondition());
                        }
                    }

                    if (somRatingRiskPrincipal != null && somRatingRiskPrincipal.getTheMultRatingFactorFromNonBasicCoverage() != null) {
                        for(MultRatingFactorFromNonBasicCoverage factor : somRatingRiskPrincipal.getTheMultRatingFactorFromNonBasicCoverage()) {
                            MultiplicativeRatingFactorFromNonBasicCoverage plFactor = new MultiplicativeRatingFactorFromNonBasicCoverage();
                            if (plRatingRiskPrincipal != null) {
                                plRatingRiskPrincipal.addMultiplicativeRatingFactorFromNonBasicCoverage(plFactor);
                            }

                            plFactor.setAccidentBenefitFactor(factor.getMultRatingFactorAccidentBenefit());
                            plFactor.setAllPerilsFactor(factor.getMultRatingFactorAllPerils());
                            plFactor.setCollisionFactor(factor.getMultRatingFactorCollision());
                            plFactor.setComprehensiveFactor(factor.getMultRatingFactorComprehensive());
                            plFactor.setLiabilityBodilyInjuryFactor(factor.getMultRatingFactorLiabilityBodilyInjury());
                            plFactor.setLiabilityFactor(factor.getMultRatingFactorLiability());
                            plFactor.setLiabilityPropertyDamageFactor(factor.getMultRatingFactorLiabilityPropertyDamage());
                            plFactor.setMedicalExpensesFactor(factor.getMultRatingFactorMedicalExpenses());
                            plFactor.setRatingFactorApplyCondition(factor.getRatingFactorApplyCondition());
                            plFactor.setSpecifiedPerilsFactor(factor.getMultRatingFactorSpecifiedPerils());
                            plFactor.setTotalDisabilityFactor(factor.getMultRatingFactorTotalDisability());
                            plFactor.setConditionType(factor.getConditionType());
                            plFactor.setCondition(factor.getConditionCode());
                            plFactor.setRatingFactorType(RatingFactorTypeCodeEnum.valueOfCode(factor.getRatingFactorType()));
                            plFactor.setRatingFactorCalculationMethod(RatingFactorCalculationMethodEnum.valueOfCode(factor.getMethodOfCalculation()));
                        }
                    }

                    if (policyHolder != null) {
                        policyHolder.setLtvBandCode(somPolicyVersion.getLtvBand());
                        policyHolder.setLtvScore(somPolicyVersion.getLtvScore());
                        plPolicyVersion.addPolicyHolder(policyHolder);
                    }

                    if (somRatingRiskOccasional != null && somRatingRiskOccasional.getTheMultRatingFactorFromNonBasicCoverage() != null) {
                        for(MultRatingFactorFromNonBasicCoverage factor : somRatingRiskOccasional.getTheMultRatingFactorFromNonBasicCoverage()) {
                            MultiplicativeRatingFactorFromNonBasicCoverage plFactor = new MultiplicativeRatingFactorFromNonBasicCoverage();
                            if (plRatingRiskOccasional != null) {
                                plRatingRiskOccasional.addMultiplicativeRatingFactorFromNonBasicCoverage(plFactor);
                            }

                            plFactor.setAccidentBenefitFactor(factor.getMultRatingFactorAccidentBenefit());
                            plFactor.setAllPerilsFactor(factor.getMultRatingFactorAllPerils());
                            plFactor.setCollisionFactor(factor.getMultRatingFactorCollision());
                            plFactor.setComprehensiveFactor(factor.getMultRatingFactorComprehensive());
                            plFactor.setLiabilityBodilyInjuryFactor(factor.getMultRatingFactorLiabilityBodilyInjury());
                            plFactor.setLiabilityFactor(factor.getMultRatingFactorLiability());
                            plFactor.setLiabilityPropertyDamageFactor(factor.getMultRatingFactorLiabilityPropertyDamage());
                            plFactor.setMedicalExpensesFactor(factor.getMultRatingFactorMedicalExpenses());
                            plFactor.setRatingFactorApplyCondition(factor.getRatingFactorApplyCondition());
                            plFactor.setSpecifiedPerilsFactor(factor.getMultRatingFactorSpecifiedPerils());
                            plFactor.setTotalDisabilityFactor(factor.getMultRatingFactorTotalDisability());
                            plFactor.setConditionType(factor.getConditionType());
                            plFactor.setCondition(factor.getConditionCode());
                            plFactor.setRatingFactorType(RatingFactorTypeCodeEnum.valueOfCode(factor.getRatingFactorType()));
                            plFactor.setRatingFactorCalculationMethod(RatingFactorCalculationMethodEnum.valueOfCode(factor.getMethodOfCalculation()));
                        }
                    }
                }

                this.adjustInsuranceRiskOfferSelection(plPolicyVersion, somPolicyVersion);
            }

            this.mapAllCoverageOfferPackageComposition(changedDataObjectsList, plPolicyVersion);
            var34 = plPolicyVersion;
        } catch (Exception e) {
            throw new SystemException("Error in conversion from SOM to PL", e);
        } finally {
            if (log.isDebugEnabled()) {
                Long execTime = (new Date()).getTime() - dateStartTime;
                log.debug("class <{}> method <convertTo_PL> execution time: {}ms ", this.getClass().getName(), execTime);
            }

        }

        return var34;
    }

    private BaseEntity getBeanFromPLGraphWithCriteria(Long uniqueId, Class<?> plBeanClass, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        if (log.isDebugEnabled()) {
            String beanClassName = ESAPI.encoder().encodeForHTML(plBeanClass.getSimpleName());
            if (uniqueId != null) {
                ESAPI.encoder().encodeForHTML(uniqueId.toString());
            } else {
                String var10000 = "<undefined>";
            }

            String pvId = plPolicyVersion.getId() != null ? ESAPI.encoder().encodeForHTML(plPolicyVersion.getId().toString()) : "<undefined>";
            log.debug(">> getBean ({})={}  for pv={}", beanClassName, uniqueId, pvId);
        }

        return this.baseEntityDAO.findEntityById(plBeanClass, uniqueId);
    }

    private void mapSOMtoPL(DataObject changedDataObject, BaseEntity plBean, String somObjectName, boolean applyInsuranceRiskOverrides, List<DataObject>... notAdditionsList) {
        String plSimpleName = null;
        plSimpleName = DataMediatorUtils.getRealSimpleName(plBean);
        if (log.isDebugEnabled()) {
            log.debug(">> map for plSimpleName=[" + plSimpleName + "]");
        }

        if (INSURANCE_RISK.equals(plSimpleName)) {
            InsuranceRiskOffer insuranceRiskOffer = null;
            InsuranceRiskBO somInsuranceRisk = (InsuranceRiskBO)changedDataObject;
            String[] plMapToInsuranceRiskOffer = null;
            String[] somMapToInsuranceRiskOffer = null;
            com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)plBean;
            if (!TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plInsuranceRisk.getPolicyVersion().getBusinessTransaction().getTransactionCode())) {
                if (TransactionCodeEnum.POLICY_CHANGE.equals(plInsuranceRisk.getPolicyVersion().getBusinessTransaction().getTransactionCode())) {
                    if (somInsuranceRisk.getInternalTechnicalOfferType() != null) {
                        insuranceRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRiskAndInternalOfferType(plInsuranceRisk, InternalTechnicalOfferTypeCodeEnum.valueOfCode(somInsuranceRisk.getInternalTechnicalOfferType()));
                    } else {
                        insuranceRiskOffer = plInsuranceRisk.getSelectedInsuranceRiskOffer();
                    }

                    plMapToInsuranceRiskOffer = DMConstants.plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
                    somMapToInsuranceRiskOffer = DMConstants.somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
                }
            } else {
                OfferTypeCodeEnum plOfferTypeCode = OfferTypeCodeEnum.valueOfCode(somInsuranceRisk.getOfferType());
                insuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(plInsuranceRisk, plOfferTypeCode);
                List<ChangeSummary.Setting> oldSettingsList = changedDataObject.getDataGraph().getChangeSummary().getOldValues(changedDataObject);
                if (oldSettingsList != null) {
                    for(ChangeSummary.Setting oldSetting : oldSettingsList) {
                        Property property = oldSetting.getProperty();
                        Object newValue = changedDataObject.get(property);
                        if ("riskSelectedInd".equals(property.getName()) && newValue == null) {
                            plInsuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(true);
                        }
                    }
                }

                plMapToInsuranceRiskOffer = DMConstants.plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
                somMapToInsuranceRiskOffer = DMConstants.somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames;
            }

            if (insuranceRiskOffer != null) {
                setAttributesByReflectionFromSOMtoPL(plMapToInsuranceRiskOffer, somMapToInsuranceRiskOffer, insuranceRiskOffer, changedDataObject);
                this.handleTransactionalMessages((InsuranceRiskBO)changedDataObject, insuranceRiskOffer, applyInsuranceRiskOverrides);
                List<String> plInsuranceRiskList = new ArrayList(Arrays.asList(DMConstants.plInsuranceRiskAttributeNames));
                List<String> somInsuranceRiskList = new ArrayList(Arrays.asList(DMConstants.somInsuranceRiskAttributeNames));
                if (applyInsuranceRiskOverrides) {
                    List<String> plInsuranceRiskOfferOverridesList = new ArrayList(Arrays.asList(DMConstants.plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames));
                    plInsuranceRiskOfferOverridesList.addAll(Arrays.asList(DMConstants.plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames));

                    for(String anElement : plInsuranceRiskOfferOverridesList) {
                        plInsuranceRiskList.remove(anElement);
                    }

                    List<String> somInsuranceRiskOfferOverridesList = new ArrayList(Arrays.asList(DMConstants.somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames));
                    somInsuranceRiskOfferOverridesList.addAll(Arrays.asList(DMConstants.somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames));

                    for(String anElement : somInsuranceRiskOfferOverridesList) {
                        somInsuranceRiskList.remove(anElement);
                    }
                }

                setAttributesByReflectionFromSOMtoPL(plInsuranceRiskList.toArray(new String[plInsuranceRiskList.size()]), (String[])somInsuranceRiskList.toArray(new String[somInsuranceRiskList.size()]), plBean, changedDataObject);
                OfferTypeCodeEnum compare = this.referenceOffer == null ? DEFAULT_REFERENCE_OFFER : this.referenceOffer;
                if (compare.equals(insuranceRiskOffer.getOfferType())) {
                    com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk = insuranceRiskOffer.getInsuranceRisk();
                    setAttributesByReflectionFromSOMtoPL(DMConstants.plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames, DMConstants.somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames, insuranceRisk, changedDataObject);

                    for(InsuranceRiskOffer iro : this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRisk(insuranceRisk)) {
                        setAttributesByReflectionFromSOMtoPL(DMConstants.plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames, DMConstants.somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskOffersAttributeNames, iro, changedDataObject);
                    }
                }
            } else {
                List<String> plInsuranceRiskList = new ArrayList(Arrays.asList(DMConstants.plInsuranceRiskAttributeNames));
                List<String> somInsuranceRiskList = new ArrayList(Arrays.asList(DMConstants.somInsuranceRiskAttributeNames));
                List<String> plInsuranceRiskOfferOverridesList = new ArrayList(Arrays.asList(DMConstants.plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames));
                plInsuranceRiskOfferOverridesList.addAll(Arrays.asList(DMConstants.plInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames));

                for(String anElement : plInsuranceRiskOfferOverridesList) {
                    plInsuranceRiskList.remove(anElement);
                }

                List<String> somInsuranceRiskOfferOverridesList = new ArrayList(Arrays.asList(DMConstants.somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames));
                somInsuranceRiskOfferOverridesList.addAll(Arrays.asList(DMConstants.somInsuranceRiskOfferReferenceOverridesOnInsuranceRiskAttributeNames));

                for(String anElement : somInsuranceRiskOfferOverridesList) {
                    somInsuranceRiskList.remove(anElement);
                }

                setAttributesByReflectionFromSOMtoPL(plInsuranceRiskList.toArray(new String[plInsuranceRiskList.size()]), somInsuranceRiskList.toArray(new String[somInsuranceRiskList.size()]), plBean, changedDataObject);
            }
        } else if (VEHICLE.equals(plSimpleName)) {
            if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(((Vehicle)plBean).getInsuranceRisk().getPolicyVersion().getBusinessTransaction().getTransactionCode())) {
                if (((Vehicle)plBean).getInsuranceRisk().getSelectedInsuranceRiskOffer() != null) {
                    for(InsuranceRiskOffer iro : this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRisk(((Vehicle)plBean).getInsuranceRisk())) {
                        setAttributesByReflectionFromSOMtoPL(DMConstants.plInsuranceRiskOfferOverridesOnVehicleAttributeNames, DMConstants.somInsuranceRiskOfferOverridesOnVehicleAttributeNames, iro, changedDataObject);
                    }
                }
            } else if (TransactionCodeEnum.POLICY_CHANGE.equals(((Vehicle)plBean).getInsuranceRisk().getPolicyVersion().getBusinessTransaction().getTransactionCode())) {
                Vehicle plVehicle = (Vehicle)plBean;
                InsuranceRiskOffer insuranceRiskOffer = null;
                InsuranceRiskBO somInsuranceRisk = ((VehicleBO)changedDataObject).getTheInsuranceRiskBO();
                if (somInsuranceRisk.getInternalTechnicalOfferType() != null) {
                    InternalTechnicalOfferTypeCodeEnum internaclTechOfferTypeCode = InternalTechnicalOfferTypeCodeEnum.valueOfCode(somInsuranceRisk.getInternalTechnicalOfferType());
                    insuranceRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRiskAndInternalOfferType(plVehicle.getInsuranceRisk(), internaclTechOfferTypeCode);
                } else {
                    insuranceRiskOffer = ((Vehicle)plBean).getInsuranceRisk().getSelectedInsuranceRiskOffer();
                }

                if (insuranceRiskOffer != null) {
                    setAttributesByReflectionFromSOMtoPL(DMConstants.plInsuranceRiskOfferOverridesOnVehicleAttributeNames, DMConstants.somInsuranceRiskOfferOverridesOnVehicleAttributeNames, insuranceRiskOffer, changedDataObject);
                }
            }

            for(InsuranceRiskOffer iro : this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRisk(((Vehicle)plBean).getInsuranceRisk())) {
                setAttributesByReflectionFromSOMtoPL(DMConstants.plInsuranceRiskOfferOverridesOnVehicleAttributeNames, DMConstants.somInsuranceRiskOfferOverridesOnVehicleAttributeNames, iro, changedDataObject);
            }

            List<String> plVehicleList = new ArrayList(Arrays.asList(DMConstants.plVehicleAttributeNames));

            for(Object anElement : new ArrayList(Arrays.asList(DMConstants.plInsuranceRiskOfferOverridesOnVehicleAttributeNames))) {
                plVehicleList.remove(anElement);
            }

            List<String> somVehicleList = new ArrayList(Arrays.asList(DMConstants.somVehicleAttributeNames));

            for(Object anElement : new ArrayList(Arrays.asList(DMConstants.somInsuranceRiskOfferOverridesOnVehicleAttributeNames))) {
                somVehicleList.remove(anElement);
            }

            String[] plVehicleStringArray = new String[plVehicleList.size()];
            String[] somVehicleStringArray = new String[somVehicleList.size()];
            setAttributesByReflectionFromSOMtoPL((String[])plVehicleList.toArray(plVehicleStringArray), (String[])somVehicleList.toArray(somVehicleStringArray), plBean, changedDataObject);
        } else if ("RatingRiskOffer".equals(plSimpleName)) {
            setAttributesByReflectionFromSOMtoPL(DMConstants.plRatingRiskOfferAttributeNames, DMConstants.somRatingRiskOfferAttributeNames, plBean, changedDataObject);
            RatingRiskBO ratingRiskBO = (RatingRiskBO)changedDataObject;
            GroupRepositoryEntryBO somGroupRepositoryEntryBO = ratingRiskBO.getTheGroupRepositoryEntryBO();
            setAttributesByReflectionFromSOMtoPL(DMConstants.plGroupRepositoryEntryOnRatingRiskAttributeNames, DMConstants.somGroupRepositoryEntryOnRatingRiskAttributeNames, plBean, (DataObject)somGroupRepositoryEntryBO);
            RatingRiskOffer ratingRiskOffer = (RatingRiskOffer)plBean;
            InsuranceRiskOffer insuranceRiskOffer = ratingRiskOffer.getInsuranceRiskOffer();
            InsuranceRiskBO somInsuranceRisk = ratingRiskBO.getTheInsuranceRiskPrincipal();
            if (somInsuranceRisk == null) {
                somInsuranceRisk = ratingRiskBO.getTheInsuranceRiskOccasional();
            }

            String offerTypeRoot = somInsuranceRisk.getOfferType();
            RatingRiskTypeCodeEnum ratingRiskType = ratingRiskOffer.getRatingRiskType();

            for(InsuranceRiskOffer iro : this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRisk(insuranceRiskOffer.getInsuranceRisk())) {
                if (!offerTypeRoot.equals(iro.getOfferType().getCode())) {
                    for(RatingRiskOffer rro : iro.getRatingRiskOffers()) {
                        if (ratingRiskType.equals(rro.getRatingRiskType())) {
                            setAttributesByReflectionFromSOMtoPL(DMConstants.plRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames, DMConstants.somRatingRiskOfferRootOverridesRatingRiskOfferAttributeNames, rro, changedDataObject);
                            setAttributesByReflectionFromSOMtoPL(DMConstants.plGroupRepositoryEntryRootOverridesRatingRiskAttributeNames, DMConstants.somGroupRepositoryEntryRootOverridesRatingRiskAttributeNames, rro, (DataObject)somGroupRepositoryEntryBO);
                            break;
                        }
                    }
                }
            }

            notAdditionsList[0].add((DataObject)somGroupRepositoryEntryBO);
        } else if ("CoverageOffer".equals(plSimpleName)) {
            CoverageOffer coverage = (CoverageOffer)plBean;
            if (!this.isKeepNonEligibleCoverages && "N".equals(((CoverageBO)changedDataObject).getCoverageEligibleInd())) {
                InsuranceRiskOffer plInsuranceRiskOffer = coverage.getInsuranceRiskOffer();
                if (plInsuranceRiskOffer != null) {
                    plInsuranceRiskOffer.removeCoverageOffer((CoverageOffer)plBean);
                }

                for(CoveragePremiumOffer premiums : coverage.getCoveragePremiumOffers()) {
                    premiums.setRatingRiskOffer(null);
                }
            } else {
                setAttributesByReflectionFromSOMtoPL(DMConstants.plCoverageOfferAttributeNames, DMConstants.somCoverageOfferAttributeNames, plBean, changedDataObject);
                CoverageRepositoryEntryBO somCoverageRepositoryEntryBO = ((CoverageBO)changedDataObject).getTheCoverageProductBO().getTheCoverageRepositoryEntryBase();
                setAttributesByReflectionFromSOMtoPL(DMConstants.plCoverageRepositoryEntryFromCoverageAttributeNames, DMConstants.somCoverageRepositoryEntryFromCoverageAttributeNames, plBean, (DataObject)somCoverageRepositoryEntryBO);
            }
        } else if ("CoveragePremiumOffer".equals(plSimpleName)) {
            setAttributesByReflectionFromSOMtoPL(DMConstants.plCoveragePremiumOfferAttributeNames, DMConstants.somCoveragePremiumOfferAttributeNames, plBean, changedDataObject);
        } else if (POLICY_ADDITIONAL_COVERAGE.equals(plSimpleName) && "N".equals(((PolicyAdditionalCoverageBO)changedDataObject).getCoverageEligibleInd()) && !this.isKeepNonEligibleCoverages) {
            com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion = ((PolicyAdditionalCoverage)plBean).getPolicyVersion();
            plPolicyVersion.removePolicyAdditionalCoverage((PolicyAdditionalCoverage)plBean);
            this.policyCoverageDAO.delete((PolicyAdditionalCoverage)plBean);
        } else if ("SubCoveragePremiumOffer".equals(plSimpleName)) {
            SubCoveragePremiumBO somSubCoveragePremium = (SubCoveragePremiumBO)changedDataObject;
            BasicCoverageCodeEnum basicCoverageCodeEnum = BasicCoverageCodeEnum.valueOfCode(somSubCoveragePremium.getBasicCoverageCode());
            switch (basicCoverageCodeEnum) {
                case ACCIDENT_BENEFITS_P1:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumMedicalExpenses(somSubCoveragePremium.getAnnualPremium());
                    break;
                case OPTIONAL_INCOME_REPLACEMENT_P2:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumTotalDisability(somSubCoveragePremium.getAnnualPremium());
                    break;
                case LIABILITY_A:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumLiability(somSubCoveragePremium.getAnnualPremium());
                    break;
                case ALL_PERILS_B1:
                case ALL_PERILS_C1:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumAllPerils(somSubCoveragePremium.getAnnualPremium());
                    break;
                case COLLISION_B2:
                case COLLISION_C2:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumCollision(somSubCoveragePremium.getAnnualPremium());
                    break;
                case COMPREHENSIVE_B3:
                case COMPREHENSIVE_C3:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumComprehensive(somSubCoveragePremium.getAnnualPremium());
                    break;
                case SPECIFIED_PERILS_B4:
                case SPECIFIED_PERILS_C4:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumSpecifiedPerils(somSubCoveragePremium.getAnnualPremium());
                    break;
                case ACCIDENT_BENEFITS_B:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumAccidentBenefit(somSubCoveragePremium.getAnnualPremium());
                    break;
                case LIABILITY_BODILY_INJURY_A1:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumLiabilityBodilyInjured(somSubCoveragePremium.getAnnualPremium());
                    break;
                case DIRECT_COMP_PROPERTY_DAM_A2:
                    ((SubCoveragePremiumOffer)plBean).setAnnualPremiumLiabilityPropertyDamage(somSubCoveragePremium.getAnnualPremium());
                    break;
                default:
                    log.warn("Unknown BasicCoverageCodeEnum: {}", basicCoverageCodeEnum);
                    break;
            }
        } else if (!ANTI_THEFT_DEVICE.equals(plSimpleName)) {
            if (VEHICLE_EQUIPMENT.equals(plSimpleName)) {
                setAttributesByReflectionFromSOMtoPL(DMConstants.plVehicleEquipmentAttributeNames, DMConstants.somVehicleEquipmentAttributeNames, plBean, changedDataObject);
            } else if (POLICY_VERSION.equals(plSimpleName)) {
                PolicyVersionBO policyVersionBO = (PolicyVersionBO)changedDataObject;
                if (policyVersionBO.getClientOfBrokerSinceRes() != null) {
                    DateTime somDate = new DateTime(policyVersionBO.getClientOfBrokerSinceRes());
                    Months m = Months.monthsBetween(somDate, new DateTime());
                    String nbrOfMonths = Integer.toString(m.getMonths());
                    ((com.ing.canada.plp.domain.policyversion.PolicyVersion)plBean).setClientOfBrokerSinceResCode("00".substring(nbrOfMonths.length()) + nbrOfMonths);
                }

                setAttributesByReflectionFromSOMtoPL(DMConstants.plPolicyVersionAttributeNames, DMConstants.somPolicyVersionAttributeNames, plBean, changedDataObject);
            } else if (DIAGNOSTIC_AUTOMATED_ADVICE.equals(plSimpleName)) {
                DiagnosticAutomatedAdvice baseDiagnosticAutomatedAdvice = (DiagnosticAutomatedAdvice)plBean;
                InsuranceRiskOffer plInsuranceRiskOffer = baseDiagnosticAutomatedAdvice.getInsuranceRiskOffer();
                com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = baseDiagnosticAutomatedAdvice.getInsuranceRisk();
                if (plInsuranceRisk != null) {
                    if ("N".equals(((DiagnosticAutomatedAdviceBO)changedDataObject).getAdviceEligibleInd())) {
                        plInsuranceRisk.removeDiagnosticAutomatedAdvice((DiagnosticAutomatedAdvice)plBean);
                        this.diagnosticAutomatedAdviceDAO.delete((DiagnosticAutomatedAdvice)plBean);
                    } else {
                        setAttributesByReflectionFromSOMtoPL(DMConstants.plDiagnosticAutomatedAdviceAttributeNames, DMConstants.somDiagnosticAutomatedAdviceAttributeNames, plBean, changedDataObject);
                    }
                } else if ("N".equals(((DiagnosticAutomatedAdviceBO)changedDataObject).getAdviceEligibleInd())) {
                    plInsuranceRiskOffer.removeDiagnosticAutomatedAdvice((DiagnosticAutomatedAdvice)plBean);
                    this.diagnosticAutomatedAdviceDAO.delete((DiagnosticAutomatedAdvice)plBean);
                } else {
                    setAttributesByReflectionFromSOMtoPL(DMConstants.plDiagnosticAutomatedAdviceAttributeNames, DMConstants.somDiagnosticAutomatedAdviceAttributeNames, plBean, changedDataObject);
                }
            } else if ("VehicleDetailSpecificationRepositoryEntry".equals(plSimpleName)) {
                setAttributesFromSOMtoPL(DMConstants.som2plVehicleDetails2VehDetSpecRepEntry, plBean, changedDataObject);
            } else if ("MunicipalityDetailSpecification".equalsIgnoreCase(plSimpleName)) {
                setAttributesByReflectionFromSOMtoPL(DMConstants.plMunicipalityDetailSpecificationAttributeNames, DMConstants.somMunicipalityDetailSpecAttributeNames, plBean, changedDataObject);
                log.trace("plBean to String:" + plBean.toString());
            } else {
                Field somArrayField = this.getSomMediators().get(somObjectName);
                Field plArrayField = (Field)this.getPlpMediators().get(plSimpleName);
                if (somArrayField == null || plArrayField == null) {
                    log.error("Could not find info to map 'from SOM to PL' the object that was changed in the SOM graph");
                    throw new SystemException("DataMediatorToPL failed");
                }

                try {
                    String[] mapSomAttributeNames = (String[])somArrayField.get(this);
                    String[] mapPLAttributeNames = (String[])plArrayField.get(this);
                    setAttributesByReflectionFromSOMtoPL(mapPLAttributeNames, mapSomAttributeNames, plBean, changedDataObject);
                } catch (IllegalArgumentException e) {
                    throw new SystemException(ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE, e);
                } catch (IllegalAccessException e) {
                    throw new SystemException(ILLEGAL_ACCESS_EXCEPTION_MESSAGE, e);
                }
            }
        }

    }

    protected void handleTransactionalMessages(InsuranceRiskBO insuranceRisk, InsuranceRiskOffer riskOffer, boolean applyInsuranceRiskOverrides) {
        for(Object message : insuranceRisk.getTheTransactionalMessageBO()) {
            TransactionalMessage newMessage = new TransactionalMessage();
            newMessage.setInsuranceRiskOffer(riskOffer);
            MessageRepositoryEntry entry = new MessageRepositoryEntry();
            newMessage.setMessageRepositoryEntry(entry);
            this.mapSOMtoPL((DataObject)message, newMessage, TRANSACTIONAL_MESSAGE, applyInsuranceRiskOverrides);
            this.mapSOMtoPL((DataObject)((TransactionalMessageBO)message).getTheMessageRepositoryEntryBO(), entry, MESSAGE_REPOSITORY_ENTRY, applyInsuranceRiskOverrides);
            if (entry.getLanguage() == null) {
                entry.setLanguage(LanguageCodeEnum.FRENCH);
                entry.setMessageDescription(entry.getMessageNumber());
                entry.setManufacturingContext(riskOffer.getPolicyOfferRating().getPolicyVersion().getInsurancePolicy().getManufacturingContext());
            }

            this.handleMessageElements((TransactionalMessageBO)message, newMessage, applyInsuranceRiskOverrides);
        }

    }

    protected void handleMessageElements(TransactionalMessageBO message, TransactionalMessage newMessage, boolean applyInsuranceRiskOverrides) {
        for(Object element : message.getTheTransactionalMessageElementBO()) {
            TransactionalMessageElement newElement = new TransactionalMessageElement();
            newElement.setTransactionalMessage(newMessage);
            this.mapSOMtoPL((DataObject)element, newElement, TRANSACTIONAL_MESSAGE_ELEMENT, applyInsuranceRiskOverrides);
            newMessage.addTransactionalMessageElement(newElement);
        }

    }

    private static void setAttributesFromSOMtoPL(Map<String, String> attributeMap, BaseEntity plBean, DataObject somDataObject) {
        try {
            List<ChangeSummary.Setting> oldSettingsList = somDataObject.getDataGraph().getChangeSummary().getOldValues(somDataObject);
            if (oldSettingsList != null) {
                String className = DataMediatorUtils.getRealClassName(plBean);
                Class<?> nonLazyLoadedPLClass = Class.forName(className);
                Field[] fields = nonLazyLoadedPLClass.getDeclaredFields();

                for(ChangeSummary.Setting oldSetting : oldSettingsList) {
                    Property property = oldSetting.getProperty();
                    Object newValue = somDataObject.get(property);
                    String plPropertyColumnName = (String)attributeMap.get(property.getName());
                    if (StringUtils.isNotEmpty(plPropertyColumnName)) {
                        assignFieldByReflection(plBean, somDataObject, nonLazyLoadedPLClass, fields, property, newValue, plPropertyColumnName);
                    } else if (log.isDebugEnabled()) {
                        log.debug(String.format("the property [%s] is unmapped... it might be a derived value modified in the SOM that we ignore,...", property.getName()));
                    }
                }
            }

        } catch (ClassNotFoundException e) {
            throw new SystemException("ClassNotFoundException in DataMediatorToPL", e);
        } catch (IllegalArgumentException e) {
            throw new SystemException(ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE, e);
        }
    }

    /** @deprecated */
    @Deprecated
    private static void setAttributesByReflectionFromSOMtoPL(String[] plAttributeNames, String[] somAttributeNames, BaseEntity plBean, DataObject somDataObject) {
        try {
            List<ChangeSummary.Setting> oldSettingsList = somDataObject.getDataGraph().getChangeSummary().getOldValues(somDataObject);
            if (oldSettingsList != null) {
                String className = DataMediatorUtils.getRealClassName(plBean);
                Class<?> nonLazyLoadedPLClass = Class.forName(className);
                Field[] fields = nonLazyLoadedPLClass.getDeclaredFields();
                List<String> plList = Arrays.asList(plAttributeNames);
                List<String> somList = Arrays.asList(somAttributeNames);

                for(ChangeSummary.Setting oldSetting : oldSettingsList) {
                    Property property = oldSetting.getProperty();
                    Object newValue = somDataObject.get(property);
                    if (somList.contains(property.getName())) {
                        String plPropertyColumnName = (String)plList.get(somList.indexOf(property.getName()));
                        assignFieldByReflection(plBean, somDataObject, nonLazyLoadedPLClass, fields, property, newValue, plPropertyColumnName);
                    } else if (log.isDebugEnabled()) {
                        log.debug(String.format("the property [%s] is unmapped... it might be a derived value modified in the SOM that we ignore,...", property.getName()));
                    }
                }
            }

        } catch (ClassNotFoundException e) {
            throw new SystemException("ClassNotFoundException in DataMediatorToPL", e);
        } catch (IllegalArgumentException e) {
            throw new SystemException(ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE, e);
        }
    }

    private static void assignFieldByReflection(BaseEntity plBean, DataObject somDataObject,
                                                Class<?> nonLazyLoadedPLClass, Field[] fields,
                                                Property property, Object newValue, String plPropertyColumnName) {
        try {
            Field plField = findPlField(nonLazyLoadedPLClass, fields, plPropertyColumnName);
            Method setterMethod = findSetterMethod(plBean, plField, property, somDataObject);

            if (newValue == null) {
                setterMethod.invoke(plBean, (Object) null);
            } else {
                invokeSetterWithProperValue(plBean, setterMethod, plField, newValue);
            }
        } catch (IllegalArgumentException e) {
            throw new SystemException(ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE, e);
        } catch (IllegalAccessException e) {
            throw new SystemException(ILLEGAL_ACCESS_EXCEPTION_MESSAGE, e);
        } catch (InvocationTargetException e) {
            throw new SystemException("InvocationTargetException in DataMediatorToPL", e);
        } catch (SecurityException e) {
            throw new SystemException("SecurityException in DataMediatorToPL", e);
        } catch (NoSuchMethodException e) {
            throw new SystemException("NoSuchMethodException in DataMediatorToPL", e);
        }
    }

    private static Field findPlField(Class<?> nonLazyLoadedPLClass, Field[] fields, String plPropertyColumnName) {
        Field plField = getFieldForPLProperty(fields, plPropertyColumnName);

        if (plField == null && nonLazyLoadedPLClass != BaseEntity.class) {
            plField = getFieldForPLProperty(nonLazyLoadedPLClass.getSuperclass().getDeclaredFields(), plPropertyColumnName);
        }

        if (plField == null) {
            log.error("Could not find the field for the given PL column name : {}", plPropertyColumnName);
            throw new SystemException("Could not find the field for the given PL column name : " + plPropertyColumnName);
        }

        return plField;
    }

    private static Method findSetterMethod(BaseEntity plBean, Field plField, Property property, DataObject somDataObject) {
        String plFieldSetMethod = DataMediatorUtils.accessorMethodName("set", plField.getName());
        List<Method> plSetterMethodList = new ArrayList<>();

        for (Method aMethod : plBean.getClass().getMethods()) {
            if (StringUtils.equalsIgnoreCase(aMethod.getName(), plFieldSetMethod)) {
                plSetterMethodList.add(aMethod);
            }
        }

        validateSetterMethodAvailability(plSetterMethodList, property, somDataObject);

        return plSetterMethodList.getFirst();
    }

    private static void validateSetterMethodAvailability(List<Method> plSetterMethodList, Property property, DataObject somDataObject) {
        if (plSetterMethodList.isEmpty()) {
            String errMessage = "Did not find a setter method in PL matching the SOM property: " +
                    property.getName() + " of SOM object: " +
                    DataMediatorUtils.getRealSimpleName(somDataObject);
            log.error(errMessage);
            throw new SystemException(errMessage);
        }

        if (plSetterMethodList.size() > 1) {
            String errMessage = "Found more than one method in PL matching the SOM property: " +
                    property.getName() + " of SOM object: " +
                    DataMediatorUtils.getRealSimpleName(somDataObject);
            log.error(errMessage);
            throw new SystemException(errMessage);
        }
    }

    private static void invokeSetterWithProperValue(BaseEntity plBean, Method setterMethod,
                                                    Field plField, Object newValue)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {

        String getterMethod = DataMediatorUtils.accessorMethodName("get", plField.getName());
        Method plGetterMethod = plBean.getClass().getMethod(getterMethod);

        Object convertedValue = getTranslatedValueFromSOMtoPL(newValue, plGetterMethod.getReturnType());
        setterMethod.invoke(plBean, convertedValue);
    }


    private static Field getFieldForPLProperty(Field[] fields, String plPropertyColumnName) {
        Field plField = null;

        for(Field field : fields) {
            Column column = (Column)field.getAnnotation(Column.class);
            if (column != null && column.name().equals(plPropertyColumnName)) {
                plField = field;
                break;
            }
        }

        return plField;
    }

    private static Object getTranslatedValueFromSOMtoPL(Object aValue, Class<?> plFieldClass) {
        if (aValue == null) {
            return null;
        }

        try {
            if (plFieldClass.equals(String.class)) {
                return handleStringConversion(aValue);
            }

            if (plFieldClass.equals(Date.class) && aValue instanceof Long) {
                return new Date((Long)aValue);
            }

            if (plFieldClass.equals(Boolean.class) && aValue instanceof String) {
                return convertStringToBoolean((String)aValue);
            }

            if (plFieldClass.equals(Integer.class) || Integer.TYPE.equals(plFieldClass)) {
                return convertToInteger(aValue);
            }

            if (plFieldClass.equals(Byte.class) && aValue instanceof Integer) {
                return ((Integer)aValue).byteValue();
            }

            if (plFieldClass.equals(Short.class) || Short.TYPE.equals(plFieldClass)) {
                return ((Integer)aValue).shortValue();
            }

            if (plFieldClass.equals(BigDecimal.class) && aValue instanceof Double) {
                return BigDecimal.valueOf((Double)aValue);
            }

            if (plFieldClass.equals(Double.class)) {
                return aValue;
            }

            if (plFieldClass.isEnum()) {
                return convertToEnum(aValue, plFieldClass);
            }

            throwTranslationError(aValue.getClass().getName(), plFieldClass.getName());
            return null;

        } catch (SecurityException e) {
            throw new SystemException("SecurityException in DataMediatorToPL", e);
        } catch (NoSuchMethodException e) {
            throw new SystemException("NoSuchMethodException in DataMediatorToPL", e);
        } catch (IllegalArgumentException e) {
            throw new SystemException(ILLEGAL_ARGUMENT_EXCEPTION_MESSAGE, e);
        } catch (IllegalAccessException e) {
            throw new SystemException(ILLEGAL_ACCESS_EXCEPTION_MESSAGE, e);
        } catch (InvocationTargetException e) {
            throw new SystemException("InvocationTargetException in DataMediatorToPL", e);
        }
    }

    private static Object handleStringConversion(Object value) {
        if (value instanceof String) {
            return value;
        }

        if (value instanceof GregorianCalendar) {
            return value.toString();
        }

        throwTranslationError(value.getClass().getName(), String.class.getName());
        return null;
    }

    private static Boolean convertStringToBoolean(String value) {
        if ("Y".equals(value)) {
            return Boolean.TRUE;
        }

        if ("N".equals(value)) {
            return Boolean.FALSE;
        }

        return null;
    }

    private static Integer convertToInteger(Object value) {
        if (value instanceof Integer) {
            return (Integer)value;
        }

        if (value instanceof String) {
            return Integer.parseInt((String)value);
        }

        if (value instanceof Double) {
            return ((Double)value).intValue();
        }

        if (value instanceof Long) {
            return ((Long)value).intValue();
        }

        throwTranslationError(value.getClass().getName(), Integer.class.getName());
        return null;
    }

    private static Object convertToEnum(Object value, Class<?> enumClass)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {

        if (value instanceof String) {
            Method enumValueOfCodeMethod = enumClass.getMethod("valueOfCode", String.class);
            return enumValueOfCodeMethod.invoke(null, (String) value);
        }

        if (value instanceof Integer && enumClass.equals(PolicyTermInMonthsEnum.class)) {
            Method enumValueOfCodeMethod = enumClass.getMethod("valueOfCode", Integer.class);
            return enumValueOfCodeMethod.invoke(null, (Integer) value);
        }

        throwTranslationError(value.getClass().getName(), enumClass.getName());
        return null;
    }

    private static void throwTranslationError(String sourceType, String targetType) {
        String errMessage = "No appropriate conversion has been found for the {SOM field class, PL field class} pair. " +
                "SOM Object: " + sourceType + " / PL Object: " + targetType;
        log.error(errMessage);
        throw new SystemException(errMessage);
    }

    private void updateModifiedAssociations(DataObject changedDataObject, String somObjectName, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        if ("Claim".equals(somObjectName)) {
            for(Object obj : changedDataObject.getDataGraph().getChangeSummary().getOldValues(changedDataObject)) {
                ChangeSummary.Setting oldSetting = (ChangeSummary.Setting) obj;
                Property property = oldSetting.getProperty();
                if (THE_INSURANCE_RISK_BO.equals(property.getName())) {
                    InsuranceRiskBO oldInsuranceRisk = (InsuranceRiskBO)oldSetting.getValue();
                    InsuranceRiskBO newInsuranceRisk = (InsuranceRiskBO)changedDataObject.get(property);
                    com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plNewInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(newInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);
                    if (oldInsuranceRisk != null) {
                        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plOldInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(oldInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);

                        for(Claim claim : plOldInsuranceRisk.getClaims()) {
                            if (((ClaimBO)changedDataObject).getPersistenceUniqueId().equals(claim.getId().toString())) {
                                plOldInsuranceRisk.removeClaim(claim);
                                plNewInsuranceRisk.addClaim(claim);
                                break;
                            }
                        }
                    } else {
                        Claim claim = (Claim)this.getBeanFromPLGraphWithCriteria(Long.valueOf(((ClaimBO)changedDataObject).getPersistenceUniqueId()), Claim.class, plPolicyVersion);
                        plNewInsuranceRisk.addClaim(claim);
                    }
                }
            }
        } else if (CONVICTION.equals(somObjectName)) {
            for(Object obj : changedDataObject.getDataGraph().getChangeSummary().getOldValues(changedDataObject)) {
                ChangeSummary.Setting oldSetting = (ChangeSummary.Setting) obj;
                Property property = oldSetting.getProperty();
                if (THE_INSURANCE_RISK_BO.equals(property.getName())) {
                    InsuranceRiskBO oldInsuranceRisk = (InsuranceRiskBO)oldSetting.getValue();
                    InsuranceRiskBO newInsuranceRisk = (InsuranceRiskBO)changedDataObject.get(property);
                    com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plNewInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(newInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);
                    if (oldInsuranceRisk != null) {
                        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plOldInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(oldInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);

                        for(Conviction conviction : plOldInsuranceRisk.getConvictions()) {
                            if (((ConvictionBO)changedDataObject).getPersistenceUniqueId().equals(conviction.getId().toString())) {
                                plOldInsuranceRisk.removeConviction(conviction);
                                plNewInsuranceRisk.addConviction(conviction);
                                break;
                            }
                        }
                    } else {
                        Conviction conviction = (Conviction)this.getBeanFromPLGraphWithCriteria(Long.valueOf(((ConvictionBO)changedDataObject).getPersistenceUniqueId()), Conviction.class, plPolicyVersion);
                        plNewInsuranceRisk.addConviction(conviction);
                    }
                }
            }
        } else if (DRIVER.equals(somObjectName)) {
            for(Object obj: changedDataObject.getDataGraph().getChangeSummary().getOldValues(changedDataObject)) {
                ChangeSummary.Setting oldSetting = (ChangeSummary.Setting) obj;
                Property property = oldSetting.getProperty();
                if (THE_INSURANCE_RISK_BO.equals(property.getName())) {
                    InsuranceRiskBO oldInsuranceRisk = (InsuranceRiskBO)oldSetting.getValue();
                    InsuranceRiskBO newInsuranceRisk = (InsuranceRiskBO)changedDataObject.get(property);
                    com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plOldInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(oldInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);
                    com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plNewInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(newInsuranceRisk.getPersistenceUniqueId()), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);

                    for(PartyRoleInRisk partyRoleInRisk : plOldInsuranceRisk.getPartyRoleInRisks()) {
                        if (((DriverBO)changedDataObject).getPersistenceUniqueId().equals(partyRoleInRisk.getId().toString())) {
                            plOldInsuranceRisk.removePartyRoleInRisk(partyRoleInRisk);
                            plNewInsuranceRisk.addPartyRoleInRisk(partyRoleInRisk);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void processAdditions(List<DataObject> additionsList, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, boolean applyInsuranceRiskOverrides, boolean isUseEISCoverageCodes) {
        Map<String, InsuranceRiskOffer> plInsuranceRiskOfferMap = new HashMap();

        for(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
            InsuranceRiskOffer plInsuranceRiskOffer = plInsuranceRisk.getSelectedInsuranceRiskOffer();
            if (plInsuranceRiskOffer != null) {
                plInsuranceRiskOfferMap.put(plInsuranceRisk.getId().toString(), plInsuranceRiskOffer);
            }
        }

        Map<String, Party> plPartiesMap = new HashMap();

        for(Party plParty : plPolicyVersion.getParties()) {
            if (plParty != null) {
                plPartiesMap.put(plParty.getId().toString(), plParty);
            }
        }

        Map<String, CoverageOffer> plCoverageOfferMap = new HashMap();
        if (additionsList != null) {
            for(DataObject dataObject : additionsList) {
                String currentSOMObjectName = DataMediatorUtils.getSOMObjectName(dataObject);
                switch (currentSOMObjectName) {
                    case COVERAGE:
                        CoverageBO somCoverage = (CoverageBO)dataObject;
                        CoverageRepositoryEntryBO somCoverageRepositoryEntryBO = null;

                        try {
                            somCoverageRepositoryEntryBO = ((CoverageBO)dataObject).getTheCoverageProductBO().getTheCoverageRepositoryEntryBase();
                        } catch (Exception var35) {
                            break;
                        }

                        CoverageRepositoryEntry plCoverageRepositoryEntry;
                        if (isUseEISCoverageCodes) {
                            String coverageCode = somCoverageRepositoryEntryBO.getCoverageCodeNative();
                            String ihvCoverageCode = somCoverageRepositoryEntryBO.getCoverageCode();
                            plCoverageRepositoryEntry = this.coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(
                                    coverageCode, ihvCoverageCode, plPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince(),
                                    plPolicyVersion.getInsurancePolicy().getManufacturerCompany(), plPolicyVersion.getRatingDate());
                        } else {
                            String coverageCode = StringUtils.isNotEmpty(somCoverageRepositoryEntryBO.getCoverageCodeNative()) ? somCoverageRepositoryEntryBO.getCoverageCodeNative() : somCoverageRepositoryEntryBO.getCoverageCode();
                            plCoverageRepositoryEntry = this.coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(coverageCode, plPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince(), plPolicyVersion.getInsurancePolicy().getManufacturerCompany(), plPolicyVersion.getRatingDate());
                        }

                        if (plCoverageRepositoryEntry == null) {
                            log.error("CoverageRepositoryEntry unexepectedly null for COVERAGE code = [" + somCoverageRepositoryEntryBO.getCoverageCode() + "]");
                            throw new SystemException("CoverageRepositoryEntry is null for COVERAGE code = [" + somCoverageRepositoryEntryBO.getCoverageCode() + "]");
                        }

                        InsuranceRiskBO somInsuranceRisk = somCoverage.getTheInsuranceRiskBO();
                        CoverageOffer plCoverageOffer = null;
                        InsuranceRiskOffer plInsuranceRiskOffer = null;
                        if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
                            plInsuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByRiskSequenceOfferTypeAndPolicyVersion(((CoverageBO)dataObject).getTheInsuranceRiskBO().getInsuranceRiskSequence(), OfferTypeCodeEnum.valueOfCode(((CoverageBO)dataObject).getTheInsuranceRiskBO().getOfferType()), plPolicyVersion);
                        } else if (TransactionCodeEnum.POLICY_CHANGE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
                            if (somInsuranceRisk.getInternalTechnicalOfferType() == null) {
                                plInsuranceRiskOffer = (InsuranceRiskOffer)plInsuranceRiskOfferMap.get(somInsuranceRisk.getPersistenceUniqueId());
                            } else {
                                plInsuranceRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRiskSequenceInternalOfferType(somInsuranceRisk.getInsuranceRiskSequence(), InternalTechnicalOfferTypeCodeEnum.valueOfCode(somInsuranceRisk.getInternalTechnicalOfferType()), plPolicyVersion);
                            }
                        }

                        plCoverageOffer = new CoverageOffer(plCoverageRepositoryEntry, plInsuranceRiskOffer);
                        plCoverageOffer.setEffectiveDate(plCoverageRepositoryEntry.getEffectiveDate());
                        plCoverageOffer.setExpiryDate(plCoverageRepositoryEntry.getExpiryDate());
                        plCoverageOfferMap.put(((CoverageBO)dataObject).getUniqueId(), plCoverageOffer);
                        this.mapSOMtoPL(dataObject, plCoverageOffer, currentSOMObjectName, applyInsuranceRiskOverrides);
                        if (plCoverageOffer.getCoverageEligibleIndicator() == null && CoverageTypeCodeEnum.ENDORSEMENT.equals(plCoverageOffer.getCoverageRepositoryEntry().getCoverageType())) {
                            plCoverageOffer.setCoverageEligibleIndicator(false);
                        }
                        break;
                    case COVERAGE_PREMIUM:
                        currentSOMObjectName = "CoveragePremiumOffer";
                        CoverageBO coveragePrincipal = ((CoveragePremiumBO)dataObject).getTheCoveragePrincipal();
                        if (coveragePrincipal != null && !"N".equals(coveragePrincipal.getCoverageEligibleInd()) && coveragePrincipal.getTheInsuranceRiskBO() != null) {
                            CoverageOffer plCoverage = (CoverageOffer)plCoverageOfferMap.get(coveragePrincipal.getUniqueId());
                            if (plCoverage == null && coveragePrincipal.getPersistenceUniqueId() != null) {
                                plCoverage = (CoverageOffer)this.getBeanFromPLGraphWithCriteria(Long.valueOf(coveragePrincipal.getPersistenceUniqueId()), CoverageOffer.class, plPolicyVersion);
                            }

                            if (plCoverage == null) {
                                log.error("CoverageOffer unexepectedly null for a CoverageBO id = [" + coveragePrincipal.getUniqueId() + "]");
                                throw new SystemException("CoverageOffer is null for a CoverageBO id = [" + coveragePrincipal.getUniqueId() + "]");
                            }

                            InsuranceRiskBO ir = coveragePrincipal.getTheInsuranceRiskBO();
                            com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = this.insuranceRiskService.findBySequence(plPolicyVersion, ir.getInsuranceRiskSequence().shortValue());
                            if (plInsuranceRisk == null) {
                                log.error("InsuranceRisk unexepectedly null for a policy version and an InsuranceRiskSequence = [{}]", ir.getInsuranceRiskSequence().shortValue());
                                throw new SystemException("InsuranceRisk is null for a policy version and an InsuranceRiskSequence = [" + ir.getInsuranceRiskSequence().shortValue() + "]");
                            }

                            plInsuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(plInsuranceRisk, OfferTypeCodeEnum.valueOfCode(ir.getOfferType()));
                            if (plInsuranceRiskOffer == null) {
                                log.error("InsuranceRiskOffer unexepectedly null for a InsuranceRisk id = [" + plInsuranceRisk.getId() + "] and an OfferType = [" + ir.getOfferType() + "]");
                                throw new SystemException("InsuranceRiskOffer is null for a InsuranceRisk id = [" + plInsuranceRisk.getId() + "] and an OfferType = [" + ir.getOfferType() + "]");
                            }

                            RatingRiskOffer plRatingRiskOfferPrincipal = this.ratingRiskOfferService.findRatingRiskByType(plInsuranceRiskOffer, RatingRiskTypeCodeEnum.PRINCIPAL_RISK);
                            if (plRatingRiskOfferPrincipal == null) {
                                log.error("RatingRiskOffer unexepectedly null for the principal and for the Insurance Risk Offer id = [{}]", plInsuranceRiskOffer.getId());
                                throw new SystemException("RatingRiskOffer is null for the principal and for the Insurance Risk Offer id = [" + plInsuranceRiskOffer.getId() + "]");
                            }

                            CoveragePremiumOffer plCoveragePremiumOfferPrincipal = new CoveragePremiumOffer(plCoverage, plRatingRiskOfferPrincipal);
                            this.mapSOMtoPL(dataObject, plCoveragePremiumOfferPrincipal, currentSOMObjectName, applyInsuranceRiskOverrides);
                            List<?> subCoveragePrincipalList = ((CoveragePremiumBO)dataObject).getTheSubCoveragePremiumBO();
                            SubCoveragePremiumOffer plSubCoveragePremiumOfferPrincipal = new SubCoveragePremiumOffer(plCoveragePremiumOfferPrincipal);

                            for(Object anElement : subCoveragePrincipalList) {
                                this.mapSOMtoPL((SubCoveragePremiumBOImpl)anElement, plSubCoveragePremiumOfferPrincipal, "SubCoveragePremiumOffer", applyInsuranceRiskOverrides);
                            }
                        }

                        CoverageBO coverageOccasional = ((CoveragePremiumBO)dataObject).getTheCoverageOccasional();
                        if (coverageOccasional != null) {
                            CoverageOffer plCoverage = (CoverageOffer)plCoverageOfferMap.get(coverageOccasional.getUniqueId());
                            if (plCoverage == null && coverageOccasional.getPersistenceUniqueId() != null) {
                                plCoverage = (CoverageOffer)this.getBeanFromPLGraphWithCriteria(Long.valueOf(coverageOccasional.getPersistenceUniqueId()), CoverageOffer.class, plPolicyVersion);
                            }

                            if (plCoverage == null) {
                                log.error("CoverageOffer unexepectedly null for a CoverageBO id = [" + coverageOccasional.getUniqueId() + "]");
                                throw new SystemException("CoverageOffer is null for a CoverageBO id = [" + coverageOccasional.getUniqueId() + "]");
                            }

                            InsuranceRiskBO ir = coverageOccasional.getTheInsuranceRiskBO();
                            com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = this.insuranceRiskService.findBySequence(plPolicyVersion, ir.getInsuranceRiskSequence().shortValue());
                            if (plInsuranceRisk == null) {
                                log.error("plInsuranceRisk unexepectedly null for a policy version and an InsuranceRiskSequence = [" + ir.getInsuranceRiskSequence().shortValue() + "]");
                                throw new SystemException("plInsuranceRisk is null for a policy version and an InsuranceRiskSequence = [" + ir.getInsuranceRiskSequence().shortValue() + "]");
                            }

                            plInsuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(plInsuranceRisk, OfferTypeCodeEnum.valueOfCode(ir.getOfferType()));
                            if (plInsuranceRiskOffer == null) {
                                log.error("InsuranceRiskOffer unexepectedly null for a InsuranceRisk id = [" + plInsuranceRisk.getId() + "] and an OfferType = [" + ir.getOfferType() + "]");
                                throw new SystemException("InsuranceRiskOffer is null for a InsuranceRisk id = [" + plInsuranceRisk.getId() + "] and an OfferType = [" + ir.getOfferType() + "]");
                            }

                            RatingRiskOffer plRatingRiskOfferOccasional = this.ratingRiskOfferService.findRatingRiskByType(plInsuranceRiskOffer, RatingRiskTypeCodeEnum.OCCASIONAL_RISK);
                            if (plRatingRiskOfferOccasional == null) {
                                log.error("RatingRiskOffer unexepectedly null for the occasional and for the Insurance Risk Offer id = [" + plInsuranceRiskOffer.getId() + "]");
                                throw new SystemException("RatingRiskOffer is null for the occasional and for the Insurance Risk Offer id = [" + plInsuranceRiskOffer.getId() + "]");
                            }

                            CoveragePremiumOffer plCoveragePremiumOfferOccasional = new CoveragePremiumOffer(plCoverage, plRatingRiskOfferOccasional);
                            this.mapSOMtoPL(dataObject, plCoveragePremiumOfferOccasional, currentSOMObjectName, applyInsuranceRiskOverrides);
                            List<?> subCoverageOccasionalList = ((CoveragePremiumBO)dataObject).getTheSubCoveragePremiumBO();
                            SubCoveragePremiumOffer plSubCoveragePremiumOfferOccasional = new SubCoveragePremiumOffer(plCoveragePremiumOfferOccasional);

                            for(Object anElement : subCoverageOccasionalList) {
                                this.mapSOMtoPL((SubCoveragePremiumBOImpl)anElement, plSubCoveragePremiumOfferOccasional, SUB_COVERAGE_PREMIUM, applyInsuranceRiskOverrides);
                            }
                        }
                        break;
                    case "CreditScore":
                        CreditScore plCreditScore = new CreditScore();
                        this.mapSOMtoPL(dataObject, plCreditScore, currentSOMObjectName, applyInsuranceRiskOverrides);
                        CreditScoreBO somCreditScore = (CreditScoreBO)dataObject;
                        PartyBO somParty = somCreditScore.getThePartyBO();
                        if (somParty != null) {
                            String partyId = somParty.getPersistenceUniqueId();
                            Party plParty = (Party)this.getBeanFromPLGraphWithCriteria(Long.valueOf(partyId), Party.class, plPolicyVersion);
                            plCreditScore.setParty(plParty);
                        } else {
                            somInsuranceRisk = somCreditScore.getTheInsuranceRiskBO();
                            if (somInsuranceRisk != null) {
                                String insuranceRiskId = somInsuranceRisk.getPersistenceUniqueId();
                                com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(insuranceRiskId), com.ing.canada.plp.domain.insurancerisk.InsuranceRisk.class, plPolicyVersion);
                                plInsuranceRisk.setCreditScorePostalCode(plCreditScore);
                            }
                        }
                        break;
                    case "MultRatingFactorFromNonBasicCoverage":
                        log.trace(">> waiting for proper SOM association to RatingRiskon on MultRatingFactorFromNonBasicCoverage");
                        break;
                    case "MultRatingFactorFromBasicCoverage":
                        log.trace(">> waiting for proper SOM association to RatingRisk on MultRatingFactorFromBasicCoverage");
                        break;
                    case POLICY_ADDITIONAL_COVERAGE:
                        String coverageCode = ((PolicyAdditionalCoverageBO)dataObject).getCoverageCode();
                        plCoverageRepositoryEntry = this.coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(coverageCode, plPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince(), plPolicyVersion.getInsurancePolicy().getManufacturerCompany(), plPolicyVersion.getRatingDate());
                        if (plCoverageRepositoryEntry == null) {
                            log.error("CoverageRepositoryEntry unexepectedly null for COVERAGE code = [" + coverageCode + "]");
                            throw new SystemException("CoverageRepositoryEntry is null for COVERAGE code = [" + coverageCode + "]");
                        }

                        PolicyAdditionalCoverage plPolicyAdditionalCoverage = new PolicyAdditionalCoverage(plPolicyVersion, plCoverageRepositoryEntry);
                        this.mapSOMtoPL(dataObject, plPolicyAdditionalCoverage, currentSOMObjectName, applyInsuranceRiskOverrides);
                        break;
                    case "PartyGroup":
                        PartyGroupBO partyGroupBO = (PartyGroupBO)dataObject;
                        PartyBO partyBO = partyGroupBO.getThePartyBO();
                        GroupRepositoryEntry plGroupRepositoryEntry = null;
                        GroupRepositoryEntryBO groupRepositoryEntryBO = null;
                        groupRepositoryEntryBO = partyGroupBO.getTheGroupRepositoryEntryBO();

                        try {
                            PartyGroupTypeCodeEnum plPartyGroupTypeCode = PartyGroupTypeCodeEnum.valueOfCode(groupRepositoryEntryBO.getPartyGroupType());
                            PartySubGroupTypeCodeEnum plPartySubGroupTypeCode = PartySubGroupTypeCodeEnum.valueOfCode(groupRepositoryEntryBO.getPartySubGroupType());
                            plGroupRepositoryEntry = this.groupRepositoryEntryDAO.findGroupRepositoryEntry(plPartyGroupTypeCode, groupRepositoryEntryBO.getPartyGroupCode(), plPartySubGroupTypeCode, groupRepositoryEntryBO.getPartySubGroupCode(), plPolicyVersion.getInsurancePolicy().getManufacturingContext(), plPolicyVersion.getDirectChanDistRepEntry());
                        } catch (IllegalArgumentException var34) {
                            plGroupRepositoryEntry = null;
                        }

                        if (plGroupRepositoryEntry == null) {
                            plGroupRepositoryEntry = new GroupRepositoryEntry(groupRepositoryEntryBO.getPartyGroupCode());
                        }

                        this.mapSOMtoPL((DataObject)groupRepositoryEntryBO, plGroupRepositoryEntry, DataMediatorUtils.getSOMObjectName((DataObject)groupRepositoryEntryBO), applyInsuranceRiskOverrides);
                        PartyGroup plPartyGroup = new PartyGroup(plGroupRepositoryEntry, (Party)plPartiesMap.get(partyBO.getPersistenceUniqueId()));
                        ((Party)plPartiesMap.get(partyBO.getPersistenceUniqueId())).addPartyGroup(plPartyGroup);
                        this.mapSOMtoPL(dataObject, plPartyGroup, currentSOMObjectName, applyInsuranceRiskOverrides);
                        break;
                    case REFERENCE_DATE:
                        ReferenceDate plReferenceDate = new ReferenceDate(plPolicyVersion);
                        this.mapSOMtoPL(dataObject, plReferenceDate, currentSOMObjectName, applyInsuranceRiskOverrides);
                        break;
                    case DIAGNOSTIC_AUTOMATED_ADVICE:
                        DiagnosticAutomatedAdviceBO diagnosticBO = (DiagnosticAutomatedAdviceBO)dataObject;
                        DiagnosticAutomatedAdviceRepositoryEntryBO diagnosticRepositoryEntryBO = diagnosticBO.getTheDiagnosticAutomatedAdviceRepositoryEntryBO();
                        DiagnosticAutomatedAdviceRepositoryEntry plDiagnosticRepositoryEntryBO = new DiagnosticAutomatedAdviceRepositoryEntry();
                        this.mapSOMtoPL((DataObject)diagnosticRepositoryEntryBO, plDiagnosticRepositoryEntryBO, DataMediatorUtils.getSOMObjectName((DataObject)diagnosticRepositoryEntryBO), applyInsuranceRiskOverrides);
                        InsuranceRiskBO risk = diagnosticBO.getTheInsuranceRiskBO();
                        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk anInsuranceRisk = ((InsuranceRiskOffer)plInsuranceRiskOfferMap.get(risk.getPersistenceUniqueId())).getInsuranceRisk();
                        if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
                            DiagnosticAutomatedAdvice workingDiagnostics = new DiagnosticAutomatedAdvice(plDiagnosticRepositoryEntryBO, anInsuranceRisk);
                            this.mapSOMtoPL(dataObject, workingDiagnostics, currentSOMObjectName, applyInsuranceRiskOverrides);
                        } else if (TransactionCodeEnum.POLICY_CHANGE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
                            if (InternalTechnicalOfferTypeCodeEnum.WORKING_OFFER.getCode().equals(risk.getInternalTechnicalOfferType())) {
                                InsuranceRiskOffer workingRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRiskAndInternalOfferType(anInsuranceRisk, InternalTechnicalOfferTypeCodeEnum.WORKING_OFFER);
                                DiagnosticAutomatedAdvice workingDiagnostics = new DiagnosticAutomatedAdvice(plDiagnosticRepositoryEntryBO, workingRiskOffer);
                                this.mapSOMtoPL(dataObject, workingDiagnostics, currentSOMObjectName, applyInsuranceRiskOverrides);
                            } else if (InternalTechnicalOfferTypeCodeEnum.INVERSE_OFFER.getCode().equals(risk.getInternalTechnicalOfferType())) {
                                InsuranceRiskOffer inverseRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRiskAndInternalOfferType(anInsuranceRisk, InternalTechnicalOfferTypeCodeEnum.INVERSE_OFFER);
                                DiagnosticAutomatedAdvice inverseDiagnostics = new DiagnosticAutomatedAdvice(plDiagnosticRepositoryEntryBO, inverseRiskOffer);
                                this.mapSOMtoPL(dataObject, inverseDiagnostics, currentSOMObjectName, applyInsuranceRiskOverrides);
                            }
                        }
                        break;
                    case TRANSACTIONAL_MESSAGE_ELEMENT:
                    case TRANSACTIONAL_MESSAGE:
                        break;
                    case "MarketSegment":
                        MarketSegment plMarketSegment = new MarketSegment();
                        plPolicyVersion.addMarketSegment(plMarketSegment);
                        this.mapSOMtoPL(dataObject, plMarketSegment, currentSOMObjectName, applyInsuranceRiskOverrides);
                        break;
                    case VEHICLE_EQUIPMENT:
                        VehicleEquipmentBO vehicleEquipmentBO = (VehicleEquipmentBOImpl)dataObject;
                        Short insuranceRiskSeq = vehicleEquipmentBO.getTheVehicleBO().getTheInsuranceRiskBO().getInsuranceRiskSequence().shortValue();
                        Vehicle plpVehicle = this.vehicleService.findBySequence(plPolicyVersion, insuranceRiskSeq);
                        VehicleEquipment plpVehicleEquipment = new VehicleEquipment();
                        plpVehicle.addVehicleEquipment(plpVehicleEquipment);
                        this.mapSOMtoPL(dataObject, plpVehicleEquipment, currentSOMObjectName, applyInsuranceRiskOverrides);
                        break;
                    case "VehicleDetailSpec":
                        if (log.isDebugEnabled()) {
                            log.warn("Found an unmapped addition in VehicleDetailSpec in the SOM graph: " + currentSOMObjectName);
                        }
                        break;
                    case "CoverageOption":
                        this.coverageOptionMediator.processCoverationOptionAddition(plPolicyVersion, dataObject);
                        break;
                    case LEGACY_RATING_INFO_BY_POSTAL_CODE:
                        this.mediatorLegacyRatingInfoByPostalCodeToPL.processLegacyRatingInfoByPostalCodeBO(plPolicyVersion, dataObject);
                        break;
                    case "GeographicalAssessment":
                        this.mediatorLegacyRatingInfoByPostalCodeToPL.processGeographicalAssessment(plPolicyVersion, dataObject);
                        break;
                    default:
                        if (!this.ignoredAdditions.contains(currentSOMObjectName)) {
                            log.error("An unexpected addition has occured in the SOM graph : " + currentSOMObjectName);
                            throw new SystemException("Unexpected addition to SOM graph : " + currentSOMObjectName);
                        }
                }
            }
        }

    }

    private class AdditionsComparator implements Comparator<DataObject> {
        public static final String COVERAGE_BO_IMPL = "CoverageBOImpl";

        private AdditionsComparator() {
        }


        public int compare(DataObject o1, DataObject o2) {
            int compareResult = -1;
            if (o1 != null && o2 != null) {
                String name1 = DataMediatorUtils.getRealSimpleName(o1);
                String name2 = DataMediatorUtils.getRealSimpleName(o2);
                if (COVERAGE_BO_IMPL.equals(name1) || name1.contains(COVERAGE_PREMIUM)) {
                    compareResult = 1;
                }

                if (COVERAGE_BO_IMPL.equals(name1) && name2.contains(COVERAGE_PREMIUM)) {
                    compareResult = -1;
                }

                if ("CoveragePremiumBOImpl".equals(name1) && "SubCoveragePremiumBOImpl".equals(name2)) {
                    compareResult = -1;
                }

                if (COVERAGE_PREMIUM.contains(name1) && COVERAGE_BO_IMPL.equals(name2)) {
                    compareResult = 1;
                }

                if ("SubCoveragePremiumBOImpl".equals(name1) && "CoveragePremiumBOImpl".equals(name2)) {
                    compareResult = 1;
                }

                if (name1.contains("RepositoryEntry")) {
                    compareResult = 1;
                }

                if (name2.contains("RepositoryEntry")) {
                    compareResult = -1;
                }
            }

            return compareResult;
        }
    }

    private void updateVehicleRateGroupAdjustment(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        OfferTypeCodeEnum compare = this.referenceOffer == null ? DEFAULT_REFERENCE_OFFER : this.referenceOffer;

        for(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
            InsuranceRiskOffer insuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(plInsuranceRisk, compare);
            if (insuranceRiskOffer != null) {
                for(CoverageOffer plPremiumCoverageOffer : insuranceRiskOffer.getCoverageOffers()) {
                    CoverageRepositoryEntry repEntry = plPremiumCoverageOffer.getCoverageRepositoryEntry();
                    if (!CoverageTypeCodeEnum.ENDORSEMENT.equals(repEntry.getCoverageType())) {
                        List<CoverageOffer> coverages = this.coverageService.findAllCoverageOffersByPolicyOfferRatingAndCoverageGroup(insuranceRiskOffer.getPolicyOfferRating(), repEntry.getCoverageCode());
                        if (coverages != null) {
                            for(CoverageOffer plCoverage : coverages) {
                                plCoverage.setVehicleRateGroupAdjustment(plPremiumCoverageOffer.getVehicleRateGroupAdjustment());
                            }
                        }
                    }
                }
            }
        }

    }

    private void adjustInsuranceRiskOfferSelection(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        List<PolicyVersion> policyVersionOffersList = somPolicyVersion.getThePolicyVersionOffer();
        if (policyVersionOffersList != null) {
            List<InsuranceRisk> somInsuranceRiskList = new ArrayList();

            for(InsuranceRisk insuranceRiskRoot : somPolicyVersion.getTheInsuranceRisk()) {
                somInsuranceRiskList.add(insuranceRiskRoot);

                for(PolicyVersion policyVersionOffer : policyVersionOffersList) {
                    for(InsuranceRisk insuranceRiskPolicyVersionOffer : policyVersionOffer.getTheInsuranceRisk()) {
                        if (insuranceRiskPolicyVersionOffer.getInsuranceRiskSequence().equals(insuranceRiskRoot.getInsuranceRiskSequence())) {
                            somInsuranceRiskList.add(insuranceRiskPolicyVersionOffer);
                        }
                    }
                }

                for(InsuranceRisk insuranceRiskTested : somInsuranceRiskList) {
                    if ("Y".equals(insuranceRiskTested.getRiskSelectedInd())) {
                        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk = null;

                        for(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk risk : plPolicyVersion.getInsuranceRisks()) {
                            if (risk.getInsuranceRiskSequence() == insuranceRiskRoot.getInsuranceRiskSequence().shortValue()) {
                                plInsuranceRisk = risk;
                                break;
                            }
                        }

                        if (plInsuranceRisk == null) {
                            log.error("plInsuranceRisk should not be null");
                            throw new SystemException("plInsuranceRisk should not be null");
                        }

                        InsuranceRiskOffer insuranceRiskOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(plInsuranceRisk, OfferTypeCodeEnum.valueOfCode(insuranceRiskTested.getOfferType()));
                        plInsuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);
                    }
                }
            }
        }
    }

    private void mapAllCoverageOfferPackageComposition(List<DataObject> dataObjectList, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        if (dataObjectList != null) {
            for(DataObject dataObject : dataObjectList) {
                String currentSOMObjectName = DataMediatorUtils.getSOMObjectName(dataObject);
                if (COVERAGE.equals(currentSOMObjectName)) {
                    CoverageBO somCoverage = (CoverageBO)dataObject;
                    if (somCoverage.getTheInsuranceRiskBO() != null) {
                        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk currentInsuranceRisk = this.getPlInsuranceRisk(somCoverage, plPolicyVersion);

                        for(InsuranceRiskOffer currentInsuranceRiskOffer : currentInsuranceRisk.getInsuranceRiskOffers()) {
                            Set<CoverageOffer> plCoverageOffer = this.insuranceRiskOfferService.findInsuranceRiskOfferByInsuranceRiskAndOfferType(currentInsuranceRisk, OfferTypeCodeEnum.valueOfCode(somCoverage.getTheInsuranceRiskBO().getOfferType())).getCoverageOffers();
                            this.mapParentCoverageOfferPackageComposition(somCoverage, plCoverageOffer);
                        }
                    }
                }
            }
        }
    }

    private void mapParentCoverageOfferPackageComposition(CoverageBO somCoverageOffer, Set<CoverageOffer> coverageOfferSet) {
        List<CoverageBO> somCoveragePackageComp = somCoverageOffer.getTheCoveragePackageComposition();
        Set<CoverageOfferPackageComp> plCoverageOfferComp = new HashSet();
        CoverageOffer parent = this.getTheCoverage(somCoverageOffer.getTheCoverageProductBO().getTheCoverageRepositoryEntryBase().getCoverageCode(), coverageOfferSet);
        if (somCoveragePackageComp.size() > 0 && parent != null) {
            for(CoverageBO somCoveragePackage : somCoveragePackageComp) {
                CoverageOfferPackageComp plCoverageOfferPkg = new CoverageOfferPackageComp();
                CoverageOffer child = this.getTheCoverage(somCoveragePackage.getTheCoverageProductBO().getTheCoverageRepositoryEntryBase().getCoverageCode(), coverageOfferSet);
                if (child != null) {
                    plCoverageOfferPkg.setChildCoverageOffer(child);
                    plCoverageOfferPkg.setParentCoverageOffer(parent);
                }

                plCoverageOfferComp.add(plCoverageOfferPkg);
            }

            parent.setCoverageOfferPackageComps(plCoverageOfferComp);
        }
    }

    private com.ing.canada.plp.domain.insurancerisk.InsuranceRisk getPlInsuranceRisk(CoverageBO somCoverage, com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plIR = null;
        InsuranceRiskBO somIR = somCoverage.getTheInsuranceRiskBO();

        for(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : plPolicyVersion.getInsuranceRisks()) {
            if (ir.getInsuranceRiskSequence() == somIR.getInsuranceRiskSequence().shortValue()) {
                plIR = ir;
            }
        }

        return plIR;
    }

    private CoverageOffer getTheCoverage(String coverageCode, Set<CoverageOffer> plCoverageOfferSet) {
        CoverageOffer cov = null;

        for(CoverageOffer co : plCoverageOfferSet) {
            if (co.getCoverageRepositoryEntry().getCoverageCode().equals(coverageCode)) {
                cov = co;
            }
        }

        return cov;
    }

    public void setIsKeepNonEligibleCoverages(boolean isKeep) {
        this.isKeepNonEligibleCoverages = isKeep;
    }


}
