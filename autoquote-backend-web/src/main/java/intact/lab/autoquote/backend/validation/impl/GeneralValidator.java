package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.exception.SecurityUtilityException;
import intact.lab.autoquote.backend.common.exception.SecurityUtilityInvalidValueException;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.validation.whitelist.WhiteList;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

public class GeneralValidator {

    public static void validateApiParameter(String language, String province) throws AutoQuoteApiParametersException {
        if (StringUtils.isEmpty(language)) {
            throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_LANGUAGE_NULL_ERROR);
        }
        if (StringUtils.isEmpty(province)) {
            throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_PROVINCE_NULL_ERROR);
        }
    }

    public static void validateContext(String language, String province, String company, Errors errors) throws AutoQuoteApiParametersException {
        validateApiParameter(language, province);
        // must pass null as error param to inform validators that theses are not part of the quote object (they will throw on error instead of populating error object)
        try {
            validateCompany(company, null);
        } catch (SecurityUtilityInvalidValueException e) {
            throw new IllegalStateException("Company specified is not valid.", e); // at time of initial writing, only Intact is supported
        }
        try {
            validateProvince(province, null);
        } catch (SecurityUtilityException | SecurityUtilityInvalidValueException e) {
            throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_PROVINCE_INVALID_ERROR, e);
        }
        try {
            validateLanguage(language, null);
        } catch (SecurityUtilityException | SecurityUtilityInvalidValueException e) {
            throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_LANGUAGE_INVALID_ERROR, e);
        }
    }

    public static void validateCompany(String company, Errors errors) throws SecurityUtilityInvalidValueException {

        if (!AutoQuoteConstants.STR_INTACT.equals(company)) {
            if (errors == null) {
                throw new SecurityUtilityInvalidValueException("An unsupported company was provided. Found: " + company);

            } else {
                errors.rejectValue("company", BRulesExceptionEnum.Pattern.getErrorCode(), "company");
            }

        }

    }

    public static void validateLanguage(String language, Errors errors) throws SecurityUtilityException, SecurityUtilityInvalidValueException {
        try {
            WhiteList.isAllowedValue(language, WhiteList.getAllowedLanguageList());
        } catch (SecurityUtilityInvalidValueException e) {
            if (errors == null) {
                throw new SecurityUtilityInvalidValueException("An unsupported company was provided. Found: " + language);

            } else {
                errors.rejectValue("language", BRulesExceptionEnum.Pattern.getErrorCode(), "[language]");
            }
        }
    }

    public static void validateProvince(String province, Errors errors) throws SecurityUtilityException, SecurityUtilityInvalidValueException {
        try {
            WhiteList.isAllowedValue(province, WhiteList.getAllowedProvinceList());
        } catch (SecurityUtilityInvalidValueException e) {
            if (errors == null) {
                throw new SecurityUtilityInvalidValueException("An unsupported province was provided. Found: " + province);

            } else {
                errors.rejectValue("province", BRulesExceptionEnum.Pattern.getErrorCode(), "[province]");
            }
        }
    }

    public static boolean isValidEmailAddress(String email) {
        //String ePattern = "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$";
        String ePattern = "^[0-9a-zA-Z]+([0-9a-zA-Z]*[-._+])*[0-9a-zA-Z]+@[0-9a-zA-Z]+([-.][0-9a-zA-Z]+)*([0-9a-zA-Z]*[.])[a-zA-Z]{2,6}$";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(ePattern);
        java.util.regex.Matcher m = p.matcher(email);
        return m.matches();
    }
}
