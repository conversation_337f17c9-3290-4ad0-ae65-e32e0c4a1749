package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IVehicleDTOValidator;
import intact.lab.autoquote.backend.validation.rule.BusinessKmValidationRule;
import intact.lab.autoquote.backend.validation.rule.MakeValidationRule;
import intact.lab.autoquote.backend.validation.rule.ModelValidationRule;
import intact.lab.autoquote.backend.validation.rule.YearValidationRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("VehicleDTOValidator")
public class VehicleDTOValidator implements IVehicleDTOValidator {

	private final YearValidationRule yearValidationRule;
	private final MakeValidationRule makeValidationRule;
	private final ModelValidationRule modelValidationRule;
	private final BusinessKmValidationRule businessKmValidationRule;

	public VehicleDTOValidator(
			YearValidationRule yearValidationRule,
			MakeValidationRule makeValidationRule,
			ModelValidationRule modelValidationRule,
			BusinessKmValidationRule businessKmValidationRule) {
		this.yearValidationRule = yearValidationRule;
		this.makeValidationRule = makeValidationRule;
		this.modelValidationRule = modelValidationRule;
		this.businessKmValidationRule = businessKmValidationRule;
	}

	@Override
	public void validate(VehicleDTO vehicleDTO, Errors errors, ValidationContext context) {
		final String province = context.getProvince();
		final String language = context.getLanguage();
		final String company = context.getCompany();

		Integer year = vehicleDTO.getYear();
		validateYear(year, province, language, errors);

		// make and model validation
		if (null != year) { // need year to validate make and model
			String make = vehicleDTO.getMake();
			validateMake(make, company, year, province, language, errors);
			if (!StringUtils.isEmpty(make)) { // need the make to validate the model
				validateModel(vehicleDTO.getMake(), vehicleDTO.getModelCode(), company, year, province, language, errors);
			}
		}

		validateBusinessKmPerYear(vehicleDTO.getBusinessKmPerYear(), province, language, errors);
//		validateKmPerYear(vehicleDTO.getKmPerYear(), province, language, errors);
		validateCommercialUsageCategoryCd(vehicleDTO.getCommercialUsageCategoryCd(), vehicleDTO.getCommercialUsageCd(), vehicleDTO.getCommercialUsageSpecificCd(), errors);
		//validateNormalRadiusKm(vehicleDTO.getNormalRadiusKm(), errors);
	}

	public void validateKmPerYear(Integer kmPerYear, String province, String language, Errors errors) {
		if (null == kmPerYear) { // Personal line validates with AnnualKmValidationRule
			errors.rejectValue("kmPerYear", BRulesExceptionEnum.NotBlank.getErrorCode(), "[kmPerYear]");
		}
	}


	private void validateYear(Integer year, String province, String language, Errors errors) {
		if (null == year) {
			errors.rejectValue("year", BRulesExceptionEnum.NotBlank.getErrorCode(), "[year]");
		} else {
			yearValidationRule.validate(year.toString(), province, language, errors);
		}
	}

	private void validateMake(String make, String company, Integer year, String province, String language, Errors errors) {
		if (StringUtils.isEmpty(make)) {
			errors.rejectValue("make", BRulesExceptionEnum.NotBlank.getErrorCode(), "[make]");
		} else {
			makeValidationRule.validate(make, company, year, province, language, errors);
		}
	}

	private void validateModel(String make, String model, String company, Integer year, String province, String language, Errors errors) {
		if (StringUtils.isEmpty(model)) {
			errors.rejectValue("model", BRulesExceptionEnum.NotBlank.getErrorCode(), "[model]");
		} else {
			modelValidationRule.validate(year, make, model, company, province, language, errors);
		}
	}

	private void validateCommercialUsageCategoryCd(String commercialUsageCategoryCd, String commercialUsageCd, String commercialUsageSpecificCd, Errors errors) {
		if (StringUtils.isEmpty(commercialUsageCategoryCd)) {
			errors.rejectValue("commercialUsageCategoryCd", BRulesExceptionEnum.NotBlank.getErrorCode(), "[commercialUsageCategoryCd]");
		} else if (StringUtils.isNotEmpty(commercialUsageCd) && StringUtils.isEmpty(commercialUsageCategoryCd)) {
			errors.rejectValue("commercialUsageCategoryCd", BRulesExceptionEnum.NotBlank.getErrorCode(), "[commercialUsageCategoryCd]");
		} else if (StringUtils.isNotEmpty(commercialUsageSpecificCd) && StringUtils.isEmpty(commercialUsageCategoryCd)) {
			errors.rejectValue("commercialUsageCategoryCd", BRulesExceptionEnum.NotBlank.getErrorCode(), "[commercialUsageCategoryCd]");
		} else if (StringUtils.isNotEmpty(commercialUsageSpecificCd) && StringUtils.isEmpty(commercialUsageCd)) {
			errors.rejectValue("commercialUsageCd", BRulesExceptionEnum.NotBlank.getErrorCode(), "[commercialUsageCd]");
		}
	}

	private void validateBusinessKmPerYear(Integer businessKmPerYear, String province, String language, Errors errors) {
		businessKmValidationRule.validate(businessKmPerYear == null ? null : businessKmPerYear.toString(), province, language, errors);
	}

	private void validateNormalRadiusKm(Integer normalRadiusKm, Errors errors) {
		if (null == normalRadiusKm) {
			errors.rejectValue("normalRadiusKm", BRulesExceptionEnum.NotBlank.getErrorCode(), "[normalRadiusKm]");
		}
	}

}
