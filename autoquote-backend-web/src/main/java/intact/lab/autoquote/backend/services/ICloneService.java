/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;

/**
 * Service that will verify if a clone is needed and do the clone if necesary.
 * 
 * <AUTHOR>
 */
public interface ICloneService {

	/**
	 * Clones the current working policy into a new PolicyVersion.
	 * 
	 * @return the new PolicyVersion clone
	 * */
	PolicyVersion clone(PolicyVersion oPolicy, String cifClientId, String marketingPromotionCode);
}
