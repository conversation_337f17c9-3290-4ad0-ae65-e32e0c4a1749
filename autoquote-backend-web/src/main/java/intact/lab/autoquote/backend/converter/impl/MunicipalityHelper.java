package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.CommunicationObjectModel;
import com.intact.com.address.ComMunicipalityInfo;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.converter.IMunicipalityHelper;
import intact.lab.autoquote.backend.facade.driver.impl.DriverFacade;
import org.springframework.stereotype.Component;


@Component("municipalityHelper")
public class MunicipalityHelper implements IMunicipalityHelper {

	@Override
	public ComMunicipalityInfo getMunicipalityInfo(CommunicationObjectModel com, AddressDTO addressDTO) throws AutoQuoteException {
        return DriverFacade.getInstance(com.getContext())
                .getMunicipalityByPostalCode(com.getContext(), addressDTO.getPostalCode());
	}
}
