package intact.lab.autoquote.backend.services.business.sessionmanager;

public interface IConfigurator {

    /**
     * Determines the version of UBI configured for the province
     *
     * @param province The province of interest; one of QC, ON, AB or BC
     * @param distributor one of BEL or BNA; if null or empty treated as BEL (use null for Intact)
     * @return true if UBI is configured for version 2.0, false otherwise (including if config prop is not found)
     */
    boolean isUbiVersion2(String province, String distributor);

    boolean isUbi(String province, String distributor);

    boolean isUbi(String province);
}
