/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.intact.business.rules.enums.RoadBlockExceptionEnum;
import com.intact.business.rules.exception.BusinessRuleException;
import com.intact.business.rules.offer.BR2496_IsHomeAssumedInsuredWithCompany;
import com.intact.business.rules.offer.BR2658_MinimumBusinessKmDriven;
import com.intact.business.rules.offer.BR2667_EffectiveDate;
import com.intact.business.rules.offer.BR5799_EligibilityToBindON;
import com.intact.business.rules.vehicle.BR232_OneVehicleIsTooOld;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.impl.ExecuteServiceONIntact;
import org.springframework.beans.factory.annotation.Autowired;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.ONTARIO;

/**
 * Business process specific to Intact Ontario CL. Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 */
@ComponentLocal(province = ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class OfferBusinessProcessONIntactCL extends OfferBusinessProcess {

    @Autowired
    private BR2667_EffectiveDate br2667;

    @Autowired
    private BR5799_EligibilityToBindON br5799;

    @Autowired
    private BR2496_IsHomeAssumedInsuredWithCompany br2496;

    @Autowired
    private BR232_OneVehicleIsTooOld br232;

    @Autowired
    private BR2658_MinimumBusinessKmDriven br2658;

    @Autowired
    private VehicleHelper vehicleHelper;

    @Autowired
    private ExecuteServiceONIntact executeService;

    @AutowiredLocal
    protected IRateManagerService rateManagerService;

    @AutowiredLocal
    protected IRatingService ratingService;

    @Override
    protected void determineQuoteInfosPostProcess(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException {
        // We must retrieve the use of vehicle category since Pega only returns the usage code.
        Vehicle aPlVehicle = this.policyVersionHelper.retrieveVehicle(aPlPolicyVersion,1);
        if (aPlVehicle.getUseOfVehicleCategory() == null && aPlVehicle.getVehicleUsage() != null) {
            aPlVehicle.setUseOfVehicleCategory(vehicleHelper.retrieveUseOfVehicleCategory(aPlVehicle.getVehicleUsage()));
        }
    }

    @Override
    public IRateManagerService getRateManagerService() {
        return this.rateManagerService;
    }

    @Override
    protected IExecuteService getExecuteService() {
        return this.executeService;
    }

    @Override
    public IRatingService getRatingService() {
        return this.ratingService;
    }

    /**
     * Validate post br.
     *
     * @param policyVersion the policy version
     *
     * @throws BusinessRuleException the road block exception
     */
    @Override
    public void validatePostBR(PolicyVersion policyVersion) throws BusinessRuleException {
        if(!this.br2667.validate(policyVersion)){
            throw new BusinessRuleException(RoadBlockExceptionEnum.BR2667,
                    RoadBlockExceptionContextEnum.OFFER);
        }
        if(!this.br2496.validate(policyVersion)){
            throw new BusinessRuleException(RoadBlockExceptionEnum.BR2496, RoadBlockExceptionContextEnum.OFFER);
        }

        this.br2658.validate(policyVersion);
        this.br5799.validate(policyVersion);
        this.br232.validate(policyVersion, Province.ONTARIO);
    }

    @Override
    public void selectOffer(final PolicyVersion policyVersion, Boolean isUbiEnabled) throws AutoquoteRatingException, BusinessRuleException {
        super.validatePostBR(policyVersion);
        super.selectOffer(policyVersion, isUbiEnabled);
    }
}
