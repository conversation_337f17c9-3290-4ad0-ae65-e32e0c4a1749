package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.DriverLicenseClassCodeEnum;
import com.ing.canada.plp.domain.enums.DriverLicenseTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LicenseJurisdictionCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UBIProviderCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCategoryCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Locale;
import java.util.Map;


/**
 * COM to PLP Adapter for AB Intact CL.
 *
 * <AUTHOR>
 */
@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class COMtoPLAdapterABIntactCL extends COMtoPLAdapterIntact {

	@Autowired
	private UBIProviderCodeEnum ubiProviderABIntact;

	public COMtoPLAdapterABIntactCL(ICommonBusinessProcess commonBusinessProcess, IMediatorComPlp mediatorComPlp,
									IMediatorVehiclePlp mediatorVehiclePlp, IMediatorDriverPlp mediatorDriverPlp,
									IMediatorClaimPlp mediatorClaim, MediatorPaymentPlp mediatorPaymentPlp,
									IVehicleHelper vehicleHelper, IPartyHelper partyHelper, IMediatorAdvisor mediatorAdvisor,
									IPolicyVersionHelper plpPolicyVersionHelper, IInsuranceRiskHelper plpInsuranceRiskHelper,
									IDateManagerService capiPolicyChangeDateService) {
		super(commonBusinessProcess, mediatorComPlp, mediatorVehiclePlp, mediatorDriverPlp, mediatorClaim, mediatorPaymentPlp,
				vehicleHelper, partyHelper, mediatorAdvisor, plpPolicyVersionHelper, plpInsuranceRiskHelper, capiPolicyChangeDateService);
	}

	@Override
	protected void setUbiProvider(DriverComplementInfo driverComplementInfo) {
		driverComplementInfo.setUBIProvider(ubiProviderABIntact);
	}

	@Override
	protected void setUbiProgramVersionCode(DriverComplementInfo driverComplementInfo) {
		// do nothing here.
	}

	/**
	 * Localized post mediation for party..
	 *
	 * @param event
	 * @param party
	 * @param insuredGroups
	 * @param context
	 * @param locale
	 */
	@Override
	protected void localizedPostMediationForParty(ComEvent event, Party party, Map<String, String> insuredGroups, ManufacturingContext context, Locale locale) {
		if (this.isDriver(party)) {
			this.setDriverLicense(party);
		}
	}

	/**
	 * Set the driver licence information.
	 *
	 * @param party
	 */
	private void setDriverLicense(Party party) {
		DriverComplementInfo dci = party.getDriverComplementInfo();

		// clear existing license class and recreate
		dci.clearDriverLicenseClass();

		if (dci.getLicenceClass() == null) {
			dci.setDriverLicenseType(DriverLicenseTypeCodeEnum.REGULAR_LICENSE);
			DriverLicenseClass newLicenseClass = new DriverLicenseClass();
			newLicenseClass.setDriverLicenseClass(DriverLicenseClassCodeEnum.GRADUATED_PERMIT);
			newLicenseClass.setEffectiveDate(new Date());
			dci.addDriverLicenseClass(newLicenseClass);
		}

	}

	/**
	 * Sets the drivers' license jurisdiction.
	 *
	 * @param party
	 */
	@Override
	protected void localizedDriversLicenseJurisdiction(Party party) {
		if (this.isDriver(party)) {
			party.getDriverComplementInfo().setLicenseJurisdiction(LicenseJurisdictionCodeEnum.ALBERTA);
		}
	}

	/**
	 * Retrieves the use of vehicle code from the category.
	 *
	 * @param useOfVehicleCategory The {@link UseOfVehicleCategoryCodeEnum}.
	 * @return the use of vehicle code
	 */
	@Override
	public UseOfVehicleCodeEnum retrieveUseOfVehicle(UseOfVehicleCategoryCodeEnum useOfVehicleCategory) {
		UseOfVehicleCodeEnum useOfVehicle = null;
		if (useOfVehicleCategory != null) {
			if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.COMMERCIAL.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.OTHER;
			} else {
				useOfVehicle = UseOfVehicleCodeEnum.PLEASURE;
			}
		}
		return useOfVehicle;
	}

	private boolean isDriver(Party party) {
		return party.getDriverComplementInfo() != null;
	}
}
