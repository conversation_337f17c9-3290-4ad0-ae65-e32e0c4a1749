package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.util.Assert;

@Slf4j
public class MediatorUtil {

    public static ManufacturingContext convertContext(ComContext context) {
        Assert.notNull(context, "ComContext parameter must be initialized.");
        String distribChannelCode = context.getDistributionChannel() != null ? context.getDistributionChannel().getCode() : null;
        DistributionChannelCodeEnum distributionChannel = DistributionChannelCodeEnum.valueOfCode(distribChannelCode);
        String provinceCode = context.getProvince() != null ? context.getProvince().getCode() : null;
        ProvinceCodeEnum province = ProvinceCodeEnum.valueOfCode(provinceCode);
        ManufacturerCompanyCodeEnum manufacturer;
        InsuranceBusinessCodeEnum insuranceBusiness;
        ComCompanyEnum company = context.getCompany();
        if (company == ComCompanyEnum.INTACT) {
            insuranceBusiness = InsuranceBusinessCodeEnum.REGULAR;
            switch (province) {
                case ALBERTA:
                    manufacturer = ManufacturerCompanyCodeEnum.ING_WESTERN_REGION;
                    return new ManufacturingContext(distributionChannel, insuranceBusiness, province, manufacturer);
                case ONTARIO:
                    manufacturer = ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION;
                    return new ManufacturingContext(distributionChannel, insuranceBusiness, province, manufacturer);
                case QUEBEC:
                    manufacturer = ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION;
                    return new ManufacturingContext(distributionChannel, insuranceBusiness, province, manufacturer);
                default:
                    throw new NotImplementedException("There is currently no context implemented for " + company + " " + province);
            }
        }
        throw new NotImplementedException("There is currently no context implemented for " + company);
    }

    public static DistributorCodeEnum getPLPdistributor(ComDistributor comDist) {
        DistributorCodeEnum plpDist = null;
        if (comDist != null) {
            switch (comDist) {
                case BEL:
                    plpDist = DistributorCodeEnum.BEL;
                    break;
                case BNA:
                    plpDist = DistributorCodeEnum.BNA;
                    break;
                default:
                    log.warn("No plp mapping is defined for value {}", comDist);
            }
        }

        return plpDist;
    }
}
