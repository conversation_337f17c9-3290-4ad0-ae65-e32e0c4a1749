/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common;

import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;

import java.util.List;
import java.util.Locale;

/**
 * The Interface IOccupationService.
 * 
 * <AUTHOR> xpham
 */
public interface IOccupationService {


	/**
	 * Gets a list of domain of occupation from classic filtered by distributor
	 * 
	 * @param aLocale the a locale
	 * @param distributionChannel {@link DistributionChannelCodeEnum}
	 * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
	 * 
	 * @return the domains
	 */
	List<ValidValueBO> getDomains(Locale aLocale, DistributionChannelCodeEnum distributionChannel,
								  InsuranceBusinessCodeEnum insuranceBusiness, DistributorCodeEnum distributor);

	/**
	 * Gets a list of occupations from classic.
	 * 
	 * @param aLocale the a locale
	 * @param domain the domain
	 * 
	 * @return the occupations
	 */
	List<ValidValueBO> getOccupations(Locale aLocale, String domain, ManufacturingContext context, DistributorCodeEnum distributor);

}
