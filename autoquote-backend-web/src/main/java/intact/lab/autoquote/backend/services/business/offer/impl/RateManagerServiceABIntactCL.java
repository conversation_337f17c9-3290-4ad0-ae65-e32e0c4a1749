/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.rating.exception.RatingException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class RateManagerServiceABIntactCL extends RateManagerService {

    private static final Logger log = ESAPI.getLogger(RateManagerServiceABIntactCL.class);

    @AutowiredLocal
    protected IRatingService ratingService;

    /**
     * @see IRateManagerService#getRatingService()
     */
    @Override
    public IRatingService getRatingService() {
        return this.ratingService;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public PolicyVersion rateTheWholeQuotation(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled) throws AutoquoteRatingException {

        StopWatch performanceWatch = new StopWatch();
        if (performanceWatch.isRunning()) {
            performanceWatch.stop();
        }

        performanceWatch.start("    >> rateTheWholeQuotation.rateOffer");
        PolicyVersion policyVersion = this.getRatingService().rateOffer(aPolicyVersion, isAgent, isUbiEnabled);
        performanceWatch.stop();
        if (log.isDebugEnabled()) {
            try {
                this.helper.printPolicyCoverage(policyVersion, true, log, this.premiumDeviationService.getReferenceOffer(aPolicyVersion.getInsurancePolicy().getApplicationMode().getCode()));
            } catch (RatingException ignored) {
                // ignored
            }
        }

        performanceWatch.start("    >> rateTheWholeQuotation.calculateOffer");
        this.calculateOffer(aPolicyVersion.getLatestPolicyOfferRating());
        performanceWatch.stop();

        if (log.isTraceEnabled()) {
            log.trace(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
        }

        return policyVersion;
    }

    private void calculateOffer(PolicyOfferRating currentPolicyOfferRating) {
        Integer policyOfferRatingFullTermPremium = null;
        Integer policyOfferRatingAnnualPremium = null;

        for (InsuranceRiskOffer insuranceRiskOffer : currentPolicyOfferRating.getInsuranceRisksOffers()) {
            Integer annualPremium = null;
            Integer fullTermPremium = insuranceRiskOffer.getFullTermPremium();

            if (fullTermPremium != null) {
                if (currentPolicyOfferRating.getPolicyVersion().getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.TWELVE_MONTHS)) {
                    annualPremium = fullTermPremium;
                } else if (currentPolicyOfferRating.getPolicyVersion().getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.TWENTYFOUR_MONTHS)) {
                    annualPremium = fullTermPremium / 2;
                } else if (currentPolicyOfferRating.getPolicyVersion().getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.SIX_MONTHS)) {
                    annualPremium = fullTermPremium * 2;
                } else {
                    log.error(Logger.EVENT_FAILURE, "unrecognized policy term: " + currentPolicyOfferRating.getPolicyVersion().getPolicyTermInMonths() + " months");
                }

                insuranceRiskOffer.setAnnualPremium(annualPremium);

                if (insuranceRiskOffer.getSelectedForPolicyOfferRatingIndicator() != null && insuranceRiskOffer.getSelectedForPolicyOfferRatingIndicator()) {
                    if (policyOfferRatingFullTermPremium != null) {
                        policyOfferRatingFullTermPremium += fullTermPremium;
                        policyOfferRatingAnnualPremium += annualPremium;
                    } else {
                        policyOfferRatingFullTermPremium = fullTermPremium;
                        policyOfferRatingAnnualPremium = annualPremium;
                    }
                }
            }
        }

        // set the policyofferrating annual and full term premiums
        currentPolicyOfferRating.setAnnualPremium(policyOfferRatingAnnualPremium);
        currentPolicyOfferRating.setFullTermPremium(policyOfferRatingFullTermPremium);

        // set the policyofferrating annual premium
        currentPolicyOfferRating.setFullTermPremiumTaxable(0);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void selectOffer(PolicyVersion aPolicyVersion, StopWatch performanceWatch, boolean isUbiEnabled) throws AutoquoteRatingException {

        this.premiumDeviationService.saveOfferSelectionsOnThePolicyOfferRating(aPolicyVersion);

        if (isUbiEnabled) {
            // Add UBI endorsement if eligible and apply discount (re-rate) if selected
            this.ratingService.manageUbi(aPolicyVersion, null, performanceWatch);
        }
    }

}
