package intact.lab.autoquote.backend.services.business.common;

import com.ing.canada.cif.domain.IClient;
import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

public interface ICommonBusinessProcess {

    /**
     * Create a Business Transaction activity for a provided policy version.
     *
     * @param policyVersion {@link PolicyVersion}
     * @param trxActivity {@link BusinessTransactionActivityCodeEnum}
     * @param userType {@link UserTypeCodeEnum}
     */
     void createActivity(final PolicyVersion policyVersion, BusinessTransactionActivityCodeEnum trxActivity,
                        UserTypeCodeEnum userType);

    /**
     * gets the Client object from CIF if the cifClientId is not null in the nameInsured party.
     *
     * @param policyVersion the policy version
     * @return the cif client if found, else null
     */
    IClient getCifClient(PolicyVersion policyVersion);

    /**
     * from a policyversion return the selected offer quote details.
     *
     * @param aPolicyVersion {@link PolicyVersion}
     * @param aProvinceCode {@link ProvinceCodeEnum}
     * @return {@link QuoteCalculationDetails}
     */
    QuoteCalculationDetails getQuoteCalculationDetails(PolicyVersion aPolicyVersion, ProvinceCodeEnum aProvinceCode);

    /**
     * Load policy version.
     *
     * @param aPolicyVersionId the a policy version id
     * @return the policy version
     */
    PolicyVersion loadPolicyVersion(Long aPolicyVersionId);

    /**
     * Save a plp policy version
     *
     * @param policyVersion - {@link PolicyVersion}
     */
    void savePolicyVersion(PolicyVersion policyVersion);
}
