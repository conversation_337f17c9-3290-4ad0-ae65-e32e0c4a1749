package intact.lab.autoquote.backend.common.exception;


import com.intact.com.enums.ComRoadBlockTypeEnum;
import com.intact.com.util.ComRoadBlock;
import intact.lab.autoquote.backend.common.dto.ErrorDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import intact.lab.autoquote.backend.common.enums.ErrorTypeEnum;
import intact.lab.security.recaptcha.api.exception.RecaptchaException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

@Log4j2
@ControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    private static final String ROAD_BLOCK_MESSAGE = "Raod block error.";
    private static final String VALIDATION_ERROR_MESSAGE = "Validation error.";

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ResponseDTO> handleUncaughtException(Exception ex) {
        log.error(ex.getMessage(), ex);
        return this.handleException(ex, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ResponseDTO> handleNotSupportedMethod(Exception ex) {
        log.error(ex.getMessage(), ex);
        return this.handleException(ex, HttpStatus.METHOD_NOT_ALLOWED);
    }

    @ExceptionHandler(AutoQuoteVehicleException.class)
    public ResponseEntity<ResponseDTO> handleAutoQuoteVehicleException(Exception ex) {
        log.error(ex.getMessage(), ex);
        return this.handleException(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(BrokerException.class)
    public ResponseEntity<ResponseDTO> handleBrokerException(Exception ex) {
        log.error(ex.getMessage(), ex);
        return this.handleException(ex, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(RecaptchaException.class)
    public ResponseEntity<ResponseDTO> handleRecaptchaException(Exception ex) {
        log.error(ex.getMessage(), ex);
        return this.handleException(ex, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(QuoteValidationException.class)
    public ResponseEntity<ResponseDTO> handleQuoteValidation(QuoteValidationException ex) {
        log.error(ex.getMessage(), ex);
        return new ResponseEntity<>(getValidationErrorsResponse(ex), HttpStatus.OK);
    }

    @ExceptionHandler(AutoQuoteRoadBlockException.class)
    public ResponseEntity<ResponseDTO> roadBlockExceptionHandler(AutoQuoteRoadBlockException ex) {
        log.error(ex.getMessage(), ex);
        return new ResponseEntity<>(getRoadBlockErrorsResponse(ex), HttpStatus.OK);
    }

    private ResponseEntity<ResponseDTO> handleException(
            Exception ex, HttpStatus httpStatus) {
        log.error(ex.getMessage(), ex);
        return ResponseEntity.status(httpStatus).body(this.buildErrorDTO(ex, httpStatus.toString()));
    }

    private ResponseDTO buildErrorDTO(Exception ex, String errorCode) {
        ErrorDTO error = new ErrorDTO();
        error.setCode(errorCode);
        error.setDescription(ExceptionUtils.getStackTrace(ex));

        List<ErrorDTO> errors = new LinkedList<>();
        errors.add(error);

        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setErrors(errors);

        return responseDTO;
    }

    private ResponseDTO getRoadBlockErrorsResponse(AutoQuoteRoadBlockException ex) {
        List<ComRoadBlock> roadBlocks = ex.getRoadBlocks();
        QuoteDTO quote = ex.getQuote();
        List<ErrorDTO> errors = new ArrayList<>(roadBlocks.size());
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setData(quote);
        responseDTO.setErrors(errors);
        if (CollectionUtils.isNotEmpty(roadBlocks)) {
            for (ComRoadBlock comRoadBlock : roadBlocks) {
                ErrorDTO errorDTO = new ErrorDTO(comRoadBlock.getRoadBlockCode(), ROAD_BLOCK_MESSAGE,
                        ComRoadBlockTypeEnum.HARD.equals(comRoadBlock.getRoadBlockType()) ?
                                ErrorTypeEnum.HARD_ROADBLOCK : ErrorTypeEnum.SOFT_ROADBLOCK, new ArrayList<>());
                errors.add(errorDTO);
            }
        }
        return responseDTO;
    }

    private ResponseDTO getValidationErrorsResponse(QuoteValidationException ex) {
        ResponseDTO responseDTO = new ResponseDTO();
        QuoteDTO quote = ex.getQuote();
        Errors errors = ex.getErrors();
        List<ObjectError> validationErrors = errors.getAllErrors();
        List<ErrorDTO> validations = new ArrayList<>(validationErrors.size());
        if (CollectionUtils.isNotEmpty(validationErrors)) {
            for (ObjectError error : validationErrors) {
                FieldError fe = (FieldError) error;
                ErrorDTO errorDTO = new ErrorDTO(fe.getCode(), VALIDATION_ERROR_MESSAGE, ErrorTypeEnum.FIELD_VALIDATION, new ArrayList<>());
                FieldPathNameMapper fieldPathMapper = new FieldPathNameMapper(fe.getObjectName(), fe.getField());
                errorDTO.addErrorSource(fieldPathMapper.fieldName, fieldPathMapper.fieldPath);
                validations.add(errorDTO);
            }
        }
        responseDTO.setData(quote);
        responseDTO.setErrors(validations);
        return responseDTO;
    }
}
