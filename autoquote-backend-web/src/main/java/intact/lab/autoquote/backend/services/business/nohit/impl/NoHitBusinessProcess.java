/*
 * Important notice: This software is the sole property of Intact Insurance. and cannot be distributed and/or copied
 * without the written permission of Intact Insurance. 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.nohit.impl;


import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import intact.lab.autoquote.backend.services.business.nohit.INoHitBusinessProcess;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * The Class NoHitBusinessProcess.
 * 
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class NoHitBusinessProcess implements INoHitBusinessProcess {

	/** The insurance policy service. */
	private IInsurancePolicyService insurancePolicyService;
	protected IPartyHelper partyHelper;

	/**
	 * Update no hit flag so that the system knowns the no hit page as been displayed.
	 * 
	 * @param aPolicyVersion the a policy version
	 */
	@Transactional
	public void updateNoHitFlag(PolicyVersion aPolicyVersion) {

		InsurancePolicy insurancePolicy = aPolicyVersion.getInsurancePolicy();
		insurancePolicy.setCreditScoreInfoClientConfirmationInd(true);

		this.insurancePolicyService.persist(insurancePolicy);
	}

}
