package intact.lab.autoquote.backend.common.dto;

import intact.lab.autoquote.backend.common.enums.ModificationCodeEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RiskDTO {

    private Integer id;
    private ModificationCodeEnum modificationCode;
    private String commercialUsageCategoryCd;
    private String commercialUsageCd;
    private String commercialUsageSpecificCd;
    private List<OfferDTO> offers = new ArrayList<>();
}
