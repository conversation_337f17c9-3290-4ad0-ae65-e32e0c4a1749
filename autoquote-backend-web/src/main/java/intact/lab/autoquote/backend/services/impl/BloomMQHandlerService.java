package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.intactfc.bloom.mq.api.ApiException;
import com.intactfc.bloom.mq.api.MqEventServiceApi;
import com.intactfc.bloom.mq.model.MQMessage;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * class to send message to bloom mq handler service for bloom project
 * for WESTERN, CENTRAL_ATLANTIC AND QUEBEC REGION
 */
@Component
public class BloomMQHandlerService {

    protected static final Logger LOG = ESAPI.getLogger(BloomMQHandlerService.class);

    private final List listIntactSourceUnderWritingCompanies = ListUtils.unmodifiableList(Arrays.asList(ManufacturerCompanyCodeEnum.ING_WESTERN_REGION,
                                                                                                        ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION, ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));

    MqEventServiceApi mqEventServiceApi;

    public BloomMQHandlerService(MqEventServiceApi mqEventServiceApi) {
        this.mqEventServiceApi = mqEventServiceApi;
    }

    @Value("${bloom-mq-handler-service.isEnabled}")
    private boolean isEnabled;

    @Async("MQThreadPoolTaskExecutor")
    public void sendMessageToMQ(String uuid, ManufacturerCompanyCodeEnum manufacturerCompanyCodeEnum) {

        if(!isEnabled || StringUtils.isBlank(uuid) || !listIntactSourceUnderWritingCompanies.contains(manufacturerCompanyCodeEnum)) {
            return;
        }

        try {
            mqEventServiceApi.convertAndSend(buildMqMessage(uuid));
            LOG.info(Logger.EVENT_SUCCESS, String.format("uuid = %s has been succesfully sent to bloom mq handler service", uuid));
        } catch (ApiException e) {
            LOG.error(Logger.EVENT_FAILURE, e.getResponseBody());
        } catch (Exception ex) {
            LOG.error(Logger.EVENT_FAILURE, "Exception occured when trying to call bloom mq service : ", ex );
        }

    }

    private MQMessage buildMqMessage(String uuid) {
        MQMessage mqMessage = new MQMessage();
        mqMessage.setMessageType("BLOOM");
        mqMessage.setQueueName("Q_BLOOM_AUTO");
        mqMessage.setUuid(uuid);

        return mqMessage;
    }
}
