package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.ss.base.BaseException;

import java.util.Map;

public interface IGenericPegaService {

    Object executeService(ManufacturingContext ctxt, Object object)	throws BaseException;

    Object executeService(Map<String, Object> pegaParams, Object object) throws BaseException;

}
