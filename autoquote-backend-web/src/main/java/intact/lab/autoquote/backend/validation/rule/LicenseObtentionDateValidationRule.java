package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.joda.time.LocalDate;
import org.joda.time.Period;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component
public class LicenseObtentionDateValidationRule {

	private static final Logger logger = ESAPI.getLogger(LicenseObtentionDateValidationRule.class);

	public void validate(final LocalDate licenseObtentionDate, final PartyDTO party, final String province, final String language, Errors errors, String fieldName) {
		if (licenseObtentionDate == null) {
			errors.rejectValue(fieldName, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(fieldName));
		} else if (!licenseObtentionDate.isBefore(LocalDate.now().plusDays(1))) {
			logger.info(Logger.EVENT_FAILURE, String.format("Validation error : date licensed obtained [%s] is not before tomorrow [%s]", licenseObtentionDate, LocalDate.now().plusDays(1)));
			errors.rejectValue(fieldName, BRulesExceptionEnum.Pattern.getErrorCode(), ValidationUtilities.bracket(fieldName));
		} else if (party != null && party.getDateOfBirth() != null) {
			Period p = new Period(party.getDateOfBirth().withDayOfMonth(1), licenseObtentionDate.withDayOfMonth(1));

			if (p.getYears() < 16 || p.getYears() > 99) {
				logger.info(Logger.EVENT_FAILURE, String.format("Validation error : years [%s] between date licensed obtained [%s] and date of birth [%s] is either less than 16 years or more than 99 years", p.getYears(), licenseObtentionDate, party.getDateOfBirth()));
				errors.rejectValue(fieldName, BRulesExceptionEnum.Pattern.getErrorCode(), ValidationUtilities.bracket(fieldName));
			}
		}
	}
}
