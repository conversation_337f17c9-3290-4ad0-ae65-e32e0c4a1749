package intact.lab.autoquote.backend.validation.rule;


import intact.lab.autoquote.backend.common.model.ValidValue;
import intact.lab.autoquote.backend.common.model.ValidValueBO;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;

public abstract class ValidationUtilities {
	/**
	 * Checks if is integer.
	 */
	public static boolean isInteger(String value_) {
		try {
			Integer.valueOf(value_);
			return true;
		} catch (NumberFormatException e) {
			return false;
		}
	}

	/**
	 * Checks if is exist value in list.
	 *
	 * @param listValueBO the list value bo
	 * @param valueBO     the value bo
	 * @return true, if is exist value in list
	 */
	public static boolean isExistValueInList(final List<? extends Object> listValueBO, final String valueBO) {
		if (listValueBO == null || listValueBO.isEmpty()) {
			return false;
		}
        for (Object next : listValueBO) {
            if ((next instanceof ValidValueBO) && (((ValidValueBO) next).getValue().equals(valueBO))) {
                return true;
            }
            if ((next instanceof ValidValue) && (((ValidValue) next).getValue().equals(valueBO))) {
                return true;
            }
        }
		return false;
	}

	public static String buildNestedPath(int index, String root) {
		String indexStr = Integer.toString(index);
		StringBuilder sb = new StringBuilder(root).append(bracket(indexStr));
		return sb.toString();
	}

	public static String bracket(String root) {
		StringBuilder sb = new StringBuilder("[").append(root).append("]");
		return sb.toString();
	}

	public static int getNbreYearsBetweenDate(String dateStart, String dateEnd) {
		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
		Date date1 = null;
		Date date2 = null;
		try {
			date1 = sdf.parse(dateStart);
			date2 = sdf.parse(dateEnd);
		} catch (ParseException e) {
		}
		GregorianCalendar gc1 = new GregorianCalendar();
		gc1.setTime(date1);
		GregorianCalendar gc2 = new GregorianCalendar();
		gc2.setTime(date2);
		int nbreYears = 0;
		gc1.add(GregorianCalendar.YEAR, 1);
		while (gc1.compareTo(gc2) <= 0) {
			nbreYears++;
			gc1.add(GregorianCalendar.YEAR, 1);
		}
		return nbreYears;
	}

}
