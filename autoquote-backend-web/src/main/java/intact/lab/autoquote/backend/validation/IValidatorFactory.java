package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.springframework.validation.Errors;

public interface IValidatorFactory {

	IQuoteDTOValidator getQuoteDTOValidator(ValidationContext context, Errors errors) throws AutoQuoteApiParametersException;

}
