package intact.lab.autoquote.backend.common.exception;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteVehicleException extends AutoQuoteException {

	public static final String EXEC_DEFAULT_ERROR = "exec.quickquote.vehicle.default.error";
	public static final String EXEC_RETRIEVE_MAKES_ERROR = "exec.quickquote.vehicle.retrieve.make.error";
	public static final String EXEC_RETRIEVE_MODELS_ERROR = "exec.quickquote.vehicle.retrieve.model.error";
	public static final String EXEC_RETRIEVE_YEARS_ERROR = "exec.quickquote.vehicle.retrieve.years.error";
	public static final String PARAM_YEAR_NULL = "param.quickquote.vehicle.year.null.error";
	public static final String PARAM_VEHICLE_MAKE_NULL = "param.quickquote.vehicle.make.null.error";
	public static final String PARAM_VEHICLE_YEAR_NULL = "param.quickquote.vehicle.year.null.error";
	public static final String PARAM_VEHICLE_YEAR_INVALID = "param.quickquote.vehicle.year.invalid.error";
	private static final long serialVersionUID = -6599740918351163035L;
	private static Map<String, String> messages = null;

	public AutoQuoteVehicleException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public AutoQuoteVehicleException(String exceptionCode, Throwable cause, Object... parameters) {
		this(AutoQuoteVehicleException.getMessage(exceptionCode, parameters), cause);
		this.setCode(exceptionCode);
	}

	public AutoQuoteVehicleException(String message, Throwable cause) {
		super(message, cause);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteVehicleException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteVehicleException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteVehicleException.getMessages() == null) {
			AutoQuoteVehicleException.initMessages();
		}

		String messageFormat = AutoQuoteVehicleException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteVehicleException.getMessages().get(AutoQuoteVehicleException.EXEC_DEFAULT_ERROR);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<String, String>();

		messages.put(
				AutoQuoteVehicleException.EXEC_DEFAULT_ERROR,
				"An unknown error occurred while using the configuration facade. This exception is not documented at this time.  The cause is {0}");
		messages.put(
				AutoQuoteVehicleException.EXEC_RETRIEVE_MAKES_ERROR,
				"An error occured while retrieving the makes of the vehicles using context {0}, language {1}, year {2}.  The cause is {0}");
		messages.put(
				AutoQuoteVehicleException.EXEC_RETRIEVE_MODELS_ERROR,
				"An error occured while retrieving the models of the vehicles using context {0}, language {1}, year {2}, make {3}.  The cause is {0}");
		messages.put(
				AutoQuoteVehicleException.PARAM_YEAR_NULL,
				"The vehicle year passed as a parameter is null. Please provide a valid year parameter");
		messages.put(
				AutoQuoteVehicleException.PARAM_VEHICLE_YEAR_NULL,
				"The vehicle year passed as a parameter is null. Please provide a valid year parameter");
		messages.put(
				AutoQuoteVehicleException.PARAM_VEHICLE_MAKE_NULL,
				"The vehicle year passed as a parameter is null. Please provide a valid year parameter");
		messages.put(
				AutoQuoteVehicleException.PARAM_YEAR_NULL,
				"The vehicle year passed as a parameter is null. Please provide a valid year parameter");
		messages.put(
				AutoQuoteVehicleException.PARAM_VEHICLE_YEAR_INVALID,
				"The vehicle year passed as a parameter is invalid. Please provide a valid vehicle year parameter");

		AutoQuoteVehicleException.setMessages(messages);
	}


}
