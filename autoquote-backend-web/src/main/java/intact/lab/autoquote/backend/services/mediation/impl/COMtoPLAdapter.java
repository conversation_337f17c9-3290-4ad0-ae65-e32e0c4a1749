/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2015, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ClaimLossDateCodeEnum;
import com.ing.canada.plp.domain.enums.CommunicationChannelCodeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DriverLicenseTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LicenseJurisdictionCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.NumberStabilityMonthsCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PriorGridLevelClientCodeEnum;
import com.ing.canada.plp.domain.enums.UBIDiscountCriteriaEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCategoryCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.driver.ComDriver;
import com.intact.com.driver.ComDriverClaim;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.state.ComState;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.com.vehicle.ComVehicle;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.services.mediation.ICOMtoPLAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * COM to PLP Adapter.
 *
 * <AUTHOR>
 */
@Component
public abstract class COMtoPLAdapter implements ICOMtoPLAdapter {

	/**
	 * The Constant logAdapter.
	 */
	private static final Logger logAdapter = ESAPI.getLogger(COMtoPLAdapter.class);

	/**
	 * The common business process.
	 */
	protected ICommonBusinessProcess commonBusinessProcess;

	/**
	 * The vehicle business process.
	 */
	@AutowiredLocal
	private IVehicleBusinessProcess vehicleBusinessProcess;

	/**
	 * The driver business process.
	 */
	@AutowiredLocal
	protected IDriverBusinessProcess driverBusinessProcess;

	/**
	 * The usage business process.
	 */
	@AutowiredLocal
	private IUsageBusinessProcess usageBusinessProcess;

	/**
	 * The mediator com plp.
	 */
	private final IMediatorComPlp mediatorComPlp;

	/**
	 * The mediator vehicle plp.
	 */
	protected IMediatorVehiclePlp mediatorVehiclePlp;

	/**
	 * The mediator driver plp.
	 */
	protected IMediatorDriverPlp mediatorDriverPlp;

	/**
	 * The mediator claim.
	 */
	protected IMediatorClaimPlp mediatorClaim;

	protected MediatorPaymentPlp mediatorPaymentPlp;

	/**
	 * The vehicle helper.
	 */
	private final IVehicleHelper vehicleHelper;

	/**
	 * The party helper.
	 */
	protected IPartyHelper partyHelper;

	/**
	 * The plp policy version helper.
	 */
	protected IPolicyVersionHelper plpPolicyVersionHelper;

	/**
	 * The plp insurance risk helper.
	 */
	protected IInsuranceRiskHelper plpInsuranceRiskHelper;

	/**
	 * The mediator advisor.
	 */
	protected IMediatorAdvisor mediatorAdvisor;

	/**
	 * The capi policy change date service.
	 */
	private final IDateManagerService capiPolicyChangeDateService;

	public COMtoPLAdapter(ICommonBusinessProcess commonBusinessProcess, IMediatorComPlp mediatorComPlp,
                          IMediatorVehiclePlp mediatorVehiclePlp, IMediatorDriverPlp mediatorDriverPlp, IMediatorClaimPlp mediatorClaim,
                          MediatorPaymentPlp mediatorPaymentPlp, IVehicleHelper vehicleHelper, IPartyHelper partyHelper,
                          IMediatorAdvisor mediatorAdvisor, IPolicyVersionHelper plpPolicyVersionHelper, IInsuranceRiskHelper plpInsuranceRiskHelper, IDateManagerService capiPolicyChangeDateService) {
		this.commonBusinessProcess = commonBusinessProcess;
		this.mediatorComPlp = mediatorComPlp;
		this.mediatorVehiclePlp = mediatorVehiclePlp;
		this.mediatorDriverPlp = mediatorDriverPlp;
		this.mediatorClaim = mediatorClaim;
		this.mediatorPaymentPlp = mediatorPaymentPlp;
		this.vehicleHelper = vehicleHelper;
		this.partyHelper = partyHelper;
		this.plpPolicyVersionHelper = plpPolicyVersionHelper;
		this.plpInsuranceRiskHelper = plpInsuranceRiskHelper;
		this.mediatorAdvisor = mediatorAdvisor;
        this.capiPolicyChangeDateService = capiPolicyChangeDateService;
    }

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean convertCOMtoPL(CommunicationObjectModel aCom, PolicyVersion aPolicyVersion) throws Exception {
		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		boolean updateSegments = false;
		PolicyVersion pvToConvert = aPolicyVersion;
		if (aCom.getComEvent() != null && ComEventEnum.SAVE_USAGE.equals(aCom.getComEvent().getEventCode())) {
			this.usageBusinessProcess.save(pvToConvert);
			pvToConvert = this.commonBusinessProcess.loadPolicyVersion(aCom.getPolicyVersionId());
		}

		// get the list of drivers from the policyVersion not found in the provided COM
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Remove Drivers.");
		}
		List<Party> driversToRemove = this.getDriversToRemove(pvToConvert, aCom);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// get the list of vehicles from the policyVersion not found in the provided COM
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Remove Vehicles.");
		}
		List<InsuranceRisk> vehiclesToRemove = getVehiclesToRemove(pvToConvert, aCom);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// must update the policyVersion with the provided COM before removing items.
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - PolicyVersion simple conversion.");
		}
		this.mediatorComPlp.convertCOMtoPLP(aCom, pvToConvert);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// -- set pl entities related to drivers
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Add Drivers.");
		}
		this.addComDriversToPolicyVersion(aCom, pvToConvert);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// -- set pl entities related to vehicles
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Add Vehicles.");
		}
		this.addComVehiclesToPolicyVersion(aCom, pvToConvert);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// -- complete drivers to vehicle assignation
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Complete Assignment.");
		}
		this.completeAssignment(pvToConvert);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		// -- set pl party relations
		this.completePartyRelations(aCom, pvToConvert);

		// -- detach old entities from policy version
		this.removeVehicles(pvToConvert, aCom.getContext(), vehiclesToRemove);
		updateSegments = this.removeDrivers(pvToConvert, aCom.getContext(), driversToRemove);

		// -- set payment information
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Payment Information.");
		}
		this.addComPaymentInformation(aCom, pvToConvert);
		if (logAdapter.isDebugEnabled()) {
			performanceWatch.stop();
		}

		if (aCom.getComPartner() != null) {
			if (logAdapter.isDebugEnabled()) {
				performanceWatch.start("COMtoPLAdapter.convertCOMtoPL - Convert Partner.");
			}
			this.mediatorAdvisor.convertCOMtoPLP(aCom.getComPartner(), aPolicyVersion);
			if (logAdapter.isDebugEnabled()) {
				performanceWatch.stop();
			}
		}

		if (logAdapter.isDebugEnabled()) {
			logAdapter.debug(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
		}

		return updateSegments;
	}

	/**
	 * Add payment information and payment business processes.
	 *
	 * @param aCom
	 * @param pvToConvert
	 */
	protected void addComPaymentInformation(final CommunicationObjectModel aCom, final PolicyVersion pvToConvert) throws Exception {
		// dirty fix because code is shared, AQ does not use this branch of code to initialize payment.
		if (!ComApplicationEnum.AUTOQUOTE.equals(aCom.getContext().getApplication())) {
			if (aCom.getComPayment() != null) {
				this.mediatorPaymentPlp.convertCOMToPLP(aCom, pvToConvert);
			}
		}
	}

	/**
	 * Internal method to loop through the list of COM drivers and map them to a PLP Party/DriverComplementInfo.
	 *
	 * @param com           {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 * @throws ParseException the parse exception
	 */
	protected void addComDriversToPolicyVersion(CommunicationObjectModel com, PolicyVersion policyVersion) throws ParseException {

		if (logAdapter.isInfoEnabled()) {
			logAdapter.info(Logger.EVENT_SUCCESS, new StringBuilder(">> converting COM to PLP Drivers for policyVersionId='")
					.append(policyVersion.getId()).append("'").toString());
		}

		for (ComDriver curComDriver : com.getDrivers()) {

			if (logAdapter.isInfoEnabled()) {
				logAdapter.info(Logger.EVENT_SUCCESS, new StringBuilder(">> converting ComDriver with id='")
						.append(curComDriver.getDriverId()).append("'").toString());
			}

			Party curParty = null;
			// get or create current matching Party
			if (LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(policyVersion.getInsurancePolicy().getLineOfBusiness())) {
				curParty = this.plpPolicyVersionHelper.retrievePartyFromWebMsgId(policyVersion, curComDriver.getWebMsgId());
			} else {
				curParty = this.plpPolicyVersionHelper.retrieveParty(policyVersion, curComDriver.getDriverId());
			}

			if (curParty == null) {
				curParty = this.partyHelper.initNewParty(policyVersion, curComDriver.getIsDriver());
				if (curParty != null) {
					curParty.setWebMsgId(curComDriver.getWebMsgId());
					//new BR for IRCA,  if party is company : partyType is OtherCompany
					if (org.apache.commons.lang3.StringUtils.isNotEmpty(curComDriver.getEntreprise())) {
						curParty.setPartyType(PartyTypeCodeEnum.OTHER_COMPANY);
					}
				}

			}
			curParty.setRoadBlockIndicator(Boolean.FALSE); // (re)set the roadblock indicator

			this.setDuoFlexEligibility(curComDriver, curParty, policyVersion);
			this.mediatorDriverPlp.convertCOMtoPLP(curComDriver, com.getContext().getLanguage(), curParty, com.getContext() != null ? com.getContext().getProvince() : null);
			if (this.partyHelper.isPolicyHolder(curParty)) {
				this.convertMarketingConsent(curParty, curComDriver);
			}

			// post-mediation
			this.postMediationParty(curComDriver, com.getContext(), policyVersion, curParty, com.getComEvent(), com.getState());
			this.postMediationCombinedPolicy(curComDriver, com.getState(), policyVersion, curParty);
		}
	}

	/**
	 * Province specific treatment to handle CombinedPolicy, CombinedPolicyScenario.
	 *
	 * @param aComDriver     the a com driver
	 * @param aComState      the a com state
	 * @param aPolicyVersion the a policy version
	 * @param aParty         the a party
	 */
	protected void postMediationCombinedPolicy(ComDriver aComDriver, ComState aComState, PolicyVersion aPolicyVersion, Party aParty) {
		// TODO Auto-generated method stub
	}

	/**
	 * manages the marketing consent and updates it with the data from the form.
	 *
	 * @param aParty       the concernend {@link Party}
	 * @param curComDriver {@link ComDriver}
	 */
	protected void convertMarketingConsent(final Party aParty, ComDriver curComDriver) {
		Boolean consentInd = curComDriver.getMarketingConsent();

		// Quickquote need to save a quote with no marketing consent.
		if (consentInd != null) {

			// RPTRE-8
			Consent marketingConsent = this.partyHelper.getConsent(aParty, ConsentTypeCodeEnum.MARKETING_CONSENT);

			// When the Marketing consent is provided (marketing consent indicator set to
			// Yes or No)
			if (marketingConsent == null) {
				this.createConsent(aParty, consentInd, ConsentTypeCodeEnum.MARKETING_CONSENT);
				// When the Credit consent is modified, the "date of credit consent"
				// will be modified to the date of
				// modification of the marketing consent.
			} else if (marketingConsent.getConsentIndicator() != null
					&& !marketingConsent.getConsentIndicator().equals(consentInd)) {
				marketingConsent.setConsentIndicator(consentInd);
				marketingConsent.setEffectiveDate(new Date());
			}
		}
	}

	/**
	 * Creates a new database consent object.
	 *
	 * @param aParty               the a party
	 * @param aConsentInd          the a consent ind
	 * @param aConsentTypeCodeEnum the a consent type code enum
	 */
	protected void createConsent(Party aParty, Boolean aConsentInd, ConsentTypeCodeEnum aConsentTypeCodeEnum) {
		Consent profilConsent;
		profilConsent = new Consent();
		profilConsent.setCommunicationChannelCode(CommunicationChannelCodeEnum.INTERNET);
		profilConsent.setConsentType(aConsentTypeCodeEnum);
		profilConsent.setConsentIndicator(aConsentInd);
		profilConsent.setEffectiveDate(new Date());
		aParty.addConsent(profilConsent);
	}

	/**
	 * Converts the form's claims and kind of loss data to Claim model objects.
	 *
	 * @param comDriver {@link ComDriver} to convert from.
	 * @param party     {@link Party} to convert to.
	 * @throws ParseException the parse exception
	 */
	protected void addComClaimsToParty(final ComDriver comDriver, Party party) throws ParseException {
		if (logAdapter.isDebugEnabled()) {
			logAdapter.debug(Logger.EVENT_SUCCESS, ">> start setFieldsComToPLforClaims...");
		}

		// Clear the existing claims, will recreate them from scratch
		Set<Claim> oldClaims = party.getClaims();
		for (Claim claim : oldClaims) {
			if (claim.getInsuranceRisk() != null) {
				claim.getInsuranceRisk().removeClaim(claim);
			}
			if (claim.getPolicyVersion() != null) {
				claim.getPolicyVersion().removeClaim(claim);
			}
		}

		party.clearClaims();

		// short order = 0; // replaced by ClaimSequence
		List<ComDriverClaim> claims = comDriver.getDriverClaims();
		if (claims != null) {
			for (ComDriverClaim curComClaim : claims) {
				// only create claims with a sequence.
				if (curComClaim.getClaimSequence() != null) {
					Claim claim = new Claim(); // init claim
					this.mediatorClaim.convertCOMtoPLP(curComClaim, claim);
					this.setLocalizedClaimKindOfLoss(claim, curComClaim.getClaimNatureAmount());
					this.setClaimDateOfLoss(claim, party.getPolicyVersion().getId());
					party.addClaim(claim);
				}
			}
		}
	}

	/**
	 * Internal method to loop through the list of COM vehicles and map them to a PLP Vehicle/InsuranceRisk.
	 *
	 * @param aCom           - {@link CommunicationObjectModel}
	 * @param aPolicyVersion - {@link PolicyVersion}
	 * @throws ParseException thrown on improperly formatted string for Dates
	 */
	protected void addComVehiclesToPolicyVersion(CommunicationObjectModel aCom, PolicyVersion aPolicyVersion)
			throws ParseException {
		if (logAdapter.isInfoEnabled()) {
			logAdapter.info(Logger.EVENT_SUCCESS, new StringBuilder(">> converting COM Vehicles to PLP vehicles for policyVersionId='")
					.append(aPolicyVersion.getId()).append("'").toString());
		}

		ComDriver principalDriver = null;
		// List of IDs of the drivers that were assigned as principal
		List<Integer> assignedPrincipalDrivers = new ArrayList<Integer>();
		for (ComVehicle veh : aCom.getVehicles()) {
			if (veh.getPrincipalDriver() != null) {
				principalDriver = MediatorUtils.getComDriver(veh.getPrincipalDriver(), aCom);
				if (principalDriver != null && !assignedPrincipalDrivers.contains(principalDriver.getDriverId())) {
					assignedPrincipalDrivers.add(principalDriver.getDriverId());
				}
			}
		}

		for (ComVehicle curComVehicle : aCom.getVehicles()) {
			if (logAdapter.isInfoEnabled()) {
				logAdapter.info(Logger.EVENT_SUCCESS, ">> converting vehicle with id=" + curComVehicle.getVehicleId());
			}

			// get or create current matching vehicle
			Vehicle curVehicle = null;
			if (curComVehicle.getWebMsgId() == null) {
				curVehicle = this.plpPolicyVersionHelper.retrieveVehicle(aPolicyVersion,
						curComVehicle.getVehicleId());
			} else {
				curVehicle = this.plpPolicyVersionHelper.retrieveVehicleByWebMsgId(aPolicyVersion,
						curComVehicle.getWebMsgId());
			}
			if (curVehicle == null) {
				curVehicle = this.vehicleHelper.initNewVehicle(aPolicyVersion);
			}

			this.mediatorVehiclePlp.convertCOMtoPLP(curComVehicle, aCom.getContext().getLanguage(), curVehicle);

			// post-mediation (complete persistence model)
			this.postMediationVehicle(curComVehicle, aCom.getContext(), aPolicyVersion, curVehicle);

			ComEvent event = aCom.getComEvent();
			if (event != null && event.getEventCode() != null && ComEventEnum.SAVE_BIND.equals(event.getEventCode())) {
				this.mediatorVehiclePlp.manageSecondRegisteredOwner(curComVehicle, curVehicle);
			} else {
				this.assignDriverstoVehicle(curComVehicle, curVehicle, aCom, assignedPrincipalDrivers);
			}
		}
	}

	/**
	 * Assign Drivers to a Vehicle.
	 *
	 * @param aComVehicle              base {@link ComVehicle}
	 * @param aVehicle                 {@link Vehicle} to assignDrivers to
	 * @param aCom                     base {@link CommunicationObjectModel}
	 * @param assignedPrincipalDrivers A list of ID's for the drivers that were assigned as principal
	 */
	protected void assignDriverstoVehicle(final ComVehicle aComVehicle, final Vehicle aVehicle,
										  final CommunicationObjectModel aCom, final List<Integer> assignedPrincipalDrivers) {
		if (MediatorUtils.driversSize(aCom) > 0) {
			this.mediatorVehiclePlp.assignDriversCOMtoPLP(aComVehicle, aVehicle, this.getDrivers(aCom.getDrivers()),
					assignedPrincipalDrivers, aCom.getContext().getLineOfBusiness());
			this.completeDriverAssignment(aVehicle);
		}
	}


	/**
	 * Gets the drivers.
	 * For IRCA the company is not a driver
	 *
	 * @param comDrivers the com drivers
	 * @return the drivers
	 */
	private List<ComDriver> getDrivers(List<ComDriver> comDrivers) {

		List<ComDriver> drivers = new ArrayList<ComDriver>();
		for (ComDriver comDriver : comDrivers) {
			if (org.apache.commons.lang3.StringUtils.isEmpty(comDriver.getEntreprise())) {
				drivers.add(comDriver);
			}
		}
		return drivers;
	}

	/**
	 * Localized post mediation for a party to prepare the plp model for persistence.<br>
	 * <p>
	 * NOTE: THIS METHOD MUST BE OVERRIDEN BY A LOCALIZED CHILD CLASS
	 *
	 * @param curComDriver  {@link ComDriver}
	 * @param comContext    {@link ComContext}
	 * @param policyVersion {@link PolicyVersion}
	 * @param party         {@link Party} to prepare
	 * @param comEvent      the com event
	 * @throws ParseException the parse exception
	 */
	protected void postMediationParty(ComDriver curComDriver, ComContext comContext, PolicyVersion policyVersion,
									  Party party, ComEvent comEvent, ComState state) throws ParseException {
		Assert.notNull(curComDriver, "ComDriver cannot be null.");
		Assert.notNull(comContext, "ComContext cannot be null.");
		Assert.notNull(policyVersion, "PolicyVersion cannot be null.");
		Assert.notNull(party, "Party cannot be null.");

		Locale locale = MediatorUtils.getLocale(comContext);
		ManufacturingContext mCtxt = MediatorUtils.convertContext(comContext);

		Map<String, String> insuredGroups = new HashMap<String, String>();
		if (this.partyHelper.isPolicyHolder(party)) {
			// Municipality info
			this.driverBusinessProcess.setModelForMunicipality(policyVersion, party, mCtxt, locale);

			// Affinity group
			this.driverBusinessProcess.setModelForAffinityGroup(policyVersion, party, mCtxt, insuredGroups);

			// set the dateOfLastMove based on NumberStabilityMonthsCode
			PolicyHolder policyHolder = this.plpPolicyVersionHelper.getPrincipalInsuredPolicyHolder(policyVersion);
			NumberStabilityMonthsCodeEnum stabilMonthCode = policyHolder != null ? policyHolder.getNumberStabilityMonthsCode() : null;
			if (stabilMonthCode != null) {
				Calendar dateOfLastMove = Calendar.getInstance();
				dateOfLastMove.setTime(this.capiPolicyChangeDateService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, policyVersion.getId()));
				dateOfLastMove.add(Calendar.MONTH, -NumberStabilityMonthsCodeEnum.getNumberStabilityMonths(stabilMonthCode));
				party.setDateOfLastMove(dateOfLastMove.getTime());
			}
		}

		DriverComplementInfo driverComplementInfo = party.getDriverComplementInfo();
		this.convertDriverLicense(curComDriver, driverComplementInfo);
		this.mediateLicenseJuridictionToPlp(party);
		this.localizedPostMediationForParty(comEvent, party, insuredGroups, mCtxt, locale);

		this.postMediateHolderAutoInsuranceIndicator(curComDriver, party);

		if (driverComplementInfo != null) {
			driverComplementInfo.setNumberOfSevereConvictions3Years(Short.valueOf("0"));
		}

		if (comEvent == null || !ComEventEnum.SAVE_BIND.equals(comEvent.getEventCode())) {
			// -- Set fields COM to PL for claims and kind of loss.
			this.addComClaimsToParty(curComDriver, party);
		}

		// ubi 2.0 eligibility mode (pl only)
		if (state != null
				&& state.isUseUBI20()
				&& driverComplementInfo != null
				&& !ComLineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(comContext.getLineOfBusiness())) {
			driverComplementInfo.setUbiDiscountCriteriaCode(UBIDiscountCriteriaEnum.MOBILE_DEVICE);
			setUbiProvider(driverComplementInfo);
			setUbiProgramVersionCode(driverComplementInfo); // ubi 4
		}
	}

	/**
	 * Set a localized value of {@link com.ing.canada.plp.domain.enums.UBIProviderCodeEnum}.
	 *
	 * @param driverComplementInfo {@link DriverComplementInfo}
	 */
	protected abstract void setUbiProvider(DriverComplementInfo driverComplementInfo);

	protected abstract void setUbiProgramVersionCode(DriverComplementInfo driverComplementInfo);

	/**
	 * Post mediate holderAutoInsuranceIndicator.
	 *
	 * @param curComDriver {@link ComDriver}
	 * @param party        {@link Party}
	 */
	protected void postMediateHolderAutoInsuranceIndicator(ComDriver curComDriver, Party party) {
		// do nothing here in common code.
	}

	/**
	 * Convert driver license to plp.
	 *
	 * @param comDriver {@link ComDriver}
	 * @param driver    {@link DriverComplementInfo}
	 */
	protected void convertDriverLicense(ComDriver comDriver, DriverComplementInfo driver) {
		if (comDriver != null && driver != null) {
			DriverLicenseTypeCodeEnum licenseTypeCode = DriverLicenseTypeCodeEnum.valueOfCode(comDriver
					.getDriverLicenseType());
			driver.setDriverLicenseType(licenseTypeCode);
		}
	}

	/**
	 * Mediate license juridiction to PLP.
	 *
	 * @param party the party
	 */
	private void mediateLicenseJuridictionToPlp(Party party) {
		if (party != null && party.getDriverComplementInfo() != null
				&& party.getDriverComplementInfo().getDriverLicenseType() != null) {
			DriverComplementInfo driver = party.getDriverComplementInfo();
			switch (driver.getDriverLicenseType()) {
				case NO_LICENSE:
					driver.setLicenseJurisdiction(LicenseJurisdictionCodeEnum.NOT_APPLICABLE);
					break;
				case VALID_LICENSE_ISSUED_IN_ANOTHER_PROVINCE_COUNTRY:
					driver.setLicenseJurisdiction(LicenseJurisdictionCodeEnum.OTHER);
					break;
				default:
					this.localizedDriversLicenseJurisdiction(party);
					break;
			}
		}
	}

	/**
	 * Specific localized post-mediation for a driver(party).<br>
	 * <p>
	 * ==> This method must be defined in an implementing child class <==
	 *
	 * @param event         {@link ComEvent}
	 * @param party         the {@link Party}
	 * @param insuredGroups the {@link Map} of insured groups
	 * @param context       the {@link ManufacturingContext}
	 * @param locale        the {@link Locale}
	 */
	protected abstract void localizedPostMediationForParty(ComEvent event, Party party,
														   Map<String, String> insuredGroups, ManufacturingContext context, Locale locale);

	/**
	 * Specific localized post-mediation for a vehicle.<br>
	 * <p>
	 * ==> This method must be defined in an implementing child class <==
	 *
	 * @param vehicle the {@link Vehicle}
	 * @param context the {@link ManufacturingContext}
	 * @param locale  the {@link Locale}
	 */
	protected abstract void localizedPostMediationForVehicle(Vehicle vehicle, ManufacturingContext context,
															 Locale locale);

	/**
	 * Localized driver's license jurisidiction.
	 *
	 * @param party {@link Party}
	 */
	protected abstract void localizedDriversLicenseJurisdiction(Party party);

	/**
	 * Localized set of a kind of loss for a claim.<br>
	 * <p>
	 * ==> This method must be defined in an implementing child class <==
	 *
	 * @param claim             {@link Claim}
	 * @param claimNatureAmount amount as {@link String}
	 */
	protected void setLocalizedClaimKindOfLoss(Claim claim, String claimNatureAmount) {
		this.driverBusinessProcess.setClaimKindOfLoss(claim, claimNatureAmount);
	}

	/**
	 * Sets the date of loss for the current claim based on the date of loss code.
	 *
	 * @param claim           the claim
	 * @param policyVersionId the policy version id
	 */
	protected void setClaimDateOfLoss(Claim claim, Long policyVersionId) {
		claim.setDateOfLoss(this.getDateOfLoss(policyVersionId, claim.getClaimLossDateCode()));
	}

	/**
	 * Populating the plp data elements that requires business rules, external service calls, etc. Anything that can't
	 * be done in a plain mediator must be performed here.
	 *
	 * @param curComVehicle current COM vehicle as {@link ComVehicle}
	 * @param comContext    current COM context as {@link ComContext}
	 * @param policyVersion the {@link PolicyVersion}
	 * @param vehicle       the matching plp {@link Vehicle}
	 */
	protected void postMediationVehicle(ComVehicle curComVehicle, ComContext comContext, PolicyVersion policyVersion,
										Vehicle vehicle) {
		Assert.notNull(curComVehicle, "ComVehicle cannot be null.");
		Assert.notNull(comContext, "ComContext cannot be null.");
		Assert.notNull(policyVersion, "PolicyVersion cannot be null.");
		Assert.notNull(vehicle, "Vehicle cannot be null.");

		if (logAdapter.isDebugEnabled()) {
			logAdapter.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> complete and validate vehicle={")
					.append("' insuranceRisk.sequence=").append(vehicle.getInsuranceRisk().getInsuranceRiskSequence())
					.append("', vehicle.id=").append(vehicle.getId()).append("', policyVersion.id=")
					.append(policyVersion.getId()).append("'}").toString());
		}
		Locale locale = MediatorUtils.getLocale(comContext);
		ManufacturingContext mCtxt = MediatorUtils.convertContext(comContext);

		this.localizedPostMediationForVehicle(vehicle, mCtxt, locale);

		InsuranceRisk vehIR = vehicle.getInsuranceRisk();
		String riskMake = this.plpInsuranceRiskHelper.getRiskMake(vehIR, Language.fromLocale(locale).getDbCode());
		String riskCode = this.plpInsuranceRiskHelper.getRiskCode(vehIR);
		String riskYear = this.plpInsuranceRiskHelper.getRiskYear(vehIR);

		// DE2/VD9 & DE3/VD10/BR112 & DE4
		// get the english model
		VehicleModel vehModelEn = this.vehicleBusinessProcess.getVehicleModelEnglishByModelCode(riskCode, riskMake,
				riskYear, mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness(), mCtxt.getProvince());

		// get the french model
		VehicleModel vehModelFR = this.vehicleBusinessProcess.getVehicleModelFrenchByModelCode(riskCode, riskMake,
				riskYear, mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness(), mCtxt.getProvince());

		this.setRepositoryEntryForVehicleModel(vehicle, riskMake, riskCode, Integer.valueOf(riskYear), vehModelEn,
				vehModelFR);

		// BR543- Change for Panorama project OPCF43 - Call in COMtoPLAdapterQC and COMtoPLAdapterON
		// if (BR0543_ConditionAtPurchaseTimeIsUsed.validate(vehicle)) {
		// vehicle.setConditionOfVehicleWhenBought(ConditionVehicleWhenBoughtCodeEnum.USED);
		// }

		this.vehicleBusinessProcess.setVehicleDetail(vehicle, locale, policyVersion.getId(),
				mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness());

		vehicle.setUsageToReviewByClientIndicator(Boolean.TRUE);

		vehIR.setRoadBlockIndicator(Boolean.FALSE); // (re)set the roadblock indicator.

		if (vehIR.getCreationDate() == null) { // set creation date if not already defined
			vehIR.setCreationDate(Calendar.getInstance().getTime());
		}

		vehicle.setVehicleUsage(this.retrieveUseOfVehicle(vehicle.getUseOfVehicleCategory()));

	}

	/**
	 * Sets the repository entry for a vehicle model.
	 *
	 * @param vehicle        the {@link Vehicle}
	 * @param make           The Vehicle make as {@link String}
	 * @param model          The Vehicle model as {@link String}
	 * @param year           The Vehicle year as {@link Integer}
	 * @param vehicleModelEn the {@link VehicleModel} english
	 * @param vehicleModelFr the {@link VehicleModel} french
	 */
	protected void setRepositoryEntryForVehicleModel(final Vehicle vehicle, final String make, final String model,
													 final Integer year, final VehicleModel vehicleModelEn, final VehicleModel vehicleModelFr) {

		// We retrieve VehicleDetailSpecificationRepositoryEntry and update it. If it doesn't exist it will be created.
		VehicleDetailSpecificationRepositoryEntry vdsre = vehicle.getVehicleDetailSpecificationRepositoryEntry();
		if (vdsre == null) {
			vdsre = new VehicleDetailSpecificationRepositoryEntry();
		}

		// We retrieve VehicleRepositoryEntry and update it. If it doesn't existe it will be created.
		VehicleRepositoryEntry aVehicleRepositoryEntry = vdsre.getVehicleRepositoryEntry();
		if (aVehicleRepositoryEntry == null) {
			aVehicleRepositoryEntry = new VehicleRepositoryEntry();
		}

		// DE3/VD10/BR112 & DE4 English
		aVehicleRepositoryEntry.setVehicleModelEnglish(vehicleModelEn.getModel());
		aVehicleRepositoryEntry.setVehicleMakeAndModelAbbreviationEnglish(vehicleModelEn.getMakeModelAbbreviation());
		aVehicleRepositoryEntry.setVehicleMakeEnglish(make);

		// DE3/VD10/BR112 & DE4 French
		aVehicleRepositoryEntry.setVehicleModelFrench(vehicleModelFr.getModel());
		aVehicleRepositoryEntry.setVehicleMakeAndModelAbbreviationFrench(vehicleModelFr.getMakeModelAbbreviation());
		aVehicleRepositoryEntry.setVehicleMakeFrench(make);

		// DE2/VD9 Year
		vdsre.setVehicleYear(year);

		// DE4 VehicleCode = ModelId
		aVehicleRepositoryEntry.setVehicleCode(model);

		// set VehicleRepositoryEntry
		vdsre.setVehicleRepositoryEntry(aVehicleRepositoryEntry);

		// set VehicleDetailSpecificationRepositoryEntry
		vehicle.setVehicleDetailSpecificationRepositoryEntry(vdsre);
	}

	/**
	 * Populates the plp data elements for a vehicle that requires business rules, external service calls, etc. Anything
	 * that can't be done in a plain mediator must be performed here.
	 *
	 * @param vehicle {@link Vehicle}
	 */
	protected void completeDriverAssignment(Vehicle vehicle) {
		// method can be localized as needed, nothing currently in common code
	}

	/**
	 * Populates the plp data elements for driver assignment assignment to vehicles.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 */
	protected void completeAssignment(PolicyVersion aPolicyVersion) {
		// completeModelPolicyHolder is common code used by other autoquotes
		// NOTE: performDBoperation must be set to allow removal of PolicyHolder when necessary
		this.usageBusinessProcess.completeModelPolicyHolder(aPolicyVersion, true);
		// this also udpates policy holders to set 2nd registered driver as a policyholder for CRM and BDI
	}

	/**
	 * Populates the plp data elements for driver assignment assignment to vehicles.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 */
	protected void completePartyRelations(CommunicationObjectModel com, PolicyVersion aPolicyVersion) {
		this.usageBusinessProcess.completeModelPartyRelatations(aPolicyVersion);
	}

	/**
	 * Remove vehicles(insurance risk) from a policy version.
	 *
	 * @param policyVersion    {@link PolicyVersion}
	 * @param comCtx           {@link ComContext}
	 * @param vehiclesToRemove {@link List} of {@link InsuranceRisk} to remove
	 * @throws Exception on any removal issues
	 */
	private void removeVehicles(final PolicyVersion policyVersion, final ComContext comCtx,
								List<InsuranceRisk> vehiclesToRemove) throws Exception {

		ApplicationEnum app = ApplicationEnum.valueOfCode(comCtx.getCompany().getCaseSensitiveName());
		for (InsuranceRisk ir : vehiclesToRemove) {
			this.vehicleBusinessProcess.detachVehicle(ir.getVehicle(), policyVersion, app);
		}
	}

	/**
	 * Remove drivers(parties) from the policy version.
	 *
	 * @param policyVersion   {@link PolicyVersion}
	 * @param comCtx          {@link ComContext}
	 * @param driversToRemove the {@link List} of {@link Party} to be removed
	 * @return true when at least one driver was set to be removed, indicates if segments need to be updated, false
	 * otherwise.
	 * @throws Exception on any removal issues
	 */
	private boolean removeDrivers(final PolicyVersion policyVersion, final ComContext comCtx,
								  List<Party> driversToRemove) throws Exception {
		boolean updateSegments = false;
		// -- remove drivers
		for (Party p : driversToRemove) {
			updateSegments = true;
			this.driverBusinessProcess.detachDriver(p.getDriverComplementInfo(), policyVersion,
					ApplicationEnum.valueOfCode(comCtx.getCompany().getCaseSensitiveName()));
		}
		// true when a driver is removed, indicating that the segments must be updated
		return updateSegments;
	}

	/**
	 * find the list of parties to detach in a policy version from an incoming COM. <br>
	 * i.e. find a party from it's id in a COM. If the id is not found in the COM assume it has been removed and
	 * therefore needs to be deleted from the policyVersion.
	 *
	 * @param pv  - {@link PolicyVersion}
	 * @param com - {@link CommunicationObjectModel}
	 * @return the list of drivers (parties) to delete from the policy version
	 * @throws IllegalArgumentException when trying to delete the policyVersion's policy holder
	 */
	private List<Party> getDriversToRemove(final PolicyVersion pv, final CommunicationObjectModel com)
			throws IllegalArgumentException {
		List<Party> toRemove = new ArrayList<Party>();
		List<ComDriver> comDrivers = com.getDrivers();

		for (final Party p : this.plpPolicyVersionHelper.getIndividualParties(pv)) {
			ComDriver found = null;
			if (!CollectionUtils.isEmpty(comDrivers)) {
				found = (ComDriver) CollectionUtils.find(comDrivers, new Predicate() {
					@Override
					public boolean evaluate(Object o) {
						return ((ComDriver) o).getDriverId() == p.getDriverComplementInfo().getDriverSequence();
					}
				});
			}
			if (found == null) { // when not found then set to remove
				toRemove.add(p);
			}
		}
		// check if trying to delete the policyHolder
		Party removingPolicyHolder = this.partyHelper.getPartyFromSequence(toRemove, 1);
		if (removingPolicyHolder != null) {
			throw new IllegalArgumentException(
					"User tried to delete the policyholder. (com could be out of synch with policyVersion)");
		}
		return toRemove;
	}

	/**
	 * find the list of insurance risks to detach in a policy version from an incoming COM. <br>
	 * i.e. find an insurance risk from it's id in a COM. If the id is not found in the COM assume it has been removed
	 * and therefore needs to be deleted from the policyVersion.
	 *
	 * @param pv  - {@link PolicyVersion}
	 * @param com - {@link CommunicationObjectModel}
	 * @return the list of drivers (parties) to delete from the policy version
	 */
	private static List<InsuranceRisk> getVehiclesToRemove(final PolicyVersion pv, final CommunicationObjectModel com) {
		List<InsuranceRisk> toRemove = new ArrayList<InsuranceRisk>();
		List<ComVehicle> comVehicles = com.getVehicles();

		for (final InsuranceRisk ir : pv.getInsuranceRisks()) {
			ComVehicle found = null;
			if (!CollectionUtils.isEmpty(comVehicles)) {
				found = (ComVehicle) CollectionUtils.find(comVehicles, new Predicate() {
					@Override
					public boolean evaluate(Object o) {
						return ((ComVehicle) o).getVehicleId() == ir.getInsuranceRiskSequence();
					}
				});
			}
			if (found == null) { // when not found then set to remove
				toRemove.add(ir);
			}
		}

		return toRemove;
	}

	/**
	 * Correctly get the date of loss using the reference date from dateManagerService Can be overridden in child
	 * classes.
	 *
	 * @param policyVersionId   the policy version id
	 * @param claimLossDateCode the ClaimLossDateCodeEnum
	 * @return the date of loss
	 */
	private Date getDateOfLoss(final Long policyVersionId, final ClaimLossDateCodeEnum claimLossDateCode) {
		Calendar dateOfLoss = Calendar.getInstance();
		dateOfLoss.setTime(this.capiPolicyChangeDateService
				.getReferenceDate(DateHelperEnum.FOR_CLAIMS, policyVersionId));

		switch (claimLossDateCode) {
			case LESS_THAN_ONE_YEAR:
				dateOfLoss.add(Calendar.MONTH, -6);
				break;
			case ONE_YEAR_BUT_LESS_THAN_TWO:
				dateOfLoss.add(Calendar.MONTH, -18);
				break;
			case TWO_YEARS_BUT_LESS_THAN_THREE:
				dateOfLoss.add(Calendar.MONTH, -30);
				break;
			case THREE_YEARS_BUT_LESS_THAN_FOUR:
				dateOfLoss.add(Calendar.MONTH, -42);
				break;
			case FOUR_YEARS_BUT_LESS_THAN_FIVE:
				dateOfLoss.add(Calendar.MONTH, -54);
				break;
			case FIVE_YEARS_BUT_LESS_THAN_SIX:
				dateOfLoss.add(Calendar.MONTH, -66);
				break;
			case SIX_YEARS_BUT_LESS_THAN_SEVEN:
				dateOfLoss.add(Calendar.MONTH, -78);
				break;
			case SEVEN_YEARS_OR_MORE:
				dateOfLoss.add(Calendar.MONTH, -90);
				break;
			default:
				break;
		}
		return dateOfLoss.getTime();
	}

	/**
	 * Retrieve the appropriate use of vehicle from the use of vehicle category.
	 *
	 * @param useOfVehicleCategory The {@link UseOfVehicleCategoryCodeEnum}.
	 * @return A {@link UseOfVehicleCodeEnum}.
	 */
	public UseOfVehicleCodeEnum retrieveUseOfVehicle(UseOfVehicleCategoryCodeEnum useOfVehicleCategory) {
		UseOfVehicleCodeEnum useOfVehicle = null;
		if (useOfVehicleCategory != null) {
			if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.BUSINESS_OCCASIONALLY.getCode())
					|| useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.BUSINESS.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.BUSINESS_OR_PLEASURE_AND_BUSINESS_EXCLUDING_DELIVERY;
			} else if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.COMMERCIAL.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.OTHER;
			} else if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.PLEASURE.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.PLEASURE;
			} else if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.DELIVERY.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.PLEASURE_AND_BUSINESS_INCLUDING_DELIVERY;
			} else if (useOfVehicleCategory.getCode().equals(UseOfVehicleCategoryCodeEnum.FARM_USE.getCode())) {
				useOfVehicle = UseOfVehicleCodeEnum.PLEASURE;
			}
		}
		return useOfVehicle;
	}

	/**
	 * Set DuoFlex eligibility.
	 *
	 * @param curComDriver  The {@link ComDriver}.
	 * @param curParty      The {@link Party}.
	 * @param policyVersion The {@link PolicyVersion}.
	 */
	protected void setDuoFlexEligibility(ComDriver curComDriver, Party curParty, PolicyVersion policyVersion) {
		// do nothing in common code.
	}

	/**
	 * Manage grid level.
	 *
	 * @param event the event
	 * @param party the party
	 */
	/*
	 * (non-javadoc) Manage Grid level. Currently only used in Alberta
	 */
	protected void manageGridLevel(ComEvent event, Party party) {
		if (event != null) {
			// initalize/reset grid level value on modifications of quote before the offer
			ComEventEnum code = event.getEventCode();
			if (code != null) {
				switch (code) {
					case ADD_DRIVER:
					case ADD_VEHICLE:
					case MODIFY_DRIVER:
					case MODIFY_VEHICLE:
					case SAVE_USAGE:
					case SAVE_OFFER:
						DriverComplementInfo dci = party.getDriverComplementInfo();
						PriorGridLevelClientCodeEnum value = dci.getPriorGridLevelClient();
						// This will force the grid level re-calculation
						if (value == PriorGridLevelClientCodeEnum.DONT_KNOW
								|| value == PriorGridLevelClientCodeEnum.NOT_INSURED) {
							dci.setGridLevelDate(null);
							dci.setGridLevelQty(null);
							dci.setPriorGridLevelQty(null);
						}
						break;
					default:
						// don't reset in any other
						break;
				}
			}
		}
	}
}
