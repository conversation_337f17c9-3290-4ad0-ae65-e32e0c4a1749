package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;


@Component
public class QuoteDTOValidatorQC extends AbstractQuoteDTOValidator {

	protected void validateParty(PartyDTO party, Errors errors, ValidationContext context) {
		super.validateParty(party, errors, this.context);
		this.partyDTOValidator.validateConsents(party.getConsents(), party.getPartyType(), errors);
	}

	protected void validateVehicle(VehicleDTO vehicle, Errors errors, ValidationContext context) {

		super.validateVehicle(vehicle, errors, context);
		final String province = context.getProvince();
		final String language = context.getLanguage();

		this.vehicleDTOValidator.validateKmPerYear(vehicle.getKmPerYear(), province, language, errors);

	}
}
