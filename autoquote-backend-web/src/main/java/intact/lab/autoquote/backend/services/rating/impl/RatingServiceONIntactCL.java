/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UBIStatusCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.claim.KindOfLoss;
import com.ing.canada.som.interfaces.intermediary.DistributorRepositoryEntry;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Distributor;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.registration.Conviction;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.ss.base.BaseException;
import com.intact.rating.IPremiumDerivationService;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import org.apache.commons.collections4.Predicate;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * The Class RatingService.
 */
@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class RatingServiceONIntactCL extends RatingService {
	
	private static final Logger log = ESAPI.getLogger(RatingServiceONIntactCL.class);

	/**
	 * The Constant EIS_INDIVIDUAL_AUTOMOBILE_PERSONAL for Policy Version Type.
	 */
	private static final String EIS_INDIVIDUAL_AUTOMOBILE_PERSONAL = "AUR";
	
	@Autowired
	protected IVehicleHelper vehicleHelper;

	@Autowired
	private RatingExternalizationEligibilityTranslator ratingExternalizationEligibilityTranslator;

	@Autowired
	private DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

	@Autowired
	private RatingConfig ratingConfig;

	@Autowired
	@Qualifier("subBrokersServiceOPC")
	private ISubBrokersService subBrokersService;

	@Autowired
	protected ExecuteServiceONIntact executeService;

	@Autowired
	@Qualifier("premiumDeviationServiceONIntact")
	private IPremiumDerivationService premiumDeviationService;

	@Autowired
	@Qualifier("application-id")
	protected String applicationId;

	@Override
	@Transactional
	public PolicyVersion rateOffer(PolicyVersion aPlPolicyVersion, boolean isAgent, boolean isUbiEnabled)
			throws AutoquoteRatingException {
		if (log.isDebugEnabled()) {
			log.debug(Logger.EVENT_SUCCESS, "Calling the rating Ontario");
		}

		StopWatch performanceWatch = new StopWatch();

		try {

			this.dataMediatorToPL.setReferenceOffer(this.getReferenceOfferType(aPlPolicyVersion.getInsurancePolicy().getApplicationMode().getCode()));
			this.dataMediatorToPL.setIsKeepNonEligibleCoverages(true);
			this.preprocessPL(aPlPolicyVersion);

			// Convert the PL PolicyVersion to the SOM format
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = dataMediatorToSOMServiceFactory
					.getService("dataMediatorToSOM").convertTo_SOM(aPlPolicyVersion);

			//this.logDataGraph("print the SOM graph right after conversion", somPolicyVersion);

			GregorianCalendar originalInceptionDate = somPolicyVersion.getTheInsurancePolicy()
					.getOriginalInceptionDate();
			GregorianCalendar clientOfBrokerSinceAuto = somPolicyVersion.getClientOfBrokerSinceAuto();
			String combinedPolicyCode = somPolicyVersion.getCombinedPolicyCode();
			this.preprocessSOM(somPolicyVersion, aPlPolicyVersion);

			// set the Policy expiry date
			this.setPolicyTermAndExpiry(aPlPolicyVersion, somPolicyVersion);

			// get the PL manufacturing context for calling PEGA services
			ManufacturingContext aCtxt = aPlPolicyVersion.getInsurancePolicy().getManufacturingContext();
			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, "this is the applicationId: " + this.applicationId);
				log.debug(Logger.EVENT_SUCCESS, "this is the manufacturing context: " + aCtxt.getProvince() + ", "
						+ aCtxt.getInsuranceBusiness() + ", " + aCtxt.getDistributionChannel());
			}

			/* Initialize the information to call PEGA services */
			Map<String, Object> ilParams = SSSUtils.getDelegateParameters(aCtxt, this.applicationId);
			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, String.format("Agreement Number %s", aPlPolicyVersion.getInsurancePolicy().getAgreementNumber()));
				log.debug(Logger.EVENT_SUCCESS, String.format("PolicyVersion Id %s", aPlPolicyVersion.getId()));
			}

			/* 1. Manage products call */
			performanceWatch.start("    >> manageProducts");
			somPolicyVersion = this.executeService.manageProducts(somPolicyVersion, ilParams);
			performanceWatch.stop();

			/* 2. Codify the offer */
			performanceWatch.start("    >> codePl");
			somPolicyVersion = this.executeService.codePlPolicyCar(aCtxt, somPolicyVersion);
			performanceWatch.stop();

			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, DataMediatorUtils.logCoveragesOnPolicyVersion(somPolicyVersion, true));
			}

			// 3. Rate the Offer

			// . 3.1 Copy the attribute value of the root InsuranceRisk in all
			// the branch InsuranceRisk of SOM
			this.copyInsuranceRiskToOffers(somPolicyVersion);

			performanceWatch.start("    >> ratePl");
			somPolicyVersion = this.executeService.ratePlPolicyCar(aCtxt, somPolicyVersion);
			performanceWatch.stop();

			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, DataMediatorUtils.logCoveragesOnPolicyVersion(somPolicyVersion, true));
			}

			// 4. Select offer type
			somPolicyVersion = this.selectOfferType(performanceWatch, aCtxt, somPolicyVersion);

			// Create all the branch InsuranceRisk of SOM in PL
			this.createRiskOffers(aPlPolicyVersion, somPolicyVersion);

			// Post process SOM
			this.postprocessSOM(somPolicyVersion, originalInceptionDate, clientOfBrokerSinceAuto, combinedPolicyCode);

			// Check if the ratagroup is empty. When no rate group populated for the current vehicle, the premium will be defaulted to higher values 9999 (PEGA).
			// If we will not check for empty rategroup, the database constraint will rise an exception when we convert to PL.
			// By checking for empty rategroup we will be able to replace the technical error with a HardRoadBlock (BR16593_MissingRateGroup)
			if(!somPolicyVersion.getTheInsuranceRisk(0).getTheVehicle().getVehicleRateGroupCollision().isEmpty()) {
				// Convert back to PL
				this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPlPolicyVersion, true);

				// Post process PL
				this.postprocessPL(aPlPolicyVersion);

				// Set selections and eligibility for discounts
				this.ratingExternalizationEligibilityTranslator.postCodePlAndOrRatePlPolicyProcessOnCustomOffer(aPlPolicyVersion);
			}

		} catch (BaseException e) {
			throw new AutoquoteRatingException("The rate offer sequence for PL PolicyVersion [" + aPlPolicyVersion
					+ "] has failed!", this.getRealException(e));
		} catch (Exception e) {
			throw new AutoquoteRatingException("The rate offer sequence for PL PolicyVersion [" + aPlPolicyVersion
					+ "] has failed!", e);
		}

		return aPlPolicyVersion;
	}

	/**
	 * @see RatingService#preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion,
	 *      PolicyVersion)
	 */
	@Override
	protected void preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion aSomPolicyVersion,
			PolicyVersion aPolicyVersion) {

		// create a distributor in som, based on the insurancePolicy sub BrokerAssignment
		SubBrokerAssignment subBrokerAssignment = aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment();
		if (subBrokerAssignment != null) {
			Distributor distributor = aSomPolicyVersion.createTheDistributor();
			DistributorRepositoryEntry distributorRepositoryEntry = distributor.createTheDistributorRepositoryEntry();
			distributorRepositoryEntry.setSubBrokerNumber(String.valueOf(subBrokerAssignment.getCifSubBrokerId()));
			distributorRepositoryEntry.setBrokerNumber(this.subBrokersService.getSubBrokerById(subBrokerAssignment.getCifSubBrokerId()).getSubBrokerNumber());
		}

		// BR2682 - modify vehicle usage for rating - to be reverted in postprocess
		if (aSomPolicyVersion.getTheInsuranceRisk() != null) {
			BR2682_Apply br2682 = new BR2682_Apply();
			for (com.ing.canada.som.interfaces.risk.InsuranceRisk risk : aSomPolicyVersion.getTheInsuranceRisk()) {
				if (br2682.evaluate(risk)) {
					risk.getTheVehicle().setUseOfVehicle(UseOfVehicleCodeEnum.PLEASURE.getCode());
				}
			}
		}

		// convert AU to AUP, AU is not recognized by PEGA
		if (PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL.equals(aPolicyVersion.getPolicyVersionType())) {
			aSomPolicyVersion.setPolicyVersionType(EIS_INDIVIDUAL_AUTOMOBILE_PERSONAL);
		}

		com.ing.canada.som.interfaces.physicalObject.Vehicle vehicle;
		for (com.ing.canada.som.interfaces.risk.InsuranceRisk ir : aSomPolicyVersion.getTheInsuranceRisk()) {
			ir.setNumberOfAdditionalInterests(0);
			vehicle = ir.getTheVehicle();
			vehicle.setRatingTableIdentification("P");
			vehicle.setActionTaken("A");
			vehicle.setPurchaseOdometerReading(vehicle.getOdometerReading());
		}

		// preprocess insuranceRisks
		List<com.ing.canada.som.interfaces.risk.InsuranceRisk> irList = aSomPolicyVersion.getTheInsuranceRisk();
		for (com.ing.canada.som.interfaces.risk.InsuranceRisk insuranceRisk : irList) {

			// Set atFaultInd and claimConsideredCd on KindOfLoss elements
			List<Driver> driverList = insuranceRisk.getTheDriver();
			for (Driver driver : driverList) {
				List<Claim> claimList = driver.getTheParty().getTheClaim();
				if (claimList != null) {
					for (Claim claim : claimList) {
						List<KindOfLoss> kolList = claim.getTheKindOfLoss();
						if (kolList != null) {
							for (KindOfLoss kindOfLoss : kolList) {
								kindOfLoss.setAtFaultInd(claim.getClaimAtFaultInd());
								kindOfLoss.setClaimConsideredCode(claim.getClaimAtFaultInd());
							}
						}
					}
				}
			}
			// set original rating dates for insuranceRisk
			GregorianCalendar transactionFirstRatingReferenceDate = new GregorianCalendar();
			transactionFirstRatingReferenceDate.setTime(aPolicyVersion.getReferenceDate().getTransactionFirstRatingReferenceDate());
			insuranceRisk.setOriginalDateRated(transactionFirstRatingReferenceDate);
			insuranceRisk.setRiskAttachedDate(aSomPolicyVersion.getPolicyInceptionDate());
		}

		// Must be AUC for Ontario Intact.
		aSomPolicyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_COMMERCIAL.getCode());
		aSomPolicyVersion.getTheEnvironmentContext().setApplicationIdentification("AQQK");

		// Must set ConvictionAccommodationInd of convictions to "N" for Pega to calculate counters correctly
		for (com.ing.canada.som.interfaces.risk.InsuranceRisk ir : aSomPolicyVersion.getTheInsuranceRisk()) {
			for (com.ing.canada.som.interfaces.partyRoleInRisk.Driver d : ir.getTheDriver()) {
				if ("P".equals(d.getTypeOfDriver()) && d.getTheParty().getTheDriverComplementInfo() != null) {
					List<Conviction> convictions = d.getTheParty().getTheDriverComplementInfo().getTheConviction();
					if (convictions != null) {
						for (Conviction c : convictions) {
							c.setConvictionAccommodationInd("N");
						}
					}
				}
			}
		}

	}
	
	@Override
	protected void preprocessPL(PolicyVersion aPlPolicyVersion) {
		// Set the current date for the first rating date
		if (aPlPolicyVersion.getRatingDate() == null) {
			Date ratingDate = new Date();
			ManufacturingContext ctxt = aPlPolicyVersion.getInsurancePolicy().getManufacturingContext();
			Date overridingRatingDate = this.ratingConfig.getOverridingRatingDate(ctxt.getProvince().getCode());
			if (overridingRatingDate != null) {
				ratingDate = overridingRatingDate;
			}

			aPlPolicyVersion.setRatingDate(ratingDate);
			aPlPolicyVersion.getReferenceDate().setTransactionFirstRatingReferenceDate(ratingDate);

			if (overridingRatingDate != null) {
				aPlPolicyVersion.getReferenceDate().setClaimReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setCreditScoreReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setDriverAgeReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setLicenseObtainedPeriodReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setTransactionCreationReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setVehicleAgeReferenceDate(overridingRatingDate);
			}

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(aPlPolicyVersion.getReferenceDate().getTransactionFirstRatingReferenceDate());

			calendar.add(Calendar.DAY_OF_MONTH, aPlPolicyVersion.getInsurancePolicy().getQuoteValidityPeriodInDays());
			aPlPolicyVersion.getInsurancePolicy().setQuotationValidityExpiryDate(calendar.getTime());
		}

		PolicyOfferRating por = new PolicyOfferRating(aPlPolicyVersion);

		for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {
			// Create the Premium InsuranceRiskOffer and RatingRiskOffers
			this.createRiskOffer(por, ir, OfferTypeCodeEnum.CUSTOM, Boolean.FALSE);
		}

		// In order for the driving record algorithm to function properly in CodePl, the continuouslyInsuredSinceDate
		// cannot be null
		Set<Party> parties = aPlPolicyVersion.getParties();
		for (Party party : parties) {
			// dci must be null checked, since some commercial parties (lienholder et al.) have no dci
			DriverComplementInfo dci = party.getDriverComplementInfo();
			if (dci != null && dci.getContinuouslyInsuredSinceDate() == null) {
				dci.setContinuouslyInsuredSinceDate(Calendar.getInstance().getTime());
			}
		}

		super.preprocessPLForAlbertaAndOntario(aPlPolicyVersion);
		List<Vehicle> vehicles = vehicleHelper.getVehicles(aPlPolicyVersion);
		for (Vehicle vehicle : vehicles) {
			if (vehicle.getVehicleDetailSpecificationRepositoryEntry() != null) {
				InsuranceRisk vehIR = vehicle.getInsuranceRisk();
				vehIR.setValuationAmount(vehicle.getVehicleDetailSpecificationRepositoryEntry().getRetailPriceWithGst());
			}
		}
	}

	@Override
	protected void postprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, GregorianCalendar originalInceptionDate, GregorianCalendar clientOfBrokerSinceAuto, String combinedPolicyCode) {
		com.ing.canada.som.interfaces.risk.InsuranceRisk ir = somPolicyVersion.getTheInsuranceRisk(0);

		// Since coverages added by the rate_pl service cannot return the native code, we need to map them here in
		// order to match the correct coverage in the coverage repository entry.
		for (Coverage somCoverage : ir.getTheCoverage()) {
			if (EndorsementCodeEnum.NBLD.getCodeIhv().equals(somCoverage.getCoverageCode()) && somCoverage.getCoverageCodeNative() == null) {
				somCoverage.setCoverageCodeNative(EndorsementCodeEnum.NBLD.getCode());
			}
		}

		// convert back AUP to AU
		if (EIS_INDIVIDUAL_AUTOMOBILE_PERSONAL.equals(somPolicyVersion.getPolicyVersionType())) {
			somPolicyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL.getCode());
		}

		// BR2682 - put back the business value once rating is done
		if (somPolicyVersion.getTheInsuranceRisk() != null) {
			BR2682_Revert br2682 = new BR2682_Revert();
			for (com.ing.canada.som.interfaces.risk.InsuranceRisk risk : somPolicyVersion.getTheInsuranceRisk()) {
				if (br2682.evaluate(risk)) {
					risk.getTheVehicle().setUseOfVehicle(UseOfVehicleCodeEnum.BUSINESS.getCode());
				}
			}
		}

		super.postprocessSOM(somPolicyVersion, originalInceptionDate, clientOfBrokerSinceAuto, combinedPolicyCode);
	}

	/**
	 * The Class BR2682_Apply.
	 */
	private class BR2682_Apply extends BR2682 {

		/**
		 * Instantiates a new b r2682_ apply.
		 */
		public BR2682_Apply() {
			super(UseOfVehicleCodeEnum.BUSINESS);
		}
	}

	/**
	 * The rule BR2682 - rate Business vehicles with less than 5000km as Pleasure
	 */
	private abstract class BR2682 implements Predicate {

		/**
		 * The Constant MIN_BUSINESS_KM.
		 */
		private static final int MIN_BUSINESS_KM = 5000;

		/**
		 * The vehicle code.
		 */
		private UseOfVehicleCodeEnum vehicleCode;

		/**
		 * Instantiates a new b r2682.
		 *
		 * @param aVehicleCode the vehicle code
		 */
		private BR2682(UseOfVehicleCodeEnum aVehicleCode) {
			this.vehicleCode = aVehicleCode;
		}

		/*
		 * (non-Javadoc)
		 *
		 * @see org.apache.commons.collections.Predicate#evaluate(java.lang.Object)
		 */
		@Override
		public boolean evaluate(Object o) {
			if (null != o && o instanceof com.ing.canada.som.interfaces.risk.InsuranceRisk) {
				com.ing.canada.som.interfaces.risk.InsuranceRisk risk = (com.ing.canada.som.interfaces.risk.InsuranceRisk) o;
				return risk.getTheVehicle() != null
						&& this.vehicleCode.getCode().equals(risk.getTheVehicle().getUseOfVehicle())
						&& risk.getTheVehicle().getAnnualBusinessKms() != null
						// bigger than 0 is important for the revert operation
						&& risk.getTheVehicle().getAnnualBusinessKms() > 0
						&& risk.getTheVehicle().getAnnualBusinessKms() < MIN_BUSINESS_KM;
			}
			return false;
		}
	}

	private class BR2682_Revert extends BR2682 {

		/**
		 * Instantiates a new b r2682_ revert.
		 */
		public BR2682_Revert() {
			super(UseOfVehicleCodeEnum.PLEASURE);
		}
	}

	@Override
	protected IPremiumDerivationService getPremiumDeviationService() {
		// NOOP
		return this.premiumDeviationService;
	}

	/**
	 * Gets the ammount that as no taxes on a policyOffer.
	 *
	 * @param currentPolicyOfferRating the current policy offer rating
	 * @return the untaxable amount
	 */
	@Override
	public double getUntaxableAmount(PolicyOfferRating currentPolicyOfferRating) {
		return 0;
	}

	@Override
	public void manageUbi(PolicyVersion aPlPolicyVersion, Map<Integer, Boolean> ubiSelectedByRiskSeq, StopWatch performanceWatch) throws AutoquoteRatingException {
		Collection<InsuranceRisk> insuranceRisks = aPlPolicyVersion.getInsuranceRisks();
		InsuranceRiskOffer selectedOffer = null;

		// Iterate on each Vehicle
		for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk : insuranceRisks) {
			selectedOffer = insuranceRisk.getSelectedInsuranceRiskOffer();

			boolean isNotSelected = selectedOffer == null
					|| insuranceRisk.getInsuranceRiskOfferSystemSelectedIndicator();
			OfferTypeCodeEnum type = isNotSelected ? null : selectedOffer.getOfferType();

			if (type != null) {
				BaseCoverage ubi = this.coverageHelper.getSelectedEndorsement(selectedOffer.getCoverageOffers(),
						EndorsementCodeEnum.UE05);
				Party principalDriver = this.partyHelper.getPrincipalDriver(selectedOffer.getInsuranceRisk());

				if (ubi != null && ubi.getCoverageSelectedIndicator()) {
					if (log.isDebugEnabled()) {
						log.debug(Logger.EVENT_SUCCESS, "manageUbi offerType-->" + selectedOffer.getOfferType().getCode()
								+ "isUbiSelected--> " + ubi.getCoverageSelectedIndicator());
					}
					principalDriver.getDriverComplementInfo().setUBIStatusCode(UBIStatusCodeEnum.ENROLLED);
				} else if (ubi == null) {
					principalDriver.getDriverComplementInfo().setUBIStatusCode(null);
				}
			}
		}
	}

}
