/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.impl.pegaservices.PegaBaseService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.delegate.services.GenericDelegate;
import intact.lab.autoquote.backend.services.rating.ISelectOfferType;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SelectOfferType extends PegaBaseService implements ISelectOfferType {

	private static final Logger log = ESAPI.getLogger(SelectOfferType.class);

	/** Service properties */
	private static final String COMPONENT_NAME = "UNDERWRITING";

	private static final String SERVICE_NAME = "SELECT_OFFER_TYPE";

	private static final String SERVICE_VERSION = "0.00";

	/**
	 * @see ISelectOfferType#selectOfferType(ManufacturingContext, PolicyVersion)
	 */
	@Override
	public PolicyVersion selectOfferType(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {

		Map<String, Object> pegaParams = this.getDelegateParameters(aCtxt);

		// call the service
		GenericDelegate service = new GenericDelegate(COMPONENT_NAME, SERVICE_NAME, SERVICE_VERSION, aPolicy, pegaParams);

		PolicyVersion outPolicy = (PolicyVersion) service.executeService();

		this.resumeLogging(outPolicy);

		if (log.isDebugEnabled()) {
			log.debug(Logger.EVENT_SUCCESS, COMPONENT_NAME + "." + SERVICE_NAME + "." + SERVICE_VERSION + "> TraceId=" + super.getTraceId(pegaParams));
		}

		return outPolicy;

	}

}
