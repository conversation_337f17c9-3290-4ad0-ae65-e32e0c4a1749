package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IDriverDTOValidator;
import intact.lab.autoquote.backend.validation.IPartyDTOValidator;
import intact.lab.autoquote.backend.validation.IPolicyHolderDTOValidator;
import intact.lab.autoquote.backend.validation.IQuoteDTOValidator;
import intact.lab.autoquote.backend.validation.IVehicleDTOValidator;
import intact.lab.autoquote.backend.validation.rule.ValidationUtilities;
import lombok.Getter;
import lombok.Setter;
import org.springframework.validation.Errors;

import java.util.List;

@Setter
public abstract class AbstractQuoteDTOValidator implements IQuoteDTOValidator {

	@Getter
    protected ValidationContext context;

	protected IPartyDTOValidator partyDTOValidator;
	protected IPolicyHolderDTOValidator policyHolderDTOValidator;
	protected IVehicleDTOValidator vehicleDTOValidator;
	protected IDriverDTOValidator driverDTOValidator;


	public void validateQuote(QuoteDTO quoteDTO, Errors errors) throws AutoQuoteApiParametersException {

		GeneralValidator.validateContext(this.context.getLanguage(), this.context.getProvince(), this.context.getCompany(), errors);

		if (null != quoteDTO) {
			//validateId(quoteDTO.getId(), errors);
			//validatePvId(quoteDTO.getPvId(), errors);
			this.validateParties(quoteDTO.getParties(), errors);
			this.validatePolicyHolders(quoteDTO.getPolicyHolders(), errors);
			this.validateVehicles(quoteDTO.getRisks(), errors);
			this.validateDrivers(quoteDTO.getDrivers(), errors);
			//validatePolicyDiscountCode(quoteDTO.getPolicyDiscountCode(), errors);
		} else {
			errors.reject("quoteDTO", BRulesExceptionEnum.NotBlank.getErrorCode());
		}
	}

	private void validateParties(List<PartyDTO> parties, Errors errors) {
		if (null != parties && !parties.isEmpty()) {
			int index = 0;
			for (PartyDTO party : parties) {
				String nestedPath = ValidationUtilities.buildNestedPath(index, "parties");
				if (null != party) {
					errors.pushNestedPath(nestedPath);
					this.validateParty(party, errors, this.context);
					errors.popNestedPath();
				} else {
					errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
				}
				++index;
			}
		} else {
			errors.rejectValue("parties", BRulesExceptionEnum.NotBlank.getErrorCode(), "[parties]");
		}
	}

	protected void validateParty(PartyDTO party, Errors errors, ValidationContext context) {
		this.partyDTOValidator.validate(party, errors, this.context);
	}

	private void validatePolicyHolders(List<PolicyHolderDTO> policyHolders, Errors errors) {
		if (null != policyHolders) {
			int index = 0;
			for (PolicyHolderDTO policyHolder : policyHolders) {
				String nestedPath = ValidationUtilities.buildNestedPath(index, "policyHolders");
				if (null != policyHolder) {
					errors.pushNestedPath(nestedPath);
					this.validatePolicyHolder(policyHolder, errors);
					errors.popNestedPath();
				} else {
					errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
				}
				++index;
			}
		} else {
			errors.rejectValue("policyHolders", BRulesExceptionEnum.NotBlank.getErrorCode(), "[policyHolders]");
		}
	}

	protected void validatePolicyHolder(PolicyHolderDTO policyHolder, Errors errors) {
		this.policyHolderDTOValidator.validate(policyHolder, errors);
	}

	private void validateVehicles(List<VehicleDTO> risks, Errors errors) {
		if (null != risks) {
			int index = 0;
			for (VehicleDTO vehicle : risks) {
				String nestedPath = ValidationUtilities.buildNestedPath(index, "risks");
				if (null != vehicle) {
					errors.pushNestedPath(nestedPath);
					this.validateVehicle(vehicle, errors, this.context);
					errors.popNestedPath();
				} else {
					errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
				}
				++index;
			}
		} else {
			errors.rejectValue("risks", BRulesExceptionEnum.NotBlank.getErrorCode(), "[risks]");
		}
	}

	protected void validateVehicle(VehicleDTO vehicle, Errors errors, ValidationContext context) {
		this.vehicleDTOValidator.validate(vehicle, errors, context);
	}

	private void validateDrivers(List<DriverDTO> drivers, Errors errors) {
		if (null != drivers) {
			int index = 0;
			for (DriverDTO driver : drivers) {
				String nestedPath = ValidationUtilities.buildNestedPath(index, "drivers");
				if (null != driver) {
					errors.pushNestedPath(nestedPath);
					this.validateDriver(driver, errors, this.context);
					errors.popNestedPath();
				} else {
					errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
				}
				++index;
			}
		} else {
			errors.rejectValue("drivers", BRulesExceptionEnum.NotBlank.getErrorCode(), "[drivers]");
		}
	}

	protected void validateDriver(DriverDTO driver, Errors errors, ValidationContext context) {
		this.driverDTOValidator.validate(driver, errors, context);
	}

}
