/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2014, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.address.impl;

import com.ing.canada.common.domain.Municipality;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import intact.lab.autoquote.backend.services.address.IAddressService;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import org.springframework.stereotype.Component;

/**
 * Address validation service
 *
 * <AUTHOR>
 * @since 2014
 */
@Component
public class AddressService implements IAddressService {

	protected IFormFieldValidatorService formFieldValidatorService;

	@AutowiredLocal
	protected IDriverBusinessProcess driverBusinessProcess;

	public AddressService(IFormFieldValidatorService formFieldValidatorService) {
		this.formFieldValidatorService = formFieldValidatorService;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.autoquote.integration.facade.service.IAddressService#isPolicyHolderInCurrentProvince(com.ing.canada
	 * .plp.domain.enums.ProvinceCodeEnum, com.ing.canada.common.domain.Municipality, java.lang.String,
	 * com.ing.canada.plp.domain.ManufacturingContext)
	 */
	@Override
	public boolean isPolicyHolderInCurrentProvince(final ProvinceCodeEnum currentProvince,
			final Municipality municipality, final String postalCode, final ManufacturingContext context) {
		ProvinceCodeEnum policyHolderProvince = this.driverBusinessProcess.getProvinceForMunicipality(municipality,
				postalCode, context);

		/*
		 * BR0456/MSG104 (this also covers BR0095) When the Postal code provided, doesn't correspond to the province
		 * selected by the user, then an error message should be displayed. (UI Validation)
		 */
		return policyHolderProvince.equals(currentProvince);
	}
}
