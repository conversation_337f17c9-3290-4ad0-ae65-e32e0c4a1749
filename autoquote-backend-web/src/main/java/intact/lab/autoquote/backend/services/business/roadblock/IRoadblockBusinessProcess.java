/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.roadblock;

import com.ing.canada.common.exception.RoadBlockException;

/**
 * <AUTHOR>
 * 
 */
public interface IRoadblockBusinessProcess {

	/**
	 * Persists roadblock state and create activities on the policy version describing the roadblock.
	 * 
	 */
	void persistRoadblock(Long policyVersionId, RoadBlockException aRoadblockException);

}
