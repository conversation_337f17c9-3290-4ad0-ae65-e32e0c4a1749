package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.offer.ComCoverageItem;
import com.intact.com.offer.ComItemChoice;
import intact.lab.autoquote.backend.common.dto.CoverageDTO;
import intact.lab.autoquote.backend.common.dto.CoverageValueDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component("comCoverageConverter")
public class COMCoverageConverter implements ICOMConverter<CoverageDTO, ComCoverageItem> {

	@Resource(name = "comCoverageValueConverter")
	private COMCoverageValueConverter comCoverageValueConverter;

	@Override
	public ComCoverageItem toCOM(CoverageDTO dto, ComCoverageItem initialCoverageItem) {
		throw new UnsupportedOperationException("Not supported.");
	}

	@Override
	public CoverageDTO toDTO(ComCoverageItem coverageItem) {
		CoverageDTO coverageDTO = new CoverageDTO();
		if (coverageItem != null) {
			coverageDTO.setCode(coverageItem.getCoverageInternalCode());
			coverageDTO.setIhvCode(coverageItem.getCoverageIhvCode());
			ComItemChoice[] coverageValues = coverageItem.getChoices() == null ? new ComItemChoice[0] : coverageItem.getChoices();
			for (ComItemChoice comItemChoice : coverageValues) {
				CoverageValueDTO coverageValueDTO = comCoverageValueConverter.toDTO(comItemChoice);
				coverageValueDTO.setSelectedInd(isSelected(coverageItem, coverageValueDTO));
				coverageDTO.getCoverageValues().add(coverageValueDTO);
			}
			coverageDTO.setSelectableInd(coverageItem.getSelectable());
			coverageDTO.setEligibleInd(coverageItem.getEligible());
		}

		return coverageDTO;
	}

	private boolean isSelected(ComCoverageItem coverageItem, CoverageValueDTO coverageValueDTO) {
		String selectedAmount = coverageItem.getCoverageValue();
		if (StringUtils.isNotEmpty(selectedAmount) && !"noop".equals(selectedAmount) && !"null".equals(selectedAmount)) {
			Integer amount = Integer.valueOf(selectedAmount);
			if (amount.equals(coverageValueDTO.getAmount())) {
				return true;
			}
		}
		return false;
	}
}
