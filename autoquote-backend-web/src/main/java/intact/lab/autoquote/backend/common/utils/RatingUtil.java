/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
* without the written permission of Intact Insurance
* Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
*/
package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.plp.domain.enums.ActionTakenCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.util.Set;

public final class RatingUtil {

	private static final Logger log = ESAPI.getLogger(RatingUtil.class);

	/**
	 * Don't call constructor - all methods are static
	 */
	private RatingUtil() {
		throw new AssertionError();
	}

	/**
	 * Checks if is plus pac eligible on pl.
	 *
	 * @param insuranceRiskOffer the insurance risk offer
	 * @return true, if is plus pac eligible on pl
	 */
	public static boolean isPlusPacEligibleOnPl(InsuranceRiskOffer insuranceRiskOffer) {
		Set<CoverageOffer> covSet = insuranceRiskOffer.getCoverageOffers();
		if (covSet != null) {
			for (CoverageOffer coverageOffer : covSet) {
				if (RatingUtil.isCoverageOfCodeOnPl(coverageOffer,
						EndorsementCodeEnum.PPC_SAV,
						EndorsementCodeEnum.ERAP_SAV,
						EndorsementCodeEnum.PPD_SAV,
						EndorsementCodeEnum.ERAY_SAV,
						EndorsementCodeEnum.EPCE_SAV,
						EndorsementCodeEnum.PPA_SAV,
						EndorsementCodeEnum.PPB_SAV,
						EndorsementCodeEnum.H1C_SAV, 
						EndorsementCodeEnum.H7C_SAV, 
						EndorsementCodeEnum.P1C_SAV, 
						EndorsementCodeEnum.P7C_SAV,
						EndorsementCodeEnum.H3Y_SAV)
						&& !ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Checks if is coverage of code on pl.
	 *
	 * @param coverageOffer the coverage offer
	 * @param coverages the coverages
	 * @return true, if is coverage of code on pl
	 */
	public static boolean isCoverageOfCodeOnPl(CoverageOffer coverageOffer, EndorsementCodeEnum... coverages) {
		if (coverages != null && coverages.length > 0) {
			for (EndorsementCodeEnum coverageCode : coverages) {
				if (coverageCode.getCode().equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Log coverage offer selection data.
	 *
	 * @param coverageOffer the coverage offer
	 */
	public static void logCoverageOfferSelectionData(CoverageOffer coverageOffer) {
		StringBuilder selectionData = new StringBuilder(256)
                .append("COVERAGE OFFER SELECTION DATA").append("CoverageCode: ")
				.append(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())
				.append(", ").append("Selectable: ").append(coverageOffer.getCoverageSelectableIndicator())
				.append(", ").append("Selected: ").append(coverageOffer.getCoverageSelectedIndicator())
				.append(", ").append("SelectedType: ").append(coverageOffer.getCoverageSelectedType())
				.append(", ").append("Eligible: ").append(coverageOffer.getCoverageEligibleIndicator())
				.append(", ").append("ActionTaken: ").append(coverageOffer.getActionTaken()).append(", ");
		if (log.isDebugEnabled()) {
			log.debug(Logger.EVENT_SUCCESS, selectionData.toString());
		}
	}

}
