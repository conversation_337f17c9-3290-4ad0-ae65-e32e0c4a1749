package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.broker.ComBrokerInfo;
import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import jakarta.xml.bind.DatatypeConverter;
import org.springframework.stereotype.Component;

@Component("comDistributorConverter")
public class COMDistributorConverter implements ICOMConverter<DistributorDTO, ComBrokerInfo> {

	@Override
	public DistributorDTO toDTO(ComBrokerInfo comBrokerInfo) {
		DistributorDTO distributor = new DistributorDTO();
		if (comBrokerInfo != null) {
			distributor.setNumber(comBrokerInfo.getSubBrokerNumber());
			distributor.setPhoneNumber(comBrokerInfo.getPhoneNumber());

			if (comBrokerInfo.getLogo() != null) {
				distributor.setLogoBase64(DatatypeConverter.printBase64Binary(comBrokerInfo.getLogo()));
			}
		}

		return distributor;
	}

	@Override
	public ComBrokerInfo toCOM(DistributorDTO distributorDTO, ComBrokerInfo initialcomBrokerInfo) {
		throw new UnsupportedOperationException("Not supported yet.");
	}

}
