package intact.lab.autoquote.backend.validation.whitelist;

import intact.lab.autoquote.backend.common.exception.SecurityUtilityException;
import intact.lab.autoquote.backend.common.exception.SecurityUtilityInvalidValueException;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import java.util.StringTokenizer;

public class WhiteList {

	/**
	 * The Constant logger.
	 */
	private static final Logger logger = ESAPI.getLogger(WhiteList.class);

	private static final String PROPERTY_FILE = "security";

	private static final String LANGUAGE_LIST = "whitelist.language";
	private static final String PROVINCE_LIST = "whitelist.province";

	private static List<String> allowedLanguageList;
	private static List<String> allowedProvinceList;

	private static synchronized void load() throws SecurityUtilityException {

		HashMap<String, List<String>> loaded = load(PROPERTY_FILE);

		allowedLanguageList = loaded.get(LANGUAGE_LIST);

		if (allowedLanguageList == null) {
			allowedLanguageList = new ArrayList<>();
			throw new SecurityUtilityException("Cannot find key '"
					+ LANGUAGE_LIST + "' in the property file");
		}

		allowedProvinceList = loaded.get(PROVINCE_LIST);

		if (allowedProvinceList == null) {
			allowedProvinceList = new ArrayList<>();
			throw new SecurityUtilityException("Cannot find key '"
					+ PROVINCE_LIST + "' in the property file");
		}

	}

	public static synchronized List<String> getAllowedLanguageList() {
		if (allowedLanguageList == null) {
			try {
				load();
			} catch (SecurityUtilityException e) {
				logger.error(Logger.SECURITY_FAILURE, new StringBuilder("WhiteList.load(): Unable to get AllowedLanguageList: ").append(e.getMessage()).toString());
				return new ArrayList<>();
			}
		}
		return allowedLanguageList;
	}

	public static List<String> getAllowedProvinceList() {
		if (allowedProvinceList == null) {
			try {
				load();
			} catch (SecurityUtilityException e) {
				logger.error(Logger.SECURITY_FAILURE, new StringBuilder("WhiteList.load(): Unable to get AllowedProvinceList: ").append(e.getMessage()).toString());
				return new ArrayList<>();
			}
		}

		return allowedProvinceList;
	}


	/**
	 * @param theValue
	 * @param possibleValues
	 * @return
	 * @throws SecurityUtilityException
	 */
	public static String isAllowedValue(String theValue, List<String> possibleValues)
			throws SecurityUtilityInvalidValueException {

		theValue = theValue.trim();

		Iterator<String> iterator = possibleValues.iterator();

		while (iterator.hasNext()) {
			String currentValue = iterator.next();

			if (currentValue != null && theValue.equalsIgnoreCase(currentValue)) {
				return currentValue;
			}

		}
		throw new SecurityUtilityInvalidValueException("Not Found");
	}

	private static HashMap<String, List<String>> load(String propertyFile) throws SecurityException {

		HashMap toReturn = new HashMap();

		try {
			ResourceBundle bundle = ResourceBundle.getBundle(propertyFile);
			Enumeration keyList = bundle.getKeys();

			while (keyList.hasMoreElements()) {
				String key = (String) keyList.nextElement();
				String value = bundle.getString(key);
				toReturn.put(key, generateList(value));
			}

			return toReturn;
		} catch (MissingResourceException missingResourceException) {
			throw new SecurityException("Cannot find the property file: (" + propertyFile + ")" + missingResourceException.getMessage());
		}
	}

	private static List<String> generateList(String line) {
		ArrayList<String> toReturn = new ArrayList();
		StringTokenizer tokenizer = new StringTokenizer(line, "|");

		while (tokenizer.hasMoreTokens()) {
			toReturn.add(tokenizer.nextToken().trim());
		}

		return toReturn;
	}
}
