package intact.lab.autoquote.backend.common.exception;

/**
 * The Enum QuickQuoteExceptionCodeEnum.
 */
public enum BRulesExceptionEnum {

	/**
	 * Global ::::::::::::::::::::::::::::::::::::::::
	 */
	ERR_AUTOQUOTE_FACADE("Internal_Server", "[Internal Server Error.]"),
	<PERSON><PERSON>("Pattern", "[Pattern]"),
	NotBlank("NotBlank", "[NotBlank]"),
	RoadBlock("RoadBlock", "[RoadBlock]"),

	ERR_VALUE_DOMAINE("ERR_VALUE_DOMAINE", "[ERR_VALUE_DOMAINE EMPTY]"),
	/**
	 * Driver ::::::::::::::::::::::::::::::::::::::::
	 */
	ERR_DRIVER_POSTALCODE_BR768("BR768", "[postalCode]"),
	ERR_DRIVER_POSTALCODE_BR2112("BR2112", "[postalCode]"),
	ERR_DRIVER_POSTALCODE_BR7916("BR7916", "[postalCode]"),
	ERR_DRIVER_POSTALCODE_BR456("BR456", "[postalCode]"),
	ERR_DRIVER_POSTALCODE_BR7946("BR7946", "[postalCode]"),
	ERR_DRIVER_DATEBIRTHY_BR7934("BR7934", "[dateOfbirh]"),
	ERR_DRIVER_TYPEOFLICENCE_BR5955("BR5955", "[dateFirstLicenceObtained]"),
	ERR_DRIVER_FIRSTNAME_BR7714("BR7714", "[firstName]"),
	ERR_DRIVER_LASTNAME_BR7714("BR7714", "[lastName]"),
	ERR_STEP3_PHONE_BR7751("BR7751", "[homePhoneNumber]"),
	ERR_STEP3_EMAIL_BR6193("BR6193", "[emailAddress]"),
	ERR_STEP3_EMAIL_BR2891("BR2891", "[emailAddress]"),
	ERR_STEP1_DISTANCE_TO_WORK_OR_SCHOOL_BR2630("BR2630", "[distanceWorkSchool]"),
	ERR_DRIVER_BR2565("BR2565", "[existingClient]"),
	ERR_DRIVER_YEARSWITHCURRENTINSURER_BR11363("BR11363", "[yearsWithCurrentInsurer]"),
	ERR_NO_DRIVER_PROFILE("BR6666", "[licenseNo]");

	/**
	 * The error code.
	 */
	private String errorCode = null;
	/**
	 * The error message.
	 */
	private String errorMessage = null;

	/**
	 * Instantiates a new quick quote exception code enum.
	 *
	 * @param aCode    the a code
	 * @param aMessage the a message
	 */
	private BRulesExceptionEnum(String aCode, String aMessage) {
		this.errorCode = aCode;
		this.errorMessage = aMessage;
	}

	/**
	 * Gets the error code.
	 *
	 * @return the errorCode
	 */
	public String getErrorCode() {
		return errorCode;
	}

	/**
	 * Gets the error message.
	 *
	 * @return the errorMessage
	 */
	public String getErrorMessage() {
		return errorMessage;
	}
}
