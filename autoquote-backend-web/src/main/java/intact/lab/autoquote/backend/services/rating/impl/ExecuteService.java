package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.impl.ilservices.base.GenericDelegate;
import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.som.interfaces.party.CreditScore;
import com.ing.canada.som.interfaces.partyRoleInAgreement.PolicyHolder;
import com.ing.canada.ss.base.BaseException;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

@Component
public class ExecuteService {

    private static final Logger LOG = ESAPI.getLogger(ExecuteService.class);

    private final GenericDelegate getCreditScore;

    public ExecuteService(@Qualifier("getCreditScore_5x00") GenericDelegate getCreditScore) {
        this.getCreditScore = getCreditScore;
    }

    /**
     * Gets the credit score.
     *
     * @param somParty the som party
     * @param ilParams the il params
     *
     * @throws BaseException the base exception
     */
    public void getCreditScore(com.ing.canada.som.interfaces.party.Party somParty, Map<String, Object> ilParams) throws BaseException {

        // we must create a new credit score object that will store the result
        CreditScore cs = somParty.addTheCreditScore();
        // For Autoquote we must set the UserId to SYSTEM - info from CEthier
        cs.setUserId("SYSTEM");
        // For Autoquote we must set the CreditScoreReportSearchType to S:Standard request - info from CEthier
        cs.setCreditScoreReportSearchType("S");
        // Usually the policy inception date is use - info from SOM doc
        List<PolicyHolder> policyHolders = somParty.getThePolicyHolder();
        if (policyHolders != null && policyHolders.size() > 0 && policyHolders.getFirst().getThePolicyVersion().getPolicyInceptionDate() != null) {
            cs.setReferenceDate(policyHolders.getFirst().getThePolicyVersion().getPolicyInceptionDate());
        } else {
            if (LOG.isDebugEnabled()) {
                LOG.debug(Logger.EVENT_SUCCESS, "No date in policyInceptionDate, using default date");
            }
            cs.setReferenceDate((GregorianCalendar) Calendar.getInstance());
        }

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Classic:GetCreditScore_5x00 (common)");
            LOG.debug(Logger.EVENT_SUCCESS, "Credit score reference ReferenceDate : " + cs.getReferenceDate());
        }

        this.getCreditScore.executeService(somParty, ilParams);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Credit score return information : ReferenceDate=" + somParty.getTheCreditScore().get(0).getReferenceDate() + " CreditScore="
                    + somParty.getTheCreditScore().getFirst().getCreditScore() + " CreditScoreServiceStatus=" + somParty.getTheCreditScore().get(0).getCreditScoreServiceStatus());

            String traceId = SSSUtils.getTraceId(ilParams);
            LOG.debug(Logger.EVENT_SUCCESS, ">" + traceId);
        }
    }
}
