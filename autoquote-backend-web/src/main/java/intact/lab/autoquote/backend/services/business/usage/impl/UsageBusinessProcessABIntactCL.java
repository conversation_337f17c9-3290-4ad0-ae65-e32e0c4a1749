/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.usage.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.plp.service.IRatingRiskService;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import intact.lab.autoquote.backend.common.exception.NoHitException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.common.IAssignClaimsService;
import intact.lab.autoquote.backend.services.business.common.IAssignDriverService;
import org.springframework.util.StopWatch;


/**
 * Business process specific to Intact Alberta CL. Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 */
@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class UsageBusinessProcessABIntactCL extends UsageBusinessProcess {

    public UsageBusinessProcessABIntactCL(IInsuranceRiskService insuranceRiskService, IPartyService partyService,
                                          IPartyRoleInRiskService partyRoleInRiskService, IRatingRiskService ratingRiskService,
                                          IPolicyVersionHelper policyVersionHelper, IPartyHelper partyHelper, IPolicyHolderService policyHolderService,
                                          DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory, IDataMediatorToPL dataMediatorToPL,
                                          IAssignDriverService assignDriverService, IAssignClaimsService assignClaimsService,
                                          IPolicyVersionService policyVersionService, IQuotationService quotationService) {
        super(insuranceRiskService, partyService, partyRoleInRiskService, ratingRiskService, policyVersionHelper, partyHelper,
                policyHolderService, dataMediatorToSOMServiceFactory, dataMediatorToPL, assignDriverService, assignClaimsService,
                policyVersionService, quotationService);
    }

    @Override
    public PolicyVersion callAssignmentServices(StopWatch performanceWatch, PolicyVersion aSOMPolicyVersion, ManufacturingContext context) {
        // Call drivers assignment service (used for future if occasional drivers present)
        performanceWatch.start("    >> assignDrivers");
        aSOMPolicyVersion = this.assignDriverService.assignDrivers(aSOMPolicyVersion, context);
        performanceWatch.stop();

        // Must call assign claims service in order to set the claims on the IR
        performanceWatch.start("    >> assignClaims");
        aSOMPolicyVersion = this.assignClaimsService.assignClaims(aSOMPolicyVersion, context);
        performanceWatch.stop();

        return aSOMPolicyVersion;
    }

    /**
     * Determines if a driver has D.R. 5 (dossier 5).
     * <p/>
     * A driver has D.R. 5 (dossier 5) if all of the following conditions are satisfied: - le conducteur est assuré à
     * titre de conducteur principal depuis plus de 5 ans - durant les 5 dernières années, un maximum de 2 sinistres
     * dont pas plus d'un sinistre responsable (ne pas tenir compte des réparations de pare-brise).
     *
     * @param partyRoleInRisk the partyRoleInRisk
     * @return true if the driver has D.R. 5 (dossier 5), false otherwise
     */
    @Override
    public boolean isDrivingRecord5Proof(final PartyRoleInRisk partyRoleInRisk) {
        return false;
    }

    @Override
    public void processTheNoHitException(final com.ing.canada.plp.domain.policyversion.PolicyVersion aPolicyVersion) throws NoHitException {
        if (ProvinceCodeEnum.ALBERTA.equals(aPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince())) {
            if (this.quotationService.isCustomerNoHit(aPolicyVersion)) {
                if (!this.quotationService.isNotRequired(aPolicyVersion)) {
                    throw new NoHitException("Cannot find credit score");
                }
            }

        }
    }
}
