/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.plp.domain.enums.PartyRelationCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRelation;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IPolicyVersionService;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.common.ICreditScoreBusinessProcess;
import intact.lab.autoquote.backend.services.rating.ICreditScoreService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum.GREY_POWER;
import static com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum.ING_WESTERN_REGION;

/**
 * Business process to call the credit score service.
 * 
 * <AUTHOR>
 */
@Component
public class CreditScoreBusinessProcess implements ICreditScoreBusinessProcess {

	private static final Logger LOG = ESAPI.getLogger(CreditScoreBusinessProcess.class);

	private static final String COMPANY = "company";
	private static final String FIRST_NAME = "firstName";
	private static final String LAST_NAME = "lastName";
	private static final String DATE_OF_BIRTH = "dateOfBirth";


	private final DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

	private final ICreditScoreService creditScoreService;

	private final IDataMediatorToPL dataMediatorToPL;

	protected IPolicyVersionService policyVersionService;

	public CreditScoreBusinessProcess(DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory,
									  ICreditScoreService creditScoreService,
									  IDataMediatorToPL dataMediatorToPL,
									  IPolicyVersionService policyVersionService) {
		this.dataMediatorToSOMServiceFactory = dataMediatorToSOMServiceFactory;
		this.creditScoreService = creditScoreService;
		this.dataMediatorToPL = dataMediatorToPL;
		this.policyVersionService = policyVersionService;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void callTheCreditScore(PolicyVersion aPolicyVersion) throws AutoquoteBusinessException {
		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}
		Map<String, Object> creditScoreInfo = null;
		// The service Party_GetCreditScore_2x00 requires only one CreditScore
		for (com.ing.canada.plp.domain.policyversion.PolicyHolder ph : aPolicyVersion.getPolicyHolders()) {
			if (PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED.equals(ph.getPolicyHolderType())) {
				Party party = ph.getParty();
				if (party.getPartyType() != null && party.getPartyType().equals(PartyTypeCodeEnum.OTHER_COMPANY)) {
					creditScoreInfo = setCreditScoreInfoFromPartyRelation(party);
				}
				for (com.ing.canada.plp.domain.party.CreditScore cs : party.getCreditScores()) {
					party.removeCreditScore(cs);
				}
			}
		}
		
		com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion;

		// Create the SOM Graph from PLP data
		performanceWatch.start("    >> dataMediatorToSOM.convertTo_SOM");
		somPolicyVersion = dataMediatorToSOMServiceFactory.getService("dataMediatorToSOM").convertTo_SOM(aPolicyVersion);
		performanceWatch.stop();

		try {
			// TODO (Redg) - Refactor this to remove the check to the company and create proper class hierarchy
			if (GREY_POWER.equals(aPolicyVersion.getInsurancePolicy().getManufacturerCompany()) || 
				ING_WESTERN_REGION.equals(aPolicyVersion.getInsurancePolicy().getManufacturerCompany())) {
				this.creditScoreService.callTheCreditScoreWithoutClientEligibility(somPolicyVersion, aPolicyVersion.getInsurancePolicy().getManufacturingContext(), performanceWatch);
			} else {
				this.creditScoreService.callTheCreditScore(somPolicyVersion, aPolicyVersion.getInsurancePolicy().getManufacturingContext(), performanceWatch);
			}
		} catch (Exception ex) {
			throw new AutoquoteBusinessException("An error occured while calling the credit score service.", ex);
		}

		// Retrieve the SOM Graph data in PLP
		performanceWatch.start("    >> dataMediatorToPL.convertTo_PL");
		undoInfoFromPartyRelation(creditScoreInfo);
		this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPolicyVersion, false);
		performanceWatch.stop();

		this.policyVersionService.persistCascadeAll(aPolicyVersion);

		LOG.trace(Logger.EVENT_SUCCESS, String.format("agreement nbr: %s\n%s", aPolicyVersion.getInsurancePolicy().getAgreementNumber(), performanceWatch.prettyPrint()));
	}
	
	private Map<String, Object> setCreditScoreInfoFromPartyRelation(Party party) {
		Map<String, Object> creditScoreInfo = new HashMap<>();
		Set<PartyRelation> partyRelations = party.getPartyRelationTo();
		PartyRelation partyRelation = null;
		if (partyRelations == null) return null;
		for (PartyRelation relation : partyRelations) {
			if (relation.getNatureTo().equals(PartyRelationCodeEnum.ICO.getCode())) {
				partyRelation = relation;
			}
		}
		if (partyRelation != null && partyRelation.getPartyTo() != null) {
			creditScoreInfo.put(COMPANY, party);
			creditScoreInfo.put(FIRST_NAME, party.getFirstName());
			creditScoreInfo.put(LAST_NAME, party.getLastName());
			creditScoreInfo.put(DATE_OF_BIRTH, party.getBirthDate());
			party.setFirstName(partyRelation.getPartyTo().getFirstName());
			party.setLastName(partyRelation.getPartyTo().getLastName());
			party.setBirthDate(partyRelation.getPartyTo().getBirthDate());
		}
		return creditScoreInfo;
	}
	
	private void undoInfoFromPartyRelation(Map<String, Object> creditScoreInfo) {
		if (creditScoreInfo == null) return;
		Party company = (Party) creditScoreInfo.get(COMPANY);
		company.setFirstName((String)creditScoreInfo.get(FIRST_NAME));
		company.setLastName((String)creditScoreInfo.get(LAST_NAME));
		company.setBirthDate((Date)creditScoreInfo.get(DATE_OF_BIRTH));
	}
}
