package intact.lab.autoquote.backend.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonTypeName("driver")
public class DriverDTO extends PartyDTO {

    private String principalInsuredSinceCode; // SV
    private Integer numberOfMinorInfractions; // SV
    private Integer numberOfMajorInfractions; // SV
    private Integer numberOfRelevantClaims; // SV
    private Boolean interestedByUbiInd; // SV
    private Integer partyId;
    private String licenseNbr;
    private String driverLicenseType;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate licenseObtentionDate;

    private List<ClaimDTO> claims = new ArrayList<>();
    private List<ConvictionDTO> convictions = new ArrayList<>();
}
