package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.driver.ComDriverConviction;
import intact.lab.autoquote.backend.common.dto.ConvictionDTO;
import intact.lab.autoquote.backend.common.enums.ConvictionCodeEnum;
import intact.lab.autoquote.backend.common.enums.ConvictionTypeEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.springframework.stereotype.Component;

@Component("comConvictionConverter")
public class COMConvictionConverter implements ICOMConverter<ConvictionDTO, ComDriverConviction> {

	@Override
	public ConvictionDTO toDTO(ComDriverConviction comConviction) {
		ConvictionDTO convictionDTO = new ConvictionDTO();
		if (comConviction != null) {
			convictionDTO.setConvictionSequence(comConviction.getConvictionSequence().toString());
			convictionDTO.setType(ConvictionTypeEnum.valueOfCode(comConviction.getConvictionType()));
			convictionDTO.setNbYearsOld(comConviction.getConvictionYear());
		}
		return convictionDTO;
	}

	@Override
	public ComDriverConviction toCOM(ConvictionDTO dto, ComDriverConviction initialComDriverConviction) {
		ComDriverConviction comDriverConviction = new ComDriverConviction();
		if (dto != null) {
			comDriverConviction.setConvictionType(dto.getType().getCode());
			comDriverConviction.setConvictionCode(this.getConvictionCode(dto.getType()));
			comDriverConviction.setConvictionYear(dto.getNbYearsOld());
		}
		return comDriverConviction;
	}


	/*
	 * The UI does not send the code, we rely on the type conviction to set this value code
	 */
	private String getConvictionCode(ConvictionTypeEnum convictionType) {
		if (convictionType != null) {
			switch (convictionType) {
				case MAJOR:
					return ConvictionCodeEnum.MAJOR_CONVICTION.getCode();
				case MINOR:
					return ConvictionCodeEnum.MINOR_CONVICTION.getCode();
				case DISTRACTED:
					return ConvictionCodeEnum.DISTRACTED_DRIVING.getCode();
			}
		}
		return null;
	}

}
