package intact.lab.autoquote.backend.common.exception;

import java.io.Serial;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteOfferException extends AutoQuoteException {

	public static final String EXEC_DEFAULT_ERROR = "exec.quickquote.default.error";
	public static final String EXEC_GET_PRICE_ERROR = "exec.quickquote.offer.get.price.error";

	@Serial
	private static final long serialVersionUID = 2191829261433969391L;
	private static Map<String, String> messages = null;

	public AutoQuoteOfferException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public AutoQuoteOfferException(String exceptionCode, Throwable cause, Object... parameters) {
		this(AutoQuoteOfferException.getMessage(exceptionCode, parameters), cause);
		this.setCode(exceptionCode);
	}

	public AutoQuoteOfferException(String message, Throwable cause) {
		super(message, cause);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteOfferException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteOfferException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteOfferException.getMessages() == null) {
			AutoQuoteOfferException.initMessages();
		}

		String messageFormat = AutoQuoteOfferException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteOfferException.getMessages().get(AutoQuoteOfferException.EXEC_DEFAULT_ERROR);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<>();

		messages.put(
				AutoQuoteOfferException.EXEC_DEFAULT_ERROR,
				"An unknown error occurred while using QuickQuoteOffer facade. This exception is not documented at this time.  The cause is {0}");
		messages.put(
				AutoQuoteOfferException.EXEC_GET_PRICE_ERROR,
				"An error occured while trying to get price - The cause is {0} Info is: AgreementNumber {1}, apiKey {2}, province {3}, language {4}.");

		AutoQuoteOfferException.setMessages(messages);
	}


}
