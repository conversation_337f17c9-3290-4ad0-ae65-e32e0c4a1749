package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.common.util.localizedcontext.ApplicationContextManager;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.LocalizedContextUtils;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import intact.lab.autoquote.backend.common.dto.ContextDTO;
import intact.lab.autoquote.backend.common.enums.LanguageEnum;
import intact.lab.autoquote.backend.common.enums.ProvinceEnum;
import intact.lab.autoquote.backend.common.enums.WebSiteOriginEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.model.BusinessContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

public class ContextUtil {

    private static final String ENGLISH_LANG = "EN";

    public static ComContext loadInitialComContext(final String postalCode, final String provinceCode, final String language, final String subBroker, WebSiteOriginEnum origin, final String sessionNumber) {
        ComContext ctx = new ComContext();

        ctx.setCompany(ComCompanyEnum.INTACT);
        ctx.setProvince(getProvince(provinceCode));
        ctx.setLanguage(ENGLISH_LANG.equalsIgnoreCase(language) ? ComLanguageCodeEnum.ENGLISH : ComLanguageCodeEnum.FRENCH);
        ctx.setApplication(ComApplicationEnum.QUICKQUOTE);
        ctx.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);
        ctx.setLineOfBusiness(ComLineOfBusinessCodeEnum.COMMERCIAL_LINES);
        ctx.setMobile(false);
        ctx.setSessionNumber(sessionNumber);

        //-- Set the BrokerSearchInfo
        ComBrokerInfo comBrokerInfo = new ComBrokerInfo();
        comBrokerInfo.setPostalCode(postalCode);

        comBrokerInfo.setSubBrokerNumber(subBroker);
        comBrokerInfo.setBrokerWebsiteOrigin((WebSiteOriginEnum.WEBBK.equals(origin) ? ComBrokerWebSiteOriginEnum.BROKER : ComBrokerWebSiteOriginEnum.INTACT));

        ctx.setBrokerInfo(comBrokerInfo);
        return ctx;
    }

    public static ContextDTO buildUserContext(String language, String province, String apiKey, String subBroker, String organizationSource) throws AutoQuoteException {
        ContextDTO userContext;
        try {
            LanguageEnum newLanguage = getLanguage(language);
            if (newLanguage == null) {
                throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_LANGUAGE_INVALID_ERROR);
            }
            ProvinceEnum provinceEnum = ProvinceEnum.fromCode(province);
            if (provinceEnum == null) {
                throw new AutoQuoteApiParametersException(AutoQuoteApiParametersException.PARAM_PROVINCE_INVALID_ERROR);
            }
            BusinessContext businessContext = BusinessContextUtil.create(provinceEnum);
            businessContext.setProvince(provinceEnum.getCode());
            userContext = new ContextDTO();
            userContext.setLanguage(newLanguage);
            userContext.setBusinessContext(businessContext);
            userContext.setLocale(new Locale(newLanguage.getIsoCode(), businessContext.getCountry(), businessContext.getProvince()));
            userContext.setSubBroker(subBroker);
            userContext.setOrigin(StringUtils.isNotEmpty(subBroker) ? WebSiteOriginEnum.WEBBK : WebSiteOriginEnum.INTACT);
            userContext.setOrganizationSource(organizationSource);
        } catch (AutoQuoteApiParametersException e) {
            throw e;
        } catch (Exception e) {
            throw new AutoQuoteException(AutoQuoteException.EXEC_BUILD_USER_ERROR, e, language, province, apiKey);
        }
        return userContext;
    }

    /**
     * Retrieves the localized facadeComponent matching the company/province
     *
     * @param aComContext {@link ComContext}
     * @param clazz {@link Class}
     * @return The facade
     * @throws AutoQuoteException
     */
    public static Object getInstance(Class clazz, ComContext aComContext) throws AutoQuoteException {
        validateInitialisation(aComContext); // ensure context has been initialized

        return LocalizedContextUtils.getLocalizedBean(ApplicationContextManager.getContext(), clazz,
                ProvinceCodeEnum.valueOfCode(aComContext.getProvince().getCode()),
                ApplicationEnum.valueOfCode(aComContext.getCompany().getCaseSensitiveName()),
                LineOfBusinessCodeEnum.valueOfCode(LineOfBusinessCodeEnum.COMMERCIAL_LINES.getCode()));
    }

    private static ComProvinceCodeEnum getProvince(final String provinceCode) {
        return switch (provinceCode.toUpperCase()) {
            case "AB" -> ComProvinceCodeEnum.ALBERTA;
            case "ON" -> ComProvinceCodeEnum.ONTARIO;
            case "QC" -> ComProvinceCodeEnum.QUEBEC;
            default -> null;
        };
    }

    private static LanguageEnum getLanguage(String language) {

        return switch (language.toUpperCase()) {
            case "FR" -> LanguageEnum.FRENCH;
            case "EN" -> LanguageEnum.ENGLISH;
            default -> null;
        };
    }

    /**
     * Utility method to verify that a COM context has been properly initialized. <br>
     * Throws an AutoquoteFacadeException exception when a mandatory field of the context is not provided.
     *
     * @param aComContext - {@link ComContext} to validate
     * @throws AutoQuoteException
     */
    public static void validateInitialisation(ComContext aComContext) throws AutoQuoteException {
        if (aComContext == null) {
            throw new AutoQuoteException("ComContext is not initalized.");
        }
        if (aComContext.getProvince() == null) {
            throw new AutoQuoteException("Province of ComContext is not initalized.");
        }
        if (aComContext.getLanguage() == null) {
            throw new AutoQuoteException("Language of ComContext is not initalized.");
        }
        if (aComContext.getDistributionChannel() == null) {
            throw new AutoQuoteException("DistributionChannel of ComContext is not initalized.");
        }
        if (aComContext.getApplication() == null) {
            throw new AutoQuoteException("Application of ComContext is not initalized.");
        }
        if (aComContext.getCompany() == null) {
            throw new AutoQuoteException("Company of ComContext is not initalized.");
        }
    }
}
