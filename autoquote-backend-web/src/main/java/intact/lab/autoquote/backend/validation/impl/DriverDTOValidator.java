package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.validation.IDriverDTOValidator;
import intact.lab.autoquote.backend.validation.rule.DriverLicenseTypeValidationRule;
import intact.lab.autoquote.backend.validation.rule.LicenseObtentionDateValidationRule;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("DriverDTOValidator")
public class DriverDTOValidator implements IDriverDTOValidator {

	private final DriverLicenseTypeValidationRule driverLicenseTypeValidationRule;
	private final LicenseObtentionDateValidationRule licenseObtentionDateValidationRule;

	public DriverDTOValidator(DriverLicenseTypeValidationRule driverLicenseTypeValidationRule,
			LicenseObtentionDateValidationRule licenseObtentionDateValidationRule) {
		this.driverLicenseTypeValidationRule = driverLicenseTypeValidationRule;
		this.licenseObtentionDateValidationRule = licenseObtentionDateValidationRule;
	}

	@Override
	public void validate(DriverDTO driverDTO, Errors errors, ValidationContext context) {
		this.validateId(driverDTO.getId(), errors);
	}

	public void validateLicenseObtentionDate(LocalDate licenseObtentionDate, Errors errors, ValidationContext context) {

		PartyDTO party = this.getPartyByType(PartyTypeEnum.PERSON, context.getQuote());
		this.licenseObtentionDateValidationRule.validate(licenseObtentionDate, party, context.getProvince(), context.getLanguage(), errors, "licenseObtentionDate");

	}

	public void validateDriverLicenseType(String driverLicenseType, Errors errors, ValidationContext context) {

		this.driverLicenseTypeValidationRule.validate(driverLicenseType, context.getProvince(), context.getLanguage(), errors, "driverLicenseType");

	}

	private void validateId(Integer id, Errors errors) {
		if (null == id) {
			//errors.rejectValue("id", BRulesExceptionEnum.NotBlank.getErrorCode(), "[id]");
		}
	}


	private PartyDTO getPartyByType(PartyTypeEnum type, QuoteDTO quote) {
		if (quote != null && quote.getParties() != null) {
			for (PartyDTO party : quote.getParties()) {
				if (type.equals(party.getPartyType())) {
					return party;
				}
			}
		}
		return null;
	}

}
