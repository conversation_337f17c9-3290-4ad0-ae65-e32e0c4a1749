/*
 * Important notice: This software is the sole property of Intact Insurance Inc.
 * and cannot be distributed and/or copied without the written permission of Intact Insurance Inc.
 *
 * Copyright (c) 2009, Intact Insurance Inc., All rights reserved.
 */
package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;

/**
 * The purpose of this service is to select the system offer type
 * 
 */
public interface ISelectOfferType {

	/**
	 * Select the system offer type
	 * 
	 * @param aPolicy SOM Policy
	 * @return Updated SOM policy
	 * @throws BaseException If an error occurs within PEGA execution
	 */
	PolicyVersion selectOfferType(ManufacturingContext ctxt, PolicyVersion aPolicy) throws BaseException;
}
