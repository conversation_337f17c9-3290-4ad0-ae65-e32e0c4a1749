/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.impl.pegaservices.PegaBaseService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.delegate.services.GenericDelegate;
import intact.lab.autoquote.backend.services.rating.IDetermineClientEligibilityLevel;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DetermineClientEligibilityLevel extends PegaBaseService implements IDetermineClientEligibilityLevel {

	private static final Logger LOG = ESAPI.getLogger(DetermineClientEligibilityLevel.class);

	/** Service properties */
	private static final String COMPONENT_NAME = "PARTY";

	private static final String SERVICE_NAME = "DETERMINE_CLIENT_ELIGIBILITY_LEVEL";

	private static final String SERVICE_VERSION = "0.00";

	@Override
	public Party determineClientEligibilityLevel(ManufacturingContext aCtxt, Party aParty) throws BaseException {
		Map<String, Object> pegaParams = this.getDelegateParameters(aCtxt);

		// call the service
		GenericDelegate service = new GenericDelegate(COMPONENT_NAME, SERVICE_NAME, SERVICE_VERSION, aParty, pegaParams);

		Party outParty = (Party) service.executeService();
		this.resumeLogging(outParty);

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s.%s.%s> TraceId=%s", COMPONENT_NAME, SERVICE_NAME, SERVICE_VERSION, super.getTraceId(pegaParams)));
		}
		return outParty;
	}

}
