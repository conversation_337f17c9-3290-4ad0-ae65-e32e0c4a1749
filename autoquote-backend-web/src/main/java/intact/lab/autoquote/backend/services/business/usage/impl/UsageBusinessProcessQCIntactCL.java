package intact.lab.autoquote.backend.services.business.usage.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.plp.service.IRatingRiskService;
import com.intact.business.util.ClaimsHelper;
import com.intact.business.util.DriverHelper;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.common.IAssignClaimsService;
import intact.lab.autoquote.backend.services.business.common.IAssignDriverService;
import org.springframework.transaction.annotation.Transactional;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

/**
 * Business process specific to Intact Quebec . Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 * <AUTHOR> Brochu
 */
@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class UsageBusinessProcessQCIntactCL extends UsageBusinessProcess{

    private final ClaimsHelper claimsHelper;
    private final DriverHelper driverHelper;
    private final UsageBusinessProcessHelperIntact usageBusinessProcessHelperIntact;

    public UsageBusinessProcessQCIntactCL(IInsuranceRiskService insuranceRiskService, IPartyService partyService,
                                          IPartyRoleInRiskService partyRoleInRiskService, IRatingRiskService ratingRiskService,
                                          IPolicyVersionHelper policyVersionHelper, IPartyHelper partyHelper, IPolicyHolderService policyHolderService,
                                          DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory, IDataMediatorToPL dataMediatorToPL,
                                          IAssignDriverService assignDriverService, IAssignClaimsService assignClaimsService,
                                          IPolicyVersionService policyVersionService, IQuotationService quotationService,
                                          ClaimsHelper claimsHelper, DriverHelper driverHelper,
                                          UsageBusinessProcessHelperIntact usageBusinessProcessHelperIntact) {
        super(insuranceRiskService, partyService, partyRoleInRiskService, ratingRiskService, policyVersionHelper, partyHelper,
                policyHolderService, dataMediatorToSOMServiceFactory, dataMediatorToPL, assignDriverService, assignClaimsService,
                policyVersionService, quotationService);
        this.claimsHelper = claimsHelper;
        this.driverHelper = driverHelper;
        this.usageBusinessProcessHelperIntact = usageBusinessProcessHelperIntact;
    }


    /**
     * Determines if a driver has D.R. 5 (dossier 5).
     * <p/>
     * A driver has D.R. 5 (dossier 5) if all of the following conditions are satisfied:
     * <p/>
     * le conducteur est assuré à titre de conducteur principal depuis plus de 5 ans
     * <p/>
     * durant les 5 dernières années, un maximum de 2 sinistres dont pas plus d'un sinistre responsable (ne pas tenir
     * compte des réparations de pare-brise).
     *
     * @param partyRoleInRisk the partyRoleInRisk *
     * @return true if the driver has D.R. 5 (dossier 5), false otherwise
     */
    @Override
    public boolean isDrivingRecord5Proof(PartyRoleInRisk partyRoleInRisk) {

        boolean indicator = false;

        Party party = partyRoleInRisk.getParty();

        // Calculate the total number of claims for driver
        int nbrTotalClaims5Years = this.claimsHelper.getTotalClaims5Years(party);

        // Calculate the total number of claims at fault for driver
        int nbrAtFaultClaims5Years = this.claimsHelper.getAtFaultClaims5Years(party);

        // Gets the nbr of year the driver as been insured as principal driver
        int nbrYearsPrincipalDriver = this.driverHelper.getNbrYearPrincipalDriverSince(partyRoleInRisk
                .getPrincipalDriverSinceCode());

        // Determine drivingRecord5Proof indicator
        // because of BR457.1, we dont need to check if the driver as been licenced in QC
        if (nbrTotalClaims5Years <= 2 && nbrAtFaultClaims5Years <= 1 && nbrYearsPrincipalDriver >= 5) {
            indicator = true;
        }

        return indicator;
    }

    @Override
    @Transactional
    public void assignDrivers(final PolicyVersion aPolicyVersion) {
        //remove the risk associated with claims before the assignDriver and assignClaims
        this.usageBusinessProcessHelperIntact.clearInsuranceRiskOnClaims(aPolicyVersion);
        super.assignDrivers(aPolicyVersion);
    }
}
