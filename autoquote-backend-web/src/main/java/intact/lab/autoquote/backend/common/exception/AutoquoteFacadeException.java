/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2013, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.exception;


import jakarta.xml.ws.WebFault;

import java.io.Serial;

/**
 * Autoquote Facade custom exception
 * 
 * <AUTHOR>
 * @since 2013
 */
@WebFault // required for SOAP exception processing for web services
public class AutoquoteFacadeException extends Exception {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * Default constructor.
	 */
	public AutoquoteFacadeException() {
		super();
	}

	/**
	 * Constructor.
	 * 
	 * @param message the message
	 * @param cause the cause
	 */
	public AutoquoteFacadeException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * Constructor.
	 *  
	 * @param message the message
	 */
	public AutoquoteFacadeException(String message) {
		super(message);
	}

	/**
	 * Constructor.
	 * 
	 * @param cause
	 */
	public AutoquoteFacadeException(Throwable cause) {
		super(cause);
	}
	
	// pmdroz : This method is to make sure the real exception is return by the webservice.  This will prevent this error to be return
	// 			"User fault processing is not supported. The @WebFault faultbean is missing for com.intact.autoquote.integration.facade.exception.AutoquoteFacadeException"
	public String getFaultInfo(){ 
		return "";
	}
}
