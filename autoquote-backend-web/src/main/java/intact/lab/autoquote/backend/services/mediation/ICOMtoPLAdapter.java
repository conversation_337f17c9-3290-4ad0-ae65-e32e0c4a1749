/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2014, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;

/**
 * <AUTHOR>
 * 
 */
public interface ICOMtoPLAdapter {

	/**
	 * 
	 * @param aCom {@link CommunicationObjectModel}
	 * @param aPolicyVersion {@link PolicyVersion}
	 * @return true when updateSegments needs to be done, false otherwise
	 * @throws Exception
	 */
	boolean convertCOMtoPL(final CommunicationObjectModel aCom, PolicyVersion aPolicyVersion) throws Exception;
}
