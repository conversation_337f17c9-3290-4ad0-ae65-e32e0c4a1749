/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.usage.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DriverTypeCodeEnum;
import com.ing.canada.plp.domain.enums.OwnerTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyRelationCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insurancerisk.RatingRisk;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRelation;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.plp.service.IRatingRiskService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.business.rules.usage.BR8277_HoneypotTrap;
import intact.lab.autoquote.backend.common.exception.NoHitException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.common.IAssignClaimsService;
import intact.lab.autoquote.backend.services.business.common.IAssignDriverService;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import lombok.AllArgsConstructor;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * This class usage business process acts as the facade for the back-end and exposes manage risk unit methods
 * implementation for the front-end usage UI.
 *
 * <AUTHOR> Droz/Annie Ladouceur
 */
@Component
@AllArgsConstructor
public abstract class UsageBusinessProcess implements IUsageBusinessProcess {

	private static final Logger LOG = ESAPI.getLogger(UsageBusinessProcess.class);

	private IInsuranceRiskService insuranceRiskService;

	private IPartyService partyService;

	private IPartyRoleInRiskService partyRoleInRiskService;

	private IRatingRiskService ratingRiskService;

	protected IPolicyVersionHelper policyVersionHelper;

	private IPartyHelper partyHelper;

	private IPolicyHolderService policyHolderService;

	private DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

	private IDataMediatorToPL dataMediatorToPL;

	protected IAssignDriverService assignDriverService;

	protected IAssignClaimsService assignClaimsService;

	private IPolicyVersionService policyVersionService;

	protected IQuotationService quotationService;

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void save(final PolicyVersion aPolicyVersion) {

		this.removePersistedPartyRoleInRisk(aPolicyVersion);
		this.removeRatingRisks(aPolicyVersion);

		for (InsuranceRisk insuranceRisk : aPolicyVersion.getInsuranceRisks()) {
			this.insuranceRiskService.persist(insuranceRisk);
		}

		for (Party party : aPolicyVersion.getParties()){
			this.partyService.persist(party);
		}
	}

	@Override
	public void completeModelPolicyHolder(PolicyVersion policyVersion, boolean performDBOperation) {
		// Update PolicyHolder
		Set<Party> listParty = this.policyVersionHelper.getIndividualParties(policyVersion);
		if (listParty != null) {
			for (Party party : listParty) {
				// only proceed if party is not null and is of legal tender (reqs. id)
				if (party != null && party.getId() != null) {
					boolean isPartyOwner = this.partyHelper.isOwner(policyVersion, party);

					PolicyHolder policyHolder = this.policyVersionHelper.retrievePolicyHolderByPartyId(policyVersion,
							party.getId());

					if (isPartyOwner && policyHolder == null) {
						policyHolder = new PolicyHolder();
						policyVersion.addPolicyHolder(policyHolder);
						policyHolder.setParty(party);
						policyHolder.setPolicyHolderType(PolicyHolderTypeCodeEnum.ADDITIONAL_INSURED);

						// Added this in order to keep the behavior of AQ (pre-TopGear)
						if (performDBOperation) {
							this.policyHolderService.persist(policyHolder);
						}

					} else if (!isPartyOwner && policyHolder != null
							&& !policyHolder.getPolicyHolderType().equals(PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED)) {
						// clean out policy holder for removal
						policyVersion.removePolicyHolder(policyHolder);
						policyHolder.getParty().removePolicyHolder(policyHolder);
						policyHolder.setPolicyHolderType(null);
						policyHolder.setPolicyHolderTypeCode(null);
						policyHolder.setNumberStabilityMonths(null);
						policyHolder.setNumberStabilityMonthsCode(null);
						policyHolder.setOriginalScenarioPolicyHolder(null);

						// Added this in order to keep the behavior of AQ (pre-TopGear)
						if (performDBOperation) {
							this.policyHolderService.delete(policyHolder.getId());
						}
					}
				}
			}
		}
	}

	@Override
	public void completeModelPartyRelatations(PolicyVersion policyVersion) {

		Set<Party> listParty = this.policyVersionHelper.getParties(policyVersion);

		Party company = null;
		Party driver = null;
		if (listParty != null) {
			for (Party party : listParty) {
				if (party.getPartyType().equals(PartyTypeCodeEnum.OTHER_COMPANY)) {
					company = party;
				}
				if (party.getPartyType().equals(PartyTypeCodeEnum.INDIVIDUAL) && party.getDriverComplementInfo() != null) {
					driver = party;
				}
			}
		}
		if (company != null && company.getPartyRelationTo()!=null) {
			if (company.getPartyRelationTo().isEmpty()) {
				addPartyRelation(company, driver);
			} else {
				for (PartyRelation partyRelation : company.getPartyRelationTo()) {
					if (!(partyRelation != null && partyRelation.getPartyTo().getId() != null && driver.getId() != null
							&& partyRelation.getPartyTo().getId().equals(driver.getId()))) {
						// set partyRelations
						addPartyRelation(company, driver);
					}
				}
			}
		}
	}

	/**
	 * Remove all the persisted PartyRoleInRisk for a policyVersion.
	 *
	 * @param aPolicyVersion the a policy version
	 *
	 */
	private void removePersistedPartyRoleInRisk(final PolicyVersion aPolicyVersion) {

		for (InsuranceRisk insuranceRisk : aPolicyVersion.getInsuranceRisks()) {

			Set<PartyRoleInRisk> partyRoleInRiskList = insuranceRisk.getPartyRoleInRisks();
			List<PartyRoleInRisk> lst = new ArrayList<>(partyRoleInRiskList);

			for (PartyRoleInRisk partyRoleInRisk : lst) {
				if (partyRoleInRisk.getId() != null
						&& !OwnerTypeCodeEnum.SECOND_REGISTERED_OWNER.equals(partyRoleInRisk.getOwnerType())) {
					Party party = partyRoleInRisk.getParty();
					// May be null if the owner of a vehicle equal to 'Other' - BR467
					if (party != null) {
						party.removePartyRoleInRisk(partyRoleInRisk);
					}

					insuranceRisk.removePartyRoleInRisk(partyRoleInRisk);

					this.partyRoleInRiskService.delete(partyRoleInRisk);
				}
			}
		}
	}

	/**
	 * Removes the rating risks. The RatingRisks are created in the ratingSequence, but since they depend on the
	 * PartyRoleInRisks we clear them here.
	 *
	 * @param aPolicyVersion the policy version
	 */
	private void removeRatingRisks(final PolicyVersion aPolicyVersion) {

		for (InsuranceRisk insuranceRisk : aPolicyVersion.getInsuranceRisks()) {

			Set<RatingRisk> ratingRiskList = insuranceRisk.getRatingRisks();
			List<RatingRisk> lst = new ArrayList<>(ratingRiskList);

			for (RatingRisk ratingRisk : lst) {
				insuranceRisk.removeRatingRisk(ratingRisk);
				this.ratingRiskService.delete(ratingRisk);
			}
		}
	}

	private void addPartyRelation(Party company, Party driver) {
		PartyRelation newPartyRelation = new PartyRelation();
		newPartyRelation.setPartyFrom(company);
		newPartyRelation.setPartyTo(driver);
		newPartyRelation.setNatureFrom(PartyRelationCodeEnum.CPO.getCode());
		newPartyRelation.setNatureTo(PartyRelationCodeEnum.ICO.getCode());
		company.getPartyRelationTo().add(newPartyRelation);
		newPartyRelation.setPartyFrom(company);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RuleExceptionResult validateHoneypotTrap(final String trap) {

        return BR8277_HoneypotTrap.validateBR(trap);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void assignDrivers(final PolicyVersion aPolicyVersion) {

		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion;

		// Retrieve the policy context
		ManufacturingContext context = aPolicyVersion.getInsurancePolicy().getManufacturingContext();

		// Create the SOM Graph from PLP data
		performanceWatch.start("    >> dataMediatorToSOM.convertTo_SOM");
		somPolicyVersion = dataMediatorToSOMServiceFactory.getService("dataMediatorToSOMUsage").convertTo_SOM(aPolicyVersion);
		performanceWatch.stop();

		// . Call the assignment services
		somPolicyVersion = this.callAssignmentServices(performanceWatch, somPolicyVersion, context);

		// Retrieve the SOM Graph data in PLP
		performanceWatch.start("    >> dataMediatorToPL.convertTo_PL");
		this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPolicyVersion, false);
		performanceWatch.stop();

		this.setDrivingRecord5Proof(aPolicyVersion);

		this.policyVersionService.persistCascadeAll(aPolicyVersion);

		LOG.trace(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
	}

	/**
	 * Call the assignment services Having a separate method here , let us customize the assignment made at the usage
	 * level to call additional services depending of the company and/or the province.
	 *
	 * @param performanceWatch the performance watch (used to monitor the performance)
	 * @param aSOMPolicyVersion SOM policy version
	 * @param context Manufacturign context
	 * @return Updated SOM policy version
	 */
	public com.ing.canada.som.interfaces.agreement.PolicyVersion callAssignmentServices(
			final StopWatch performanceWatch,
			final com.ing.canada.som.interfaces.agreement.PolicyVersion aSOMPolicyVersion,
			final ManufacturingContext context) {

		// BR368-Assignation of the Occasional Drivers
		performanceWatch.start("    >> assignDrivers");
		com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = this.assignDriverService.assignDrivers(aSOMPolicyVersion, context);
		performanceWatch.stop();

		// BR2103/BR2104-Assignation of the Claims
		performanceWatch.start("    >> assignClaims");
		somPolicyVersion = this.assignClaimsService.assignClaims(somPolicyVersion, context);
		performanceWatch.stop();
		return somPolicyVersion;
	}

	/**
	 * Loops through all drivers and sets their driving record 5 proof.
	 *
	 * @param aPolicyVersion the a policy version
	 */
	public void setDrivingRecord5Proof(final PolicyVersion aPolicyVersion) {
		for (InsuranceRisk ir : aPolicyVersion.getInsuranceRisks()) {

			for (PartyRoleInRisk partyRoleInRisk : ir.getPartyRoleInRisks()) {

				if (DriverTypeCodeEnum.PRINCIPAL.equals(partyRoleInRisk.getDriverType())) {
					boolean drivingRecord5Proof = this.isDrivingRecord5Proof(partyRoleInRisk);
					ir.setProofDrivingRecord5Indicator(drivingRecord5Proof);
				}
				// for now we dont do anything for occasional driver as asked by Brigitte Mosley
				// else if (DriverTypeCodeEnum.OCCASIONAL.equals(partyRoleInRisk.getDriverType())) {
				// ir.setProofDrivingRecord5OccIndicator(drivingRecord5Proof);
				// }
			}
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void processTheNoHitException(final PolicyVersion aPolicyVersion) throws NoHitException {

		/*
		 * . Starting with the Ontario Auto Remediation project, we can't retrieve the
		 *
		 * credit score in Ontario for Auto quote.
		 */
		if (!ProvinceCodeEnum.ONTARIO.equals(aPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince())) {
			if (this.quotationService.isCustomerNoHit(aPolicyVersion)) {
				if (!this.quotationService.isNotRequired(aPolicyVersion)) {
					throw new NoHitException("Cannot find credit score");
				}
			}
		}
	}

	/**
	 * Determines if a driver has D.R. 5 (dossier 5).
	 *
	 * A driver has D.R. 5 (dossier 5) if all of the following conditions are satisfied:
	 *
	 * le conducteur est assuré à titre de conducteur principal depuis plus de 5 ans
	 *
	 * durant les 5 dernières années, un maximum de 2 sinistres dont pas plus d'un sinistre responsable (ne pas tenir
	 * compte des réparations de pare-brise).
	 *
	 * @param partyRoleInRisk the partyRoleInRisk *
	 * @return true if the driver has D.R. 5 (dossier 5), false otherwise
	 */
	public abstract boolean isDrivingRecord5Proof(PartyRoleInRisk partyRoleInRisk);
}
