/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.common.domain.Municipality;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.municipality.IMunicipalitiesByPostalCodeService;
import com.ing.canada.plp.domain.ManufacturingContext;
import intact.lab.autoquote.backend.services.IMunicipalityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;

/**
 * Service for the municipalities
 * 
 * <AUTHOR> pabonnea
 */
@Component("MunicipalityServiceAutoquote")
public class MunicipalityServiceAutoquote implements IMunicipalityService {

	private final IMunicipalitiesByPostalCodeService municipalityByPostalCodeService;

	public MunicipalityServiceAutoquote(IMunicipalitiesByPostalCodeService municipalityByPostalCodeService) {
		this.municipalityByPostalCodeService = municipalityByPostalCodeService;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Municipality getFirstMunicipalityForPostalCode(Locale locale, String postalCode, ManufacturingContext context) {
		if (StringUtils.isNotEmpty(postalCode)) {
			List<Municipality> municipalities = this.municipalityByPostalCodeService
					.getMunicipalitiesListForPostalCode(Language.fromLocale(locale), postalCode, context);

			if (municipalities != null) {
				for (Municipality municipality : municipalities) {
					return municipality;
				}
			}
		}

		return null;
	}
}
