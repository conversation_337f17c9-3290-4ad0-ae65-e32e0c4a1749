package intact.lab.autoquote.backend.common.exception;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteException extends RuntimeException {

	public static final String EXEC_DEFAULT_ERROR = "exec.application.default.error";
	public static final String EXEC_BUILD_USER_ERROR = "exec.build.user.context.error";
	private static final long serialVersionUID = -4683495659146222785L;
	private static Map<String, String> messages = null;
	private String code = null;

	public AutoQuoteException(String message) {
		super(message);
	}

	public AutoQuoteException(String message, Throwable cause) {
		super(message, cause);
	}

	public AutoQuoteException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public AutoQuoteException(String exceptionCode, Throwable cause, Object... parameters) {
		this(AutoQuoteException.getMessage(exceptionCode, parameters), cause);
		this.setCode(exceptionCode);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteException.getMessages() == null) {
			AutoQuoteException.initMessages();
		}

		String messageFormat = AutoQuoteConfigurationException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteConfigurationException.getMessages().get(AutoQuoteException.EXEC_DEFAULT_ERROR);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<>();

		messages.put(
				AutoQuoteException.EXEC_DEFAULT_ERROR,
				"An unknown error occurred while runnig the application. This exception is not documented at this time.  The cause is {0}");
		messages.put(AutoQuoteException.EXEC_BUILD_USER_ERROR,
				"An error occured while trying to build the user context with language {0}, province {1} and apiKey {2} . The cause of the error is {0}");
		AutoQuoteConfigurationException.setMessages(messages);
	}

	public final String getCode() {
		return code;
	}

	public final void setCode(String code) {
		this.code = code;
	}


}
