package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.address.ComAddress;
import com.intact.com.driver.ComDriver;
import com.intact.com.util.ComDate;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.enums.ConsentTypeEnum;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("comPartyConverter")
public class COMPartyConverter implements ICOMConverter<PartyDTO, ComDriver> {

	@Resource(name = "comAddressConverter")
	private ICOMConverter<AddressDTO, ComAddress> comAddressConverter;

	@Override
	public PartyDTO toDTO(ComDriver comDriver) {
		PartyDTO partyDTO = new PartyDTO();
		partyDTO.setId(comDriver.getWebMsgId());
		if (comDriver != null) {
			partyDTO.setPartyType(StringUtils.isNoneEmpty(comDriver.getEntreprise()) ? PartyTypeEnum.COMPANY : PartyTypeEnum.PERSON);
			if (partyDTO.getPartyType().equals(PartyTypeEnum.COMPANY)) {
				partyDTO.setAddress(comAddressConverter.toDTO(comDriver.getCurrentAddress()));
				partyDTO.setUnstructuredName(comDriver.getEntreprise());
				partyDTO.setEmailAddress(comDriver.getEmailAddress());
				partyDTO.setPhoneNumber(comDriver.getHomePhoneNumber());
			} else {
				partyDTO.setFirstName(comDriver.getFirstName());
				partyDTO.setLastName(comDriver.getLastName());
				if (comDriver.getDateOfBirth() != null) {
					LocalDate dateOfBirthDTO = new LocalDate(Integer.valueOf(comDriver.getDateOfBirth().getYear()),
							Integer.valueOf(comDriver.getDateOfBirth().getMonth()), Integer.valueOf(comDriver.getDateOfBirth().getDay()));
					partyDTO.setDateOfBirth(dateOfBirthDTO);
				}
			}
			if (comDriver.getCurrentAddress() != null) {
				partyDTO.setAddress(comAddressConverter.toDTO(comDriver.getCurrentAddress()));
			}
			partyDTO.setGender(comDriver.getGender());
			loadConsents(partyDTO, comDriver);
		}

		return partyDTO;
	}

	@Override
	public ComDriver toCOM(PartyDTO partyDTO, ComDriver initialDriver) {
		ComDriver comDriver = initialDriver == null ? new ComDriver() : initialDriver;

		comDriver.setWebMsgId(partyDTO.getId());
		comDriver.setCreateProfileConsentInd(Boolean.FALSE);
		if (partyDTO != null) {
			comDriver.setPolicyHolderName(partyDTO.getUnstructuredName());
			if (partyDTO.getAddress() != null) {
				comDriver.setCurrentAddress(this.getComAddressConverter().toCOM(partyDTO.getAddress(), comDriver.getCurrentAddress()));
			}

			if (partyDTO.getPartyType().equals(PartyTypeEnum.COMPANY)) {
				comDriver.setCurrentAddress(this.getComAddressConverter().toCOM(partyDTO.getAddress(), comDriver.getCurrentAddress()));
				comDriver.setEntreprise(partyDTO.getUnstructuredName());
				comDriver.setEmailAddress(partyDTO.getEmailAddress());
				comDriver.setHomePhoneNumber(StringUtils.isNotEmpty(partyDTO.getPhoneNumber()) ? partyDTO.getPhoneNumber().replaceAll("-", "") : null);
			} else {
				ComDate comDate = new ComDate();
				comDate.setDay(String.valueOf(partyDTO.getDateOfBirth().getDayOfMonth()));
				comDate.setMonth(String.valueOf(partyDTO.getDateOfBirth().getMonthOfYear()));
				comDate.setYear(String.valueOf(partyDTO.getDateOfBirth().getYear()));
				comDriver.setDateOfBirth(comDate);
				comDriver.setGender(partyDTO.getGender());
				comDriver.setFirstName(partyDTO.getFirstName());
				comDriver.setLastName(partyDTO.getLastName());
			}
			prepareConsents(partyDTO, comDriver);
		}

		return comDriver;
	}

	public ICOMConverter<AddressDTO, ComAddress> getComAddressConverter() {
		return comAddressConverter;
	}

	public void setComAddressConverter(
			ICOMConverter<AddressDTO, ComAddress> comAddressConverter) {
		this.comAddressConverter = comAddressConverter;
	}

	private void prepareConsents(PartyDTO party, ComDriver driver) {
		if (party.getConsents() == null) return;
		for (ConsentDTO consent : party.getConsents()) {
			if (consent.getConsentType().equals(ConsentTypeEnum.CREDIT_SCORE)) {
				driver.setConsentInd(consent.getConsentInd());
			}
		}
	}

	private void loadConsents(PartyDTO party, ComDriver driver) {
		List<ConsentDTO> consents = new ArrayList<ConsentDTO>();
		if (driver.getConsentInd() != null) {
			ConsentDTO consent = new ConsentDTO();
			consent.setConsentInd(driver.getConsentInd());
			consent.setConsentType(ConsentTypeEnum.CREDIT_SCORE);
			consents.add(consent);
			party.setConsents(consents);
		}
	}

}
