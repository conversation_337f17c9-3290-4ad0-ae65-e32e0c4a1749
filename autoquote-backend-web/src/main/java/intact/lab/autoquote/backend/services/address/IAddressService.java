/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2014, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.address;

import com.ing.canada.common.domain.Municipality;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;

/**
 * Interface for the Address validation service.
 * 
 * <AUTHOR>
 * @since 2014
 */
public interface IAddressService {

	/**
	 * Verifies whether or not the postal code matches the policy holder's province.
	 * 
	 * @param currentProvince {@link ProvinceCodeEnum}
	 * @param municipality {@link Municipality}
	 * @param postalCode as {@link String}
	 * @param context {@link ManufacturingContext}
	 * @return true when in current province, false otherwise
	 */
	boolean isPolicyHolderInCurrentProvince(final ProvinceCodeEnum currentProvince,
			final Municipality municipality, final String postalCode, final ManufacturingContext context);
}
