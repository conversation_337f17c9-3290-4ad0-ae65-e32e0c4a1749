package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteVehicleException;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.List;

@Component
public class MakeValidationRule {

	private final IVehicleFacade vehicleFacade;

	public MakeValidationRule(IVehicleFacade vehicleFacade) {
		this.vehicleFacade = vehicleFacade;
	}

	public void validate(final String make, final String company, final int year, final String province, final String language, Errors errors) {
		try {
			List<MakeDTO> listMake = vehicleFacade.getVehicleMakeList(String.valueOf(year), province, language);
            for (MakeDTO makeDTO : listMake) {
                if (makeDTO.getValue().equals(make)) {
                    return;
                }
            }
		} catch (AutoQuoteVehicleException e) {
		} catch (Error e) {
			throw new AutoQuoteException(e.getMessage(), e);
		}
		errors.rejectValue("make", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), "[make]");
	}
}
