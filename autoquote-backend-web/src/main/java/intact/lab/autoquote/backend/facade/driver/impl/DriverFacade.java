package intact.lab.autoquote.backend.facade.driver.impl;

import com.ing.canada.cif.domain.IPostalCode;
import com.ing.canada.common.domain.Municipality;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComErrorCodeEnum;
import com.intact.com.enums.ComErrorFieldEnum;
import com.intact.com.util.ComValidationError;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.facade.driver.IDriverFacade;
import intact.lab.autoquote.backend.facade.impl.BaseFacade;
import intact.lab.autoquote.backend.services.address.IAddressService;
import intact.lab.autoquote.backend.services.IMunicipalityService;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Class to share driver common code between Intact Ontario & Quebec.
 * 
 * <AUTHOR> L.
 * @since 12-11-2014
 */
public abstract class DriverFacade extends BaseFacade implements IDriverFacade {

	@Autowired
	protected IMunicipalityService municipalityService;

	@AutowiredLocal
	protected IAddressService addressService;

	@AutowiredLocal
	protected IDriverBusinessProcess driverBusinessProcess;

	private static final Logger LOG = ESAPI.getLogger(DriverFacade.class);
	
	@Override
	@Transactional(readOnly = true)
	public ComMunicipalityInfo getMunicipalityByPostalCode(ComContext comContext, String postalCode) throws AutoQuoteException {
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, ">> get municipality for postalCode=[" + postalCode + "]");
		}

		ComMunicipalityInfo info = new ComMunicipalityInfo();
		List<ComValidationError> errors = new ArrayList<>();

		try {
			if (StringUtils.isNotBlank(postalCode)) {
				if (this.isPostalCodeCorrectlyFormatted(postalCode)) {
					// driver.error.postalCode.invalid
					addError(errors, ComErrorCodeEnum.FORMAT_ERROR, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
				} else {
					ManufacturingContext manContext = MediatorUtils.convertContext(comContext);

					Municipality municipality = this.getFirstMunicipalityFromPostalCode(new Locale(comContext.getLanguage().getCode()), postalCode, manContext);
					if (municipality == null) {
						// driver.error.noCityOrMunicipality
						addError(errors, ComErrorCodeEnum.MUNICIPALITY_NOT_FOUND, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
					} else {
						if (!this.addressService.isPolicyHolderInCurrentProvince(ProvinceCodeEnum.valueOfCode(comContext.getProvince().getCode()),
								municipality, postalCode, manContext)) {
							// driver.error.postalCode.client
							addError(errors, ComErrorCodeEnum.MUNICIPALITY_OUTSIDE_PROVINCE, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
						} else {
							/*
							 * BR333 When there is only one (1) street named associated to the postal code, then the street name should be defaulted to that
							 * one. Otherwise the street name should remain blank.
							 */
							IPostalCode pc = this.driverBusinessProcess.getPostalCodeInfo(new Locale(comContext.getLanguage().getCode()), postalCode);

							if (pc != null) {
								info.setStreetName(pc.getStreetName());
								info.setStreetType(pc.getStreetType());
								info.setStreetDirection(pc.getStreetDirection());
							}

							info.setMunicipalities(this.getMunicipalitiesValidValueList(new Locale(comContext.getLanguage().getCode()), postalCode, manContext));

							// Note: No postal code prohibition for Intact
							ProvinceCodeEnum provinceCodeEnum = this.driverBusinessProcess.getProvinceForMunicipality(municipality, postalCode, manContext);
							info.setProvince(provinceCodeEnum.getCode());
							info.setCountry("CA");
						}
					}
				}
			} else {
				addError(errors, ComErrorCodeEnum.MANDATORY_FIELD, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
			}
		} catch (Exception e) {
			if (LOG.isErrorEnabled()) {
				LOG.error(Logger.EVENT_FAILURE, "An error occured: " + e.getMessage());
			}
			throw new AutoQuoteQuoteServiceException(AutoQuoteQuoteServiceException.DEFAULT_EXCEPTION_CODE);
		}
		info.setValidationErrors(errors);

		return info;
	}

	@Override
	public List<ValidValue> getWorkSectorsList(final ComContext comContext) {
		ManufacturingContext manContext = MediatorUtils.convertContext(comContext);
		Locale locale = MediatorUtils.getLocale(comContext);
		List<ValidValueBO> domainList = this.driverBusinessProcess.getDomainsList(locale, manContext.getDistributionChannel(), manContext.getInsuranceBusiness(), DistributorCodeEnum.DEFAULT);

		List<ValidValue> aList = new ArrayList<ValidValue>();
		for (ValidValueBO validValueBO : domainList) {
			ValidValue validValue = new ValidValue(validValueBO.getValue(), validValueBO.getLabel(), validValueBO.getIndex());
			aList.add(validValue);
		}
		return aList;
	}

	@Override
	public List<ValidValue> getOccupationsList(ComContext comContext, String workSector) throws AutoquoteFacadeException {
		try {
			ManufacturingContext manufContext = MediatorUtils.convertContext(comContext);
			Locale locale = MediatorUtils.getLocale(comContext);
			List<ValidValueBO> occupation = this.driverBusinessProcess.getOccupationsList(locale, workSector, manufContext, null);
			List<ValidValue> values = new ArrayList<ValidValue>();

			for (ValidValueBO curOccupation : occupation) {
				if (LOG.isDebugEnabled()) {
					LOG.debug(Logger.EVENT_SUCCESS, ">> add occupation to values. " + "index=[" + curOccupation.getIndex() +
                            "], label(key)=[" + curOccupation.getLabel() + "], value=[" + curOccupation.getValue() + "]");
				}

				values.add(new ValidValue(curOccupation.getValue(), curOccupation.getLabel(), curOccupation.getIndex()));
			}
			return values;

		} catch (Exception e) {
			if (LOG.isErrorEnabled()) {
				LOG.error(Logger.EVENT_FAILURE, "An error occured: " + e.getMessage());
			}
			throw new AutoquoteFacadeException(e);
		}
	}

	@Override
	public List<ValidValue> getGroupList(ComContext comContext) {
		return null;
	}

	/**
	 * Add an error to the list of COM validation errors.
	 * 
	 * @param errors
	 *            - {@link List}<{@link ComValidationError}>
	 * @param code
	 *            - {@link ComErrorCodeEnum}
	 * @param field
	 *            - {@link ComErrorFieldEnum}
	 */
	protected static void addError(List<ComValidationError> errors, ComErrorCodeEnum code, ComErrorFieldEnum field) {
		ComValidationError error = new ComValidationError(code);
		error.setErrorField(field);
		errors.add(error);
	}

	/**
	 * Returns a list of municipalities (as ValidValue instances) that match the postal code.
	 */
	protected List<ValidValue> getMunicipalitiesValidValueList(final Locale locale, String postalCode, ManufacturingContext context) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, ">> get municipalities for locale=[" + locale + "], postalCode=[" + postalCode +
                    "], context=[" + context + "]");
		}

		/*
		 * BR0427 The City/municipality available is/are determined and limited by the list of city generated by a specific Postal Code
		 */
		List<Municipality> municipalities = this.driverBusinessProcess.getMunicipalitiesFromPostalCode(locale, postalCode, context);

		List<ValidValue> values = new ArrayList<>();
		if (municipalities != null) {
			int i = 1;
			for (Municipality m : municipalities) {
				values.add(new ValidValue(m.getId(), m.getDescription(), i));
				i++;
			}
		}

		return values;
	}

	/**
	 * Returns the first municipality that matches the postal code.
	 * 
	 * @param locale {@link Locale}
	 * @param postalCode as {@link String}
	 * @param context {@link ManufacturingContext}
	 * @return the {@link Municipality}
	 */
	private Municipality getFirstMunicipalityFromPostalCode(final Locale locale, final String postalCode, final ManufacturingContext context) {
		String postalCodeValidated = this.formFieldValidatorService.getFormattedPostalCode(postalCode).toUpperCase();
		return this.municipalityService.getFirstMunicipalityForPostalCode(locale, postalCodeValidated, context);
	}

	/**
	 * Verifies whether or not the postal code is correctly formatted.
	 * 
	 * @param postalCode as {@link String}
	 * @return true when properly formatted, false otherwise
	 */
	private boolean isPostalCodeCorrectlyFormatted(final String postalCode) {
		/*
		 * BR2112 The system must warn the user when it determines there is no city or municipality related to the provided Postal Code. (UI validation)
		 */
		String sPostalCode = StringUtils.deleteWhitespace(postalCode.toUpperCase());
		return !this.formFieldValidatorService.isPostalCodeValid(sPostalCode);
	}

	/**
	 * Retrieves the localized facade component matching the company and province found in the provided instance of.
	 *
	 * @param aComContext {@link ComContext}
	 * @return {@link IDriverFacade}
	 * @throws AutoQuoteException the autoquote facade exception
	 * {@link ComContext}.
	 */
	public static IDriverFacade getInstance(ComContext aComContext) throws AutoQuoteException {
		return (IDriverFacade) ContextUtil.getInstance(DriverFacade.class, aComContext);
	}

}
