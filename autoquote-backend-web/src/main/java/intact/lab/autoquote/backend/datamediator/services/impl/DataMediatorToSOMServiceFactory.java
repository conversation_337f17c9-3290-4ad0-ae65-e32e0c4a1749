package intact.lab.autoquote.backend.datamediator.services.impl;

import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOM;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class DataMediatorToSOMServiceFactory {

    private final ApplicationContext applicationContext;

    DataMediatorToSOMServiceFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public IDataMediatorToSOM getService(String serviceName) {
        return applicationContext.getBean(serviceName, IDataMediatorToSOM.class);
    }
}
