package intact.lab.autoquote.backend.controller;

import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.facade.IDistributorFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/irca/v2")
@AllArgsConstructor
public class DistributorController {

	private final IDistributorFacade distributorFacade;

	@Operation(summary = "distributor", description = "Retrieve broker information based on subBroker number")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "Successfully retrieved DistributorDTO"),
			@ApiResponse(responseCode = "401", description = "You are not authorized to view the resource"),
			@ApiResponse(responseCode = "403", description = "Accessing the resource you were trying to reach is forbidden"),
			@ApiResponse(responseCode = "404", description = "The resource you were trying to reach is not found")
	})
	@RequestMapping(value = "/distributors/{subBrokerNo}", method = RequestMethod.GET, produces = "application/json")
	public DistributorDTO retrieveBrokerInfo(
			@RequestParam(value = "apiKey") String apiKey,
			@RequestParam(value = "language") String language,
			@RequestParam(value = "province") String province,
			@RequestParam(value = "postalCode", required = false) String postalCode,
			@PathVariable("subBrokerNo") String subBrokerNo,
			@RequestParam(value = "origin", required = false) String origin) throws Exception {

		return this.distributorFacade.retrieveBrokerInfo(apiKey, language, province, postalCode, subBrokerNo, origin);
	}

}
