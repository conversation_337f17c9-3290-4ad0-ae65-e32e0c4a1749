/*
 * Important notice: This software is the sole property of Intact Insurance Inc.
 * and cannot be distributed and/or copied without the written permission of Intact Insurance Inc.
 *
 * Copyright (c) 2009, Intact Insurance Inc., All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.common;

/** 
 * 
 * <AUTHOR>
 */
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;

/**
 * The Interface IAssignClaimsService.
 */
public interface IAssignClaimsService {

	/**
	 * This function will reassign all the claims.
	 * 
	 * @param aSomPolicyVersion {@link PolicyVersion}
     * @param aContext {@link ManufacturingContext}
     * @return Updated Policy version
	 */
	PolicyVersion assignClaims(PolicyVersion aSomPolicyVersion, ManufacturingContext aContext);
}
