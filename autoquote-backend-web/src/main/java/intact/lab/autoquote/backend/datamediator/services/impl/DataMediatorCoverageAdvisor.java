package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.plp.domain.coverage.CoverageOption;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.ICoverageService;
import com.ing.canada.som.sdo.product.CoverageProductBO;
import com.ing.canada.som.sdo.product.CoverageRepositoryEntryBO;
import com.ing.canada.som.sdo.risk.CoverageOptionBO;
import com.ing.canada.som.sdo.risk.InsuranceRiskBO;
import commonj.sdo.DataObject;
import intact.lab.autoquote.backend.datamediator.DMConstants;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorCoverageAdvisor;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Component
public class DataMediatorCoverageAdvisor extends BaseMediatorToPL implements IDataMediatorCoverageAdvisor {
    private static final Log log = LogFactory.getLog(DataMediatorCoverageAdvisor.class);

    private final ICoverageService coverageService;

    public DataMediatorCoverageAdvisor(IBaseEntityDAO baseEntityDAO, ICoverageService coverageService) {
        super(baseEntityDAO);
        this.coverageService = coverageService;
    }

    public void processCoverationOptionAddition(PolicyVersion plPolicyVersion, DataObject dataObject) {
        if (!isValidInput(plPolicyVersion, dataObject)) {
            return;
        }

        CoverageOptionBO coverageOptionBo = (CoverageOptionBO) dataObject;
        CoverageProductBO coverageProductBo = coverageOptionBo.getTheCoverageProductBO();

        if (coverageProductBo == null) {
            return;
        }

        CoverageRepositoryEntryBO somCoverageRepositoryEntryBO = coverageProductBo.getTheCoverageRepositoryEntryBase();

        if (!isValidContext(plPolicyVersion, somCoverageRepositoryEntryBO)) {
            return;
        }

        processCoverageOption(plPolicyVersion, dataObject, coverageOptionBo, somCoverageRepositoryEntryBO);
    }

    private boolean isValidInput(PolicyVersion plPolicyVersion, DataObject dataObject) {
        return plPolicyVersion != null && dataObject != null;
    }

    private boolean isValidContext(PolicyVersion plPolicyVersion, CoverageRepositoryEntryBO somCoverageRepositoryEntryBO) {
        return somCoverageRepositoryEntryBO != null
                && plPolicyVersion.getInsurancePolicy() != null
                && plPolicyVersion.getInsurancePolicy().getManufacturingContext() != null;
    }

    private void processCoverageOption(PolicyVersion plPolicyVersion, DataObject dataObject,
                                       CoverageOptionBO coverageOptionBo,
                                       CoverageRepositoryEntryBO somCoverageRepositoryEntryBO) {
        CoverageRepositoryEntry plCoverageRepositoryEntry = findCoverageRepositoryEntry(
                plPolicyVersion, somCoverageRepositoryEntryBO);

        if (plCoverageRepositoryEntry == null) {
            log.warn("CoverageRepositoryEntry unexpectedly null for COVERAGE code = ["
                    + somCoverageRepositoryEntryBO.getCoverageCode() + "]");
            return;
        }

        InsuranceRisk insuranceRisk = findInsuranceRisk(plPolicyVersion, coverageOptionBo);

        if (insuranceRisk == null) {
            log.warn("InsuranceRisk unexpectedly null for COVERAGE code = ["
                    + somCoverageRepositoryEntryBO.getCoverageCode() + "]");
            return;
        }

        createCoverageOption(dataObject, plCoverageRepositoryEntry, insuranceRisk);
    }

    private CoverageRepositoryEntry findCoverageRepositoryEntry(
            PolicyVersion plPolicyVersion, CoverageRepositoryEntryBO somCoverageRepositoryEntryBO) {
        return this.coverageService.findCoverageRepositoryEntriesByProvinceCodeAndRatingDate(
                somCoverageRepositoryEntryBO.getCoverageCode(),
                plPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince(),
                plPolicyVersion.getInsurancePolicy().getManufacturerCompany(),
                plPolicyVersion.getRatingDate());
    }

    private InsuranceRisk findInsuranceRisk(PolicyVersion plPolicyVersion, CoverageOptionBO coverageOptionBo) {
        InsuranceRiskBO somInsuranceRisk = coverageOptionBo.getTheInsuranceRiskBO();

        if (somInsuranceRisk == null || StringUtils.isEmpty(somInsuranceRisk.getPersistenceUniqueId())) {
            return null;
        }

        String insuranceRiskId = somInsuranceRisk.getPersistenceUniqueId();
        return (InsuranceRisk) this.getBeanFromPLGraphWithCriteria(
                Long.valueOf(insuranceRiskId), InsuranceRisk.class, plPolicyVersion);
    }

    private void createCoverageOption(DataObject dataObject,
                                      CoverageRepositoryEntry plCoverageRepositoryEntry,
                                      InsuranceRisk insuranceRisk) {
        CoverageOption plCoverationOption = new CoverageOption();
        plCoverationOption.setCoverageRepositoryEntry(plCoverageRepositoryEntry);
        plCoverationOption.setInsuranceRisk(insuranceRisk);
        DataMediatorUtils.setAttributesByReflectionFromSOMtoPL(
                DMConstants.plCoverageOptionAttributeNames,
                DMConstants.somCoverageOptionAttributeNames,
                plCoverationOption,
                dataObject);
    }
}
