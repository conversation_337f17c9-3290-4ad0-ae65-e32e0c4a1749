package intact.lab.autoquote.backend.common.enums;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

public enum LanguageEnum {
    FRENCH("F", "fr"), ENGLISH("E", "en");

    private static Map<String, LanguageEnum> valuesMap = null;
    private static Map<String, LanguageEnum> valuesIsoMap = null;

    @Getter
    @Setter
    private String code;

    @Getter
    @Setter
    private String isoCode;

    private LanguageEnum(String code, String isoCode) {
        this.code = code;
        this.isoCode = isoCode;
    }

    public static synchronized LanguageEnum fromCode(String code) {
        if (valuesMap == null) {
            valuesMap = new HashMap<>();
            for (LanguageEnum language : LanguageEnum.values()) {
                valuesMap.put(language.getCode(), language);
            }
        }
        return valuesMap.get(code);
    }

    public static synchronized LanguageEnum fromIsoCode(String isoCode) {
        if (isoCode != null) {
            isoCode = isoCode.toUpperCase();
        }
        if (valuesIsoMap == null) {
            valuesIsoMap = new HashMap<>();
            for (LanguageEnum language : LanguageEnum.values()) {
                valuesIsoMap.put(language.getIsoCode().toUpperCase(), language);
            }
        }
        return valuesIsoMap.get(isoCode);
    }

    @Override
    public String toString() {
        return "[code=" + this.getCode() + ", iso=" + this.getIsoCode() + "]";
    }
}
