package intact.lab.autoquote.backend.services.address.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;

@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AddressServiceONIntactCL extends AddressService {

    public AddressServiceONIntactCL(IFormFieldValidatorService formFieldValidatorService) {
        super(formFieldValidatorService);
    }
}
