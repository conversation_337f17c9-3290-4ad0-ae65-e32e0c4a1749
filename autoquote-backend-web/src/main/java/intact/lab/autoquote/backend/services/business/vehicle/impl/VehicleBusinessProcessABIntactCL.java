/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.vehicle.impl;

import com.ing.canada.common.domain.VehicleDetail;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.services.api.vehicle.IVehicleDetailService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.common.webmethods.api.rategroup.RetrieveVehicleRateGroupService;
import com.ing.canada.common.webmethods.api.restrictedvehicle.GetRestrictedVehicleStatusService;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.RiskTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.service.IAdditionalInterestRoleService;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 * Business process specific to Intact Alberta . Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 * <AUTHOR>
 */
@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class VehicleBusinessProcessABIntactCL extends VehicleBusinessProcess {

	/** The Constant RESTRICTED_STATUS. */
	private static final String RESTRICTED_VEHICLE_IND = "X";

	private final ICommonBusinessProcess commonBusinessProcess;

	public VehicleBusinessProcessABIntactCL(VehicleMakesByYearService vehicleMakesByYearService,
											VehicleModelsService vehicleModelsService, IDateManagerService dateManagerService,
											IVehicleDetailService vehicleDetailService, ICommonBusinessProcess commonBusinessProcess,
											IPartyRoleInRiskService partyRoleInRiskService, IPartyService partyService,
											IAdditionalInterestRoleService additionalInterestRoleService,
											IInsuranceRiskService insuranceRiskService, ICommonBusinessProcess commonBusinessProcess1,
											RetrieveVehicleRateGroupService retrieveVehicleRateGroupService,
											GetRestrictedVehicleStatusService restrictedVehicleService) {
		super(vehicleMakesByYearService, vehicleModelsService, dateManagerService, vehicleDetailService, commonBusinessProcess,
				partyRoleInRiskService, partyService, additionalInterestRoleService, insuranceRiskService);
		this.commonBusinessProcess = commonBusinessProcess1;
		this.retrieveVehicleRateGroupService = retrieveVehicleRateGroupService;
		this.restrictedVehicleService = restrictedVehicleService;
	}

	private final RetrieveVehicleRateGroupService retrieveVehicleRateGroupService;
	private final GetRestrictedVehicleStatusService restrictedVehicleService;


	/**
	 * {@inheritDoc}
	 */
	@Override
	public void setVehicleDetail(Vehicle aVehicle, Locale locale, Long aPolicyVersionId, DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness) {

		VehicleDetailSpecificationRepositoryEntry curVehDetSpecRepEntry = aVehicle.getVehicleDetailSpecificationRepositoryEntry();
		
		String vehicleCode = curVehDetSpecRepEntry.getVehicleRepositoryEntry().getVehicleCode();
		String shortVehicleCode = vehicleCode.substring(0, 4).replaceFirst("^0", "");

		String vehicleYear = curVehDetSpecRepEntry.getVehicleYear().toString();
		String vehicleModel = curVehDetSpecRepEntry.getVehicleRepositoryEntry().getVehicleModelEnglish();

		Province province = Province.fromLocale(locale);
		PolicyVersion policyVersion = this.getPolicyVersion(aPolicyVersionId);
		ManufacturerCompanyCodeEnum manufacturerCompanyCodeEnum = policyVersion.getInsurancePolicy().getManufacturerCompany();
		
		Date ratingDate = this.dateManagerService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, aPolicyVersionId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String effectiveDate = sdf.format(ratingDate);

		Map<String, String> result = this.retrieveVehicleRateGroupService.executeService(
				manufacturerCompanyCodeEnum.getCode(), province.getCode(), shortVehicleCode, vehicleYear, "", effectiveDate
		);

		curVehDetSpecRepEntry.setRestrictedVehicleIndicator(Boolean.FALSE);

		if (!result.isEmpty()) {
			curVehDetSpecRepEntry.setRateGroupClearCollision(result.get("COLL"));
			curVehDetSpecRepEntry.setRateGroupClearDcpd(result.get("DCPD"));
			curVehDetSpecRepEntry.setRateGroupClearComprehensive(result.get("COMP"));
			curVehDetSpecRepEntry.setRateGroupClearAccidentBenefit(result.get("ACCB"));
		}
		
		String restrictedStatus = this.restrictedVehicleService.executeService(province.getCode(), shortVehicleCode,
				vehicleYear, RiskTypeCodeEnum.PRIVATE_PASSENGER_VEHICLE.getCode(), vehicleModel);

		if (RESTRICTED_VEHICLE_IND.equals(restrictedStatus)) {
			curVehDetSpecRepEntry.setRestrictedVehicleIndicator(Boolean.TRUE);
		}
		
		//curVehDetSpecRepEntry.setRestrictedVehicleIndicator(Boolean.TRUE);
		
		/* 
		 * As a there will be a rewrite of the AQ/QQ Serge Duchesne has provided the following patch/solution 
		 * The Classic service PHYSICAL_OBJECT.GET_DETAILED_VEHICLE_TABLE_INFORMATIONx10.00 is called with DistributorChannel = DirectSeller
		 * to get retailPriceWithGST of vehicle 
		 */
		VehicleDetail details = this.getVehicleDetailToVehicleDetailSpecificationRepositoryEntry(locale, aVehicle, aPolicyVersionId, 
				DistributionChannelCodeEnum.DIRECT_SELLER, insuranceBusiness);
		if (details != null) {
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRetailPriceWithGst(details.getRetailPriceWithGst());
		}
	}

	/**
	 * Gets the PolicyVersion.
	 *
	 * @param aPolicyVersionId the PolicyVersion id
	 *
	 * @return the PolicyVersion
	 */
	public PolicyVersion getPolicyVersion(final Long aPolicyVersionId) {
		return this.commonBusinessProcess.loadPolicyVersion(aPolicyVersionId);
	}

	@Override
	public RuleExceptionResult validateHardRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage) {
		return null; // no vehicle hard roadblock for AB CL.
	}

}
