package intact.lab.autoquote.backend.services.impl;

import com.intact.com.enums.ComCompanyEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Locale;

/**
 * InvocationHandler to handle call on the interface
 * com.intact.autoquote.service.IValuesDomainService
 *
 * <AUTHOR> pabonnea, xpham
 */
@Component("ValueDomainInvocationHandler")
public class ValueDomainInvocationHandler implements InvocationHandler {

	private static final String METHOD_NAME_PREFIX = "get";

	// private static final String BASE_PACKAGE = "com.intact.quickquoteweb";
	private static final String BASE_PACKAGE = "com.intact.autoquote";

	private static final String RESOURCE_PACKAGE = "resources";

	/**
	 * @see InvocationHandler#invoke(Object,
	 * Method, Object[])
	 */
	public Object invoke(Object aProxy, Method aMethod, Object[] args) {
		String bundleName = transform(StringUtils.substringAfter(aMethod.getName(), METHOD_NAME_PREFIX));

		if (args == null) {
			throw new IllegalArgumentException("Method argument " + aMethod.getName()
					+ " should not be null, the mimimum required is at least the Locale argument.");
		}

		Locale locale = (Locale) args[0];
		Boolean ascendant = args.length > 1 ? (Boolean) args[1] : Boolean.TRUE;

		return ResourceBundleHelper.getBundleContent(
				BASE_PACKAGE + "." + ComCompanyEnum.INTACT.getCaseSensitiveName().toLowerCase() + "." + RESOURCE_PACKAGE + "." + bundleName, locale,
				ascendant);
	}

	/**
	 * Transform the method name into the bundle name.
	 *
	 * @param input The methodName
	 * @return The bundle name.
	 */
	private String transform(String input) {
		StringBuilder sb = new StringBuilder();
		char[] cs = input.toCharArray();

		for (int i = 0; i < cs.length; i++) {
			if (Character.isUpperCase(cs[i]) && i != 0) {
				sb.append("_");
			}
			sb.append(Character.toLowerCase(cs[i]));
		}
		return sb.toString();
	}
}
