/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid.impl;


import com.ing.canada.cif.domain.ClientFactory;
import com.ing.canada.cif.domain.IAddress;
import com.ing.canada.cif.domain.IBody;
import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.domain.IClientGroup;
import com.ing.canada.cif.domain.IClientSpokenLanguage;
import com.ing.canada.cif.domain.IElectronicContact;
import com.ing.canada.cif.domain.IGeneral;
import com.ing.canada.cif.domain.IGroup;
import com.ing.canada.cif.domain.IName;
import com.ing.canada.cif.domain.IPhoneNumber;
import com.ing.canada.cif.domain.IPrivacy;
import com.ing.canada.cif.domain.IRegistration;
import com.ing.canada.cif.domain.enums.PhoneTypeCodeEnum;
import com.ing.canada.cif.domain.enums.RegistrationTypeCodeEnum;
import com.ing.canada.cif.domain.helpers.ClientHelper;
import com.ing.canada.cif.domain.impl.Addresses;
import com.ing.canada.cif.domain.impl.ClientCreationParameters;
import com.ing.canada.cif.domain.impl.ClientGroups;
import com.ing.canada.cif.domain.impl.ClientRelations;
import com.ing.canada.cif.domain.impl.ClientSpokenLanguages;
import com.ing.canada.cif.domain.impl.IClientCreationParameters;
import com.ing.canada.cif.domain.impl.Privacies;
import com.ing.canada.cif.domain.impl.Registrations;
import com.ing.canada.cif.service.IClientRelationService;
import com.ing.canada.cif.service.IClientService;
import com.ing.canada.cif.util.CompanyUtils;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.AddressTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AddressUsageCodeEnum;
import com.ing.canada.plp.domain.enums.AgreementTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.MaritalStatusCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IConsentHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.domain.Client;
import com.ing.canada.singleid.accessmanager.domain.IState;
import com.ing.canada.singleid.accessmanager.domain.SecureDomain;
import com.ing.canada.singleid.accessmanager.domain.User;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.ing.canada.singleid.accessmanager.service.IClientAccountService;
import com.ing.canada.singleid.accessmanager.service.IUserAccountService;
import com.ing.canada.singleid.accessmanager.service.MatchAccountParams;
import com.ing.canada.singleid.accessmanager.service.factory.AccessManagerServiceFactory;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.common.utils.PartnerUtil;
import intact.lab.autoquote.backend.common.utils.SecureDomainUtil;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.singleid.IProfileService;
import intact.lab.autoquote.backend.services.singleid.ISingleIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Collection;
import java.util.Date;

import static intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum.BR550;
import static intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum.BR551;
import static intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum.BR574;
import static intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum.BR732_AGENT;
import static intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum.BR732_CLIENT;

/**
 * ProfileService
 *
 * <AUTHOR>
 */
@ComponentLocal
public class ProfileService implements IProfileService {

	private static String CLIENT_TYPE_INDIVIDUAL = "IN";
	private static String CLIENT_GROUP_02 = "02";
	private static String MAIL_TYPE_CODE_PO = "PO";
	private static int MAX_ADR_LENGTH = 32;
	protected static final String CIF_PAPER_IND_VALUE_YES = "Y";
	protected static final String CIF_PAPER_IND_VALUE_NO = "N";

	private final ICommonBusinessProcess commonBusinessProcess;
	protected IPartyHelper partyHelper;
	protected IClientService clientService;
	protected IClientRelationService clientRelationService;
	protected IClientAccountService clientAccountService;

	protected IConsentHelper consentHelper;
	protected IPartyService partyService;
	protected IUserAccountService userAccountService;
	protected ISingleIdService singleIdService;

	private static final Logger LOG = ESAPI.getLogger(ProfileService.class);
	private static final Logger STATSLOGGER = ESAPI.getLogger("statsLogger");

	public ProfileService(ICommonBusinessProcess commonBusinessProcess, IPartyHelper partyHelper, IClientService clientService,
						  IClientRelationService clientRelationService, IConsentHelper consentHelper, IPartyService partyService,
						  ISingleIdService singleIdService) {
		this.commonBusinessProcess = commonBusinessProcess;
		this.partyHelper = partyHelper;
		this.clientService = clientService;
		this.clientRelationService = clientRelationService;
		this.consentHelper = consentHelper;
		this.partyService = partyService;
		this.singleIdService = singleIdService;
	}

	@Override
	public void removeRelationShip(final Long aPolicyVersionId, final ApplicationEnum application, final Integer aDriverSequence) throws AccessManagerException {
		this.removeRelationShip(this.commonBusinessProcess.loadPolicyVersion(aPolicyVersionId), application, aDriverSequence);
	}

	/**
	 * {@inheritDoc}
	 *
	 * @throws AccessManagerException
	 */
	@Override
	public void removeRelationShip(final PolicyVersion aPolicyVersion, final ApplicationEnum application, final Integer aDriverSequence) throws AccessManagerException {
		try {
			Party plpPartyNameInsured = this.partyHelper.getNamedInsured(aPolicyVersion);
			Party plpParty = this.partyHelper.getDriverBySequence(aPolicyVersion, aDriverSequence);
			Address plpAddress = this.partyHelper.getCurrentResidentialAddress(plpPartyNameInsured);

			IClient nameInsuredClient = this.clientService.getClientById(plpPartyNameInsured.getCifClientId());

			if (plpParty.getCifClientId() != null) {
				ClientRelations clientRelation = this.clientRelationService.findByClientIdRelatedTo(plpPartyNameInsured.getCifClientId(), plpParty.getCifClientId());
				this.clientRelationService.delete(clientRelation);
			} else {
				// This code will be absolete after 30 days (duration of a quote)...
				MatchAccountParams params = new MatchAccountParams(plpParty.getFirstName(), plpParty.getLastName(), plpParty.getBirthDate(), plpAddress.getPostalCode(),
						aPolicyVersion.getDistributorCode(), AgreementTypeCodeEnum.QUOTATION, CompanyUtils.getCsioNumbers(application.toString()));
				Client accessManagerClient = this.getClientAccountService().matchAccount(params);

				if (accessManagerClient != null && !StringUtils.isBlank(accessManagerClient.getClientID())) {
					Long driverClientId = Long.valueOf(accessManagerClient.getClientID());
					ClientRelations prevRelation = this.clientRelationService.findByClientIdRelatedTo(nameInsuredClient.getClient(), driverClientId);
					this.clientRelationService.delete(prevRelation);
				}
			}
			// TODO
		} catch (AccessManagerException e) {
			throw new AccessManagerException(e.getCode(), "exception occured in removeRelationShip", e);
		}
	}

	/**
	 * Gets the client account service.
	 *
	 * @return the client account service
	 */
	@Override
	public IClientAccountService getClientAccountService() {
		if (this.clientAccountService == null) {
			this.clientAccountService = AccessManagerServiceFactory.getInstance().getClientAccountService();
		}

		return this.clientAccountService;
	}

	@Override
	public void manageProfile(final PolicyVersion aPolicyVersion,
							  final boolean isAgent,
							  final RoadBlockExceptionContextEnum contextEnum,
							  final ApplicationEnum inApplication,
							  final String partner) throws AccessManagerException, SingleIdActiveProductException {

		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		Party plpParty = this.partyHelper.getNamedInsured(aPolicyVersion);

		ApplicationEnum app = this.confirmAppForProv(aPolicyVersion, inApplication);

		Address plpAddress = this.partyHelper.getCurrentResidentialAddress(plpParty);

		String clientInfo = null;
		if (LOG.isDebugEnabled()) {
			clientInfo = String.format("(%s, %s, (birthDate if quickquote) %s, %s, %s)",
					plpParty.getFirstName(), plpParty.getLastName(), plpParty.getBirthDate(), plpAddress.getCivicNumber(), plpAddress.getPostalCode());
			LOG.debug(Logger.EVENT_SUCCESS, String.format("Perform 1st validation with %s", clientInfo));
		}

		performanceWatch.start("ProfileService.manageProfile - matchAccount");
		Client accessManagerClient = null;

		DistributorCodeEnum distributorCode = aPolicyVersion.getDistributorCode();
		String[] csioNumbers = findCSIONumbers(distributorCode, app);

		MatchAccountParams params = new MatchAccountParams(plpParty.getFirstName(),
				plpParty.getLastName(),
				plpParty.getBirthDate(),
				plpAddress.getPostalCode(),
				distributorCode,
				AgreementTypeCodeEnum.QUOTATION,
				csioNumbers);

		accessManagerClient = this.getClientAccountService().matchAccount(params);

		performanceWatch.stop();

		Long cifClientId = null;

		// userid = email
		// String matchedClientEmail = null;
		String matchedClientEmailWithPartner = null;

		if (accessManagerClient != null && accessManagerClient.getClientID() != null) {
			cifClientId = Long.parseLong(accessManagerClient.getClientID());
			// matchedClientEmail = accessManagerClient.getEmail();
			matchedClientEmailWithPartner = PartnerUtil.getEmailWithPartner(accessManagerClient.getEmail(), partner);
		}

		boolean update = false;
		SecureDomain secureDomain = SecureDomainUtil.getSecureDomain(aPolicyVersion);
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("using secureDomain: %s", secureDomain));
		}

		// Client not match in cif
		if (cifClientId == null) {
			if (LOG.isDebugEnabled()) {
				LOG.debug(Logger.EVENT_SUCCESS, String.format("No match on 1st validation for new name insured %s", clientInfo));
			}

			/*
			 * BR710 -The system must create a new client profile in the client database and links the Autoquote to that
			 * profile when the system identified that there is no existing profile for the name insured in the client database.
			 */
			IClient cifClient = this.createCifClient(aPolicyVersion, app);
			cifClientId = cifClient.getClient();
			if (LOG.isDebugEnabled()) {
				LOG.debug(Logger.EVENT_SUCCESS, String.format("Create new cliendId %d for new name insured %s", cifClientId, clientInfo));
			}

			/**
			 * Check if the quotation already have a Cif attach to it. This can append when the quotation was done and
			 * policy holder information change
			 */
			if (plpParty.getCifClientId() == null) {
				// Quote is brand new also is the client, so we link the quote to is new CIF file.
				this.linkQuoteToProfile(plpParty, cifClientId);
			} else {
				// Quote is linked to a different CIF file than the new one.
				Long currentClientId = plpParty.getCifClientId();
				IClient currentClient = this.clientService.getClientById(currentClientId);

				// We Link the quote(party) with the new CIF
				this.linkQuoteToProfile(plpParty, cifClientId);

				// Retrieve the previous CIF electronic address
				IElectronicContact electronicContact = ClientHelper.getCurrentElectronicContact(currentClient);

				if (electronicContact != null && StringUtils.isNotEmpty(electronicContact.getElectronicAddress())) {
					// If electronic address exist, we try to put the previous email address to the new CIF file
					// Only if the previous CIF File is not Expired or ...
					// We also modify TAM to point on that CIF account now.
					int state = this.getUserAccountService().getAccountState(secureDomain, PartnerUtil.getEmailWithPartner(electronicContact.getElectronicAddress(), partner));

					// TODO -pameunie- refactor to remove call to getClientAccountService().getAutoPolicyState > bring back code to autoquote
					int policyState = this.getClientAccountService().getAutoPolicyState(currentClientId);

					// jkayee (2013-02-04) PM9785 - Ensure that the email of current client is not a registered TAM
					// account related to an existing valid contract
					if (state != User.STATE_USERACCOUNT_NONE
							&& state != User.STATE_USERACCOUNT_EXPIRED
							&& policyState != IState.AUTOPOLICY_FUTURE
							&& policyState != IState.AUTOPOLICY_VALID) {

						this.linkTamAccountToNewClientId(secureDomain, currentClientId, cifClientId, partner);
						currentClient.removeElectronicContacts(electronicContact);
						this.changeElectronicContactToAnOtherClient(cifClient, electronicContact);
					}
				}
			}

		}
		// Client found in Cif
		else {

			// Current client(doing the quote) is not assign to a cif client.
			if (plpParty.getCifClientId() == null) {
				if (LOG.isDebugEnabled()) {
					LOG.debug(Logger.EVENT_SUCCESS, "Match new name insured " + clientInfo + " on 1st validation with clientId: " + cifClientId);
				}

				// Link the matched client to this quote(party)
				this.linkQuoteToProfile(plpParty, cifClientId);
				update = true;

				// We match a client and we already have a client linked to this quote.
			} else {
				if (LOG.isDebugEnabled()) {
					LOG.debug(Logger.EVENT_SUCCESS, "Match on modified name insured " + clientInfo);
				}

				Long currentClientId = plpParty.getCifClientId();

				// Current Quote is not associated to the found CIF File.
				if (!cifClientId.equals(currentClientId)) {
					if (LOG.isDebugEnabled()) {
						LOG.debug(Logger.EVENT_SUCCESS, "Replace clientId " + currentClientId + " with new clientId " + cifClientId + " for modified name insured " + clientInfo);
					}

					IClient currentClient = this.clientService.getClientById(currentClientId);

					IClient newClient = this.clientService.getClientById(cifClientId);

					IElectronicContact newElectronicContact = ClientHelper.getCurrentElectronicContact(newClient);

					// Link the quote(party) the matched client
					this.linkQuoteToProfile(plpParty, cifClientId);

					// check if matched Client have a registred account in TAM?
					if (newElectronicContact != null && StringUtils.isNotEmpty(newElectronicContact.getElectronicAddress())
							&& this.getUserAccountService().getUser(secureDomain, PartnerUtil.getEmailWithPartner(newElectronicContact.getElectronicAddress(), partner)) != null) {

						String email = PartnerUtil.getEmailWithPartner(newElectronicContact.getElectronicAddress(), partner);
						this.verifyProductOnFirstValidation(secureDomain, cifClientId, email, contextEnum, isAgent);

						this.getUserAccountService().deleteAccount(secureDomain, email);
						newClient.removeElectronicContacts(newElectronicContact);
					}

					// change user account
					IElectronicContact currentElectronicContact = ClientHelper.getCurrentElectronicContact(currentClient);

					if (currentElectronicContact != null && StringUtils.isNotEmpty(currentElectronicContact.getElectronicAddress())) {

						String currentEmail = PartnerUtil.getEmailWithPartner(currentElectronicContact.getElectronicAddress(), partner);
						int state = this.getUserAccountService().getAccountState(secureDomain, currentEmail);

						int policyState = this.getClientAccountService().getAutoPolicyState(currentClientId);

						// jkayee (2013-02-04) PM9785 - Ensure that the email of current client is not a registered TAM
						// account related to an existing valid contract
						if (state != User.STATE_USERACCOUNT_NONE
								&& state != User.STATE_USERACCOUNT_EXPIRED
								&& policyState != IState.AUTOPOLICY_FUTURE
								&& policyState != IState.AUTOPOLICY_VALID) {

							// it is important to get the new value of cifClientId (plpParty.getCifClientId()) after
							// that the quote is linked to the matched client
							this.linkTamAccountToNewClientId(secureDomain, plpParty.getCifClientId(), cifClientId, partner);
							currentClient.removeElectronicContacts(currentElectronicContact);
							this.changeElectronicContactToAnOtherClient(newClient, currentElectronicContact);
						}
					}

				}
				update = true;
			}
		}

		if (update) {
			// In the case of the partner we don't want to block the client to do a new quote because he have
			// product with us.
			if (StringUtils.isBlank(partner) && !this.hasPrefixedPartnerEmailAdress(secureDomain, accessManagerClient.getEmail())) {
				this.verifyProductOnFirstValidation(secureDomain, cifClientId, matchedClientEmailWithPartner, contextEnum, isAgent);
			}

			// BR1779
			this.updateProfile(aPolicyVersion, cifClientId);
		}

		if (STATSLOGGER.isDebugEnabled()) {
			STATSLOGGER.debug(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void verifyProductOnFirstValidation(final SecureDomain secureDomain, final Long clientId, final String userId, final RoadBlockExceptionContextEnum contextEnum, final boolean isAgent)
			throws SingleIdActiveProductException, AccessManagerException {

		String clientInfo = "";
		if (LOG.isDebugEnabled()) {
			clientInfo = String.format("clientId=[%d], userId=[%s]", clientId, userId);
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s, Verify Policy State (Active or cancelled for critical reasons)", clientInfo));
		}
		int policyState = this.getClientAccountService().getAutoPolicyState(clientId);
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s, policyState=[%d]", clientInfo, policyState));
		}
		this.validatePolicyState(secureDomain, policyState, clientInfo, userId, contextEnum, isAgent);

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s, Verify Autoquote State (Bound)", clientInfo));
		}
		int autoquoteState = this.getClientAccountService().getAutoQuoteState(clientId);
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s, autoquoteState=[%d]", clientInfo, autoquoteState));
		}
		this.validateAutoQuoteState(autoquoteState, contextEnum);
	}

	/**
	 * Validate the policy state
	 *
	 * @throws SingleIdActiveProductException the single id active production exception
	 * @throws AccessManagerException         the access manager exception
	 */
	private void validatePolicyState(final SecureDomain secureDomain, final int policyState, final String clientInfo, final String userId, final RoadBlockExceptionContextEnum contextEnum, final boolean isAgent)
			throws SingleIdActiveProductException, AccessManagerException {
		if (policyState == IState.AUTOPOLICY_FUTURE || policyState == IState.AUTOPOLICY_VALID) {
			this.validateActivePolicy(secureDomain, clientInfo, userId, contextEnum);
		} else {
			this.validatePolicyCancelledForCriticalReasons(policyState, isAgent, contextEnum);
		}
	}

	/**
	 * Validate the autoquote state
	 *
	 * @throws SingleIdActiveProductException the single id active production exception
	 */
	protected void validateAutoQuoteState(int autoquoteState, final RoadBlockExceptionContextEnum contextEnum) throws SingleIdActiveProductException {
		if (autoquoteState == IState.AUTOQUOTE_BOUND) {
			throw new SingleIdActiveProductException(BR574, contextEnum);
		}
	}

	/**
	 * Validate the user account's state
	 *
	 * @throws SingleIdActiveProductException the single id active production exception
	 */
	protected void validateActivePolicy(final SecureDomain secureDomain, final String clientInfo, final String userId, final RoadBlockExceptionContextEnum contextEnum) throws SingleIdActiveProductException, AccessManagerException {
		int accountState = -1;

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("%s Verify Account State", clientInfo));
		}

		if (userId != null) {
			accountState = this.getUserAccountService().getAccountState(secureDomain, userId);
			if (LOG.isDebugEnabled()) {
				LOG.debug(Logger.EVENT_SUCCESS, String.format("%s accountState = %d", clientInfo, accountState));
			}
		}

		if (accountState != User.STATE_USERACCOUNT_NONE) {
			// existing or expired account
			throw new SingleIdActiveProductException(BR551, contextEnum);
		}

		// userid = null or no user account exists
		throw new SingleIdActiveProductException(BR550, contextEnum);
	}

	/**
	 * Validate if policy is cancelled for critical reasons
	 *
	 * @throws SingleIdActiveProductException the single id active production exception
	 */
	protected void validatePolicyCancelledForCriticalReasons(int policyState, boolean isAgent, final RoadBlockExceptionContextEnum contextEnum) throws SingleIdActiveProductException {
		if (policyState == IState.AUTOPOLICY_CANCELLED_CRITICAL_RECENT || policyState == IState.AUTOPOLICY_CANCELLED_CRITICAL_OLD) {
			if (isAgent) {
				// BR1785 Only agents must be able to continue the quote process.
				throw new SingleIdActiveProductException(BR732_AGENT, contextEnum);
			}
			throw new SingleIdActiveProductException(BR732_CLIENT, contextEnum);
		}
	}

	/**
	 * Link quote to profile in a new transaction
	 *
	 * @param plpParty    the plp party
	 * @param cifClientId the cif client id
	 * @throws Exception the exception
	 */
	@Transactional
	public void linkQuoteToProfile(final Party plpParty, final Long cifClientId) {

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("Link Quotation(agreement #) %s to clientId %d", plpParty.getPolicyVersion().getInsurancePolicy().getAgreementNumber(), cifClientId));
		}

		plpParty.setCifClientId(cifClientId);
		this.partyService.persist(plpParty);
	}

	/**
	 * Gets the user account service.
	 *
	 * @return the user account service
	 */
	@Override
	public IUserAccountService getUserAccountService() {

		if (this.userAccountService == null) {
			this.userAccountService = AccessManagerServiceFactory.getInstance().getUserAccountService();
		}

		return this.userAccountService;
	}

	/**
	 * Confirm Application for province.
	 * This methos overrides the application code for certain province.
	 * NOTE: this is not very clean but has been the accepted method of doing so -pameunie-
	 *
	 * @param policyVersion {@link PolicyVersion}
	 * @param initApp       the initial {@link ApplicationEnum}
	 * @return
	 */
	protected ApplicationEnum confirmAppForProv(final PolicyVersion policyVersion, final ApplicationEnum initApp) {
		ApplicationEnum app = initApp;
		//Greypower is now Belair Alberta.  But when we match or create the client we must create it as Greypower
		ProvinceCodeEnum prov = policyVersion.getProvince();
		if (ApplicationEnum.BELAIR.equals(app) &&
				(ProvinceCodeEnum.ALBERTA.equals(prov) || ProvinceCodeEnum.BRITISH_COLUMBIA.equals(prov))) {
			app = ApplicationEnum.GREYPOWER;
		}
		return app;
	}

	/**
	 * Link tam account to the matched client cifId.
	 *
	 * @param secureDomain
	 * @param currentClientId the current client id
	 * @param newClientCifId  the new client id
	 * @param partner         the partner code
	 * @throws AccessManagerException an access manager exception
	 */
	private void linkTamAccountToNewClientId(final SecureDomain secureDomain, final Long currentClientId, final Long newClientCifId, String partner) throws AccessManagerException {

		// Try to find if the current client have a TAM account. if yes, update the cifId to the matched client.

		// Load the email of the current client.
		IClient currentClient = this.clientService.getClientById(currentClientId);
		IElectronicContact eContact = ClientHelper.getCurrentElectronicContact(currentClient);
		if (eContact != null && eContact.getElectronicAddress() != null) {

			// Get the user informations from TAM for the current user
			String partnerEmail = PartnerUtil.getEmailWithPartner(eContact.getElectronicAddress(), partner);
			User user = this.getUserAccountService().getUser(secureDomain, partnerEmail);

			// TAM user found
			if (user != null) {
				// Update the cifId of TAM current user to the matched client
				this.getUserAccountService().associateClient(secureDomain, partnerEmail, newClientCifId);
			}
		}
	}

	/**
	 * Add an electronic contact the new client and close the electronic contact from the current client.
	 *
	 * @param cifClient         the new client
	 * @param electronicContact the electronicContact from the current client
	 */
	@Transactional
	public void changeElectronicContactToAnOtherClient(final IClient cifClient, final IElectronicContact electronicContact) {
		// Add sanity check to cifClient to ensure it is initialized properly -- don't change if client is not defined
		if (cifClient != null) {
			IElectronicContact cifEContact = ClientFactory.createElectronicContact();
			cifEContact.setElectronicAddress(electronicContact.getElectronicAddress());
			cifEContact.setStartDate(new Date());
			// Default value of this not nullable field.
			cifEContact.setUsageCode(electronicContact.getUsageCode());
			cifEContact.setPreferredInd(electronicContact.getPreferredInd());
			cifEContact.setClient(cifClient);

			cifClient.addElectronicContacts(cifEContact);

			this.clientService.delete(electronicContact);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean hasPrefixedPartnerEmailAdress(SecureDomain secureDomain, String emailAdress) {
		Long tamClientId = null;
		try {
			String userId = String.format("%s.%s", PartnerUtil.SUN_PARTNER_PREFIX, emailAdress);
			tamClientId = this.getUserAccountService().getClientID(secureDomain, userId);
		} catch (AccessManagerException e) {
			// nothing to handle (tamClient doesn't exist and it is possible in this
			// context. We return null.
		}
		return tamClientId != null;
	}

	public static String[] findCSIONumbers(DistributorCodeEnum distributorCode, ApplicationEnum app) {
		// because BNA does not have any CSIO number defined
		String[] values = new String[]{};
		if (!DistributorCodeEnum.BNA.equals(distributorCode)) {
			values = CompanyUtils.getCsioNumbers(app.toString());
		}
		return values;
	}

	/**
	 * Creates the cif client for the Policy Holder
	 *
	 * @param policyVersion the policy version
	 * @return client
	 * @throws Exception the exception
	 */
	@Transactional
	/* Create CIF client in a new transaction */
	public IClient createCifClient(final PolicyVersion policyVersion, final ApplicationEnum application) {

		Long cifCientId = this.createProfile(policyVersion, application, null);
		IClient cifClient = this.clientService.getClientById(cifCientId);
		this.updateProfile(policyVersion, cifClient);
		return cifClient;
	}

	/**
	 * Creates the profile.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 * @param application    {@link ApplicationEnum}
	 * @param driverSequence the driver sequence
	 * @return the cif client id
	 */
	private Long createProfile(final PolicyVersion aPolicyVersion, final ApplicationEnum application, final Long driverSequence) {
		// Populate ClientCreationParameters from the policyVersion
		IClientCreationParameters params = this.populateForCreateProfile(aPolicyVersion, application, driverSequence);
		// Create the client in cif
		IClient cifClient = this.clientService.create(params);
		return cifClient.getClient();
	}

	/**
	 * Update profile.
	 *
	 * @param aPolicyVersion the a policy version
	 * @param cifClient      the cif client
	 * @return the i client
	 * @throws Exception the exception
	 */
	private IClient updateProfile(final PolicyVersion aPolicyVersion, final IClient cifClient) {

		// Update cifClient object from the policyVersion
		this.populateCifForUpdateProfile(aPolicyVersion, cifClient, null);
		// Persist the client in cif
		this.clientService.update(cifClient);
		return cifClient;
	}

	@Override
	public void updateProfile(PolicyVersion aPolicyVersion, Long cifClientId) throws AccessManagerException {

		if (cifClientId != null) {
			// update if :
			// it's QA and no product OR
			// it's QF and no product and no active quotes
			boolean hasProducts = this.singleIdService.hasActiveLifeAutoOrResidentialProduct(cifClientId);
			boolean hasActiveAutoQuotes = this.singleIdService.hasActiveAutoQuotes(cifClientId);
			if (!hasProducts) {
				if ((this.isQuickQuote(aPolicyVersion) && !hasActiveAutoQuotes) || !this.isQuickQuote(aPolicyVersion)) {
					this.updateCifClient(aPolicyVersion, cifClientId);
				}
			}
		} else {
			LOG.error(Logger.EVENT_SUCCESS, "updateProfileForQuickQuote : cifClientId is null, profile was not updated!");
		}
	}

	/**
	 * Update CIF client in a new transaction
	 *
	 * @param policyVersion the policy version
	 * @param clientId      the client id
	 * @throws Exception the exception
	 */
	@Transactional
	public void updateCifClient(final PolicyVersion policyVersion, final Long clientId) {

		IClient cifClient = this.clientService.getClientById(clientId);
		if (cifClient != null) {
			this.updateProfile(policyVersion, cifClient);
		} else {
			throw new SystemException("Unable to load cifClient with id " + clientId);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void populateCifForUpdateProfile(final PolicyVersion aPolicyVersion, final IClient aClient, final Long driverSequence) {

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, "BEGIN ProfileHelper.updateProfile");
		}

		Party plpParty = this.partyHelper.getNamedInsured(aPolicyVersion);
		Address plpAddress = this.partyHelper.getCurrentResidentialAddress(plpParty);

		if (driverSequence != null) {
			plpParty = this.partyHelper.getDriverBySequence(aPolicyVersion, driverSequence.intValue());
		} else {
			// First driver's license number
			DriverComplementInfo plpDCI = plpParty.getDriverComplementInfo();
			if (StringUtils.isNotEmpty(plpDCI.getLicenseNumber())) {
				IRegistration registration = ClientHelper.getDriverLicenseRegistration(aClient);

				// Create if null
				if (registration == null) {
					registration = new Registrations();
					registration.setStartDate(new Date());
					registration.setRegistrationType(this.clientService.findRegistrationTypeFromType(RegistrationTypeCodeEnum.DRIVER_LICENCE));
					aClient.addRegistration(registration);
				}

				if (registration.getExternalReference() == null || !registration.getExternalReference().equals(plpDCI.getLicenseNumber())) {
					registration.setExternalReference(plpDCI.getLicenseNumber());

					if (plpDCI.getLicenseJurisdiction() != null) {
						registration.setLocation(plpDCI.getLicenseJurisdiction().getCode());
					}
					registration.setLastUpdateTime(new Date());
				}
			}
		}

		// Get the client spoken language of the client.
		IClientSpokenLanguage clientSpokenLanguage = ClientHelper.getClientSpokenLanguage(aClient);
		if (clientSpokenLanguage == null) {
			clientSpokenLanguage = new ClientSpokenLanguages();
			clientSpokenLanguage.setPreferredInd("Y");
			aClient.addClientSpokenLanguage(clientSpokenLanguage);
		}

		// Update the language in the client spoken language
		if (aPolicyVersion.getLanguageOfCommunication().equals(LanguageCodeEnum.FRENCH)) {
			clientSpokenLanguage.setLanguageFrench();
		} else {
			clientSpokenLanguage.setLanguageEnglish();
		}

		// Name
		IName cifName = (IName) aClient.getNameses().toArray()[0];
		cifName.setFirstName(plpParty.getFirstName());
		cifName.setLastName(plpParty.getLastName());

		IBody cifBody = (IBody) aClient.getBodieses().toArray()[0];
		cifBody.setBirthDate(plpParty.getBirthDate());

		if (!this.isQuickQuote(aPolicyVersion)) {
			cifBody.setGender(plpParty.getSex().getCode());
		}

		// MaritalStatus
		if (plpParty.getMaritalStatus() != null) {
			switch (plpParty.getMaritalStatus()) {
				case SINGLE:
					cifBody.setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.SINGLE.getCode());
					break;
				case MARRIED:
					cifBody.setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.MARRIED.getCode());
					break;
				case COMMON_LAW:
					cifBody.setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.COMMON_LAW.getCode());
					break;
				default:
					cifBody.setMaritalStatus(com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.OTHER.getCode());
			}
		}
		cifBody.setMaritalStatus(getCIFMaritalStatusCode(plpParty.getMaritalStatus()));

		// Consent
		Consent consent = this.consentHelper.getConsent(plpParty, ConsentTypeCodeEnum.CREDIT_SCORE);
		if (consent != null) {
			aClient.setCreditScoreConsentInd(consent.getConsentIndicator() ? "Y" : "N");
			aClient.setCreditScoreConsentDate(consent.getEffectiveDate());
		}

		String transactionalPaperInd = obtainTransactionalPaperInd(aPolicyVersion);
		aClient.setTransactionalPaperInd(transactionalPaperInd);

		// Get the CIF address
		IAddress cifAddress = ClientHelper.getCurrentAddress(aClient);

		// Create a new address if there is no valid residential address
		if (cifAddress == null) {

			cifAddress = ClientFactory.createAddress();

			/** ADDRESS : populate DEFAULT value * */
			cifAddress.setAddressType(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode());
			cifAddress.setUsageCode(AddressUsageCodeEnum.ADDRESS_USED_FOR_THE_MAILING.getCode());
			cifAddress.setMailTypeCode(ProfileService.MAIL_TYPE_CODE_PO);
		}

		/** ADDRESS : populate cifAddress from plpAddress **/
		if (!this.isQuickQuote(aPolicyVersion)) {
			cifAddress.setCivicNo(plpAddress.getCivicNumber());
			cifAddress.setStreetName(plpAddress.getStreetName());
			cifAddress.setUnitNbr(plpAddress.getUnitNumber());
			cifAddress.setCity(plpAddress.getMunicipality());
			cifAddress.setProvinceState(plpAddress.getProvince() != null ? plpAddress.getProvince().getCode(): null);
			cifAddress.setCountry(plpAddress.getCountry() != null ? plpAddress.getCountry().getCode(): null);
			cifAddress.setMailCode(plpAddress.getPostalCode());
			// We don't have civic number and street name in Quick Quote. So don't need to generate addr_linexx
			this.formatCifAddress(cifAddress, aPolicyVersion.getLanguageOfCommunication());
		}

		// Occupation
		GroupRepositoryEntry groupRepositoryEntry = this.partyHelper.findGroupRepositoryEntry(plpParty,
				PartyGroupTypeCodeEnum.DOMAIN);
		if (groupRepositoryEntry != null) {
			IGeneral cifGeneral = ClientHelper.getCurrentGeneral(aClient);
			if (aPolicyVersion.getLanguageOfCommunication().equals(LanguageCodeEnum.FRENCH)) {
				if (StringUtils.isNotBlank(groupRepositoryEntry.getPartyGroupDescriptionFrench())) {
					cifGeneral.setOccupation(groupRepositoryEntry.getPartyGroupDescriptionFrench());
				} else {
					cifGeneral.setOccupation(groupRepositoryEntry.getPartySubGroupDescriptionFrench());
				}

			} else {
				if (StringUtils.isNotBlank(groupRepositoryEntry.getPartyGroupDescriptionEnglish())) {
					cifGeneral.setOccupation(groupRepositoryEntry.getPartyGroupDescriptionEnglish());
				} else {
					cifGeneral.setOccupation(groupRepositoryEntry.getPartySubGroupDescriptionEnglish());
				}
			}
		}

		// Quick Quote
		if (this.isQuickQuote(aPolicyVersion)) {
			// Employer - Work Sector (Logic to be validated)
			Collection<IGeneral> generals = aClient.getGenerals();
			if (!CollectionUtils.isEmpty(generals)) {
				IGeneral general = generals.iterator().next();
				general.setEmployerName(this.partyHelper.getInsuredGroup(plpParty));
			}

			if (aPolicyVersion.getAffinityGroupCode() != null) {
				// Group
				Collection<IClientGroup> clientGroups = aClient.getClientGroups();
				// If client has already existing groups, set the endate to today for each one
				if (!CollectionUtils.isEmpty(clientGroups)) {
					for (IClientGroup clientGroup : clientGroups) {
						if (clientGroup.getEndDate() == null) {
							clientGroup.setEndDate(new Date());
						}
					}
				}
				IGroup groupDB = this.clientService.findGroupFromType(aPolicyVersion.getAffinityGroupCode());
				if (groupDB != null) {
					IClientGroup clientGroup = new ClientGroups();
					clientGroup.setGroup(groupDB);
					clientGroup.setStartDate(new Date());
					clientGroup.setCreationDate(new Date());
					clientGroup.setClient(aClient);
					aClient.addClientGroup(clientGroup);
				}
			}

			// PHONE/MARKETING CONSIGNT
			Phone phone = this.partyHelper.getCurrentHomePhone(plpParty);
			if (phone != null) {
				//***** PHONE
				String pNumber = phone.getPhoneNumber();
				IPhoneNumber currentPhoneNumber = ClientHelper.getCurrentHomePhoneNumber(aClient);
				if (currentPhoneNumber != null) {
					currentPhoneNumber.setEndDate(new Date());// deactivate current home phone before replacing it
				}
				ClientHelper.addPhone(aClient, PhoneTypeCodeEnum.HOME_PHONE, phone.getPhoneAreaCode(), pNumber.substring(0, 3), pNumber.substring(3), null);
			}
			Phone cellphone = this.partyHelper.getCurrentCellPhone(plpParty);
			if (cellphone != null) {
				String cNumber = cellphone.getPhoneNumber();
				IPhoneNumber currentCellPhone = ClientHelper.getCurrentPhoneNumberByType(aClient, PhoneTypeCodeEnum.CELLULAR_PHONE);
				if (currentCellPhone != null) {
					currentCellPhone.setEndDate(new Date());// deactivate current home phone before replacing it
				}
				ClientHelper.addPhone(aClient, PhoneTypeCodeEnum.CELLULAR_PHONE, cellphone.getPhoneAreaCode(), cNumber.substring(0, 3), cNumber.substring(3), null);
			}

			if (phone != null || cellphone != null) {
				//***** MARKETING CONSIGNT
				IPrivacy oldPrivacy = ClientHelper.getCurrentPrivacy(aClient);
				// Set a endDate to the current privacy if one exist
				if (oldPrivacy != null) {
					oldPrivacy.setEndDate(new Date());
				}

				// EC = Explicit consent
				// NS = Do not solicit
				boolean marketingConsentInd = true;
				String solicitationIndicator = "NS";
				if (org.apache.commons.lang3.BooleanUtils.isTrue(marketingConsentInd)) {
					solicitationIndicator = "EC";
				}

				// Create a new one
				IPrivacy newPrivacy = new Privacies();
				newPrivacy.setClient(aClient);
				newPrivacy.setCliClientCreatedBy(aClient);
				newPrivacy.setCreationDate(new Date());
				newPrivacy.setStartDate(new Date());
				newPrivacy.setSolicitationInd(solicitationIndicator);

				String consentInd = org.apache.commons.lang3.BooleanUtils.isTrue(marketingConsentInd) ? "Y" : "N";
				newPrivacy.setSolicitationByPhoneInd(consentInd);
				newPrivacy.setSolicitationByMailInd(consentInd);
				newPrivacy.setSolicitationByEmailInd(consentInd);
				aClient.addPrivacy(newPrivacy);
			}
		}

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, "END ProfileHelper.updateProfile");
		}

	}

	/*
	 * transactionalPaperInd in CIF is allowed to be null (signifying no preference selected yet)
	 * therefore if any object in the chain policyversion->transactionalPaperInd is null,
	 * return null, otherwise return Y for true and N for false.
	 */
	protected static String obtainTransactionalPaperInd(final PolicyVersion aPolicyVersion) {
		String transactionalPaperIndValue = null;
		if (null != aPolicyVersion) {
			BusinessTransaction businessTransaction = aPolicyVersion.getBusinessTransaction();
			if (null != businessTransaction) {
				Boolean transactionalPaperInd = businessTransaction.getTransactionalPaperIndicator();
				if (null != transactionalPaperInd) {
					transactionalPaperIndValue = transactionalPaperInd ? CIF_PAPER_IND_VALUE_YES : CIF_PAPER_IND_VALUE_NO;
				}
			}
		}
		return transactionalPaperIndValue;
	}

	@Override
	public boolean isQuickQuote(final PolicyVersion aPolicyVersion) {
		return ApplicationModeEnum.QUICK_QUOTE.equals(aPolicyVersion.getInsurancePolicy().getApplicationMode());
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IClientCreationParameters populateForCreateProfile(final PolicyVersion aPolicyVersion, final ApplicationEnum company, final Long driverSequence) {
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, "BEGIN ProfileHelper.createProfile");
		}

		Party plpParty = this.partyHelper.getNamedInsured(aPolicyVersion);
		Address plpAddress = this.partyHelper.getCurrentResidentialAddress(plpParty);

		if (driverSequence != null) {
			plpParty = this.partyHelper.getDriverBySequence(aPolicyVersion, driverSequence.intValue());
		}

		IClientCreationParameters params = new ClientCreationParameters();

		/** Client : populate params from plpParty * */
		params.setFirstName(plpParty.getFirstName());
		params.setLastName(plpParty.getLastName());
		params.setBirthDate(plpParty.getBirthDate());
		params.setGender(plpParty.getSex().getCode());
		params.setMaritalStatus(getCIFMaritalStatusCode(plpParty.getMaritalStatus()));

		/* Client : MANDATORY values */
		params.setClientType(ProfileService.CLIENT_TYPE_INDIVIDUAL);
		params.setClientGroup(ProfileService.CLIENT_GROUP_02);
		params.setLanguage(aPolicyVersion.getLanguageOfCommunication().equals(LanguageCodeEnum.FRENCH) ? "FR" : "EN");
		params.setOfficialLanguage(params.getLanguage());

		IAddress cifAddress = ClientFactory.createAddress();
		/* ADDRESS : populate cifAddress from plpAddress */
		cifAddress.setCivicNo(plpAddress.getCivicNumber());
		cifAddress.setStreetName(plpAddress.getStreetName());
		cifAddress.setUnitNbr(plpAddress.getUnitNumber());
		cifAddress.setCity(plpAddress.getMunicipality());
		cifAddress.setProvinceState(plpAddress.getProvince().getCode());
		cifAddress.setCountry(plpAddress.getCountry().getCode());
		cifAddress.setMailCode(plpAddress.getPostalCode());
		cifAddress.setStartDate(plpAddress.getEffectiveDate());
		cifAddress.setStructureInd(Addresses.STRUCTURED_ADDRESS_IND);

		// We don't have civic number and street name in quickquote. So don't need to generate addr_linexx
		if (!this.isQuickQuote(aPolicyVersion)) {
			this.formatCifAddress(cifAddress, aPolicyVersion.getLanguageOfCommunication());
		}
		/* ADDRESS : populate DEFAULT value */
		cifAddress.setAddressType(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS.getCode());
		cifAddress.setUsageCode(AddressUsageCodeEnum.ADDRESS_USED_FOR_THE_MAILING.getCode());
		cifAddress.setMailTypeCode(ProfileService.MAIL_TYPE_CODE_PO);
		params.setAddress(cifAddress);

		ProvinceCodeEnum prov = aPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince();
		String companyCode = CompanyUtils.getCsioNumber(company.toString(), prov.getCode());

		params.setCsioNumber(companyCode);
		// Quick Quote changes BR447.2
		if (this.isQuickQuote(aPolicyVersion)) {
			params.setOccupation(this.getOccupation(aPolicyVersion, plpParty));
			// EmployerName
			params.setEmployerName(this.partyHelper.getInsuredGroup(plpParty));

			// ClientGroup and ClientType - set at line 176 & 177
			// Street - Already set line 182
		}

		/*
		 * NOTE: For CIF legacy reason when Application is Grewypower THEN Company/CSIO number is GP
		 *       THEN distributor must be GP otherwise client creation fails. i.e. GP - GP
		 */
		String directChanDistributor = null;
		if (ApplicationEnum.GREYPOWER.equals(company)) {
			directChanDistributor = companyCode;
		} else {
			directChanDistributor = aPolicyVersion.getDistributorCode() != null ? aPolicyVersion.getDistributorCode().getCode() : null;
		}
		params.setDirectChanDistributor(directChanDistributor);

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format("create profile with params=%s", params.toJSON()));
			LOG.debug(Logger.EVENT_SUCCESS, "END ProfileHelper.createProfile");
		}
		return params;
	}

	public static String getCIFMaritalStatusCode(MaritalStatusCodeEnum plpMaritalStatus) {
		// RHEM-49 marital status now available for other drivers
		String maritalStatus = "";
		if (plpMaritalStatus != null) {
			switch (plpMaritalStatus) {
				case SINGLE:
					maritalStatus = com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.SINGLE.getCode();
					break;
				case MARRIED:
					maritalStatus = com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.MARRIED.getCode();
					break;
				case COMMON_LAW:
					maritalStatus = com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.COMMON_LAW.getCode();
					break;
				default:
					maritalStatus = com.ing.canada.cif.domain.enums.MaritalStatusCodeEnum.OTHER.getCode();
			}
		}
		return maritalStatus;
	}

	/**
	 * This method format the addressLine1 and addressLine2 base on the value include in the param anAddress. The fields
	 * civicNo and StreetName are mandatory, appartment is not.
	 * <p>
	 * This method also sets the address.structureInd to Y and the address.unitType when available.
	 *
	 * @param anAddress The party address
	 * @param aLanguage The party Language
	 */
	@Override
	public void formatCifAddress(final IAddress anAddress, final LanguageCodeEnum aLanguage) {

		if (StringUtils.isEmpty(anAddress.getCivicNo())) {
			throw new IllegalArgumentException("CivicNo is empty or null");
		}
		if (StringUtils.isEmpty(anAddress.getStreetName())) {
			throw new IllegalArgumentException("StreetName is empty or null");
		}

		// Always truncate to the Classic max size
		String adr = anAddress.getCivicNo() + " " + anAddress.getStreetName();
		anAddress.setAddrLine1(adr.length() > ProfileService.MAX_ADR_LENGTH ? adr.substring(0, ProfileService.MAX_ADR_LENGTH) : adr);

		if (StringUtils.isNotEmpty(anAddress.getUnitNbr())) {
			String apt = (aLanguage.isFrench() ? "APP. " : "APT. ") + anAddress.getUnitNbr();

			if (anAddress.getAddrLine1().length() + apt.length() > ProfileService.MAX_ADR_LENGTH) {
				anAddress.setAddrLine2(apt.toString());
			} else {
				anAddress.setAddrLine1(anAddress.getAddrLine1() + " " + apt);
			}
			anAddress.setUnitType(Addresses.UNIT_TYPE_APARTMENT);
		}

		anAddress.setStructureInd(Addresses.STRUCTURED_ADDRESS_IND);
	}

	/**
	 * Gets the occupation based on policy version and party.
	 */
	private String getOccupation(final PolicyVersion aPolicyVersion, Party plpParty) {

		String result = null;
		GroupRepositoryEntry groupRepositoryEntry = this.partyHelper.findGroupRepositoryEntry(plpParty, PartyGroupTypeCodeEnum.DOMAIN);
		if (groupRepositoryEntry != null) {
			if (aPolicyVersion.getLanguageOfCommunication().equals(LanguageCodeEnum.FRENCH)) {
				if (StringUtils.isNotBlank(groupRepositoryEntry.getPartyGroupDescriptionFrench())) {
					result = groupRepositoryEntry.getPartyGroupDescriptionFrench();
				} else {
					result = groupRepositoryEntry.getPartySubGroupDescriptionFrench();
				}
			} else {
				if (StringUtils.isNotBlank(groupRepositoryEntry.getPartyGroupDescriptionEnglish())) {
					result = groupRepositoryEntry.getPartyGroupDescriptionEnglish();
				} else {
					result = groupRepositoryEntry.getPartySubGroupDescriptionEnglish();
				}
			}
		}
		return result;
	}

	@Override
	public void manageProfile(final Long aPolicyVersionId, final RoadBlockExceptionContextEnum contextEnum, final ApplicationEnum application) throws AutoQuoteRoadBlockException, SingleIdActiveProductException {
		throw new NotImplementedException("This method is not implemented");
	}

}
