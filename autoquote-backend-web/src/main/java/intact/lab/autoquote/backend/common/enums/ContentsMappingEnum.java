package intact.lab.autoquote.backend.common.enums;

/**
 * The Enum ResponsePriceMappingEnum.
 */
public enum ContentsMappingEnum {
    LABELS("LABELS"),
    YEARS("year"),
    ANNUAL_KM("annualKm"),
    TYPE_OF_LICENCE("typeOfLicence"),
    YEARS_WITH_CURRENT_INSURER("yearsWithCurrentInsurer"),
    YEARS_INSURED_AS_PRINCIPAL("yearsInsuredAsPrincipal"),
    WORK_SECTOR("workSector"),
    YEARS_AT_THIS_ADRESS("yearsAtThisAddress"),
    OTHER_ANTI_THEFT_DEVICE_INDICATOR("otherAntiTheftDeviceIndicator"),
    TRACKING_SYSTEM("trackingSystem"),
    MONTH_INSURED_WITH_OUR_COMPANY("monthsInsuredWithOurCompany"),
    BUSINESS_KM("businessKm"),
    MARITAL_STATUS("maritalStatus"),
    DATE1("date1"),
    DATE2("date2"),
    HOME_AND_AUTO_IND("homeAndAutoInd"),
    TYPE1("type1"),
    TYPE2("type2"),
    INSURANCE_CARRIERS("currentInsurer"),
    LOSSES_YEARS_IND("lossesYearsInd"),
    MINOR_INFRACTION_COUNT("minorInfractionCount"),
    DISTRACTED_DRIVING("distractedDriving"),
    MINOR_INFRACTION_YEARS("convictionNatureYear"),
    YN("YN"),
    GROUPE_CODE("groupeCode"),
    OCCUPATION("occupation"),
    GENDER("gender"),
    LAST_CONVICTION_YEARS("lastConvictionYear"),
    MONTHS("months");

    /*
     * The json id.
     */
    private String contentId = null;

    /*
     * Instantiates a new response price mapping enum.
     */
    private ContentsMappingEnum(String contentId) {
        this.contentId = contentId;
    }
}
