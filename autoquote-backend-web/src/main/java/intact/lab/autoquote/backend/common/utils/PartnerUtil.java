/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * Partnership helper class.
 *
 * <AUTHOR>
 */

@Component
public class PartnerUtil {

	public static final String SUN_PARTNER_PREFIX = "belair-slf";

	/**
	 * This method add the partner at the end of the email.  Exemple, for the partner belair-slf, the
	 * return email <NAME_EMAIL>-slf
	 *
	 * @param email the email
	 * @param partner the partner code
	 * @return the email for the partner
	 */
	public static String getEmailWithPartner(String email, String partner){
		if (StringUtils.isBlank(email)){
			return null;
		}


		if (StringUtils.isNotBlank(partner)){

			// TODO : pmdroz
			//Depuis le début du projet 5333, autoquote a été codé avec SLF comme valeur pour le partner
			//sunlife. étant donné que le WAA, le WEP et le email utilise belair-slf nous allons le changer
			//dans autoquote aussi.  en attente de patrick pour le changement.
			if (StringUtils.equals("SLF", partner.toUpperCase())){

				//Validate if the email already contains the suffix
				if (email.toLowerCase().startsWith(SUN_PARTNER_PREFIX)){
					return email;
				}
				return String.format("%s.%s", SUN_PARTNER_PREFIX, email);
			}
		}
		return email;
	}
}
