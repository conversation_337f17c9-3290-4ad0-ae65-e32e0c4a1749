package intact.lab.autoquote.backend.config;

import com.ing.canada.common.util.localizedcontext.LocalizedContextInjector;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;

@Component
public class LocalizedContextStartupListenerConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext() instanceof WebApplicationContext) {
            LocalizedContextInjector.inject(event.getApplicationContext());
        }
    }
}
