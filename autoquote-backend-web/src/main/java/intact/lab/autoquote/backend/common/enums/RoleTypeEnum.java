package intact.lab.autoquote.backend.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum RoleTypeEnum {

    PRINCIPAL_DRIVER("PRINCIPAL_DRIVER"),
    OCCASIONAL_DRIVER("OCCASIONAL_DRIVER"),
    REGISTERED_OWNER("REGISTERED_OWNER"),
    BUSINESS_OWNER("BUSINESS_OWNER");

    private final String code;

    @Override
    public String toString() {
        return "[code=" + this.getCode() + "]";
    }
}
