package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromBasicCoverage;
import com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromNonBasicCoverage;
import com.ing.canada.plp.domain.insurancerisk.RatingRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoveragePremiumOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.RatingRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.services.rating.IRatingServiceHelper;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.io.OutputStream;

import static org.owasp.esapi.Logger.EVENT_UNSPECIFIED;

@Component
public class RatingServiceHelper implements IRatingServiceHelper {

  private static final Logger LOG = ESAPI.getLogger(RatingServiceHelper.class);

  @Override
  public void printPolicyCoverage(final PolicyVersion policyVersion, final boolean beforeDeviation, OutputStream output, final OfferTypeCodeEnum aReferenceOfferType) {
    // default to class logger when calling deprecated method
    this.printPolicyCoverage(policyVersion, beforeDeviation, LOG, aReferenceOfferType);
  }

  @Override
  public void printPolicyCoverage(final PolicyVersion policyVersion, final boolean beforeDeviation, final Logger log, final OfferTypeCodeEnum aReferenceOfferType) {
    if (beforeDeviation) {
      log.debug(EVENT_UNSPECIFIED, ">>> BEFORE DEVIATION");
    } else {
      log.debug(EVENT_UNSPECIFIED, ">>> AFTER DEVIATION");
    }

    for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
      for (InsuranceRiskOffer insuranceRiskOffer : insuranceRisk.getInsuranceRiskOffers()) {
        if (!beforeDeviation || insuranceRiskOffer.getOfferType().equals(aReferenceOfferType)) {
          log.debug(EVENT_UNSPECIFIED, "*******************************************");
          log.debug(EVENT_UNSPECIFIED, " Risk #= " + insuranceRisk.getInsuranceRiskSequence());
          log.debug(EVENT_UNSPECIFIED, " RiskOffer #= " + insuranceRiskOffer.getId());
          log.debug(EVENT_UNSPECIFIED, " Offer Type = " + insuranceRiskOffer.getOfferType());
          log.debug(EVENT_UNSPECIFIED, " Premium = " + insuranceRiskOffer.getAnnualPremium());
          log.debug(EVENT_UNSPECIFIED, "*******************************************");
          for (RatingRiskOffer rro : insuranceRiskOffer.getRatingRiskOffers()) {
            log.debug(EVENT_UNSPECIFIED, " - For rating risk : " + rro.getRatingRiskType());

            for (CoveragePremiumOffer covo : rro.getCoveragePremiumOffers()) {
              CoverageOffer cov = covo.getCoverageOffer();
              Integer amount = cov.getDeductibleAmount() != null ? cov.getDeductibleAmount() : cov.getLimitOfInsurance();
              String annualPremi = covo.getAnnualPremium() != null ? Double.toString(covo.getAnnualPremium()) : "null";
              log.debug(EVENT_UNSPECIFIED, String.format("     - Coverage :%s[%d] = %s $", cov.getCoverageRepositoryEntry().getCoverageCode(), amount, annualPremi));
            }
          }
        }
      }
    }
  }

  @Override
  public void printFactors(final PolicyVersion policyVersion, final Logger log) {
    for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
      log.debug(EVENT_UNSPECIFIED, "*****************************");
      log.debug(EVENT_UNSPECIFIED, "*      Factor listing       *");
      log.debug(EVENT_UNSPECIFIED, "*****************************");
      log.debug(EVENT_UNSPECIFIED, "Insurance risk #" + insuranceRisk.getInsuranceRiskSequence());
      for (RatingRisk ratingRisk : insuranceRisk.getRatingRisks()) {
        log.debug(EVENT_UNSPECIFIED, "   RatingRisk Type:" + ratingRisk.getRatingRiskType());
        log.debug(EVENT_UNSPECIFIED, "   ---------------------------------");
        for (MultiplicativeRatingFactorFromBasicCoverage mrf : ratingRisk.getMultiplicativeRatingFactorFromBasicCoverages()) {
          StringBuilder f = new StringBuilder();
          f.append("Factor : code=").append(mrf.getBasicCoverageCode())
              .append(",condition=").append(mrf.getConditionType())
              .append(",applyCond=").append(mrf.getRatingFactorApplyCondition())
              .append(",type=").append(mrf.getRatingFactorType())
              .append(",calcMethod=").append(mrf.getRatingFactorCalculationMethod());
          if (mrf.getLimitOfInsuranceAmount() != null) {
            f.append(",limit=").append(mrf.getLimitOfInsuranceAmount());
          } else if (mrf.getDeductibleAmount() != null) {
            f.append(",deduc=").append(mrf.getDeductibleAmount());
          } else if (mrf.getLimitMedicalExpensesPerPersonAmount() != null) {
            f.append(",medExpPerPerson=").append(mrf.getLimitMedicalExpensesPerPersonAmount());
          } else if (mrf.getLimitMutilationDeathIndemnityAmount() != null) {
            f.append(",limMutilDeathIndem=").append(mrf.getLimitMutilationDeathIndemnityAmount());
          } else if (mrf.getWeeklyBenefitsAmount() != null) {
            f.append(",weeklyBenef=").append(mrf.getWeeklyBenefitsAmount());
          }
          f.append(",         Factor value=").append(mrf.getMultiplicativeRatingFactor());
          log.debug(EVENT_UNSPECIFIED, f.toString());
        }

        for (MultiplicativeRatingFactorFromNonBasicCoverage mrf : ratingRisk.getMultiplicativeRatingFactorFromNonBasicCoverages()) {
          StringBuilder f = new StringBuilder("Factor : condition=").append(mrf.getCondition())
              .append(",applyCond=").append(mrf.getRatingFactorApplyCondition());
          if (mrf.getLiabilityFactor() != null) {
            f.append(String.format(",liabilityFactor=%f", mrf.getLiabilityFactor()));
          } else if (mrf.getLiabilityBodilyInjuryFactor() != null) {
            f.append(String.format(",liabBodyFactor=%f", mrf.getLiabilityBodilyInjuryFactor()));
          } else if (mrf.getLiabilityPropertyDamageFactor() != null) {
            f.append(String.format(",liabPropertyFactor=%f", mrf.getLiabilityPropertyDamageFactor()));
          } else if (mrf.getCollisionFactor() != null) {
            f.append(String.format(",collFactor=%f", mrf.getCollisionFactor()));
          } else if (mrf.getComprehensiveFactor() != null) {
            f.append(String.format(",compFactor=%f", mrf.getComprehensiveFactor()));
          } else if (mrf.getMedicalExpensesFactor() != null) {
            f.append(String.format(",medExpPerPersonFactor=%f", mrf.getMedicalExpensesFactor()));
          } else if (mrf.getTotalDisabilityFactor() != null) {
            f.append(String.format(",totalDisabFactor=%f", mrf.getTotalDisabilityFactor()));
          } else if (mrf.getAccidentBenefitFactor() != null) {
            f.append(String.format(",accidentBenefFactor=%f", mrf.getAccidentBenefitFactor()));
          } else if (mrf.getFixedAmount() != null) {
            f.append(String.format(",fixeAmountFactor=%d", mrf.getFixedAmount()));
          }
          f.append(",  factType=").append(mrf.getRatingFactorType());
          log.debug(EVENT_UNSPECIFIED, f.toString());
        }
      }
    }
  }
}
