/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.common.domain.AffinityGroupCode;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.enums.ApplicationIdEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyGroup;
import com.ing.canada.plp.domain.policyversion.PolicyAdditionalCoverage;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPolicyAdditionalCoverageService;
import com.ing.canada.plp.service.IPolicyVersionService;
import intact.lab.autoquote.backend.services.ICloneService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Set;

/**
 * Service that will verify if a clone is needed and do the clone if necesary.
 *
 * <AUTHOR>
 */
@Component
public class CloneService implements ICloneService {

	/** The policy version service. */
	protected IPolicyVersionService policyVersionService;
	private final IPolicyVersionHelper policyVersionHelper;
	private final IAffinityGroupService affinityGroupService;
	private final IPolicyAdditionalCoverageService policyAdditionalCoverageService;

	public CloneService(IPolicyVersionService iPolicyVersionService, IPolicyVersionHelper iPolicyVersionHelper,
						IAffinityGroupService iAffinityGroupService, IPolicyAdditionalCoverageService policyAdditionalCoverageService) {
		this.policyVersionService = iPolicyVersionService;
		this.policyVersionHelper = iPolicyVersionHelper;
		this.affinityGroupService = iAffinityGroupService;
		this.policyAdditionalCoverageService = policyAdditionalCoverageService;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public PolicyVersion clone(PolicyVersion oPolicy, String cifClientId, String marketingPromotionCode) {
		return this.clonePolicyVersion(oPolicy, cifClientId, UserTypeCodeEnum.CLIENT, marketingPromotionCode);
	}

	/**
	 * Clones the current working policy into a new PolicyVersion.
	 *
	 * @return the new PolicyVersion clone
	 * */
	private PolicyVersion clonePolicyVersion(PolicyVersion originalPolicyVersion, String cifClientId, UserTypeCodeEnum userType, String marketingPromotionCode) {
		BusinessTransaction bt = originalPolicyVersion.getBusinessTransaction();

		Long pvWorkingId = this.policyVersionService.clone(originalPolicyVersion, Boolean.FALSE, Boolean.FALSE, userType, cifClientId, ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE,
				BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED);

		PolicyVersion clonedPolicyVersion = this.policyVersionService.findById(pvWorkingId);
		this.clearInsuranceRiskOnClaims(clonedPolicyVersion);
		clonedPolicyVersion.setMarketingPromotionCode(marketingPromotionCode);
		this.setAffinityOnPartyGroup(clonedPolicyVersion);

		// Fix for null TransactionEffectiveDateTime
		BusinessTransaction btClone = clonedPolicyVersion.getBusinessTransaction();
		btClone.setTransactionEffectiveDateTime(bt.getTransactionEffectiveDateTime());

		this.removeAdditionnalCoverage(clonedPolicyVersion);

		this.clearRatingValues(clonedPolicyVersion);

		return clonedPolicyVersion;
	}

	/**
	 * Removes all policyAdditionalCoverage from the policy since this is not done during the clone (maybee because of
	 * policyChange)
	 *
	 * @param oPolicy Policy Version
	 */
	protected void removeAdditionnalCoverage(PolicyVersion oPolicy) {
		while (!oPolicy.getPolicyAdditionalCoverages().isEmpty()) {
			PolicyAdditionalCoverage aditionalCoverage = oPolicy.getPolicyAdditionalCoverages().iterator().next();
			oPolicy.removePolicyAdditionalCoverage(aditionalCoverage);
			this.policyAdditionalCoverageService.delete(aditionalCoverage);
		}
	}

	/**
	 * get the affinity group and reassign it to the right partygroup. This should be done inside the oracle clone but
	 * for now we hack it in the code
	 *
	 * @param oPolicy Policy Version
	 */
	protected void setAffinityOnPartyGroup(PolicyVersion oPolicy) {
		if (oPolicy.getAffinityGroupRepositoryEntry() != null) {
			ProvinceCodeEnum provinceCodeEnum = oPolicy.getInsurancePolicy().getManufacturingContext().getProvince();

			PolicyHolder policyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(oPolicy);
			String affinityGroupCode = oPolicy.getAffinityGroupRepositoryEntry().getAffinityGroupCode().toUpperCase();

			Province province = null;

			if (ProvinceCodeEnum.QUEBEC.equals(provinceCodeEnum)) {
				province = Province.QUEBEC;
			} else if (ProvinceCodeEnum.ONTARIO.equals(provinceCodeEnum)) {
				province = Province.ONTARIO;
			} else if (ProvinceCodeEnum.BRITISH_COLUMBIA.equals(provinceCodeEnum)) {
				province = Province.BRITISH_COLUMBIA;
			} else if (ProvinceCodeEnum.ALBERTA.equals(provinceCodeEnum)) {
				province = Province.ALBERTA;
			}

			AffinityGroupCode affinityGroup = this.affinityGroupService.getAffinityGroupCode(province, affinityGroupCode, new Date(), oPolicy.getInsurancePolicy().getManufacturingContext()
					.getDistributionChannel(), oPolicy.getInsurancePolicy().getManufacturingContext().getInsuranceBusiness(), oPolicy.getDistributorCode());

			for (PartyGroup partyGroup : policyHolder.getParty().getPartyGroups()) {
				GroupRepositoryEntry gre = partyGroup.getGroupRepositoryEntry();
				if (gre != null) {
					// group codes are set as EMPLOYER type
					if (PartyGroupTypeCodeEnum.EMPLOYER.equals(gre.getPartyGroupType()) && gre.getPartyGroupCode().equals(affinityGroup.getPartyGroupCode())) {
						partyGroup.setAffinityGroupRepositoryEntry(oPolicy.getAffinityGroupRepositoryEntry());
					}
				}
			}
		}
	}

	/**
	 * Remove any reference of claims to insurance risks.
	 *
	 * @param aPolicyVersion the policy version
	 */
	protected void clearInsuranceRiskOnClaims(PolicyVersion aPolicyVersion) {
		Set<Party> parties = this.policyVersionHelper.getIndividualParties(aPolicyVersion);

		for (Party aParty : parties) {
			Set<Claim> claims = aParty.getClaims();

			for (Claim aClaim : claims) {
				aClaim.setInsuranceRisk(null);
			}
		}
	}

	/**
	 * We are only clearing the retention band following Nathalie St-jean's recommendation
	 *
	 * @param aPolicyVersion Policy Version
	 */
	protected void clearRatingValues(PolicyVersion aPolicyVersion) {

		for (InsuranceRisk ir : aPolicyVersion.getInsuranceRisks()) {
			ir.setRetentionBand(null);
		}
	}
}
