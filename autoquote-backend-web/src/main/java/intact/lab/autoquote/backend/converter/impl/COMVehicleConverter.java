package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.offer.ComOffer;
import com.intact.com.vehicle.ComVehicle;
import com.intact.com.vehicle.ComVehicleModel;
import intact.lab.autoquote.backend.common.dto.OfferDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.enums.TrackingSystemCodeEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("comVehicleConverter")
public class COMVehicleConverter implements ICOMConverter<VehicleDTO, ComVehicle> {

	@Resource(name = "comOfferConverter")
	private ICOMConverter<OfferDTO, ComOffer> comOfferConverter;

	@Override
	public VehicleDTO toDTO(ComVehicle comVehicle) {
		VehicleDTO vehicleDTO = new VehicleDTO();
		if (comVehicle != null) {
			vehicleDTO.setId(comVehicle.getWebMsgId());
			vehicleDTO.setYear(Integer.valueOf(comVehicle.getYear()));
			vehicleDTO.setKmPerYear(!StringUtils.isEmpty(comVehicle.getAnnualKmDriven()) && !"null".equals(comVehicle.getAnnualKmDriven()) ? Integer.valueOf(comVehicle.getAnnualKmDriven()) : null);
			vehicleDTO.setMake(comVehicle.getVehicleModel().getMake());
			vehicleDTO.setModel(comVehicle.getVehicleModel().getModel());
			vehicleDTO.setModelCode(comVehicle.getVehicleModel().getCode());
			vehicleDTO.setTrackingSystemInd(BooleanUtils.toBoolean(comVehicle.getTrackingSystem()) ? Boolean.TRUE : Boolean.FALSE); //SV
			vehicleDTO.setTrackingSystem(StringUtils.isNotEmpty(comVehicle.getTrackingSystemCode()) ? TrackingSystemCodeEnum.valueOfCode(comVehicle.getTrackingSystemCode()) : null);

			vehicleDTO.setNormalRadiusKm(comVehicle.getNormalRadiusKm()); //SV - QC
			vehicleDTO.setGrossVehicleWeight(comVehicle.getGrossVehicleWeightQty());//SV
			vehicleDTO.setUseOfVehicleCode(comVehicle.getPrimaryVehicleUsage());//SV
			vehicleDTO.setMidHaulingDaysPerMonth(comVehicle.getMidHaulingDaysPerMonth());
			vehicleDTO.setMultiVehicleDiscountEligibilityInd(comVehicle.getMultiVehicleDiscountEligibilityInd());
			vehicleDTO.setMaximumRadiusKm(comVehicle.getMaximumRadiusKm());//SV - ON

			vehicleDTO.setCommercialUsageCategoryCd(comVehicle.getCommercialUsageCategoryCd());
			vehicleDTO.setCommercialUsageCd(comVehicle.getCommercialUsageCd());
			vehicleDTO.setCommercialUsageSpecificCd(comVehicle.getCommercialUsageSpecificCd());

			// set coverages
			List<OfferDTO> offers = new ArrayList<>();
			for (ComOffer comOffer : comVehicle.getOfferSet()) {
				offers.add(this.comOfferConverter.toDTO(comOffer));
			}
			vehicleDTO.setOffers(offers);
		}

		return vehicleDTO;
	}

	@Override
	public ComVehicle toCOM(VehicleDTO vehicleDTO, ComVehicle initialVehicle) {
		ComVehicle comVehicle = initialVehicle == null ? new ComVehicle() : initialVehicle;

		if (vehicleDTO != null) {
			comVehicle.setWebMsgId(vehicleDTO.getId());
			comVehicle.setYear(String.valueOf(vehicleDTO.getYear()));
			ComVehicleModel comModel = new ComVehicleModel();
			comModel.setMake(vehicleDTO.getMake());
			comModel.setCode(String.valueOf(vehicleDTO.getModelCode()));
			comModel.setModel(vehicleDTO.getModel());
			comVehicle.setVehicleModel(comModel);

			comVehicle.setAnnualKmDriven(vehicleDTO.getKmPerYear() != null ? String.valueOf(vehicleDTO.getKmPerYear()) : null);
			comVehicle.setTrackingSystem(vehicleDTO.getTrackingSystemInd()); //SV
			comVehicle.setNormalRadiusKm(vehicleDTO.getNormalRadiusKm()); //SV
			comVehicle.setGrossVehicleWeightQty(vehicleDTO.getGrossVehicleWeight());//SV
			comVehicle.setPrimaryVehicleUsage(vehicleDTO.getUseOfVehicleCode());//SV - ON
			comVehicle.setMaximumRadiusKm(vehicleDTO.getMaximumRadiusKm());//SV - ON
			comVehicle.setMidHaulingDaysPerMonth(vehicleDTO.getMidHaulingDaysPerMonth());
			comVehicle.setMultiVehicleDiscountEligibilityInd(vehicleDTO.getMultiVehicleDiscountEligibilityInd());

			comVehicle.setCommercialUsageCategoryCd(vehicleDTO.getCommercialUsageCategoryCd());
			comVehicle.setCommercialUsageCd(vehicleDTO.getCommercialUsageCd());
			comVehicle.setCommercialUsageSpecificCd(vehicleDTO.getCommercialUsageSpecificCd());

			this.populateTrackingSystem(vehicleDTO, comVehicle);
		}

		return comVehicle;
	}

	private void populateTrackingSystem(final VehicleDTO vehicleDTO, ComVehicle comVehicle) {
		comVehicle.setTrackingSystem(false);
		comVehicle.setTrackingSystemCode(null);
		comVehicle.setIntensiveEngraving(Boolean.FALSE);
		comVehicle.setOtherAntiTheftDeviceIndicator(Boolean.FALSE);
		if (BooleanUtils.isTrue(vehicleDTO.getTrackingSystemInd()) && vehicleDTO.getTrackingSystem() != null) {
			comVehicle.setTrackingSystem(true);
			comVehicle.setTrackingSystemCode(vehicleDTO.getTrackingSystem().getCode());
			if (TrackingSystemCodeEnum.OTHER.getCode().equals(vehicleDTO.getTrackingSystem().getCode())) {
				comVehicle.setOtherAntiTheftDeviceIndicator(Boolean.TRUE);
			}
		}
	}


}
