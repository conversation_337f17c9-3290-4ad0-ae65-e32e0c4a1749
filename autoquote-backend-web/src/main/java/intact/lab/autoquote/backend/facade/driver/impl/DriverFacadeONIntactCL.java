package intact.lab.autoquote.backend.facade.driver.impl;

import com.ing.canada.cif.domain.IPostalCode;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComErrorCodeEnum;
import com.intact.com.enums.ComErrorFieldEnum;
import com.intact.com.util.ComValidationError;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Intact Ontario driver facade.
 * 
 * <AUTHOR> L.
 * @since 12-11-2014
 */
@ComponentLocal(application = ApplicationEnum.INTACT, province = ProvinceCodeEnum.ONTARIO, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class DriverFacadeONIntactCL extends DriverFacade {
	
	private static final Logger LOG = ESAPI.getLogger(DriverFacadeONIntactCL.class);
	
	private static final String COUNTRY_CANADA_CODE = "CA";

	@Override
	public List<ValidValue> getWorkSectorsList(final ComContext comContext) throws NotImplementedException {
		throw new NotImplementedException("Method getWorkSectorsList not implemented for Ontario");
	}

	@Override
	public List<ValidValue> getOccupationsList(ComContext comContext, String workSector) throws AutoQuoteException {
		throw new NotImplementedException("Method getOccupationsList not implemented for Ontario");
	}

	/* (non-Javadoc)
	 * @see com.intact.autoquote.intact.integration.facade.driver.impl.AbstractDriverFacadeIntact#getMunicipalityByPostalCode(com.intact.com.context.ComContext, java.lang.String)
	 */
	@Override
	@Transactional(readOnly = true)
	public ComMunicipalityInfo getMunicipalityByPostalCode(ComContext comContext, String postalCode)throws AutoQuoteException {
		//return super.getMunicipalityByPostalCode(comContext, postalCode);
		
		ComMunicipalityInfo municipalityInfo = new ComMunicipalityInfo();
		
		try {
			List<ComValidationError> errors = new ArrayList<>();

			if (StringUtils.isNotBlank(postalCode)) {
				postalCode = postalCode.toUpperCase();
				postalCode = StringUtils.deleteWhitespace(postalCode);
				
				if (!this.formFieldValidatorService.isPostalCodeValid(postalCode)) {
					addError(errors, ComErrorCodeEnum.FORMAT_ERROR, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
				} else {
					postalCode = this.formFieldValidatorService.getFormattedPostalCode(postalCode).toUpperCase();
					
					ProvinceCodeEnum provinceCodeEnum = this.driverBusinessProcess.getProvince(postalCode);
					if (provinceCodeEnum == null) {
						addError(errors, ComErrorCodeEnum.MUNICIPALITY_NOT_FOUND, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
					} else {
						if (!provinceCodeEnum.equals(ProvinceCodeEnum.valueOfCode(comContext.getProvince().getCode()))) {
							addError(errors, ComErrorCodeEnum.MUNICIPALITY_OUTSIDE_PROVINCE, ComErrorFieldEnum.MUNICIPALITY_INFO_POSTAL_CODE);
						} else {
							IPostalCode postalCodeInfo = this.driverBusinessProcess.getPostalCodeInfo(new Locale(comContext.getLanguage().getCode()), postalCode);
							if (postalCodeInfo != null) {
								municipalityInfo.setStreetName(postalCodeInfo.getStreetName());
								municipalityInfo.setStreetType(postalCodeInfo.getStreetType());
								municipalityInfo.setStreetDirection(postalCodeInfo.getStreetDirection());
							}
							
							//municipalityInfo.setMunicipalities(this.getMunicipalities(postalCode));

							ManufacturingContext manContext = MediatorUtils.convertContext(comContext);
							municipalityInfo.setMunicipalities(this.getMunicipalitiesValidValueList(new Locale(comContext.getLanguage().getCode()), postalCode, manContext));

							municipalityInfo.setProhibitedPostalCode(Boolean.FALSE);
							municipalityInfo.setProvince(provinceCodeEnum.getCode());
							municipalityInfo.setCountry(COUNTRY_CANADA_CODE);
						}
					}
				}
				
				municipalityInfo.setValidationErrors(errors);
			}
		} catch (Exception e) {
			if (LOG.isErrorEnabled()) {
				LOG.error(Logger.EVENT_FAILURE, new StringBuilder("An error occured: ").append(e.getMessage()).toString());
			}
			
			throw new AutoQuoteException(AutoQuoteException.EXEC_DEFAULT_ERROR);
		}
		
		return municipalityInfo;
	}

	/**
	 * Get cities.
	 * 
	 * @param aPostalCode {@link String}
	 * @return {@link List} of {@link ValidValue}
	 */
	protected List<ValidValue> getMunicipalities(String aPostalCode) {
		List<ValidValue> listOfCity = new ArrayList<>();

		if (StringUtils.isNotBlank(aPostalCode)) {
			String postalCode = this.formFieldValidatorService.getFormattedPostalCode(aPostalCode).toUpperCase();
			List<ValidValueBO> cities = this.driverBusinessProcess.getCitiesValidValues(postalCode);
			for (ValidValueBO city : cities) {
				listOfCity.add(new ValidValue(city.getLabel(), city.getValue()));
			}
		}

		return listOfCity;
	}
	
}
