/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.driver.impl;

import com.ing.canada.cif.service.IPostalCodeService;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.common.services.api.municipality.IMunicipalitiesByPostalCodeService;
import com.ing.canada.common.services.api.municipality.IMunicipalityDetailService;
import com.ing.canada.common.services.api.party.IInsuredGroupService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.KindOfLossCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.NatureOfClaimCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyGroupHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.driver.BR10617_TooManyClaims6years;
import com.intact.business.rules.driver.BR759_ConvictionsHistory;
import com.intact.business.rules.driver.BR7996_TooManyAtFaultClaims6years;
import com.intact.business.rules.driver.BR7998_TooManyClaims2years;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.common.IOccupationService;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Business process specific to Intact Quebec . Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 * <AUTHOR>
 */
@ComponentLocal(province = ProvinceCodeEnum.QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class DriverBusinessProcessQCIntactCL extends DriverBusinessProcess {

	private static final Logger log = ESAPI.getLogger(DriverBusinessProcessQCIntactCL.class);
	private static final String EDUCATION_OCCUPATION = "0033";
	private static final String STUDENT_OCCUPATION = "0013";
	private static final String OTHER_OCCUPATION = "0015";

	private final BR10617_TooManyClaims6years br10617;
	private final BR7998_TooManyClaims2years br7998;
	private final BR759_ConvictionsHistory br759;
	private final BR7996_TooManyAtFaultClaims6years br7996;

	public DriverBusinessProcessQCIntactCL(IPolicyVersionHelper policyVersionHelper, IMunicipalityDetailService municipalityDetailsService,
										   IMunicipalitiesByPostalCodeService municipalityByPostalCodeService, IPostalCodeService postalCodeService,
										   IPartyHelper partyHelper, IFormFieldValidatorService formFieldValidatorService,
										   IPartyGroupHelper partyGroupHelper, IAffinityGroupService affinityGroupService,
										   IInsuredGroupService insuredGroupService, IPartyRoleInRiskService partyRoleInRiskService,
										   IPartyService partyService, BR10617_TooManyClaims6years br10617, BR7998_TooManyClaims2years br7998,
										   BR759_ConvictionsHistory br759, BR7996_TooManyAtFaultClaims6years br7996, IOccupationService occupationService) {
		super(policyVersionHelper, municipalityDetailsService, municipalityByPostalCodeService, postalCodeService, partyHelper,
				formFieldValidatorService, partyGroupHelper, affinityGroupService, insuredGroupService, partyRoleInRiskService, partyService, occupationService);
		this.br10617 = br10617;
		this.br7998 = br7998;
		this.br759 = br759;
		this.br7996 = br7996;
	}

	@Override
	public Claim setClaimKindOfLoss(Claim aClaim, String claimNatureAmount) {

		KindOfLoss kol = this.extractKindOfLoss(aClaim);

		/*
		 * accident non responsable          = 1$
		 * accident responsable              = 1$
		 * délit de fuite                    = 1$
		 * incendie du véhicule              = 251$
		 * remplacement de pare-brise        = 251$
		 * réparation de pare-brise          = 1$
		 * tempête de vent et grêle          = 251$
		 * vandalisme                        = 251$
		 * vol                               = Entre par l'utilisateur
		 * In QuickQuote FEU/VOL/VENDALISME/TEMPÊTE are grouped into NatureOfClaimCodeEnum.OTHER_NON_COLLISION_CLAIM
		 * and the default amount is 251$
		 */
		BigDecimal amountPaid = new BigDecimal(1);
		if (NatureOfClaimCodeEnum.FIRE.equals(aClaim.getNatureOfClaim())
				|| NatureOfClaimCodeEnum.WINDSHIELD_REPLACEMENT.equals(aClaim.getNatureOfClaim())
				|| NatureOfClaimCodeEnum.WINDSTORM_OR_HAIL.equals(aClaim.getNatureOfClaim())
				|| NatureOfClaimCodeEnum.VANDALISM.equals(aClaim.getNatureOfClaim())
				//	In QuickQuote FEU/VOL/VENDALISME/TEMPÊTE are grouped into NatureOfClaimCodeEnum.OTHER_NON_COLLISION_CLAIM
				|| NatureOfClaimCodeEnum.OTHER_NON_COLLISION_CLAIM.equals(aClaim.getNatureOfClaim())) {
			amountPaid = new BigDecimal(251);
		} else if (StringUtils.isNotBlank(claimNatureAmount)) {
			try {
				amountPaid = new BigDecimal(claimNatureAmount);
			} catch (NumberFormatException nfe) {
				log.error(Logger.EVENT_SUCCESS, String.format(">> Can't parse the claim nature amount= %s", claimNatureAmount));
			}
		}

		switch (aClaim.getNatureOfClaim()) {
		case NOT_AT_FAULT_ACCIDENT:
			this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_PROP_DAMAGE_TO_INSURED_VEHICLE_3,
					CoverageTypeCodeEnum.AUTOMOBILE_LIABILITY_BODILY_INJURY, BasicCoverageCodeEnum.LIABILITY_A,
					(short) 0, Boolean.FALSE, ClaimTypeCodeEnum.COLLISION, amountPaid);
			break;
		case AT_FAULT_ACCIDENT:
			this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_BODILY_INJURY_CLAIMS_BY_PASSEN,
					CoverageTypeCodeEnum.COLLISION, BasicCoverageCodeEnum.COLLISION_B2, (short) 100, Boolean.TRUE,
					ClaimTypeCodeEnum.COLLISION, amountPaid);
			break;
		case HIT_AND_RUN:
			this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_DAMAGE_TO_CONTENTS_UNDER_INSU,
					CoverageTypeCodeEnum.COLLISION, BasicCoverageCodeEnum.COLLISION_B2, (short) 0, Boolean.FALSE,
					ClaimTypeCodeEnum.COLLISION, amountPaid);
			break;
//		case FIRE:
//			setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_BODILY_INJURY_CLAIMS_BY_ANY_OTH, CoverageTypeCodeEnum.COMPREHENSIVE,
//					BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0, Boolean.FALSE, ClaimTypeCodeEnum.OTHER,
//					amountPaid);
//			break;
		case WINDSHIELD_REPLACEMENT:
			this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_PROP_DAMAGE_TO_INSURED_CONTE,
					CoverageTypeCodeEnum.COMPREHENSIVE, BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0,
					Boolean.FALSE, ClaimTypeCodeEnum.WINDSHIELD, amountPaid);
			break;
		case WINDSHIELD_REPAIR:
			this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_PROP_DAMAGE_TO_INSURED_CONTE,
					CoverageTypeCodeEnum.COMPREHENSIVE, BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0,
					Boolean.FALSE, ClaimTypeCodeEnum.WINDSHIELD, amountPaid);
			break;
//		case WINDSTORM_OR_HAIL:
//			setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_LOSS_OF_USE_DIRECT_COMPENSATION,
//					CoverageTypeCodeEnum.COMPREHENSIVE, BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0,
//					Boolean.FALSE, ClaimTypeCodeEnum.OTHER, amountPaid);
//			break;
//		case VANDALISM:
//			setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_PROP_DAMAGE_TO_OTHER_VEH, CoverageTypeCodeEnum.COMPREHENSIVE,
//					BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0, Boolean.FALSE, ClaimTypeCodeEnum.OTHER,
//					amountPaid);
//			break;
//		case THEFT:
//			setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_PROP_DAMAGE_TO_INSURED_VEHICLE_3,
//					CoverageTypeCodeEnum.COMPREHENSIVE, BasicCoverageCodeEnum.COMPREHENSIVE_B3, (short) 0,
//					Boolean.FALSE, ClaimTypeCodeEnum.THEFT, amountPaid);
//			break;
		case OTHER_NON_COLLISION_CLAIM: //-- FEU/VOL/VENDALISME/TEMPÊTE
				this.setKindOfLoss(
					kol,
					aClaim,
					KindOfLossCodeEnum.A_PROP_DAMAGE_TO_INSURED_VEHICLE_3,
					CoverageTypeCodeEnum.COMPREHENSIVE,
					BasicCoverageCodeEnum.COMPREHENSIVE_B3,
					(short) 0,
					Boolean.FALSE,
					ClaimTypeCodeEnum.THEFT,
					amountPaid
				);
			break;
		default:
			throw new IllegalArgumentException("unrecognized nature of claim: " + aClaim.getNatureOfClaim());
		}

		return aClaim;
	}

	/**
	 * {@inheritDoc}
	 * */
	@Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(DriverComplementInfo driver, ProvinceCodeEnum aProvince) {

		List<RuleExceptionResult> ruleExceptions = new ArrayList<>();

		/* minor conviction : BR7995 BR7995*/
		ruleExceptions.add(this.br759.validateBR(driver));

		// BR7998 - too many claims (2 last years) : A roadblock must be raised when a driver has three (3) or more claims of any nature (except Glass repair) in the last two (2) years.
		ruleExceptions.add(this.br7998.validateBR(driver.getParty()));

		// BR7996 - A roadblock must be raised when a driver has two (2) or more at-fault claims in the last six (6) years.
		ruleExceptions.add(this.br7996.validateBR(driver.getParty()));

		ruleExceptions.add(this.br10617.validateBR(driver.getParty()));

		return ruleExceptions;
	}

	/**
	 * Manage profile. Added for quickquote needs.
	 *
	 * @param aPolicyVersion the a policy version
	 * @param application - {@link ApplicationEnum}
	 * @throws SingleIdActiveProductException the single id active product exception
	 * @throws AccessManagerException the access manager exception
	 */
	@Override
	public void manageProfile(PolicyVersion aPolicyVersion, ApplicationEnum application)
			throws SingleIdActiveProductException, AccessManagerException {

		RoadBlockExceptionContextEnum context = RoadBlockExceptionContextEnum.SAVE_DRIVER;

		try {
			this.profileService.manageProfile(aPolicyVersion.getId(), context, application);
		} catch (AutoQuoteRoadBlockException e) {
			throw new SingleIdActiveProductException(AutoquoteRoadBlockExceptionEnum.BR551, context);
		}
	}

	@Override
	public List<ValidValueBO> getDomainsList(Locale aLocale, DistributionChannelCodeEnum distributionChannel,
											 InsuranceBusinessCodeEnum insuranceBusiness, DistributorCodeEnum distributor) {

		List<ValidValueBO> values = super.getDomainsList(aLocale, distributionChannel, insuranceBusiness, distributor);
        return this.sortDomainsList(values);
	}

	private List<ValidValueBO> sortDomainsList(List<ValidValueBO> domains) {

		List<ValidValueBO> listNormalOrder = new ArrayList<>();
		List<ValidValueBO> mergeList = new ArrayList<>();

		ValidValueBO educationDomain = null;
		ValidValueBO studentDomain = null;
		ValidValueBO otherDomain = null;

		for (ValidValueBO domain : domains) {
			if (EDUCATION_OCCUPATION.equals(domain.getValue())) {
				educationDomain = domain;
			} else if (STUDENT_OCCUPATION.equals(domain.getValue())) {
				studentDomain = domain;
			} else if (OTHER_OCCUPATION.equals(domain.getValue())) {
				otherDomain = domain;
			} else {
				listNormalOrder.add(new ValidValueBO(domain.getValue(), domain.getLabel()));
			}
		}

		// Insert education and student domains at the beginning
		if (educationDomain != null) {
			mergeList.add(new ValidValueBO(educationDomain.getValue(), educationDomain.getLabel()));
		}
		if (studentDomain != null) {
			mergeList.add(new ValidValueBO(studentDomain.getValue(), studentDomain.getLabel()));
		}

		// Insert domains in preserved order
		mergeList.addAll(listNormalOrder);

		// Insert other domain at the end
		if (otherDomain != null) {
			mergeList.add(new ValidValueBO(otherDomain.getValue(), otherDomain.getLabel()));
		}

		return mergeList;
	}
}
