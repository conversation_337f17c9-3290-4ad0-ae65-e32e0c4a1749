package intact.lab.autoquote.backend.quotestatemanager.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;

@ComponentLocal(province = ProvinceCodeEnum.QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class QuoteStateManagerQCIntactCL extends QuoteStateManagerIntact {
	// -- 
}
