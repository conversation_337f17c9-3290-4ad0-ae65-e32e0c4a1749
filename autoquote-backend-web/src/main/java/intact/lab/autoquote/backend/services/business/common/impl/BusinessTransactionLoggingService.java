package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.plp.domain.enums.ApplicationIdEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionService;
import intact.lab.autoquote.backend.common.enums.StateMachineEventEnum;
import intact.lab.autoquote.backend.services.business.common.IBusinessTransactionLoggingService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Business Transaction Logging Service Implementation.
 */
@Component
public class BusinessTransactionLoggingService implements IBusinessTransactionLoggingService {

    private static final Logger LOG = ESAPI.getLogger(BusinessTransactionLoggingService.class);

	private final IBusinessTransactionService btService;

	public BusinessTransactionLoggingService(IBusinessTransactionService btService) {
		this.btService = btService;
	}

	private final Map<StateMachineEventEnum, BusinessTransactionSubActivityCodeEnum> mapping = new HashMap<>() {
        private static final long serialVersionUID = 1L;

        {
            this.put(StateMachineEventEnum.SAVE_VEHICLE, BusinessTransactionSubActivityCodeEnum.MODIFY_VEHICLE);
            this.put(StateMachineEventEnum.ADD_VEHICLE, BusinessTransactionSubActivityCodeEnum.ADD_VEHICLE);
            this.put(StateMachineEventEnum.DELETE_VEHICLE, BusinessTransactionSubActivityCodeEnum.DELETE_VEHICLE);
            this.put(StateMachineEventEnum.SAVE_DRIVER, BusinessTransactionSubActivityCodeEnum.MODIFY_DRIVER);
            this.put(StateMachineEventEnum.ADD_DRIVER, BusinessTransactionSubActivityCodeEnum.ADD_DRIVER);
            this.put(StateMachineEventEnum.DELETE_DRIVER, BusinessTransactionSubActivityCodeEnum.DELETE_DRIVER);
            this.put(StateMachineEventEnum.SAVE_USAGE, BusinessTransactionSubActivityCodeEnum.MODIFY_USAGE);

            // Offer page activitys
            this.put(StateMachineEventEnum.VIEW_OFFER, BusinessTransactionSubActivityCodeEnum.GET_OFFER);
            this.put(StateMachineEventEnum.RECALCULATE, BusinessTransactionSubActivityCodeEnum.RECALCULATE);
            this.put(StateMachineEventEnum.UPDATE_SAVINGS, BusinessTransactionSubActivityCodeEnum.UPDATE_SAVINGS);

            // Bind activitys
            this.put(StateMachineEventEnum.VIEW_BIND, BusinessTransactionSubActivityCodeEnum.GET_BIND_COV);
            this.put(StateMachineEventEnum.VIEW_PROFILE, BusinessTransactionSubActivityCodeEnum.GET_BIND_PROFILE);
            this.put(StateMachineEventEnum.VIEW_VEHICLE_DETAILS, BusinessTransactionSubActivityCodeEnum.GET_BIND_VEH_DRVR);
            this.put(StateMachineEventEnum.VIEW_PAYMENT, BusinessTransactionSubActivityCodeEnum.GET_PURCHASE);

            this.put(StateMachineEventEnum.PURCHASE, BusinessTransactionSubActivityCodeEnum.PURCHASE_REQUESTED);
            this.put(StateMachineEventEnum.ROADBLOCK, BusinessTransactionSubActivityCodeEnum.ROADBLOCK);
        }
    };


	/**
	 * This method is used to create a BusinessTransactionActivity.
	 *
	 * @param code            the code
	 * @param policyId        the policy id
	 * @param userId          the user id
	 * @param mainframeUserId the mainframe user id
	 * @param userTypeCode    the user type code
	 */
	@Override
	public void createActivity(BusinessTransactionActivityCodeEnum code, long policyId, String userId,
                               String mainframeUserId, UserTypeCodeEnum userTypeCode) {

		this.btService.createActivity(code, policyId, userId, mainframeUserId, userTypeCode,
				ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE);
	}

    /**
     * This method can be called by any controller in the application.
     *
     * @param event           BusinessTransactionSubActivityCodeEnum
     * @param policy          the policy
     * @param rootReferenceId the root reference id
     * @return the long
     */
    @Override
    public long createSubActivity(BusinessTransactionSubActivityCodeEnum event, long policy, long rootReferenceId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "createSubActivity method of ClientSubActivityManagerService");
            LOG.debug(Logger.EVENT_SUCCESS, "BusinessTransactionSubActivityCodeEnum = " + event.toString());
        }

        Long btsaId = this.btService.createSubActivity(event, policy, rootReferenceId);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "****** ID OF THE BTSA = " + btsaId);
        }

        return btsaId;
    }

    /**
     * this method can be called by any controller in the application to create a compl. info on defined sub activity
     * code. (e.g roadBlock)
     *
     * @param complInfoCode         the code representing the compl info
     * @param complInfoValue        a value attach to the code
     * @param businessTransSubActId the id of the parent sub activity
     */
    @Override
    public void createSubActivityComplInfo(String complInfoCode, String complInfoValue, long businessTransSubActId) {
        this.btService.createSubActivityComplInfo(complInfoCode, complInfoValue, businessTransSubActId);
    }
}
