package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.enums.ContentsMappingEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.services.impl.AutoQuoteServiceCache;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.List;

@Component
public class YearValidationRule {

	private final AutoQuoteServiceCache autoQuoteServiceCache;

	public YearValidationRule(AutoQuoteServiceCache autoQuoteServiceCache) {
		this.autoQuoteServiceCache = autoQuoteServiceCache;
	}

	public void validate(String year, String province, String language, Errors errors) {
		if (!ValidationUtilities.isInteger(year)) {
			errors.rejectValue("year", BRulesExceptionEnum.Pattern.getErrorCode(), "[year]");
		} else {
			final List<ValidValueBO> yearList = autoQuoteServiceCache.getListByProvinceAndLocaleBO(ContentsMappingEnum.YEARS.name(),
					province, language);
			if (!ValidationUtilities.isExistValueInList(yearList, year)) {
				errors.rejectValue("year", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), "[year]");
			}
		}
	}

}
