package intact.lab.autoquote.backend.common.exception;

import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import org.springframework.validation.Errors;

public class QuoteValidationException extends AutoQuoteException {

	public static final String VALIDATION_ERROR = "quote.validation.error";
	private final QuoteDTO quote;
	private final Errors errors;

	public QuoteValidationException(String exceptionCode, QuoteDTO quote, Errors errors) {
		super(exceptionCode);
		this.quote = quote;
		this.errors = errors;
	}

	public QuoteValidationException(String exceptionCode, Throwable cause, QuoteDTO quote, Errors errors) {
		super(exceptionCode, cause);
		this.quote = quote;
		this.errors = errors;
	}

	public QuoteDTO getQuote() {
		return quote;
	}

	public Errors getErrors() {
		return errors;
	}


}
