package intact.lab.autoquote.backend.facade.offer.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AutoQuoteOfferFacadeQCIntactCL extends AutoQuoteOfferFacade {

	@Autowired
	private BloomMQHandlerService bloomMQHandlerService;

	@Override
	protected PolicyVersion ratePolicy(CommunicationObjectModel aCom) throws AutoQuoteRoadBlockException, AutoquoteBusinessException {
		return super.ratePolicy(aCom);
	}

	@Override
	@Transactional
	public CommunicationObjectModel retrieveOffer(CommunicationObjectModel aCom)
			throws AutoquoteFacadeException {

		CommunicationObjectModel quote = super.retrieveOffer(aCom);

		if(CollectionUtils.isNotEmpty(quote.getRoadblock())){
			bloomMQHandlerService.sendMessageToMQ(aCom.getUuId(), MediatorUtils.convertContext(aCom.getContext()).getManufacturerCompany());
			return quote;
		}

		try {
			// We simulate an offer selection made by the user,
			// then to proceed, we unselect the systemSelectedInd flag
			// and apply a selection over the only offer we have.
			this.undoInsuranceRiskOfferSystemSelectedInd(quote);
			PolicyVersion pv = this.getPolicyVersion(quote.getPolicyVersionId());
			if(!ComLineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(quote.getContext().getLineOfBusiness())){
				this.offerBusinessProcess.selectOffer(pv, true);
			}
			this.transactionHistoryService.updateTransactionHistory(ComEventEnum.SAVE_OFFER, pv);
			bloomMQHandlerService.sendMessageToMQ(pv.getInsurancePolicy().getUuId(), pv.getInsurancePolicy().getManufacturerCompany());
			this.updateUBI(quote, pv);
		} catch (Exception e) {
			throw new AutoquoteFacadeException(e);
		}

		return quote;
	}

	protected void undoInsuranceRiskOfferSystemSelectedInd(CommunicationObjectModel quote) {
		PolicyVersion pv = this.getPolicyVersion(quote.getPolicyVersionId());
		Set<InsuranceRisk> irs = pv.getInsuranceRisks();
		for (InsuranceRisk insuranceRisk : irs) {
			insuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(Boolean.FALSE);
		}
	}

	protected void updateUBI(CommunicationObjectModel com, PolicyVersion policyVersion) {
		InsuranceRisk ir = policyVersion.getInsuranceRisks().iterator().next();
		if (ir == null) {
			return;
		}

		InsuranceRiskOffer selectedOffer = policyVersion.getInsuranceRisks().iterator().next().getSelectedInsuranceRiskOffer();
		boolean isNotSelected = selectedOffer == null
				|| ir.getInsuranceRiskOfferSystemSelectedIndicator();
		OfferTypeCodeEnum type = isNotSelected ? null : selectedOffer.getOfferType();

		if (type != null) {
			BaseCoverage ubi = this.coverageHelper.getSelectedEndorsement(selectedOffer.getCoverageOffers(),
					EndorsementCodeEnum.UBI);
			if (ubi != null && ubi.getCoverageSelectedIndicator()) {
				com.getState().setCanDisplayUBI(Boolean.TRUE);
			}
		}
	}
}
