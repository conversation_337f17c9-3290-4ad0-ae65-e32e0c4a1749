package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

public abstract class PhoneNumberValidationRule {

	public static void validate(String rawPhoneNumber, Errors errors, String fieldName) {
		if (StringUtils.isBlank(rawPhoneNumber)) {
			errors.rejectValue(fieldName, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(fieldName));
		} else {
			String phoneNumber = rawPhoneNumber;
			phoneNumber = phoneNumber.replaceAll("-", "");
			if (phoneNumber.contains("+")) phoneNumber = phoneNumber.substring(2);
			if (phoneNumber.length() != 10) {
				errors.rejectValue(fieldName, BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode(), ValidationUtilities.bracket(fieldName));
			} else {
				try {
					Long.parseLong(phoneNumber);
				} catch (NumberFormatException e) {
					errors.rejectValue(fieldName, BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode(), ValidationUtilities.bracket(fieldName));
				}

				// first digit can't be a 0 or a one
				if (phoneNumber.charAt(0) == '0' || phoneNumber.charAt(0) == '1') {
					errors.rejectValue(fieldName, BRulesExceptionEnum.ERR_STEP3_PHONE_BR7751.getErrorCode(), ValidationUtilities.bracket(fieldName));
				}

			}
		}
	}

}
