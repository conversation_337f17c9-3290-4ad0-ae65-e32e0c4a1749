package intact.lab.autoquote.backend.common.exception;


import com.intact.com.util.ComRoadBlock;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import lombok.Getter;

import java.util.List;

@Getter
public class AutoQuoteRoadBlockException extends AutoQuoteException {

	public static final String ROAD_BLOCK_ERROR = "quote.road.block.error";
	private final QuoteDTO quote;
	private final List<ComRoadBlock> roadBlocks;

	public AutoQuoteRoadBlockException(String exceptionCode, QuoteDTO quote, List<ComRoadBlock> roadBlocks) {
		super(exceptionCode);
		this.quote = quote;
		this.roadBlocks = roadBlocks;
	}

}
