package intact.lab.autoquote.backend.facade.driver.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;

/**
 * Intact Quebec driver facade.
 * 
 * <AUTHOR>
 * @since 12-11-2014
 */
@ComponentLocal(application = ApplicationEnum.INTACT, province = ProvinceCodeEnum.QUEBEC, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class DriverFacadeQCIntactCL extends DriverFacade {

}
