package intact.lab.autoquote.backend.common.filter;

import jakarta.servlet.Filter;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;

import java.io.IOException;

public class LoggingFilter implements Filter {

    private static final String MDC_TRACKING_NUMBER = "tn";
    private static final String MDC_SESSION_NUMBER = "sn";
    private static final String MDC_PROVINCE = "province";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String province = request.getParameter("province");
        String sessionNumber = request.getParameter("sn");
        String trackingNumber = request.getParameter("tn");

        if (StringUtils.isNotEmpty(province)) {
            MDC.put(MDC_PROVINCE, province);
        }

        if (StringUtils.isNotEmpty(trackingNumber)) {
            MDC.put(MDC_TRACKING_NUMBER, trackingNumber);
        }

        if (StringUtils.isNotEmpty(sessionNumber)) {
            MDC.put(MDC_SESSION_NUMBER, sessionNumber);
        }

        filterChain.doFilter(request, response);
    }
}
