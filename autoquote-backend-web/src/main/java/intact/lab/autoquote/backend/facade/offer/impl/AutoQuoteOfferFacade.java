/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.offer.impl;

import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.LanguageEnum;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.ApplicationOriginEnum;
import com.ing.canada.cif.domain.enums.BrokerWebAccessTypeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.enums.PhoneNumberUsageEnum;
import com.ing.canada.cif.domain.helpers.SubBrokerHelper;
import com.ing.canada.cif.domain.impl.BrokerAssignationParameterBean;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.exception.SubBrokerServiceException;
import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.EquipmentTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyGroup;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.VehicleEquipment;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.enums.ComRoadBlockTypeEnum;
import com.intact.com.state.ComState;
import com.intact.com.util.ComRoadBlock;
import com.intact.com.vehicle.ComVehicle;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import com.intact.common.security.log.SecurityAuditLog;
import com.intact.common.security.log.SecurityLogger;
import com.intact.common.web.security.dos.DenyOfServiceException;
import com.intact.common.web.security.dos.IDenyOfService;
import com.intact.common.web.security.webattack.WebAttackConfigurationService;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;
import intact.lab.autoquote.backend.common.enums.BuildPhoneNumberActionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.facade.ICommonFacade;
import intact.lab.autoquote.backend.facade.impl.BaseFacade;
import intact.lab.autoquote.backend.facade.offer.IAutoQuoteOfferFacade;
import intact.lab.autoquote.backend.services.INewQuoteBusinessProcessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Component
public abstract class AutoQuoteOfferFacade extends BaseFacade implements IAutoQuoteOfferFacade {

	private static final Logger log = ESAPI.getLogger(AutoQuoteOfferFacade.class);

	@Autowired
	WebAttackConfigurationService webAttackConfigurationService;

	@Autowired
	@Qualifier("cifSubBrokersService")
	protected ISubBrokersService subBrokersService;

	@Autowired
	private IInsurancePolicyService insurancePolicyService;

	@Autowired
	VehicleHelper vehicleHelper;

	@Autowired
	protected IPolicyVersionHelper policyVersionHelper;

	@Autowired
	protected INewQuoteBusinessProcessService newQuoteBusinessProcess;

	@Autowired
	protected IPartyHelper partyHelper;

	@Autowired
	protected IDenyOfService denyOfService;

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public CommunicationObjectModel retrieveOffer(CommunicationObjectModel aCom) throws AutoquoteFacadeException {
		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		PolicyVersion aPolicyVersion = null;

		// 46231 need to figure out first if there are details for the vehicle and
		// if the vehicle group is not 69 and over because save or rate will fail.
		// since glm, regular validation doesn't work for QC. Other provinces are ok.
		if (this.validateVehiclesDEFAULTRB(aCom, MediatorUtils.getLocale(aCom.getContext()))) {
			List<ComRoadBlock> result = new ArrayList<ComRoadBlock>();
			result.add(new ComRoadBlock("BR10482", ComRoadBlockTypeEnum.HARD));
			aCom.setRoadblock(result);
			return aCom;
		}


		try {
			aPolicyVersion = this.policyVersionService.findById(aCom.getPolicyVersionId());
			if (aPolicyVersion != null) {
				if (aPolicyVersion != null && !this.policyVersionHelper.isQuoteAlreadyRatedOnce(aPolicyVersion)) {
					performanceWatch.start("QuickquoteCommonFacade.save - smartValues");

					aPolicyVersion = this.offerBusinessProcess.determineQuoteInfos(aPolicyVersion);
					performanceWatch.stop();

					if (log.isDebugEnabled()) {
						this.printSmartValues(aPolicyVersion);
					}
				}
				try {
					//TODO : Mounir : Temporary condition for IRCA
					if (!ComLineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(aCom.getContext().getLineOfBusiness())) {
						// Convert the company from quickquote to plp
						performanceWatch.start("QuickquoteCommonFacade.save - manageProfile");
						this.driverBusinessProcess.manageProfile(aPolicyVersion, ApplicationEnum.valueOf(aCom.getContext().getCompany().name()));
						performanceWatch.stop();
					}
				} catch (SingleIdActiveProductException e) {
					return this.handleSingleIdActiveProductException(aCom, aPolicyVersion, AutoquoteRoadBlockExceptionEnum.BR2565, ComRoadBlockTypeEnum.HARD);
				} catch (AccessManagerException e) {
					log.error(Logger.EVENT_FAILURE, e.getMessage(), e);
				}
			} else {

				// this is overriding changes made by the local adapters and imho is not needed here
				// see story 141025 where currently insured was set to false at re-rate for qq intact ON
				//this.definePrincipalDriverRelatedInformation(aCom);

				aCom = this.loadQuoteFromPolicyVersionId(aCom.getContext(), aCom.getPolicyVersionId());

				performanceWatch.start("QuickquoteCommonFacade.save - assignDriverOnStep2");
				this.assignDriverToVehicle(aCom);
				performanceWatch.stop();

				aPolicyVersion = this.policyVersionService.findById(aCom.getPolicyVersionId());
			}

			aCom = this.buildCom(aPolicyVersion, aCom);

			this.setBrokerInfo(aPolicyVersion, aCom);

		} catch (AutoquoteRatingException e) {
			log.error(Logger.EVENT_FAILURE, String.format("an AutoquoteRatingException occured: %s", e.getMessage()), e);
			throw new AutoquoteFacadeException(e);
		} catch (Exception e) {
			log.error(Logger.EVENT_FAILURE, String.format("an exception occured: %s", e.getMessage()), e);
			throw new AutoquoteFacadeException(e);
		}

		try {
			log.debug(Logger.EVENT_SUCCESS, "Verify web attacks");

			//Verify is web attack protection is on or off
			boolean byPassWebAttackVerification = this.webAttackConfigurationService.isByPassWebAttackVerification();

			if (byPassWebAttackVerification) {
				log.warning(Logger.EVENT_SUCCESS, "ATTENTION : BYPASSING WEB ATTACK VERIFICATION IS ON");
			}

			if (!byPassWebAttackVerification) {
				this.verifyWebAttack(aCom, EVENT_TYPE_RATINGREQ, aCom.getAgreementNumber());
			}

			log.debug(Logger.EVENT_SUCCESS, "Verify denied of service");
			this.denyOfService.isDeniedOfService(
					aCom.getContext().getClientXForwardIPNbr(),
					aCom.getContext().getApplication().toString()
			);

			//Rate Policy
			PolicyVersion ratePolicy = this.ratePolicy(aCom);

			// qq prohibited postal code
      		this.verifyProhibitedPostalCode(ratePolicy, aCom);

			return this.buildCom(ratePolicy, aCom);

		} catch (AutoquoteFacadeException e) {
			throw e;
		} catch (DenyOfServiceException e) {
			this.auditDOS("The pricing of the quote is cancelled because too many pricing at the same time from this IP are running right now.", aCom.getContext().getClientXForwardIPNbr());
			throw new AutoquoteFacadeException("DOS");
		} catch (Exception e) {
			if (log.isErrorEnabled()) {
				log.error(Logger.EVENT_FAILURE, String.format("An error occured: %s", e.getMessage()).toString(), e);
			}
			throw new AutoquoteFacadeException(e);
		} finally {
			this.denyOfService.clean(aCom.getContext().getClientXForwardIPNbr());
		}
	}

	protected void setBrokerInfo(PolicyVersion aPolicyVersion, CommunicationObjectModel quote) throws Exception {
		ISubBrokers subBroker = null;
		try {
			subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
		} catch (Exception e) {
			ManufacturingContext mCtxt = MediatorUtils.convertContext(quote.getContext());
			long subBrokerId = this.buildBrokerInfoBeforeCreateQuote(quote.getContext(), mCtxt);
			this.newQuoteBusinessProcess.assignBrokerToQuote(aPolicyVersion.getInsurancePolicy(), aPolicyVersion, String.valueOf(subBrokerId));
			// Save the insurance policy
			this.insurancePolicyService.persist(aPolicyVersion.getInsurancePolicy());
			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, String.format("policyVersion before saving = %s", ToStringBuilder.reflectionToString(aPolicyVersion.getInsurancePolicy())));
			}

			// Save the policy version
			aPolicyVersion = this.policyVersionService.persist(aPolicyVersion);
			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, String.format("policyVersion after saving = %s", ToStringBuilder.reflectionToString(aPolicyVersion)));
			}
			subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
		}


		ComBrokerInfo brokerInfo = quote.getContext().getBrokerInfo();
		if (brokerInfo != null) {
			subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
			if (subBroker != null && subBroker.getSubBrokerId() > 0) {
				IContextualSubBrokerGnInfos contextualSubBrokerGnInfos = subBroker.getAccessType(ApplicationIdEnum.WEB_QUOTE, LineOfBusinessEnum.PERSONAL_LINE);
				String webAccessType = contextualSubBrokerGnInfos.getWebAccessType();

				brokerInfo.setAddressLine1(subBroker.getAddressLine1());
				brokerInfo.setAddressLine2(subBroker.getAddressLine2());
				brokerInfo.setAddressLine3(subBroker.getAddressLine3());
				brokerInfo.setCallBackAvailable(subBroker.getAllowClientSchCallbackInd());
				brokerInfo.setLogo(SubBrokerHelper.getLogo(quote.getContext().getLanguage().getCode(), subBroker));
				brokerInfo.setPostalCode(subBroker.getMailCode());
				brokerInfo.setProvince(subBroker.getProvince());
				brokerInfo.setSubBrokerName(subBroker.getNameLine1());
				brokerInfo.setSubBrokerNumber(subBroker.getSubBrokerNumber());

				if (webAccessType != null && (webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue())
						|| webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue())
						|| webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue()))) {
					brokerInfo.setHomeQuoteAvailable(true);
				} else {
					brokerInfo.setHomeQuoteAvailable(false);
				}

				this.setPhoneNumberForAction((CollectionUtils.isEmpty(quote.getRoadblock()) ? BuildPhoneNumberActionEnum.FOR_CREATE : BuildPhoneNumberActionEnum.FOR_ROADBLOCK),
						subBroker,
						quote.getContext());
			}
		}
	}

	private CommunicationObjectModel loadQuoteFromPolicyVersionId(ComContext aComContext, Long policyVersionId) throws AutoquoteFacadeException {
		// load the policyVersion
		PolicyVersion policyVersion = this.commonBusinessProcess.loadPolicyVersion(policyVersionId);
		CommunicationObjectModel newCom = new CommunicationObjectModel();
		newCom.setContext(aComContext);
		return this.buildCom(policyVersion, newCom);
	}

	/**
	 * Assign driver to vehicle.
	 *
	 * @param quote {@link CommunicationObjectModel}
	 * @throws AutoquoteFacadeException an {@link AutoquoteFacadeException}
	 */
	protected void assignDriverToVehicle(CommunicationObjectModel quote) throws AutoquoteFacadeException {
		// For now we have only one vehicle. Get the first one.
		ComVehicle vehicle = quote.getVehicles().get(0);
		if (vehicle == null) {
			throw new AutoquoteFacadeException("Must have one vehicle on the quote.");
		}

		vehicle.setPrincipalDriver(quote.getDriver(0).getDriverId());
		vehicle.setRegisterOwner(quote.getDriver(0).getDriverId());

		// We need to save the vehicle information before calling the assignDriver.
		//quote = super.save(quote, true);

		this.assignDriver(quote);
	}

	private CommunicationObjectModel handleSingleIdActiveProductException(CommunicationObjectModel quote, PolicyVersion aPolicyVersion, AutoquoteRoadBlockExceptionEnum autoquoteRoadBlockExceptionEnum, ComRoadBlockTypeEnum comRoadBlockTypeEnum) {
		// saving the roadBlock is necessary to display this information in webzone.
		List<RoadBlockException> roadblocks = new ArrayList<RoadBlockException>();
		//roadblocks.add(new RoadBlockException(brCode, aPolicyVersion.getParties().iterator().next(), AutoquoteRoadBlockExceptionEnum.BR2565.getCode()));
		roadblocks.add(new RoadBlockException(autoquoteRoadBlockExceptionEnum.getCode(), aPolicyVersion.getParties().iterator().next(), autoquoteRoadBlockExceptionEnum.getCode()));
		this.persistRoadblocks(aPolicyVersion, roadblocks, quote.getState());
		// this line is required in order to get a proper error message at the REST level
		//quote.getRoadblock().add(new ComRoadBlock(AutoquoteRoadBlockExceptionEnum.BR2565.getCode(), comRoadBlockTypeEnum));
		quote.getRoadblock().add(new ComRoadBlock(autoquoteRoadBlockExceptionEnum.getCode(), comRoadBlockTypeEnum));
		return quote;
	}

	/**
	 * Prints the smart values.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 */
	private void printSmartValues(PolicyVersion aPolicyVersion) {
		StringBuilder sb = new StringBuilder("******* BEGIN PRINTING SMARTS VALUES ********");
		if (aPolicyVersion.getParties() != null && aPolicyVersion.getParties().size() > 0) {
			Party party = aPolicyVersion.getParties().iterator().next();
			sb.append("Marital Status: ").append(party.getMaritalStatus());
			sb.append("Full-time student: ").append(party.getCurrentlyStudyingInCanadianCollegeOrUniversity());
			sb.append("Retired: ").append(party.getRetiredIndicator());
			sb.append("University degree: ").append(party.getHolderOfDiplomaFromCanadianUniversity());
			if (party.getDriverComplementInfo() != null) {
				sb.append("Licence X obtained in last 12 months: ").append(party.getDriverComplementInfo().getObtainedG2OrGClassInLast12MonthsIndicator());
				sb.append("Driver training course: ").append(party.getDriverComplementInfo().getDriverTrainingIndicator());
				sb.append("Number of minor infraction: ").append(party.getDriverComplementInfo().getNumberOfMinorConvictions3Years());
				sb.append("Number of serious or major infraction: ").append(party.getDriverComplementInfo().getNumberOfMajorConvictions3Years());
				sb.append("Claims indicator: ").append(party.getDriverComplementInfo().getRelevantClaimIndicator() == null ? "false" : party.getDriverComplementInfo().getRelevantClaimIndicator());
			}

			// Last Move
			Set<PolicyHolder> policyHolders = party.getPolicyHolders();
			if (policyHolders != null && !policyHolders.isEmpty()) {
				sb.append("Last move: ").append(policyHolders.iterator().next().getNumberStabilityMonths());
			}

			Set<PartyGroup> partyGroups = party.getPartyGroups();

			if (partyGroups != null && partyGroups.size() > 0) {
				// Work Sector and Occupation
				GroupRepositoryEntry gre = this.partyHelper.getOccupationDomainGroup(party);
				sb.append("Work Sector: ").append(gre == null ? "NA" : gre.getPartyGroupCode());
				sb.append("Occupation: ").append(gre == null ? "NA" : gre.getPartySubGroupCode());
			} else {
				sb.append("Work Sector(no partyGroup): NA");
				sb.append("Occupation(no partyGroup): NA");
				sb.append("CAA Member(no partyGroup): NA");
			}
		}

		if (aPolicyVersion.getInsuranceRisks() != null && !aPolicyVersion.getInsuranceRisks().isEmpty()) {
			InsuranceRisk insuranceRisk = aPolicyVersion.getInsuranceRisks().iterator().next();
			sb.append("UBI Illigible(automerite): ").append(insuranceRisk.getVehicle().getUBIEligibilityIndicator());

			VehicleEquipment vehicleEquipment = this.vehicleHelper.getVehicleEquipment(insuranceRisk.getVehicle(), EquipmentTypeCodeEnum.WINTER_TIRE);
			sb.append("Winter tire: ").append(vehicleEquipment == null ? "No" : "Yes");
			sb.append("Antitheft device: ").append(insuranceRisk.getVehicle().getAntiTheftDevices() == null ? "None" : "yes");
			sb.append("ParkingType: ").append(insuranceRisk.getVehicle().getParkingType());
			sb.append("Lease vehicle: ").append(insuranceRisk.getVehicle().getLeasedVehicleIndicator());
			sb.append("Annual business km: ").append(insuranceRisk.getVehicle().getAnnualBusinessKilometers());
			sb.append("Multi vehicle discount: ").append(insuranceRisk.getMultiVehicleDiscountEligibilityIndicator());
		}

		sb.append("Group discount: ").append(aPolicyVersion.getAffinityGroupCode());
		sb.append("Car and Home discount: ").append(aPolicyVersion.getCombinedPolicyCode() != null && CombinedPolicyCodeEnum.COMBO_POLICY.equals(aPolicyVersion.getCombinedPolicyCode()) ? "Yes" : "No");
		sb.append("Insured for: ").append(aPolicyVersion.getClientOfBrokerSinceAuto());
		sb.append("******* END PRINTING SMARTS VALUES ********");

		log.debug(Logger.EVENT_SUCCESS, sb.toString());
	}

	/*
	 * Assign Drivers to Vehicles
	 */
	private Boolean assignDriver(final CommunicationObjectModel aCom) throws AutoquoteFacadeException {
		Boolean isAssigned = Boolean.FALSE;
		final PolicyVersion aPolicyVersion = this.getPolicyVersion(aCom.getPolicyVersionId());

		// only assignDrivers if current state allows it
		ComState currentState = aCom.getState();
		if (aPolicyVersion != null && currentState != null && currentState.isCanRate()) {
			try {
				this.usageBusinessProcess.assignDrivers(aPolicyVersion);
				isAssigned = Boolean.TRUE; // gets here so we assume assignDrivers passed
			} catch (Exception e) {
				if (log.isErrorEnabled()) {
					log.error(Logger.EVENT_SUCCESS, String.format("An error occured: %s", e.getMessage()).toString(), e);
				}
				throw new AutoquoteFacadeException("Exception occured in assignDrivers", e);
			}
		}
		return isAssigned;
	}

	private long buildBrokerInfoBeforeCreateQuote(ComContext aComContext, ManufacturingContext mCtxt) throws SubBrokerServiceException, Exception {
		ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();
		String postalCode = brokerInfo.getPostalCode();
		String companyNumber = mCtxt.getManufacturerCompany().getCode();
		String subBrokerNumber = aComContext.getBrokerInfo().getSubBrokerNumber();
		String brokerWebSiteOrigin = (brokerInfo.getBrokerWebsiteOrigin() == null) ? null : brokerInfo.getBrokerWebsiteOrigin().getCode();

		BrokerAssignationParameterBean parameter = new BrokerAssignationParameterBean.Builder()
				.applicationId(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode())
				.company(companyNumber)
				.language(LanguageEnum.valueOfIsoCode(aComContext.getLanguage().getCode().toLowerCase()).get3CharLanguageCode())
				.lineOfBusiness(aComContext.getLineOfBusiness() == null ? LineOfBusinessEnum.PERSONAL_LINE.getCode() : LineOfBusinessEnum.COMMERCIAL_LINE.getCode())
				.postalCode(postalCode)
				.quoteSource((StringUtils.equalsIgnoreCase(brokerWebSiteOrigin, ComBrokerWebSiteOriginEnum.INTACT.getCode()) ? null : brokerWebSiteOrigin))
				.subBrokerNumber(subBrokerNumber)
				.fetchSubBroker(true)
				.build();

		ISubBrokers subBroker = this.subBrokersService.findBroker(parameter);

		if (subBroker != null && subBroker.getSubBrokerId() > 0) {
			brokerInfo.setLogo(SubBrokerHelper.getLogo(aComContext.getLanguage().getCode(), subBroker));
			this.setPhoneNumberForAction(BuildPhoneNumberActionEnum.FOR_CREATE, subBroker, aComContext);

			return subBroker.getSubBrokerId();

		} else {
			throw new Exception("Invalid sub broker");
		}
	}

	private void setPhoneNumberForAction(BuildPhoneNumberActionEnum action, ISubBrokers subBroker, ComContext aComContext) {
		ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();
		ApplicationOriginEnum origin = ComBrokerWebSiteOriginEnum.BROKER.equals(brokerInfo.getBrokerWebsiteOrigin()) ? ApplicationOriginEnum.CNT : ApplicationOriginEnum.WINI;
		LineOfBusinessEnum lineOfBusiness = aComContext.getLineOfBusiness() == null ? LineOfBusinessEnum.PERSONAL_LINE : LineOfBusinessEnum.COMMERCIAL_LINE;
		IContextualPhoneNumbers contextualPhoneNumber;

		switch (action) {
			case FOR_CREATE:
				contextualPhoneNumber = subBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, lineOfBusiness, origin, "OFFPG", PhoneNumberUsageEnum.BUSINESS_PHONE);

				//Set the Broker Phone Number for the offer page.
				if (contextualPhoneNumber != null) {
					brokerInfo.setPhoneNumber(contextualPhoneNumber.toDashedString());
				}
				brokerInfo.setSubBrokerNumber(subBroker.getSubBrokerNumber());
				break;

			case FOR_ROADBLOCK:
				contextualPhoneNumber = subBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, lineOfBusiness, origin, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
				if (contextualPhoneNumber != null) {
					brokerInfo.setPhoneNumber(contextualPhoneNumber.toDashedString());
				}
				break;
		}
	}

	/*
	 * Rates the policy version associated to the current CommunicationObjectModel instance.
	 */
	protected PolicyVersion ratePolicy(CommunicationObjectModel aCom) throws AutoQuoteRoadBlockException, AutoquoteBusinessException {
		PolicyVersion policyVersion = this.getPolicyVersion(aCom.getPolicyVersionId());
		//TODO value needs to be pushed back in the COM
		boolean firstRate = !this.policyVersionHelper.isQuoteAlreadyRatedOnce(policyVersion);
		boolean isUbiEnabled = this.configurator.isUbi(aCom.getContext().getProvince().getCode(), policyVersion.getDistributorCode() == null ? null : policyVersion.getDistributorCode().getCode());
		this.offerBusinessProcess.ratePolicy(policyVersion, false, isUbiEnabled);
		if (firstRate) {
			this.offerBusinessProcess.executePostFirstRating(policyVersion);
		}
		return policyVersion;
	}

	//Send message to audit
	private void auditDOS(String message, String Ip) {
		SecurityAuditLog securityAuditLog;
		try {
			securityAuditLog = new SecurityAuditLog("DOS", message);
			if (Ip != null) {
				securityAuditLog.setSourceIP(Ip);
			}

			securityAuditLog.setSeverityVeryHigh();
			securityAuditLog.setSuccess();
			SecurityLogger.logAudit(securityAuditLog);
		} catch (SecurityException e) {
			log.error(Logger.SECURITY_FAILURE, "SECURITY AUDIT DOES NOT WORK. PLEASE FIX THIS.", e);
		}
	}

	protected ApplicationModeEnum getApplicationMode() {
		return ApplicationModeEnum.QUICK_QUOTE;
	}

	public boolean validateVehiclesDEFAULTRB(CommunicationObjectModel aCom, Locale locale) {
		return false;
	}

	protected void verifyProhibitedPostalCode(PolicyVersion ratePolicy, CommunicationObjectModel aCom) {
		// noop
	}

	@Override
	public void validateRoadblocks(CommunicationObjectModel aCom) throws Exception {
	}

	/**
	 * Retrieves the localized facade component matching the company and province found in the provided instance of.
	 *
	 * @param aComContext {@link ComContext}
	 * @return {@link ICommonFacade}
	 * @throws AutoQuoteException the autoquote facade exception
	 * {@link ComContext}.
	 */
	public static IAutoQuoteOfferFacade getInstance(ComContext aComContext) throws AutoQuoteException {
		return (IAutoQuoteOfferFacade) ContextUtil.getInstance(AutoQuoteOfferFacade.class, aComContext);
	}

}
