/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <PERSON>.
 *
 */
public class BaseFacadeIntact implements IBaseFacade {

	private static final Logger LOG = ESAPI.getLogger(BaseFacadeIntact.class);

	@Override
	public void updatePatternBehaviors(PolicyVersion aPolicyVersion, CommunicationObjectModel com) {
		LOG.debug(Logger.EVENT_SUCCESS, "There's not pattern behaviors to apply for Intact.");
	}

	@Override
	public List<OfferTypeCodeEnum> getOfferTypes(ComContext context) {
		return List.of(OfferTypeCodeEnum.CUSTOM);
	}

}
