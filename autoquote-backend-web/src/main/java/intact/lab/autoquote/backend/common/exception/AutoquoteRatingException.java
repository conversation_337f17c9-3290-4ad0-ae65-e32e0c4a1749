/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.exception;


import java.io.Serial;

/**
 * This class is thrown when an error occurs during the rating operations
 * 
 */
public class AutoquoteRatingException extends Exception {

	/** The Constant serialVersionUID. */
	@Serial
	private static final long serialVersionUID = -7874233266293614528L;

	/**
	 * 
	 * @param message the message
	 * @param cause {@link Throwable}
	 */
	public AutoquoteRatingException(String message, Throwable cause) {
		super(message, cause);
	}

	/**
	 * 
	 * @param message the message
	 */
	public AutoquoteRatingException(String message) {
		super(message);
	}
}
