package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.util.DateUtils;
import com.ing.canada.plp.domain.billing.Account;
import com.ing.canada.plp.domain.billing.PaymentSchedule;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.MessageRepositoryEntry;
import com.ing.canada.plp.domain.businesstransaction.TransactionalMessage;
import com.ing.canada.plp.domain.businesstransaction.TransactionalMessageElement;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.coverage.Coverage;
import com.ing.canada.plp.domain.coverage.CoveragePremium;
import com.ing.canada.plp.domain.diagnostics.DiagnosticAutomatedAdvice;
import com.ing.canada.plp.domain.driver.AffinityGroupSpecialConditionRepositoryEntry;
import com.ing.canada.plp.domain.driver.Conviction;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.ActionTakenCodeEnum;
import com.ing.canada.plp.domain.enums.AgreementTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CountryCodeEnum;
import com.ing.canada.plp.domain.enums.DriverLicenseClassCodeEnum;
import com.ing.canada.plp.domain.enums.DriverTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyRoleInRiskTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.RatingRiskTypeCodeEnum;
import com.ing.canada.plp.domain.enums.RelationTypeCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRole;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.insurancerisk.RatingRisk;
import com.ing.canada.plp.domain.insurancerisk.Trailer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoveragePremiumOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.RatingRiskOffer;
import com.ing.canada.plp.domain.partnership.Partnership;
import com.ing.canada.plp.domain.party.CarrierRepositoryEntry;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.party.GeographicalAssessment;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRelation;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.MarketSegment;
import com.ing.canada.plp.domain.policyversion.PolicyAdditionalCoverage;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PriorCarrierPolicyInfo;
import com.ing.canada.plp.domain.policyversion.ReferenceDate;
import com.ing.canada.plp.domain.policyversion.RelatedInsurancePolicy;
import com.ing.canada.plp.domain.usertype.BaseEntity;
import com.ing.canada.plp.domain.vehicle.AntiTheftDevice;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleEquipment;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.ing.canada.plp.service.IBusinessTransactionActivityService;
import com.ing.canada.plp.service.ICarrierRepositoryEntryService;
import com.ing.canada.som.impl.contactPoint.AddressImpl;
import com.ing.canada.som.interfaces.agreement.AffinityGroupRepositoryEntry;
import com.ing.canada.som.interfaces.agreement.DiagnosticAutomatedAdviceRepositoryEntry;
import com.ing.canada.som.interfaces.agreement.InsurancePolicy;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.businessModel.BusinessModel;
import com.ing.canada.som.interfaces.businessModel.EnvironmentContext;
import com.ing.canada.som.interfaces.businessModel.ManufacturingContext;
import com.ing.canada.som.interfaces.businessTransaction.BusinessTransaction;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.contactPoint.Address;
import com.ing.canada.som.interfaces.intermediary.ProducerRepositoryEntry;
import com.ing.canada.som.interfaces.moneyProvision.Billing;
import com.ing.canada.som.interfaces.party.GroupRepositoryEntry;
import com.ing.canada.som.interfaces.party.PartyGroup;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Producer;
import com.ing.canada.som.interfaces.partyRoleInRisk.AdditionalInterest;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo;
import com.ing.canada.som.interfaces.partyRoleInRisk.Insured;
import com.ing.canada.som.interfaces.partyRoleInRisk.Owner;
import com.ing.canada.som.interfaces.physicalObject.VehicleDetailSpec;
import com.ing.canada.som.interfaces.physicalObject.VehicleRepositoryEntry;
import com.ing.canada.som.interfaces.place.MunicipalityDetailSpec;
import com.ing.canada.som.interfaces.place.MunicipalityRepositoryEntry;
import com.ing.canada.som.interfaces.product.CoverageRepositoryEntry;
import com.ing.canada.som.interfaces.registration.DriverLicense;
import com.ing.canada.som.interfaces.risk.CommercialUsage;
import com.ing.canada.som.interfaces.security.Credential;
import com.ing.canada.som.rootclasses.GenericRootObject;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import intact.lab.autoquote.backend.datamediator.DMConstants;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOM;
import intact.lab.autoquote.backend.datamediator.utils.CipherUtils;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import jakarta.persistence.Column;

import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOMLegacyRatingInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Slf4j
@Component("dataMediatorToSOM")
@Scope("prototype")
public class DataMediatorToSOM implements IDataMediatorToSOM {
    private static final String APPLICATION_IDENTIFICATION = "ECOM";
    private static final String IRCA_APPLICATION_IDENTIFICATION = "AQQK";
    private final String applicationId;
    private static final Comparator<DriverLicenseClass> ontarioLicenseClassesComparator = new DriverLicenseClassCodeEnum.OntarioLicenseClassesComparator();
    private static final List<ManufacturerCompanyCodeEnum> NO_CREDIT_SCORE_MANUFACTURERS;
    private static final List<ManufacturerCompanyCodeEnum> SAVERS_MANUFACTURERS;
    protected Map<Long, com.ing.canada.som.interfaces.risk.InsuranceRisk> insuranceRiskCache = new HashMap<>();
    protected Map<Long, VehicleDetailSpec> vehicleDetailSpecCache = new HashMap<>();
    protected Map<Long, VehicleRepositoryEntry> vehicleRepositoryEntryCache = new HashMap<>();
    protected Map<Long, com.ing.canada.som.interfaces.party.Party> partyCache = new HashMap<>();
    protected Map<Long, PartyGroup> partyGroupCache = new HashMap<>();
    protected Map<Long, MunicipalityRepositoryEntry> municipalityRepositoryEntryCache = new HashMap<>();
    protected Map<Long, MunicipalityDetailSpec> municipalityDetailSpecCache = new HashMap<>();
    protected Map<Long, GroupRepositoryEntry> groupRepositoryEntryCache = new HashMap<>();
    protected Map<Long, ProducerRepositoryEntry> producerRepositoryEntryCache = new HashMap<>();
    protected Map<Long, AdditionalInterest> additionalInterestCache = new HashMap<>();
    protected Map<Long, Address> addressCache = new HashMap<>();
    protected Map<Long, Claim> claimEntryCache = new HashMap<>();
    protected IInsuranceRiskOfferHelper insuranceRiskOfferHelper;
    private final ICarrierRepositoryEntryService carrierRepositoryEntryService;
    private final IBusinessTransactionActivityService businessTransactionActivityService;
    private final IDataMediatorToSOMLegacyRatingInfo dmLegacyRatingInfoByPostalCode;
    private final CipherUtils cipherUtils;
    private boolean useEISCoverageCodes;
    public static final Comparator<PolicyHolder> PolicyHolderComparator;


    DataMediatorToSOM(@Qualifier("application-id") final String applicationId, @Lazy final IInsuranceRiskOfferHelper insuranceRiskOfferHelper,
                      final ICarrierRepositoryEntryService carrierRepositoryEntryService,
                      final IBusinessTransactionActivityService businessTransactionActivityService,
                      final IDataMediatorToSOMLegacyRatingInfo dmLegacyRatingInfoByPostalCode,
                      final CipherUtils cipherUtils) {
        this.applicationId = applicationId;
        this.insuranceRiskOfferHelper = insuranceRiskOfferHelper;
        this.carrierRepositoryEntryService = carrierRepositoryEntryService;
        this.businessTransactionActivityService = businessTransactionActivityService;
        this.dmLegacyRatingInfoByPostalCode = dmLegacyRatingInfoByPostalCode;
        this.cipherUtils = cipherUtils;
    }

    public PolicyVersion convertTo_SOM(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion) {
        log.debug("Java:convertToSOM");
        log.trace("Entering DataMediatorToSOM");
        LogFactory.getFactory().getAttributeNames();
        com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersionPriorTrans = plPolicyVersion.getOriginalScenarioPolicyVersion();
        GenericRootObject gro = new GenericRootObject(PolicyVersion.class);
        PolicyVersion somPolicyVersion = (PolicyVersion)gro.createTheRootObject();
        gro.endLogging();
        EnvironmentContext environmentContext = somPolicyVersion.createTheEnvironmentContext();
        environmentContext.setCurrentDate((GregorianCalendar)Calendar.getInstance());
        if (SAVERS_MANUFACTURERS.contains(plPolicyVersion.getInsurancePolicy().getManufacturingContext().getManufacturerCompany())) {
            environmentContext.setApplicationIdentification(this.applicationId);
        } else if (LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(plPolicyVersion.getInsurancePolicy().getLineOfBusiness())) {
            environmentContext.setApplicationIdentification("AQQK");
        } else {
            environmentContext.setApplicationIdentification("ECOM");
        }

        this.createPolicyVersionGraph(plPolicyVersion, somPolicyVersion, false);
        if (plPolicyVersionPriorTrans != null && !AgreementTypeCodeEnum.QUOTATION.equals(plPolicyVersion.getInsurancePolicy().getAgreementType())) {
            PolicyVersion somPolicyVersionPriorTrans = somPolicyVersion.createThePolicyVersionPriorTrans();
            this.createPolicyVersionGraph(plPolicyVersionPriorTrans, somPolicyVersionPriorTrans, true);
            this.createThePriorLinks(plPolicyVersion, somPolicyVersion, somPolicyVersionPriorTrans);
        }

        gro.beginLogging();
        log.trace("Leaving DataMediatorToSOM");
        return somPolicyVersion;
    }

    protected void createThePriorLinks(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion, PolicyVersion somPolicyVersionPriorTrans) {
        log.info("DataMediatorToSOM.createThePriorLinks - start");
        Set<InsuranceRisk> insuranceRiskSet = plPolicyVersion.getInsuranceRisks();
        if (CollectionUtils.isNotEmpty(insuranceRiskSet)) {
            for(InsuranceRisk plInsuranceRisk : insuranceRiskSet) {
                List<com.ing.canada.som.interfaces.risk.InsuranceRisk> somInsuranceRiskList = somPolicyVersion.getTheInsuranceRisk();
                com.ing.canada.som.interfaces.risk.InsuranceRisk matchingInsuranceRisk = null;
                if (CollectionUtils.isNotEmpty(somInsuranceRiskList)) {
                    for(com.ing.canada.som.interfaces.risk.InsuranceRisk insuranceRisk : somInsuranceRiskList) {
                        if (insuranceRisk.getPersistenceUniqueId().equals(plInsuranceRisk.getId().toString())) {
                            matchingInsuranceRisk = insuranceRisk;
                            break;
                        }
                    }
                }

                InsuranceRisk plInsuranceRiskPrior = plInsuranceRisk.getOriginalScenarioInsuranceRisk();
                List<com.ing.canada.som.interfaces.risk.InsuranceRisk> somInsuranceRiskPriorList = somPolicyVersionPriorTrans.getTheInsuranceRisk();
                com.ing.canada.som.interfaces.risk.InsuranceRisk matchingInsuranceRiskPrior = null;
                if (CollectionUtils.isNotEmpty(somInsuranceRiskPriorList) && plInsuranceRiskPrior != null) {
                    for(com.ing.canada.som.interfaces.risk.InsuranceRisk insuranceRiskPrior : somInsuranceRiskPriorList) {
                        if (insuranceRiskPrior.getPersistenceUniqueId().equals(plInsuranceRiskPrior.getId().toString())) {
                            matchingInsuranceRiskPrior = insuranceRiskPrior;
                            break;
                        }
                    }
                }

                if (matchingInsuranceRisk != null && matchingInsuranceRiskPrior != null) {
                    matchingInsuranceRisk.setTheInsuranceRiskPriorTrans(matchingInsuranceRiskPrior);
                }
            }
        }

        Set<Party> partySet = plPolicyVersion.getParties();
        if (CollectionUtils.isNotEmpty(partySet)) {
            for(Party plParty : partySet) {
                List<com.ing.canada.som.interfaces.party.Party> somPartyList = somPolicyVersion.getTheParty();
                DriverComplementInfo matchingDriverComplementInfo = null;
                if (CollectionUtils.isNotEmpty(somPartyList)) {
                    for(com.ing.canada.som.interfaces.party.Party party : somPartyList) {
                        if (party.getPersistenceUniqueId().equals(plParty.getId().toString())) {
                            matchingDriverComplementInfo = party.getTheDriverComplementInfo();
                            break;
                        }
                    }
                }

                List<com.ing.canada.som.interfaces.party.Party> somPartyPriorList = somPolicyVersionPriorTrans.getTheParty();
                DriverComplementInfo matchingDriverComplementInfoPrior = null;
                Party plPartyPrior = plParty.getOriginalScenarioParty();
                if (somPartyPriorList != null && plPartyPrior != null) {
                    for(com.ing.canada.som.interfaces.party.Party partyPrior : somPartyPriorList) {
                        if (plPartyPrior.getId() != null && partyPrior.getPersistenceUniqueId().equals(plPartyPrior.getId().toString())) {
                            matchingDriverComplementInfoPrior = partyPrior.getTheDriverComplementInfo();
                            break;
                        }
                    }
                }

                if (matchingDriverComplementInfo != null && matchingDriverComplementInfoPrior != null) {
                    matchingDriverComplementInfo.setTheDriverComplementInfoPriorTrans(matchingDriverComplementInfoPrior);
                }

                Set<PartyRoleInRisk> partyRoleInRiskSet = plParty.getPartyRoleInRisks();
                if (partyRoleInRiskSet != null) {
                    for(PartyRoleInRisk partyRoleInRisk : partyRoleInRiskSet) {
                        if (PartyRoleInRiskTypeCodeEnum.IS_DRIVER_OF.equals(partyRoleInRisk.getPartyRoleInRiskType())) {
                            Driver matchingSomDriver = null;
                            if (somPartyList != null) {
                                for(com.ing.canada.som.interfaces.party.Party party : somPartyList) {
                                    List<Driver> driverList = party.getTheDriver();
                                    if (driverList != null) {
                                        for(Driver driver : driverList) {
                                            if (driver.getPersistenceUniqueId().equals(partyRoleInRisk.getId().toString())) {
                                                matchingSomDriver = driver;
                                                break;
                                            }
                                        }

                                        if (matchingSomDriver != null) {
                                            break;
                                        }
                                    }
                                }
                            }

                            PartyRoleInRisk partyRoleInRiskPrior = partyRoleInRisk.getOriginalScenarioPartyRoleInRisk();
                            Driver matchingSomDriverPrior = null;
                            if (partyRoleInRiskPrior != null && somPartyPriorList != null) {
                                for(com.ing.canada.som.interfaces.party.Party partyPrior : somPartyPriorList) {
                                    List<Driver> driverPriorList = partyPrior.getTheDriver();
                                    if (driverPriorList != null) {
                                        for(Driver driverPrior : driverPriorList) {
                                            if (driverPrior.getPersistenceUniqueId().equals(partyRoleInRiskPrior.getId().toString())) {
                                                matchingSomDriverPrior = driverPrior;
                                                break;
                                            }
                                        }

                                        if (matchingSomDriverPrior != null) {
                                            break;
                                        }
                                    }
                                }
                            }

                            if (matchingSomDriver != null && matchingSomDriverPrior != null) {
                                matchingSomDriver.setTheDriverPriorTrans(matchingSomDriverPrior);
                            }
                        }
                    }
                }
            }
        }

        log.info("DataMediatorToSOM.createThePriorLinks - end");
    }

    protected void createPolicyVersionGraph(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion, Boolean isPrior) {
        log.debug("DataMediatorToSOM.createPolicyVersionGraph - start");
        this.mapPolicyVersion(plPolicyVersion, somPolicyVersion);
        if (plPolicyVersion.getReferenceDate() != null) {
            this.mapReferenceDate(plPolicyVersion.getReferenceDate(), somPolicyVersion.createTheReferenceDate());
        }

        this.setRelatedInsurancePolicyViaPolicyVersion(plPolicyVersion, somPolicyVersion);
        InsurancePolicy somInsurancePolicy = somPolicyVersion.createTheInsurancePolicy();
        this.mapInsurancePolicy(plPolicyVersion, somInsurancePolicy);
        if (plPolicyVersion.getInsurancePolicy().getRelatedInsurancePolicy() != null) {
            InsurancePolicy somRelatedInsurancePolicy = somInsurancePolicy.createTheInsurancePolicyIsReplacing();
            this.mapRelatedInsurancePolicy(plPolicyVersion.getInsurancePolicy().getRelatedInsurancePolicy(), somRelatedInsurancePolicy);
        }

        ManufacturingContext somManufacturingContext = somInsurancePolicy.createTheManufacturingContext();
        this.mapManufacturingContext(plPolicyVersion.getInsurancePolicy().getManufacturingContext(), somManufacturingContext);
        this.setTheBusinessTransactionTree(plPolicyVersion, somPolicyVersion);
        if (plPolicyVersion.getPriorCarrierPolicyInfo() != null) {
            this.mapPriorCarrierPolicyInfo(plPolicyVersion.getPriorCarrierPolicyInfo(), somPolicyVersion.createThePriorCarrierPolicyInfo(), plPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince(), plPolicyVersion.getInsurancePolicy().getManufacturerCompany());
        }

        this.setProducerRepositoryEntry(plPolicyVersion, somPolicyVersion);

        for(MarketSegment plMarketSegment : plPolicyVersion.getMarketSegments()) {
            this.mapMarketSegments(plMarketSegment, somPolicyVersion.addTheMarketSegment());
        }

        if (CollectionUtils.isNotEmpty(plPolicyVersion.getPolicyAdditionalCoverages())) {
            for(PolicyAdditionalCoverage plPolicyAdditionalCoverage : plPolicyVersion.getPolicyAdditionalCoverages()) {
                com.ing.canada.som.interfaces.agreement.PolicyAdditionalCoverage somCoverage = somPolicyVersion.addThePolicyAdditionalCoverage();
                this.mapPolicyAdditionalCoverage(plPolicyAdditionalCoverage, somCoverage);
                CoverageRepositoryEntry somCoverageRepositoryEntry = somCoverage.createTheCoverageProduct().createTheCoverageRepositoryEntryBase();
                this.mapCoverageRepositoryEntry(plPolicyAdditionalCoverage, somCoverageRepositoryEntry);
            }
        }

        this.setClaimsAndKindOfLossTreeViaPolicyVersion(plPolicyVersion, somPolicyVersion);
        this.setInsuranceRiskTree(plPolicyVersion, somPolicyVersion, somManufacturingContext, isPrior);
        this.setThePartyTree(plPolicyVersion, somPolicyVersion, somManufacturingContext);
        this.setThePolicyHolderTree(plPolicyVersion, somPolicyVersion);
        this.setTheBillingTree(plPolicyVersion, somPolicyVersion);
        this.setTheAffinityGroupTree(plPolicyVersion, somPolicyVersion);
        if (CollectionUtils.isNotEmpty(plPolicyVersion.getPartnerships())) {
            Partnership plPartnership = (Partnership)plPolicyVersion.getPartnerships().iterator().next();
            this.mapPartnership(plPartnership, somPolicyVersion.createThePartnership());
            String advisorNumber = StringUtils.trimToNull(plPartnership.getAdvisorNumber());
            if (advisorNumber != null) {
                somPolicyVersion.getThePartnership().setAdvisorNumber(advisorNumber);
            }

            Party plAdvisor = plPartnership.getAdvisor();
            if (plAdvisor != null) {
                this.mapPartnershipAdvisor(plAdvisor, somPolicyVersion.getThePartnership());
            }
        }

        if (plPolicyVersion.getDirectChanDistRepEntry() != null) {
            this.mapPolicyVersionDistributor(plPolicyVersion, somPolicyVersion);
        }

    }

    private void setThePolicyHolderTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getPolicyHolders() != null) {
            List<PolicyHolder> sortedPolicyHolders = new ArrayList<>(plPolicyVersion.getPolicyHolders());
            sortedPolicyHolders.sort(PolicyHolderComparator);

            for(PolicyHolder plPolicyHolder : sortedPolicyHolders) {
                com.ing.canada.som.interfaces.partyRoleInAgreement.PolicyHolder somPolicyHolder = somPolicyVersion.addThePolicyHolder();
                this.mapPolicyHolder(plPolicyHolder, somPolicyHolder);
                Party plParty = plPolicyHolder.getParty();
                com.ing.canada.som.interfaces.party.Party somParty = (com.ing.canada.som.interfaces.party.Party)this.partyCache.get(plParty.getId());
                if (somParty == null) {
                    throw new SystemException("an association that was supposed to be not nullable seems to have changed...");
                }

                somPolicyHolder.setTheParty(somParty);
                if (NO_CREDIT_SCORE_MANUFACTURERS.contains(plPolicyVersion.getInsurancePolicy().getManufacturingContext().getManufacturerCompany())) {
                    somParty.createTheEnvironmentContext().setApplicationIdentification(this.applicationId);
                } else {
                    somParty.createTheEnvironmentContext().setApplicationIdentification("ECOM");
                }
            }

        }
    }

    private void setTheAffinityGroupTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getAffinityGroupRepositoryEntry() != null) {
            AffinityGroupRepositoryEntry somAff = somPolicyVersion.createTheAffinityGroupRepositoryEntry();
            this.mapAffinityGroupRepositoryEntry(plPolicyVersion.getAffinityGroupRepositoryEntry(), somAff);

            for(AffinityGroupSpecialConditionRepositoryEntry agscre : plPolicyVersion.getAffinityGroupRepositoryEntry().getAffinityGroupSpecialConditionRepositoryEntries()) {
                com.ing.canada.som.interfaces.agreement.AffinityGroupSpecialConditionRepositoryEntry somAffSpecial = somAff.addTheAffinityGroupSpecialConditionRepositoryEntry();
                somAffSpecial.setAffinityGroupSpecialConditionCode(agscre.getAffinityGroupSpecialConditionCode());
            }

            somPolicyVersion.setAffinityGroupCode(plPolicyVersion.getAffinityGroupRepositoryEntry().getAffinityGroupCode());
        }

    }

    protected void setTheVehicleDetailsSpec(Vehicle plVehicle, com.ing.canada.som.interfaces.physicalObject.Vehicle somVehicle) {
        if (plVehicle.getVehicleDetailSpecificationRepositoryEntry() != null) {
            VehicleDetailSpec somVehicleDetailSpec = (VehicleDetailSpec)this.vehicleDetailSpecCache.get(plVehicle.getVehicleDetailSpecificationRepositoryEntry().getId());
            if (somVehicleDetailSpec == null) {
                somVehicleDetailSpec = somVehicle.createTheVehicleDetailSpec();
                this.mapVehicleDetailSpecificationRepositoryEntry(plVehicle.getVehicleDetailSpecificationRepositoryEntry(), somVehicleDetailSpec);
                this.vehicleDetailSpecCache.put(plVehicle.getVehicleDetailSpecificationRepositoryEntry().getId(), somVehicleDetailSpec);
            } else {
                somVehicle.setTheVehicleDetailSpec(somVehicleDetailSpec);
            }

            if (plVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry() != null) {
                VehicleRepositoryEntry somVehicleRepositoryEntry = (VehicleRepositoryEntry)this.vehicleRepositoryEntryCache.get(plVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry().getId());
                if (somVehicleRepositoryEntry == null) {
                    somVehicleRepositoryEntry = somVehicle.getTheVehicleDetailSpec().createTheVehicleRepositoryEntry();
                    this.mapVehicleRepositoryEntry(plVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry(), somVehicleRepositoryEntry);
                    this.vehicleRepositoryEntryCache.put(plVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry().getId(), somVehicleRepositoryEntry);
                } else {
                    somVehicle.getTheVehicleDetailSpec().setTheVehicleRepositoryEntry(somVehicleRepositoryEntry);
                }
            }
        }

    }

    protected void setInsuranceRiskTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion, ManufacturingContext somManufacturingContext, Boolean isPrior) {
        if (plPolicyVersion.getInsuranceRisks() != null) {
            for(InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRisk.getId());
                if (somInsuranceRisk != null) {
                    somPolicyVersion.addTheInsuranceRisk(somInsuranceRisk);
                } else {
                    somInsuranceRisk = somPolicyVersion.addTheInsuranceRisk();
                    this.mapInsuranceRisk(plInsuranceRisk, somInsuranceRisk);
                    this.mapInsuranceRiskCommercialUsage(plInsuranceRisk, somInsuranceRisk);
                    this.insuranceRiskCache.put(plInsuranceRisk.getId(), somInsuranceRisk);
                }

                this.setClaimsAndKindOfLossTreeViaInsuranceRisk(plInsuranceRisk, somInsuranceRisk);
                Vehicle plVehicle = plInsuranceRisk.getVehicle();
                if (plVehicle != null) {
                    com.ing.canada.som.interfaces.physicalObject.Vehicle somVehicle = somInsuranceRisk.createTheVehicle();
                    this.mapVehicle(plVehicle, somVehicle, plInsuranceRisk, somInsuranceRisk);
                    if (plVehicle.getVehicleEquipments() != null) {
                        for(VehicleEquipment plVehicleEquipment : plVehicle.getVehicleEquipments()) {
                            this.mapVehicleEquipment(plVehicleEquipment, somVehicle.addTheVehicleEquipment());
                        }
                    }

                    if (plVehicle.getAntiTheftDevices() != null) {
                        for(AntiTheftDevice plAntiTheftDevice : plVehicle.getAntiTheftDevices()) {
                            this.mapAntiTheftDevices(plAntiTheftDevice, somVehicle.addTheAntiTheftDevice());
                        }
                    }

                    this.setTheVehicleDetailsSpec(plVehicle, somVehicle);
                    if (plVehicle.getAdditionalInterestRoles() != null) {
                        for(AdditionalInterestRole plAdditionalInterestRole : plVehicle.getAdditionalInterestRoles()) {
                            AdditionalInterest somAdditionalInterest = (AdditionalInterest)this.additionalInterestCache.get(plAdditionalInterestRole.getId());
                            if (somAdditionalInterest != null) {
                                somVehicle.addTheAdditionalInterest(somAdditionalInterest);
                            } else {
                                somAdditionalInterest = somVehicle.addTheAdditionalInterest();
                                this.mapAdditionalInterestRole(plAdditionalInterestRole, somAdditionalInterest);
                                this.additionalInterestCache.put(plAdditionalInterestRole.getId(), somAdditionalInterest);
                            }
                        }
                    }
                }

                Trailer plTrailer = plInsuranceRisk.getTrailer();
                if (plTrailer != null) {
                    com.ing.canada.som.interfaces.physicalObject.Trailer somTrailer = somInsuranceRisk.createTheTrailer();
                    this.mapTrailer(plTrailer, somTrailer);
                    somTrailer.setTheVehicle(somInsuranceRisk.getTheVehicle());
                    if (plTrailer.getAdditionalInterestRoles() != null) {
                        for(AdditionalInterestRole plAdditionalInterestRole : plTrailer.getAdditionalInterestRoles()) {
                            AdditionalInterest somAdditionalInterest = (AdditionalInterest)this.additionalInterestCache.get(plAdditionalInterestRole.getId());
                            if (somAdditionalInterest != null) {
                                somTrailer.addTheAdditionalInterest(somAdditionalInterest);
                            } else {
                                somAdditionalInterest = somTrailer.addTheAdditionalInterest();
                                this.mapAdditionalInterestRole(plAdditionalInterestRole, somAdditionalInterest);
                                this.additionalInterestCache.put(plAdditionalInterestRole.getId(), somAdditionalInterest);
                            }
                        }
                    }
                }

                com.ing.canada.plp.domain.party.Address plAddress = plInsuranceRisk.getAddress();
                if (plAddress != null) {
                    Address somAddress = (Address)this.addressCache.get(plAddress.getId());
                    if (somAddress != null) {
                        somInsuranceRisk.setTheAddress(somAddress);
                    } else {
                        somAddress = somInsuranceRisk.createTheAddress();
                        this.mapAddress(plAddress, somAddress);
                        this.addressCache.put(plAddress.getId(), somAddress);
                    }

                    this.setAddressTree(plAddress, somAddress, somManufacturingContext);
                    GeographicalAssessment plGeographicalAssessment = plAddress.getGeographicalAssessment();
                    if (plGeographicalAssessment != null) {
                        com.ing.canada.som.interfaces.place.GeographicalAssessment somGeographicalAssessment = somAddress.getTheGeographicalAssessment();
                        if (somGeographicalAssessment == null) {
                            somGeographicalAssessment = somAddress.createTheGeographicalAssessment();
                            this.mapGeographicalAssessment(plGeographicalAssessment, somGeographicalAssessment);
                        }
                    }
                }

                CreditScore plCreditScore = plInsuranceRisk.getCreditScorePostalCode();
                if (plCreditScore != null) {
                    com.ing.canada.som.interfaces.party.CreditScore somCreditScore = somInsuranceRisk.getTheCreditScorePostalCode();
                    if (somCreditScore == null) {
                        somCreditScore = somInsuranceRisk.createTheCreditScorePostalCode();
                    }

                    this.mapCreditScore(plCreditScore, somCreditScore);
                }

                Set<DiagnosticAutomatedAdvice> plDiagnosticSet = plInsuranceRisk.getDiagnosticAdvices();
                if (plDiagnosticSet != null) {
                    for(DiagnosticAutomatedAdvice plDiagnostic : plDiagnosticSet) {
                        com.ing.canada.som.interfaces.agreement.DiagnosticAutomatedAdvice somDiagnostic = somInsuranceRisk.addTheDiagnosticAutomatedAdvice();
                        this.mapDiagnostic(plDiagnostic, somDiagnostic);
                        DiagnosticAutomatedAdviceRepositoryEntry somDiagnosticRepositoryEntry = somDiagnostic.createTheDiagnosticAutomatedAdviceRepositoryEntry();
                        this.mapDiagnosticRepositoryEntry(plDiagnostic, somDiagnosticRepositoryEntry);
                    }
                }

                if (!isPrior) {
                    this.setInsuranceRiskOffers(plPolicyVersion, somPolicyVersion, somInsuranceRisk, plInsuranceRisk);
                } else {
                    Set<RatingRisk> ratingRiskSet = plInsuranceRisk.getRatingRisks();
                    if (ratingRiskSet != null) {
                        for(RatingRisk plRatingRisk : ratingRiskSet) {
                            com.ing.canada.som.interfaces.risk.RatingRisk somRatingRisk;
                            if (RatingRiskTypeCodeEnum.PRINCIPAL_RISK.equals(plRatingRisk.getRatingRiskType())) {
                                somRatingRisk = somInsuranceRisk.createTheRatingRiskPrincipal();
                            } else {
                                somRatingRisk = somInsuranceRisk.createTheRatingRiskOccasional();
                            }

                            this.mapRatingRisk(plRatingRisk, somRatingRisk);
                            this.mapGroupRepositoryEntryOnRatingRisk(plRatingRisk, somRatingRisk.createTheGroupRepositoryEntry());
                        }
                    }

                    Set<Coverage> coverageSet = plInsuranceRisk.getCoverages();
                    if (coverageSet != null) {
                        for(Coverage plCoverage : coverageSet) {
                            com.ing.canada.som.interfaces.risk.Coverage somCoverage = somInsuranceRisk.addTheCoverage();
                            this.mapCoverage(plCoverage, somCoverage);
                            CoverageRepositoryEntry somCoverageRepositoryEntry = somCoverage.createTheCoverageProduct().createTheCoverageRepositoryEntryBase();
                            this.mapCoverageRepositoryEntry((BaseCoverage)plCoverage, somCoverageRepositoryEntry);
                            if (plCoverage.getCoveragePremiums() != null) {
                                for(CoveragePremium plCoveragePremium : plCoverage.getCoveragePremiums()) {
                                    com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium somCoveragePremium;
                                    if (RatingRiskTypeCodeEnum.PRINCIPAL_RISK.equals(plCoveragePremium.getRatingRisk().getRatingRiskType())) {
                                        somCoveragePremium = somCoverage.createTheCoveragePremiumPrincipal();
                                    } else {
                                        somCoveragePremium = somCoverage.createTheCoveragePremiumOccasional();
                                    }

                                    this.mapCoveragePremium(plCoveragePremium, somCoveragePremium);
                                }
                            }
                        }
                    }
                }

                this.dmLegacyRatingInfoByPostalCode.convertToSom(plInsuranceRisk, somInsuranceRisk);
            }
        }

    }

    protected void setInsuranceRiskOffers(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk, InsuranceRisk plInsuranceRisk) {
        if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode())) {
            for(InsuranceRiskOffer iro : this.insuranceRiskOfferHelper.getInsuranceRiskOfferForRisk(plInsuranceRisk)) {
                if (iro.getOfferType().equals(OfferTypeCodeEnum.CUSTOM)) {
                    this.setInsuranceRiskOfferTree(somInsuranceRisk, iro, true);
                } else {
                    PolicyVersion somPolicyVersionOffer = null;
                    List<PolicyVersion> policyVersionOffers = somPolicyVersion.getThePolicyVersionOffer();
                    String offerTypeCode = iro.getOfferType().getCode();

                    for(PolicyVersion pv : policyVersionOffers) {
                        if (offerTypeCode.equals(((com.ing.canada.som.interfaces.risk.InsuranceRisk)pv.getTheInsuranceRisk().get(0)).getOfferType())) {
                            somPolicyVersionOffer = pv;
                            break;
                        }
                    }

                    if (somPolicyVersionOffer == null) {
                        somPolicyVersionOffer = somPolicyVersion.addThePolicyVersionOffer();
                    }

                    com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRiskOffer = somPolicyVersionOffer.addTheInsuranceRisk();
                    this.mapInsuranceRisk(plInsuranceRisk, somInsuranceRiskOffer);
                    this.setInsuranceRiskOfferTree(somInsuranceRiskOffer, iro, false);
                }
            }
        } else {
            InsuranceRiskOffer plInsuranceRiskOffer = plInsuranceRisk.getSelectedInsuranceRiskOffer();
            if (plInsuranceRiskOffer != null) {
                this.setInsuranceRiskOfferTree(somInsuranceRisk, plInsuranceRiskOffer, true);
            }
        }

    }

    protected void setInsuranceRiskOfferTree(com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk, InsuranceRiskOffer plInsuranceRiskOffer, boolean isRoot) {
        this.mapInsuranceRiskOffer(plInsuranceRiskOffer, somInsuranceRisk);
        if (isRoot) {
            Set<RatingRiskOffer> ratingRiskOfferSet = plInsuranceRiskOffer.getRatingRiskOffers();
            if (ratingRiskOfferSet != null) {
                for(RatingRiskOffer plRatingRiskOffer : ratingRiskOfferSet) {
                    com.ing.canada.som.interfaces.risk.RatingRisk somRatingRiskOffer = null;
                    if (RatingRiskTypeCodeEnum.PRINCIPAL_RISK.equals(plRatingRiskOffer.getRatingRiskType())) {
                        somRatingRiskOffer = somInsuranceRisk.createTheRatingRiskPrincipal();
                    } else {
                        somRatingRiskOffer = somInsuranceRisk.createTheRatingRiskOccasional();
                    }

                    this.mapRatingRiskOffer(plRatingRiskOffer, somRatingRiskOffer);
                    this.mapGroupRepositoryEntryOnRatingRiskOffer(plRatingRiskOffer, somRatingRiskOffer.createTheGroupRepositoryEntry());
                }
            }
        }

        Set<CoverageOffer> coverageOfferSet = plInsuranceRiskOffer.getCoverageOffers();
        if (coverageOfferSet != null) {
            for(CoverageOffer plCoverageOffer : coverageOfferSet) {
                com.ing.canada.som.interfaces.risk.Coverage somCoverageOffer = somInsuranceRisk.addTheCoverage();
                this.mapCoverageOffer(plCoverageOffer, somCoverageOffer);
                CoverageRepositoryEntry somCoverageRepositoryEntry = somCoverageOffer.createTheCoverageProduct().createTheCoverageRepositoryEntryBase();
                this.mapCoverageRepositoryEntry((BaseCoverage)plCoverageOffer, somCoverageRepositoryEntry);
                if (plCoverageOffer.getCoveragePremiumOffers() != null) {
                    for(CoveragePremiumOffer plCoveragePremiumOffer : plCoverageOffer.getCoveragePremiumOffers()) {
                        com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium somCoveragePremiumOffer = null;
                        if (RatingRiskTypeCodeEnum.PRINCIPAL_RISK.equals(plCoveragePremiumOffer.getRatingRiskOffer().getRatingRiskType())) {
                            somCoveragePremiumOffer = somCoverageOffer.createTheCoveragePremiumPrincipal();
                        } else {
                            somCoveragePremiumOffer = somCoverageOffer.createTheCoveragePremiumOccasional();
                        }

                        this.mapCoveragePremiumOffer(plCoveragePremiumOffer, somCoveragePremiumOffer);
                    }
                }
            }
        }

        Set<DiagnosticAutomatedAdvice> diagnosticsSet = plInsuranceRiskOffer.getDiagnosticAdvices();
        if (diagnosticsSet != null) {
            for(DiagnosticAutomatedAdvice plDiagnostic : diagnosticsSet) {
                com.ing.canada.som.interfaces.agreement.DiagnosticAutomatedAdvice somDiagnostic = somInsuranceRisk.addTheDiagnosticAutomatedAdvice();
                this.mapDiagnostic(plDiagnostic, somDiagnostic);
                DiagnosticAutomatedAdviceRepositoryEntry somDiagnosticRepositoryEntry = somDiagnostic.createTheDiagnosticAutomatedAdviceRepositoryEntry();
                this.mapDiagnosticRepositoryEntry(plDiagnostic, somDiagnosticRepositoryEntry);
            }
        }

    }

    protected void setTheBillingTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getBilling() != null) {
            Billing somBilling = somPolicyVersion.createTheBilling();
            this.mapBilling(plPolicyVersion.getBilling(), somBilling);
            if (plPolicyVersion.getBilling().getPaymentSchedules() != null) {
                for(PaymentSchedule plPaymentSchedule : plPolicyVersion.getBilling().getPaymentSchedules()) {
                    this.mapPaymentSchedule(plPaymentSchedule, somBilling.addThePaymentSchedule());
                }
            }

            if (plPolicyVersion.getBilling().getAccount() != null) {
                this.mapAccount(plPolicyVersion.getBilling().getAccount(), somBilling.createTheAccount());
            }

            com.ing.canada.plp.domain.party.PartyGroup plPartyGroup = plPolicyVersion.getBilling().getPartyGroupSalaryDeduction();
            if (plPartyGroup != null) {
                PartyGroup somPartyGroup = (PartyGroup)this.partyGroupCache.get(plPartyGroup.getId());
                if (somPartyGroup != null) {
                    somBilling.setThePartyGroupSalaryDeduction(somPartyGroup);
                }
            }
        }

    }

    protected void setThePartyTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion, ManufacturingContext somManufacturingContext) {
        if (plPolicyVersion.getParties() != null) {
            LanguageCodeEnum languageCode = plPolicyVersion.getLanguageOfCommunication();
            String language = languageCode == null ? "" : languageCode.getCode();

            for(Party plParty : plPolicyVersion.getParties()) {
                com.ing.canada.som.interfaces.party.Party somParty = (com.ing.canada.som.interfaces.party.Party)this.partyCache.get(plParty.getId());
                if (somParty != null) {
                    somPolicyVersion.addTheParty(somParty);
                } else {
                    somParty = somPolicyVersion.addTheParty();
                    this.mapParty(plParty, somParty);
                    somParty.setTheManufacturingContext(somManufacturingContext);
                    this.partyCache.put(plParty.getId(), somParty);
                }

                this.setClaimsAndKindOfLossTreeViaParty(plParty, somParty);
                if (plParty.getConsents() != null) {
                    for(Consent plConsent : plParty.getConsents()) {
                        this.mapConsent(plConsent, somParty.addTheConsent());
                    }
                }

                com.ing.canada.plp.domain.driver.DriverComplementInfo plDriverComplementInfo = plParty.getDriverComplementInfo();
                if (plDriverComplementInfo != null) {
                    DriverComplementInfo somDriverComplementInfo = somParty.createTheDriverComplementInfo();
                    this.mapDriverComplementInfo(plDriverComplementInfo, somDriverComplementInfo);
                    DriverLicense somDriverLicense = somParty.addTheDriverLicense();
                    this.mapDriverLicence(plDriverComplementInfo, somManufacturingContext, somParty, somDriverLicense);
                    if (plDriverComplementInfo.getRelatedInsurancePolicy() != null) {
                        InsurancePolicy somRelatedInsurancePolicy = somDriverComplementInfo.createTheInsurancePolicyParents();
                        this.mapRelatedInsurancePolicy(plDriverComplementInfo.getRelatedInsurancePolicy(), somRelatedInsurancePolicy);
                    }

                    if (plDriverComplementInfo.getConvictions() != null) {
                        for(Conviction plConviction : plDriverComplementInfo.getConvictions()) {
                            this.mapConviction(plConviction, somDriverComplementInfo.addTheConviction());
                            this.mapConviction(plConviction, somDriverLicense.addTheConviction());
                        }
                    }

                    if (plDriverComplementInfo.getDriverLicenseClasses() != null) {
                        List<DriverLicenseClass> orderedDriverLicenseClasses = new ArrayList(plDriverComplementInfo.getDriverLicenseClasses());
                        Collections.sort(orderedDriverLicenseClasses, ontarioLicenseClassesComparator);

                        for(DriverLicenseClass plDriverLicenseClass : orderedDriverLicenseClasses) {
                            this.mapDriverLicenceClass(plDriverLicenseClass, somDriverComplementInfo.addTheDriverLicenseClass());
                            this.mapDriverLicenceClass(plDriverLicenseClass, somDriverLicense.addTheDriverLicenseClass());
                        }
                    }
                }

                if (plParty.getPartyRelationTo() != null) {
                    for(PartyRelation plPartyRelation : plParty.getPartyRelationTo()) {
                        this.mapPartyRelation(plPartyRelation, somParty.addThePartyRelationTo());
                    }
                }

                if (plParty.getCreditScores() != null) {
                    for(CreditScore plCreditScore : plParty.getCreditScores()) {
                        this.mapCreditScore(plCreditScore, somParty.addTheCreditScore());
                        if (somParty.getTheCreditScore() != null && somParty.getTheCreditScore().size() > 0 && "B".equals(somManufacturingContext.getDistributionChannel()) && "AB".equals(somManufacturingContext.getProvince()) && "R".equals(somManufacturingContext.getInsuranceBusiness())) {
                            somParty.getTheCreditScore(0).setCreditScoreCategory("RATED");
                        }
                    }
                }

                if (plParty.getPhones() != null) {
                    for(Phone plPhone : plParty.getPhones()) {
                        this.mapPhone(plPhone, somParty.addThePhone());
                    }
                }

                if (plParty.getAddresses() != null) {
                    for(com.ing.canada.plp.domain.party.Address plAddress : plParty.getAddresses()) {
                        Address somAddress = this.addressCache.get(plAddress.getId());
                        if (somAddress != null) {
                            somParty.addTheAddress(somAddress);
                        } else {
                            somAddress = somParty.addTheAddress();
                            this.mapAddress(plAddress, somAddress);
                            this.addressCache.put(plAddress.getId(), somAddress);
                        }

                        this.setAddressTree(plAddress, somAddress, somManufacturingContext);
                        GeographicalAssessment plGeographicalAssessment = plAddress.getGeographicalAssessment();
                        if (plGeographicalAssessment != null) {
                            com.ing.canada.som.interfaces.place.GeographicalAssessment somGeographicalAssessment = somAddress.getTheGeographicalAssessment();
                            if (somGeographicalAssessment == null) {
                                somGeographicalAssessment = somAddress.createTheGeographicalAssessment();
                                this.mapGeographicalAssessment(plGeographicalAssessment, somGeographicalAssessment);
                            }
                        }
                    }
                }

                if (plParty.getPartyRoleInRisks() != null) {
                    for(PartyRoleInRisk plPartyRoleInRisk : plParty.getPartyRoleInRisks()) {
                        if (PartyRoleInRiskTypeCodeEnum.IS_DRIVER_OF.equals(plPartyRoleInRisk.getPartyRoleInRiskType())) {
                            Driver somDriver = somParty.addTheDriver();
                            this.mapPartyRoleInRiskOnDriver(plPartyRoleInRisk, somDriver);
                            InsuranceRisk plInsuranceRiskViaPartyRoleInRisk = plPartyRoleInRisk.getInsuranceRisk();
                            if (plInsuranceRiskViaPartyRoleInRisk != null) {
                                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRiskViaPartyRoleInRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRiskViaPartyRoleInRisk.getId());
                                if (somInsuranceRiskViaPartyRoleInRisk != null) {
                                    somDriver.setTheInsuranceRisk(somInsuranceRiskViaPartyRoleInRisk);
                                    if (plPartyRoleInRisk.getDriverType() == DriverTypeCodeEnum.PRINCIPAL) {
                                        Insured insured = somInsuranceRiskViaPartyRoleInRisk.addTheInsured();
                                        insured.setInsuredType("P");
                                        if (!plParty.getCreditScores().isEmpty()) {
                                            for(CreditScore creditScore : plParty.getCreditScores()) {
                                                com.ing.canada.som.interfaces.party.CreditScore cs = insured.addTheCreditScore();
                                                cs.setCreditScore(creditScore.getCreditScore());
                                                cs.setCreditScoreCategory("RATED");
                                            }
                                        }

                                        if (!plParty.getConsents().isEmpty()) {
                                            for(Consent consent : plParty.getConsents()) {
                                                if (consent.getConsentType() == ConsentTypeCodeEnum.CREDIT_SCORE) {
                                                    com.ing.canada.som.interfaces.party.Consent ct = insured.addTheConsent();
                                                    ct.setConsentInd(consent.getConsentIndicator() ? "Y" : "N");
                                                    ct.setConsentType("CS");
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    log.error("An impossible situation has occured... a PartyRoleInRisk is linked to a InsuranceRisk of another PolicyVersion");
                                }
                            }
                        } else if (PartyRoleInRiskTypeCodeEnum.IS_OWNER_OF.equals(plPartyRoleInRisk.getPartyRoleInRiskType())) {
                            Owner somOwner = somParty.addTheOwner();
                            this.mapPartyRoleInRiskOnOwner(plPartyRoleInRisk, somOwner);
                            InsuranceRisk plInsuranceRiskViaPartyRoleInRisk = plPartyRoleInRisk.getInsuranceRisk();
                            if (plInsuranceRiskViaPartyRoleInRisk != null) {
                                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRiskViaPartyRoleInRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRiskViaPartyRoleInRisk.getId());
                                if (somInsuranceRiskViaPartyRoleInRisk != null) {
                                    somOwner.setTheInsuranceRisk(somInsuranceRiskViaPartyRoleInRisk);
                                } else {
                                    log.error("An impossible situation has occured... a PartyRoleInRisk is linked to a InsuranceRisk of another PolicyVersion");
                                }
                            }
                        }
                    }
                }

                if (plParty.getPartyGroups() != null) {
                    for(com.ing.canada.plp.domain.party.PartyGroup plPartyGroup : plParty.getPartyGroups()) {
                        com.ing.canada.plp.domain.party.GroupRepositoryEntry plGroupRepositoryEntry = plPartyGroup.getGroupRepositoryEntry();
                        PartyGroup somPartyGroup = null;
                        somPartyGroup = (PartyGroup)this.partyGroupCache.get(plPartyGroup.getId());
                        if (somPartyGroup != null) {
                            somParty.addThePartyGroup(somPartyGroup);
                        } else {
                            somPartyGroup = somParty.addThePartyGroup();
                            this.mapPartyGroup(plPartyGroup, somPartyGroup);
                            this.partyGroupCache.put(plPartyGroup.getId(), somPartyGroup);
                        }

                        if (plGroupRepositoryEntry != null) {
                            GroupRepositoryEntry somGroupRepositoryEntry = (GroupRepositoryEntry)this.groupRepositoryEntryCache.get(plGroupRepositoryEntry.getId());
                            if (somGroupRepositoryEntry != null) {
                                somPartyGroup.setTheGroupRepositoryEntry(somGroupRepositoryEntry);
                            } else {
                                somGroupRepositoryEntry = somPartyGroup.createTheGroupRepositoryEntry();
                                this.mapGroupRepositoryEntry(plGroupRepositoryEntry, somGroupRepositoryEntry, language);
                                this.groupRepositoryEntryCache.put(plGroupRepositoryEntry.getId(), somGroupRepositoryEntry);
                            }

                            somGroupRepositoryEntry.setTheManufacturingContext(somManufacturingContext);
                        }
                    }
                }

                if (plParty.getAdditionalInterestRoles() != null) {
                    for(AdditionalInterestRole plAdditionalInterestRole : plParty.getAdditionalInterestRoles()) {
                        AdditionalInterest somAdditionalInterest = (AdditionalInterest)this.additionalInterestCache.get(plAdditionalInterestRole.getId());
                        if (somAdditionalInterest != null) {
                            somParty.addTheAdditionalInterest(somAdditionalInterest);
                        } else {
                            somAdditionalInterest = somParty.addTheAdditionalInterest();
                            this.mapAdditionalInterestRole(plAdditionalInterestRole, somAdditionalInterest);
                            this.additionalInterestCache.put(plAdditionalInterestRole.getId(), somAdditionalInterest);
                        }
                    }
                }

                if (plPolicyVersion.getDirectChanDistRepEntry() != null) {
                    this.mapPartyDistributor(plPolicyVersion, somParty);
                }
            }

        }
    }

    protected void mapPartyRelation(PartyRelation plPartyRelation, com.ing.canada.som.interfaces.party.PartyRelation somPartyRelation) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartyRelationAttributeNames, DMConstants.somPartyRelationAttributeNames, plPartyRelation, somPartyRelation);
        this.mapParty(plPartyRelation.getPartyTo(), somPartyRelation.createThePartyTo());
    }

    protected void mapPolicyVersionDistributor(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (somPolicyVersion.getTheDistributor() == null) {
            somPolicyVersion.createTheDistributor();
        }

        if (somPolicyVersion.getTheDistributor().getTheDistributorRepositoryEntry() == null) {
            somPolicyVersion.getTheDistributor().createTheDistributorRepositoryEntry();
        }

        somPolicyVersion.getTheDistributor().getTheDistributorRepositoryEntry().setDirectChannelDistributorCode(plPolicyVersion.getDirectChanDistRepEntry().getCode().getCode());
    }

    protected void mapPartyDistributor(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, com.ing.canada.som.interfaces.party.Party somParty) {
        if (somParty.getTheDistributorRepositoryEntry() == null) {
            somParty.createTheDistributorRepositoryEntry();
        }

        somParty.getTheDistributorRepositoryEntry().setDirectChannelDistributorCode(plPolicyVersion.getDirectChanDistRepEntry().getCode().getCode());
    }

    protected void setAddressTree(com.ing.canada.plp.domain.party.Address plAddress, Address somAddress, ManufacturingContext somManufacturingContext) {
        boolean isMunicipalityDetailSpecFromCache = false;
        MunicipalityDetailSpecification plMunicipalityDetailSpecification = plAddress.getMunicipalityDetailSpecification();
        if (plMunicipalityDetailSpecification != null) {
            MunicipalityDetailSpec somMunicipalityDetailSpec = (MunicipalityDetailSpec)this.municipalityDetailSpecCache.get(plMunicipalityDetailSpecification.getId());
            if (somMunicipalityDetailSpec != null) {
                somAddress.setTheMunicipalityDetailSpec(somMunicipalityDetailSpec);
                isMunicipalityDetailSpecFromCache = true;
            } else {
                somMunicipalityDetailSpec = somAddress.createTheMunicipalityDetailSpec();
                this.mapMunicipalityDetailSpecification(plMunicipalityDetailSpecification, somMunicipalityDetailSpec);
                this.municipalityDetailSpecCache.put(plMunicipalityDetailSpecification.getId(), somMunicipalityDetailSpec);
            }

            somMunicipalityDetailSpec.setTheManufacturingContext(somManufacturingContext);
            if (!isMunicipalityDetailSpecFromCache) {
                com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry plMunicipalityRepositoryEntry = plMunicipalityDetailSpecification.getMunicipalityRepositoryEntry();
                if (plMunicipalityRepositoryEntry != null) {
                    MunicipalityRepositoryEntry somMunicipalityRepositoryEntry = (MunicipalityRepositoryEntry)this.municipalityRepositoryEntryCache.get(plMunicipalityDetailSpecification.getId());
                    if (somMunicipalityRepositoryEntry != null) {
                        somMunicipalityDetailSpec.setTheMunicipalityRepositoryEntry(somMunicipalityRepositoryEntry);
                    } else {
                        somMunicipalityRepositoryEntry = somMunicipalityDetailSpec.createTheMunicipalityRepositoryEntry();
                        this.mapMunicipalityRepositoryEntry(plMunicipalityDetailSpecification.getMunicipalityRepositoryEntry(), somMunicipalityRepositoryEntry);
                        this.municipalityRepositoryEntryCache.put(plMunicipalityRepositoryEntry.getId(), somMunicipalityRepositoryEntry);
                    }
                }
            }
        }

    }

    protected void setProducerRepositoryEntry(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getProducer() != null) {
            Producer somProducer = somPolicyVersion.createTheProducer();
            this.mapProducer(plPolicyVersion.getProducer(), somProducer);
            com.ing.canada.plp.domain.policyversion.ProducerRepositoryEntry plProducerRepositoryEntry = plPolicyVersion.getProducer().getProducerRepositoryEntry();
            if (plProducerRepositoryEntry != null) {
                ProducerRepositoryEntry somProducerRepositoryEntry = (ProducerRepositoryEntry)this.producerRepositoryEntryCache.get(plProducerRepositoryEntry.getId());
                if (somProducerRepositoryEntry != null) {
                    somProducer.setTheProducerRepositoryEntry(somProducerRepositoryEntry);
                } else {
                    somProducerRepositoryEntry = somProducer.createTheProducerRepositoryEntry();
                    this.mapProducerRepositoryEntry(plProducerRepositoryEntry, somProducerRepositoryEntry);
                    this.producerRepositoryEntryCache.put(plProducerRepositoryEntry.getId(), somProducerRepositoryEntry);
                }
            }
        }

    }

    protected void setTheBusinessTransactionTree(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getBusinessTransaction() != null) {
            BusinessTransaction somBusinessTransaction = somPolicyVersion.createTheBusinessTransaction();
            this.mapBusinessTransaction(plPolicyVersion.getBusinessTransaction(), somBusinessTransaction);
            Set<BusinessTransactionActivity> btas = plPolicyVersion.getBusinessTransaction().getBusinessTransactionActivities();
            if (CollectionUtils.isNotEmpty(btas)) {
                BusinessTransactionActivity bta = this.businessTransactionActivityService.findLastBusinessTransactionActivity(plPolicyVersion.getBusinessTransaction());
                if (bta != null && bta.getUserType() != null) {
                    somBusinessTransaction.setOriginatorUserType(bta.getUserType().getCode());
                }

                for(BusinessTransactionActivity plBusinessTransactionActivity : plPolicyVersion.getBusinessTransaction().getBusinessTransactionActivities()) {
                    com.ing.canada.som.interfaces.businessTransaction.BusinessTransactionActivity somBusinessTransactionActivity = somBusinessTransaction.addTheBusinessTransactionActivity();
                    this.mapBusinessTransactionActivity(plBusinessTransactionActivity, somBusinessTransactionActivity);
                    Credential somCredential = somBusinessTransactionActivity.createTheCredential();
                    this.mapCredential(plBusinessTransactionActivity, somCredential);
                }
            }
        }

    }

    protected void setRelatedInsurancePolicyViaPolicyVersion(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getRelatedInsurancePolicies() != null) {
            for(RelatedInsurancePolicy plRelatedInsurancePolicy : plPolicyVersion.getRelatedInsurancePolicies()) {
                InsurancePolicy somRelatedInsurancePolicy = null;
                RelationTypeCodeEnum plRelationType = plRelatedInsurancePolicy.getRelationType();
                if (plRelationType != null) {
                    if (plRelationType.equals(RelationTypeCodeEnum.COMBINED)) {
                        somRelatedInsurancePolicy = somPolicyVersion.createTheInsurancePolicyCombinedWith();
                    } else if (plRelationType.equals(RelationTypeCodeEnum.OTHER) || plRelationType.equals(RelationTypeCodeEnum.OTHER_DRIVER_WITH_COMPANY_IN_HOUSEHOLD)) {
                        somRelatedInsurancePolicy = somPolicyVersion.addTheInsurancePolicyOther();
                    }
                }

                if (somRelatedInsurancePolicy != null) {
                    this.mapRelatedInsurancePolicy(plRelatedInsurancePolicy, somRelatedInsurancePolicy);
                }
            }
        }

    }

    protected void setClaimsAndKindOfLossTreeViaPolicyVersion(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getClaims() != null) {
            for(com.ing.canada.plp.domain.insurancerisk.Claim plClaim : plPolicyVersion.getClaims()) {
                Claim somClaim = (Claim)this.claimEntryCache.get(plClaim.getId());
                if (somClaim != null) {
                    somPolicyVersion.addTheClaim(somClaim);
                } else {
                    somClaim = somPolicyVersion.addTheClaim();
                    this.mapClaim(plClaim, somClaim);
                    this.claimEntryCache.put(plClaim.getId(), somClaim);
                    if (plClaim.getKindOfLosses() != null) {
                        for(KindOfLoss plKindOfLoss : plClaim.getKindOfLosses()) {
                            com.ing.canada.som.interfaces.claim.KindOfLoss somKindOfLoss = somClaim.addTheKindOfLoss();
                            this.mapKindOfLoss(plKindOfLoss, somKindOfLoss);
                        }
                    }
                }
            }
        }

    }

    protected void setClaimsAndKindOfLossTreeViaParty(Party plParty, com.ing.canada.som.interfaces.party.Party somParty) {
        if (plParty.getClaims() != null) {
            for(com.ing.canada.plp.domain.insurancerisk.Claim plClaim : plParty.getClaims()) {
                Claim somClaim = (Claim)this.claimEntryCache.get(plClaim.getId());
                if (somClaim != null) {
                    somParty.addTheClaim(somClaim);
                } else {
                    somClaim = somParty.addTheClaim();
                    this.mapClaim(plClaim, somClaim);
                    this.claimEntryCache.put(plClaim.getId(), somClaim);
                    if (plClaim.getKindOfLosses() != null) {
                        for(KindOfLoss plKindOfLoss : plClaim.getKindOfLosses()) {
                            com.ing.canada.som.interfaces.claim.KindOfLoss somKindOfLoss = somClaim.addTheKindOfLoss();
                            this.mapKindOfLoss(plKindOfLoss, somKindOfLoss);
                        }
                    }
                }
            }
        }

    }

    protected void setClaimsAndKindOfLossTreeViaInsuranceRisk(InsuranceRisk plInsuranceRisk, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        if (plInsuranceRisk.getClaims() != null) {
            for(com.ing.canada.plp.domain.insurancerisk.Claim plClaim : plInsuranceRisk.getClaims()) {
                Claim somClaim = (Claim)this.claimEntryCache.get(plClaim.getId());
                if (somClaim != null) {
                    somInsuranceRisk.addTheClaim(somClaim);
                } else {
                    somClaim = somInsuranceRisk.addTheClaim();
                    this.mapClaim(plClaim, somClaim);
                    this.claimEntryCache.put(plClaim.getId(), somClaim);
                    if (plClaim.getKindOfLosses() != null) {
                        for(KindOfLoss plKindOfLoss : plClaim.getKindOfLosses()) {
                            com.ing.canada.som.interfaces.claim.KindOfLoss somKindOfLoss = somClaim.addTheKindOfLoss();
                            this.mapKindOfLoss(plKindOfLoss, somKindOfLoss);
                        }
                    }
                }
            }
        }

    }

    protected void mapReferenceDate(ReferenceDate plReferenceDate, com.ing.canada.som.interfaces.businessTransaction.ReferenceDate somReferenceDate) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plReferenceDateAttributeNames, DMConstants.somReferenceDateAttributeNames, plReferenceDate, somReferenceDate);
    }

    protected void mapAdditionalInterestRole(AdditionalInterestRole plAdditionalInterestRole, AdditionalInterest somAdditionalInterest) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plAdditionalInterestRoleAttributeNames, DMConstants.somAdditionalInterestRoleAttributeNames, plAdditionalInterestRole, somAdditionalInterest);
    }

    protected void mapKindOfLoss(KindOfLoss plKindOfLoss, com.ing.canada.som.interfaces.claim.KindOfLoss somKindOfLoss) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plKindOfLossAttributeNames, DMConstants.somKindOfLossAttributeNames, plKindOfLoss, somKindOfLoss);
    }

    protected void mapClaim(com.ing.canada.plp.domain.insurancerisk.Claim plClaim, Claim somClaim) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plClaimAttributeNames, DMConstants.somClaimAttributeNames, plClaim, somClaim);
    }

    protected void mapProducerRepositoryEntry(com.ing.canada.plp.domain.policyversion.ProducerRepositoryEntry plProducerRepositoryEntry, ProducerRepositoryEntry somProducerRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plProducerRepositoryEntryAttributeNames, DMConstants.somProducerRepositoryEntryAttributeNames, plProducerRepositoryEntry, somProducerRepositoryEntry);
    }

    protected void mapProducer(com.ing.canada.plp.domain.policyversion.Producer producer, Producer somProducer) {
        // Method has no implementation in the original code, assuming it is a placeholder
    }

    protected void mapGroupRepositoryEntry(com.ing.canada.plp.domain.party.GroupRepositoryEntry plGroupRepositoryEntry, GroupRepositoryEntry somGroupRepositoryEntry, String language) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plGroupRepositoryEntryAttributeNames, DMConstants.somGroupRepositoryEntryAttributeNames, plGroupRepositoryEntry, somGroupRepositoryEntry);
        if (LanguageCodeEnum.ENGLISH.getCode().equals(language)) {
            somGroupRepositoryEntry.setPartyGroupDescription(plGroupRepositoryEntry.getPartyGroupDescriptionEnglish());
            somGroupRepositoryEntry.setPartySubGroupDescription(plGroupRepositoryEntry.getPartySubGroupDescriptionEnglish());
        } else if (LanguageCodeEnum.FRENCH.getCode().equals(language)) {
            somGroupRepositoryEntry.setPartyGroupDescription(plGroupRepositoryEntry.getPartyGroupDescriptionFrench());
            somGroupRepositoryEntry.setPartySubGroupDescription(plGroupRepositoryEntry.getPartySubGroupDescriptionFrench());
        }

    }

    protected void mapPartyGroup(com.ing.canada.plp.domain.party.PartyGroup plPartyGroup, PartyGroup somPartyGroup) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartyGroupAttributeNames, DMConstants.somPartyGroupAttributeNames, plPartyGroup, somPartyGroup);
    }

    protected void mapPartyRoleInRiskOnDriver(PartyRoleInRisk plPartyRoleInRisk, Driver somDriver) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartyRoleInRiskAttributeNames, DMConstants.somDriverAttributeNames, plPartyRoleInRisk, somDriver);
    }

    protected void mapPartyRoleInRiskOnOwner(PartyRoleInRisk plPartyRoleInRisk, Owner somOwner) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartyRoleInRiskOnOwnerAttributeNames, DMConstants.somOwnerAttributeNames, plPartyRoleInRisk, somOwner);
    }

    protected void mapMunicipalityRepositoryEntry(com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry plMunicipalityRepositoryEntry, MunicipalityRepositoryEntry soMunicipalityRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plMunicipalityRepositoryEntryAttributeNames, DMConstants.somMunicipalityRepositoryEntryAttributeNames, plMunicipalityRepositoryEntry, soMunicipalityRepositoryEntry);
    }

    protected void mapMunicipalityDetailSpecification(MunicipalityDetailSpecification plMunicipalityDetailSpecification, MunicipalityDetailSpec somMunicipalityDetailSpec) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plMunicipalityDetailSpecificationAttributeNames, DMConstants.somMunicipalityDetailSpecAttributeNames, plMunicipalityDetailSpecification, somMunicipalityDetailSpec);
    }

    protected void mapAddress(com.ing.canada.plp.domain.party.Address plAddress, Address somAddress) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plAddressAttributeNames, DMConstants.somAddressAttributeNames, plAddress, somAddress);
    }

    protected void mapGeographicalAssessment(GeographicalAssessment plGeographicalAssessment, com.ing.canada.som.interfaces.place.GeographicalAssessment somGeographicalAssessment) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plGeographicalAssessmentNames, DMConstants.somGeographicalAssessmentNames, plGeographicalAssessment, somGeographicalAssessment);
    }

    protected void mapDiagnostic(DiagnosticAutomatedAdvice plDiagnostic, com.ing.canada.som.interfaces.agreement.DiagnosticAutomatedAdvice somDiagnostic) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plDiagnosticAutomatedAdviceAttributeNames, DMConstants.somDiagnosticAutomatedAdviceAttributeNames, plDiagnostic, somDiagnostic);
    }

    protected void mapPhone(Phone plPhone, com.ing.canada.som.interfaces.contactPoint.Phone somPhone) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPhoneAttributeNames, DMConstants.somPhoneAttributeNames, plPhone, somPhone);
    }

    protected void mapCreditScore(CreditScore plCreditScore, com.ing.canada.som.interfaces.party.CreditScore somCreditScore) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCreditScoreAttributeNames, DMConstants.somCreditScoreAttributeNames, plCreditScore, somCreditScore);
    }

    protected void mapDriverLicenceClass(DriverLicenseClass plDriverLicenseClass, com.ing.canada.som.interfaces.registration.DriverLicenseClass somDriverLicenseClass) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plDriverLicenceClassAttributeNames, DMConstants.somDriverLicenceClassAttributeNames, plDriverLicenseClass, somDriverLicenseClass);
    }

    protected void mapConviction(Conviction plConviction, com.ing.canada.som.interfaces.registration.Conviction somConviction) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plConvictionAttributeNames, DMConstants.somConvictionAttributeNames, plConviction, somConviction);
        somConviction.setConvictionTypeGrid(somConviction.getConvictionType());
    }

    protected void mapDriverComplementInfo(com.ing.canada.plp.domain.driver.DriverComplementInfo plDriverComplementInfo, DriverComplementInfo somDriverComplementInfo) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plDriverComplementInfoAttributeNames, DMConstants.somDriverComplementInfoAttributeNames, plDriverComplementInfo, somDriverComplementInfo);
        com.ing.canada.plp.domain.policyversion.PolicyVersion policy = plDriverComplementInfo.getParty().getPolicyVersion();
        if (policy != null) {
            Date dateUsed = policy.getPolicyInceptionDate() == null ? new Date() : policy.getPolicyInceptionDate();
            Integer nbYears = 0;
            if (plDriverComplementInfo.getDateDriverLicenseObtained() != null) {
                if (!policy.getInsurancePolicy().getManufacturerCompany().equals(ManufacturerCompanyCodeEnum.BELAIRDIRECT) && !policy.getInsurancePolicy().getManufacturerCompany().equals(ManufacturerCompanyCodeEnum.GREY_POWER)) {
                    nbYears = DateUtils.getYearsDifference(plDriverComplementInfo.getDateDriverLicenseObtained(), dateUsed);
                } else {
                    nbYears = DateUtils.getYearsDifferencesMth(plDriverComplementInfo.getDateDriverLicenseObtained(), dateUsed);
                }
            }

            somDriverComplementInfo.setNumberOfYearsDrivingExperience(nbYears);
            if (plDriverComplementInfo.getUbiDiscountCriteriaCode() != null) {
                somDriverComplementInfo.setUbiDeviceDiscountCriteria(plDriverComplementInfo.getUbiDiscountCriteriaCode().getCode());
            } else {
                somDriverComplementInfo.setUbiDeviceDiscountCriteria("ODB");
            }

            PolicyHolder principalPolicyHolder = policy.getPolicyHolder(PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED);
            if (principalPolicyHolder != null && principalPolicyHolder.getParty() != null && plDriverComplementInfo.getParty() != null && principalPolicyHolder.getParty().getId() == plDriverComplementInfo.getParty().getId()) {
                if (Boolean.TRUE.equals(policy.getLicenseSuspendedOrRevokedIndicator())) {
                    somDriverComplementInfo.setLicenseSuspensionInd("Y");
                } else if (Boolean.FALSE.equals(policy.getLicenseSuspendedOrRevokedIndicator())) {
                    somDriverComplementInfo.setLicenseSuspensionInd("N");
                }
            }
        }

    }

    protected void mapDriverLicence(com.ing.canada.plp.domain.driver.DriverComplementInfo plDriverComplementInfo, ManufacturingContext somManufacturingContext, com.ing.canada.som.interfaces.party.Party somParty, DriverLicense somDriverLicense) {
        somDriverLicense.setLicenseNumber(plDriverComplementInfo.getLicenseNumber());
        somDriverLicense.setCountry(CountryCodeEnum.CANADA.getCode());
        somDriverLicense.setProvince(somManufacturingContext.getProvince());
        somDriverLicense.setTheParty(somParty);
    }

    protected void mapConsent(Consent plConsent, com.ing.canada.som.interfaces.party.Consent somConsent) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plConsentAttributeNames, DMConstants.somConsentAttributeNames, plConsent, somConsent);
    }

    protected void mapParty(Party plParty, com.ing.canada.som.interfaces.party.Party somParty) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartyAttributeNames, DMConstants.somPartyAttributeNames, plParty, somParty);
    }

    protected void mapPolicyHolder(PolicyHolder plPolicyHolder, com.ing.canada.som.interfaces.partyRoleInAgreement.PolicyHolder somPolicyHolder) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPolicyHolderAttributeNames, DMConstants.somPolicyHolderAttributeNames, plPolicyHolder, somPolicyHolder);
    }

    protected void mapAccount(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plAccountAttributeNames, DMConstants.somAccountAttributeNames, plAccount, somAccount);
        this.mapBankAccount(plAccount, somAccount);
        this.mapCreditCardAccount(plAccount, somAccount);
    }

    protected void mapCreditCardAccount(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        if (StringUtils.isNotEmpty(plAccount.getCreditCardAccountNumber())) {
            this.mapCreditCardAccountNumber(plAccount, somAccount);
        }

        if (StringUtils.isNotEmpty(plAccount.getCreditCardExpiryDate())) {
            this.mapCreditCardExpriryDate(plAccount, somAccount);
        }

        if (StringUtils.isNotEmpty(plAccount.getCreditCardHolderName())) {
            this.mapCreditCardHolderName(plAccount, somAccount);
        }

    }

    private void mapCreditCardHolderName(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String creditCardHolderName = this.cipherUtils.getCreditCardHolderName(plAccount.getCreditCardHolderName());
        somAccount.setCreditCardHolderName(creditCardHolderName);
    }

    private void mapCreditCardAccountNumber(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String creditCardAccountNumber = this.cipherUtils.getCreditCardAccountNumber(plAccount.getCreditCardAccountNumber());
        somAccount.setCreditCardAccountNumber(creditCardAccountNumber);
    }

    private void mapCreditCardExpriryDate(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String creditCardExpiryDate = this.cipherUtils.getCreditCardExpriryDate(plAccount.getCreditCardExpiryDate());
        GregorianCalendar gc = new GregorianCalendar();
        gc.set(2, Integer.parseInt(creditCardExpiryDate.substring(0, 2)) - 1);
        gc.set(1, 2000 + Integer.parseInt(creditCardExpiryDate.substring(2, 4)));
        somAccount.setCreditCardExpiryDate(gc);
    }

    protected void mapBankAccount(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        if (StringUtils.isNotEmpty(plAccount.getFinancialInstitutionNumber())) {
            this.mapFinancialInstitutionNumber(plAccount, somAccount);
        }

        if (StringUtils.isNotEmpty(plAccount.getRoutingNumber())) {
            this.mapRoutingNumber(plAccount, somAccount);
        }

        if (StringUtils.isNotEmpty(plAccount.getAccountNumber())) {
            this.mapBankAccountNumber(plAccount, somAccount);
        }

    }

    private void mapFinancialInstitutionNumber(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String financialInstitutionNumber = this.cipherUtils.getFinancialInstitutionNumber(plAccount.getFinancialInstitutionNumber());
        somAccount.setFinancialInstitutionNumber(financialInstitutionNumber);
    }

    private void mapRoutingNumber(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String routingNumber = this.cipherUtils.getRoutingNumber(plAccount.getRoutingNumber());
        somAccount.setFinancialInstitutionNumber(routingNumber);
    }

    private void mapBankAccountNumber(Account plAccount, com.ing.canada.som.interfaces.account.Account somAccount) {
        String bankAccountNumber = this.cipherUtils.getBankAccountNumber(plAccount.getAccountNumber());
        somAccount.setBankAccountNumber(bankAccountNumber);
    }

    protected void mapPaymentSchedule(PaymentSchedule plPaymentSchedule, com.ing.canada.som.interfaces.moneyProvision.PaymentSchedule somPaymentSchedule) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPaymentScheduleAttributeNames, DMConstants.somPaymentScheduleAttributeNames, plPaymentSchedule, somPaymentSchedule);
    }

    protected void mapBilling(com.ing.canada.plp.domain.billing.Billing plBilling, Billing somBilling) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plBillingAttributeNames, DMConstants.somBillingAttributeNames, plBilling, somBilling);
    }

    protected void mapAffinityGroupRepositoryEntry(com.ing.canada.plp.domain.driver.AffinityGroupRepositoryEntry plAffinityGroupRepositoryEntry, AffinityGroupRepositoryEntry somAffinityGroupRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plAffinityGroupRepositoryEntryAttributeNames, DMConstants.somAffinityGroupRepositoryEntryAttributeNames, plAffinityGroupRepositoryEntry, somAffinityGroupRepositoryEntry);
    }

    protected void mapTrailer(Trailer plTrailer, com.ing.canada.som.interfaces.physicalObject.Trailer somTrailer) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plTrailerAttributeNames, DMConstants.somTrailerAttributeNames, plTrailer, somTrailer);
    }

    protected void mapVehicleRepositoryEntry(com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry plVehicleRepositoryEntry, VehicleRepositoryEntry somVehicleRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plVehicleRepositoryEntryAttributeNames, DMConstants.somVehicleRepositoryEntryAttributeNames, plVehicleRepositoryEntry, somVehicleRepositoryEntry);
        if (plVehicleRepositoryEntry.getVehicleCode() != null) {
            somVehicleRepositoryEntry.setExtendedVehicleCode(plVehicleRepositoryEntry.getVehicleCode());
        }

    }

    protected void mapVehicleDetailSpecificationRepositoryEntry(VehicleDetailSpecificationRepositoryEntry plVDSRE, VehicleDetailSpec somVehicleDetailSpec) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plVehicleDetailSpecificationRepositoryEntryAttributeNames, DMConstants.somVehicleDetailSpecificationRepositoryEntryAttributeNames, plVDSRE, somVehicleDetailSpec);
        if (log.isDebugEnabled()) {
            log.debug("wheelBase            pl::{} -> som::{}", plVDSRE.getWheelbaseQty(), somVehicleDetailSpec.getWheelbase());
            log.debug("airbags              pl::{} -> som::{}", plVDSRE.getAirbagsCd(), somVehicleDetailSpec.getAirbags());
            log.debug("absBrakes            pl::{} -> som::{}", plVDSRE.getAbsBrakesCd(), somVehicleDetailSpec.getAbsBrakes());
            log.debug("audibleAlarm         pl::{} -> som::{}", plVDSRE.getAudibleAlarmCd(), somVehicleDetailSpec.getAudibleAlarm());
            log.debug("cutOffSystem         pl::{} -> som::{}", plVDSRE.getCutOffSystemCd(), somVehicleDetailSpec.getCutOffSystem());
            log.debug("securityKeySystem    pl::{} -> som::{}", plVDSRE.getSecurityKeySystemCd(), somVehicleDetailSpec.getSecurityKeySystem());
            log.debug("ibcApproved          pl::{} -> som::{}", plVDSRE.getIbcApprovedCd(), somVehicleDetailSpec.getIbcApproved());
            log.debug("engineCylinder       pl::{} -> som::{}", plVDSRE.getEngineCylinderCd(), somVehicleDetailSpec.getEngineCylinder());
            log.debug("ibcMarket            pl::{} -> som::{}", plVDSRE.getIbcMarketCd(), somVehicleDetailSpec.getIbcMarket());
            log.debug("vehicleSize          pl::{} -> som::{}", plVDSRE.getVehicleSizeCd(), somVehicleDetailSpec.getVehicleSize());
            log.debug("vehicleGeneration    pl::{} -> som::{}", plVDSRE.getVehicleGenerationCd(), somVehicleDetailSpec.getVehicleGeneration());
            log.debug("fuelUsedByVehicle    pl::{} -> som::{}", plVDSRE.getFuelUsedByVehicleCd(), somVehicleDetailSpec.getFuelUsedByVehicle());
            log.debug("engineForceInduction pl::{} -> som::{}", plVDSRE.getEngineForceInductionCd(), somVehicleDetailSpec.getEngineForceInduction());
            log.debug("engineHybrid         pl::{} -> som::{}", plVDSRE.getEngineHybridCd(), somVehicleDetailSpec.getEngineHybrid());
            log.debug("tractionControl      pl::{} -> som::{}", plVDSRE.getTractionControlCd(), somVehicleDetailSpec.getTractionControl());
            log.debug("stabilityControl     pl::{} -> som::{}", plVDSRE.getStabitlityControlCd(), somVehicleDetailSpec.getStabilityControl());
            log.debug("driveTrain           pl::{} -> som::{}", plVDSRE.getDriveTrainCd(), somVehicleDetailSpec.getDriveTrain());
            log.debug("rateGroupAccidentBenefitVicc          pl::{} -> som::{}", plVDSRE.getRateGroupAccidentBenefitVicc(), somVehicleDetailSpec.getRateGroupAccidentBenefitVicc());
            log.debug("rateGroupCollisionVicc                pl::{} -> som::{}", plVDSRE.getRateGroupCollisionVicc(), somVehicleDetailSpec.getRateGroupCollisionVicc());
            log.debug("rateGroupComprehensiveVicc            pl::{} -> som::{}", plVDSRE.getRateGroupComprehensiveVicc(), somVehicleDetailSpec.getRateGroupComprehensiveVicc());
            log.debug("rateGroupLiabilityPropertyDamageVicc  pl::{} -> som::{}", plVDSRE.getRateGroupLiabilityPropertyDamageVicc(), somVehicleDetailSpec.getRateGroupLiabilityPropertyDamageVicc());
            log.debug("rateGroupCollisionViccPure            pl::{} -> som::{}", plVDSRE.getRateGroupCollisionViccPure(), somVehicleDetailSpec.getRateGroupCollisionViccPure());
            log.debug("rateGroupLiabilityPropertyDamageViccPure            pl::{} -> som::{}", plVDSRE.getRateGroupLiabilityPropertyDamageViccPure(), somVehicleDetailSpec.getRateGroupLiabilityPropertyDamageViccPure());
        }

    }

    protected void mapAntiTheftDevices(AntiTheftDevice plAntiTheftDevice, com.ing.canada.som.interfaces.physicalObject.AntiTheftDevice somAntiTheftDevice) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plAntiTheftDevicesAttributeNames, DMConstants.somAntiTheftDevicesAttributeNames, plAntiTheftDevice, somAntiTheftDevice);
    }

    protected void mapVehicleEquipment(VehicleEquipment plVehicleEquipment, com.ing.canada.som.interfaces.physicalObject.VehicleEquipment somVehicleEquipment) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plVehicleEquipmentAttributeNames, DMConstants.somVehicleEquipmentAttributeNames, plVehicleEquipment, somVehicleEquipment);
    }

    protected void mapInsuranceRiskOffer(InsuranceRiskOffer plInsuranceRiskOffer, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        String insuranceRiskPersistenceUniqueIdBackup = somInsuranceRisk.getPersistenceUniqueId();
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames, DMConstants.somInsuranceRiskOfferOverridesOnInsuranceRiskAttributeNames, plInsuranceRiskOffer, somInsuranceRisk);
        somInsuranceRisk.setPersistenceUniqueId(insuranceRiskPersistenceUniqueIdBackup);
        if (plInsuranceRiskOffer.equals(plInsuranceRiskOffer.getInsuranceRisk().getSelectedInsuranceRiskOffer())) {
            somInsuranceRisk.setRiskSelectedInd("Y");
        } else {
            somInsuranceRisk.setRiskSelectedInd("N");
        }

        if (somInsuranceRisk.getTheVehicle() != null) {
            String vehiclePersistenceUniqueIdBackup = somInsuranceRisk.getTheVehicle().getPersistenceUniqueId();
            this.setAttributesByReflectionFromPLtoSOM(DMConstants.plInsuranceRiskOfferOverridesOnVehicleAttributeNames, DMConstants.somInsuranceRiskOfferOverridesOnVehicleAttributeNames, plInsuranceRiskOffer, somInsuranceRisk.getTheVehicle());
            somInsuranceRisk.getTheVehicle().setPersistenceUniqueId(vehiclePersistenceUniqueIdBackup);
        }

        if (plInsuranceRiskOffer.getAnnualPremium() != null) {
            somInsuranceRisk.setAnnualPremiumDiscountedSurchargedCapped(Double.parseDouble(String.valueOf(plInsuranceRiskOffer.getAnnualPremium())));
        }

    }

    protected void mapVehicle(Vehicle plVehicle, com.ing.canada.som.interfaces.physicalObject.Vehicle somVehicle, InsuranceRisk plInsuranceRisk, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plVehicleAttributeNames, DMConstants.somVehicleAttributeNames, plVehicle, somVehicle);
        if (ActionTakenCodeEnum.SUBSTITUTION.equals(plInsuranceRisk.getActionTaken())) {
            somInsuranceRisk.getTheVehicle().setSubstituteVehicleInd("Y");
        }

    }

    protected void mapInsuranceRisk(InsuranceRisk plInsuranceRisk, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plInsuranceRiskAttributeNames, DMConstants.somInsuranceRiskAttributeNames, plInsuranceRisk, somInsuranceRisk);
        somInsuranceRisk.setLineOfInsurance(LineOfInsuranceCodeEnum.AUTOMOBILE.getCode());
    }

    protected void mapInsuranceRiskCommercialUsage(InsuranceRisk plInsuranceRisk, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        somInsuranceRisk.clearTheCommercialUsage();
        if (plInsuranceRisk != null && plInsuranceRisk.getCommercialUsage() != null) {
            somInsuranceRisk.addTheCommercialUsage();
            ((CommercialUsage)somInsuranceRisk.getTheCommercialUsage().get(0)).setCategory(plInsuranceRisk.getCommercialUsage().getCategoryCode());
            ((CommercialUsage)somInsuranceRisk.getTheCommercialUsage().get(0)).setGeneralUse(plInsuranceRisk.getCommercialUsage().getGeneralUseCode());
            ((CommercialUsage)somInsuranceRisk.getTheCommercialUsage().get(0)).setSpecificUse(plInsuranceRisk.getCommercialUsage().getSpecificUseCode());
        }

    }

    protected void mapCoverage(Coverage plCoverage, com.ing.canada.som.interfaces.risk.Coverage somCoverage) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageAttributeNames, DMConstants.somCoverageAttributeNames, plCoverage, somCoverage);
    }

    protected void mapCoverageOffer(CoverageOffer plCoverageOffer, com.ing.canada.som.interfaces.risk.Coverage somCoverageOffer) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageOfferAttributeNames, DMConstants.somCoverageOfferAttributeNames, plCoverageOffer, somCoverageOffer);
    }

    protected void mapCoveragePremium(CoveragePremium plCoveragePremium, com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium somCoveragePremium) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoveragePremiumAttributeNames, DMConstants.somCoveragePremiumAttributeNames, plCoveragePremium, somCoveragePremium);
    }

    protected void mapCoveragePremiumOffer(CoveragePremiumOffer plCoveragePremiumOffer, com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium somCoveragePremiumOffer) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoveragePremiumOfferAttributeNames, DMConstants.somCoveragePremiumOfferAttributeNames, plCoveragePremiumOffer, somCoveragePremiumOffer);
    }

    protected void mapRatingRisk(RatingRisk plRatingRisk, com.ing.canada.som.interfaces.risk.RatingRisk somRatingRisk) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plRatingRiskAttributeNames, DMConstants.somRatingRiskAttributeNames, plRatingRisk, somRatingRisk);
    }

    protected void mapRatingRiskOffer(RatingRiskOffer plRatingRiskOffer, com.ing.canada.som.interfaces.risk.RatingRisk somRatingRiskOffer) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plRatingRiskOfferAttributeNames, DMConstants.somRatingRiskOfferAttributeNames, plRatingRiskOffer, somRatingRiskOffer);
    }

    protected void mapGroupRepositoryEntryOnRatingRisk(RatingRisk plRatingRisk, GroupRepositoryEntry somGroupRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plGroupRepositoryEntryOnRatingRiskAttributeNames, DMConstants.somGroupRepositoryEntryOnRatingRiskAttributeNames, plRatingRisk, somGroupRepositoryEntry, true);
    }

    protected void mapGroupRepositoryEntryOnRatingRiskOffer(RatingRiskOffer plRatingRiskOffer, GroupRepositoryEntry somGroupRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plGroupRepositoryEntryOnRatingRiskAttributeNames, DMConstants.somGroupRepositoryEntryOnRatingRiskAttributeNames, plRatingRiskOffer, somGroupRepositoryEntry, true);
    }

    protected void mapCoverageRepositoryEntry(BaseCoverage plBaseCoverage, CoverageRepositoryEntry somCoverageRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageRepositoryEntryFromCoverageAttributeNames, DMConstants.somCoverageRepositoryEntryFromCoverageAttributeNames, plBaseCoverage, somCoverageRepositoryEntry, true);
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageRepositoryEntryAttributeNames, DMConstants.somCoverageRepositoryEntryAttributeNames, plBaseCoverage.getCoverageRepositoryEntry(), somCoverageRepositoryEntry);
        if (this.useEISCoverageCodes) {
            somCoverageRepositoryEntry.setCoverageCode(plBaseCoverage.getCoverageRepositoryEntry().getIhvCoverage());
            somCoverageRepositoryEntry.setCoverageCodeNative(plBaseCoverage.getCoverageRepositoryEntry().getCoverageCode());
        }

    }

    protected void mapCoverageRepositoryEntry(PolicyAdditionalCoverage plBaseCoverage, CoverageRepositoryEntry somCoverageRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageRepositoryEntryFromCoverageAttributeNames, DMConstants.somCoverageRepositoryEntryFromCoverageAttributeNames, plBaseCoverage, somCoverageRepositoryEntry, true);
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCoverageRepositoryEntryAttributeNames, DMConstants.somCoverageRepositoryEntryAttributeNames, plBaseCoverage.getCoverageRepositoryEntry(), somCoverageRepositoryEntry);
        if (this.useEISCoverageCodes) {
            somCoverageRepositoryEntry.setCoverageCode(plBaseCoverage.getCoverageRepositoryEntry().getIhvCoverage());
            somCoverageRepositoryEntry.setCoverageCodeNative(plBaseCoverage.getCoverageRepositoryEntry().getCoverageCode());
        }

    }

    protected void mapDiagnosticRepositoryEntry(DiagnosticAutomatedAdvice plDiagnostic, DiagnosticAutomatedAdviceRepositoryEntry somDiagnosticRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plDiagnosticAutomatedAdviceRepositoryEntryAttributeNames, DMConstants.somDiagnosticAutomatedAdviceRepositoryEntryAttributeNames, plDiagnostic.getDiagnosticAutoAdviceRepositoryEntry(), somDiagnosticRepositoryEntry);
    }

    protected void mapPolicyAdditionalCoverage(PolicyAdditionalCoverage plPolicyAdditionalCoverage, com.ing.canada.som.interfaces.agreement.PolicyAdditionalCoverage somPolicyAdditionalCoverage) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames, DMConstants.somPolicyAdditionalCoverageFromCoverageRepositoryEntryAttributeNames, plPolicyAdditionalCoverage.getCoverageRepositoryEntry(), somPolicyAdditionalCoverage, true);
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPolicyAdditionalCoverageAttributeNames, DMConstants.somPolicyAdditionalCoverageAttributeNames, plPolicyAdditionalCoverage, somPolicyAdditionalCoverage);
    }

    protected void mapPriorCarrierPolicyInfo(PriorCarrierPolicyInfo plCarrierPolicyInfo, com.ing.canada.som.interfaces.agreement.PriorCarrierPolicyInfo somPriorCarrierPolicyInfo, ProvinceCodeEnum province, ManufacturerCompanyCodeEnum company) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPriorCarrierPolicyInfoAttributeNames, DMConstants.somPriorCarrierPolicyInfoAttributeNames, plCarrierPolicyInfo, somPriorCarrierPolicyInfo);
        CarrierRepositoryEntry cre = this.carrierRepositoryEntryService.getCarrierRepositoryEntryByCarrierCodeManuf(plCarrierPolicyInfo.getCarrierCode(), province, company);
        if (cre != null) {
            somPriorCarrierPolicyInfo.setLegacyCarrierCode(cre.getLegacyCarrierCode());
        }

    }

    protected void mapMessageRepositoryEntry(MessageRepositoryEntry plMessageRepositoryEntry, com.ing.canada.som.interfaces.businessTransaction.MessageRepositoryEntry somMessageRepositoryEntry) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plMessageRepositoryEntryAttributeNames, DMConstants.somMessageRepositoryEntryAttributeNames, plMessageRepositoryEntry, somMessageRepositoryEntry);
    }

    protected void mapTransactionalMessageElement(TransactionalMessageElement plTransactionalMessageElement, com.ing.canada.som.interfaces.businessTransaction.TransactionalMessageElement somTransactionalMessageElement) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plTransactionalMessageElementAttributeNames, DMConstants.somTransactionalMessageElementAttributeNames, plTransactionalMessageElement, somTransactionalMessageElement);
    }

    protected void mapTransactionalMessage(TransactionalMessage plTransactionalMessage, com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage somTransactionalMessage) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plTransactionalMessageAttributeNames, DMConstants.somTransactionalMessageAttributeNames, plTransactionalMessage, somTransactionalMessage);
    }

    protected void mapCredential(BusinessTransactionActivity plBusinessTransactionActivity, Credential somCredential) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plCredentialAttributeNames, DMConstants.somCredentialAttributeNames, plBusinessTransactionActivity, somCredential);
    }

    protected void mapBusinessTransactionActivity(BusinessTransactionActivity plBusinessTransactionActivity, com.ing.canada.som.interfaces.businessTransaction.BusinessTransactionActivity somBusinessTransactionActivity) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plBusinessTransactionActivityAttributeNames, DMConstants.somBusinessTransactionActivityAttributeNames, plBusinessTransactionActivity, somBusinessTransactionActivity);
    }

    protected void mapBusinessTransaction(com.ing.canada.plp.domain.businesstransaction.BusinessTransaction plBusinessTransaction, BusinessTransaction somBusinessTransaction) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plBusinessTransactionAttributeNames, DMConstants.somBusinessTransactionAttributeNames, plBusinessTransaction, somBusinessTransaction);
        somBusinessTransaction.setOriginOfTransaction("9");
    }

    protected void mapRelatedInsurancePolicy(RelatedInsurancePolicy plRelatedInsurancePolicy, InsurancePolicy somRelatedInsurancePolicy) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plRelatedInsurancePolicyAttributeNames, DMConstants.somRelatedInsurancePolicyAttributeNames, plRelatedInsurancePolicy, somRelatedInsurancePolicy);
    }

    protected void mapManufacturingContext(com.ing.canada.plp.domain.ManufacturingContext plManufacturingContext, ManufacturingContext somManufacturingContext) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plManufacturingContextAttributeNames, DMConstants.somManufacturingContextAttributeNames, plManufacturingContext, somManufacturingContext);
    }

    protected void mapInsurancePolicy(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, InsurancePolicy somInsurancePolicy) {
        com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy plInsurancePolicy = plPolicyVersion.getInsurancePolicy();
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plInsurancePolicyAttributeNames, DMConstants.somInsurancePolicyAttributeNames, plInsurancePolicy, somInsurancePolicy);
        somInsurancePolicy.createTheManufacturer().createTheParty().setCompanyCode(plInsurancePolicy.getManufacturerCompany().getCode());
        if (TransactionCodeEnum.NEW_BUSINESS_QUOTE.equals(plPolicyVersion.getBusinessTransaction().getTransactionCode()) && plPolicyVersion.getPolicyInceptionDate() != null) {
            GregorianCalendar somPolicyFirstInceptionDate = new GregorianCalendar();
            somPolicyFirstInceptionDate.setTime(plPolicyVersion.getPolicyInceptionDate());
            somInsurancePolicy.setPolicyFirstInceptionDate(somPolicyFirstInceptionDate);
        }

    }

    protected void mapMarketSegments(MarketSegment plMarketSegment, com.ing.canada.som.interfaces.activity.MarketSegment somMarketSegment) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plMarketSegmentAttributeNames, DMConstants.somMarketSegmentAttributeNames, plMarketSegment, somMarketSegment);
    }

    protected void mapPartnership(Partnership plPartnership, com.ing.canada.som.interfaces.agreement.Partnership somPartnership) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPartnershipAttributeNames, DMConstants.somPartnershipAttributeNames, plPartnership, somPartnership);
        somPartnership.setGroupNumber(StringUtils.join(new String[]{somPartnership.getGroupNumber(), StringUtils.trimToEmpty(plPartnership.getSubgroupNumber())}));
    }

    protected void mapPartnershipAdvisor(Party plpAdvisor, com.ing.canada.som.interfaces.agreement.Partnership somPartnership) {
        com.ing.canada.som.interfaces.party.Party advisor = somPartnership.createThePartyAdvisor();
        advisor.setFirstName(StringUtils.trimToNull(plpAdvisor.getFirstName()));
        advisor.setLastName(StringUtils.trimToNull(plpAdvisor.getLastName()));
        if (CollectionUtils.isNotEmpty(plpAdvisor.getAddresses())) {
            List<Address> addresses = new LinkedList();

            for(com.ing.canada.plp.domain.party.Address plAddress : plpAdvisor.getAddresses()) {
                Address advisorAddress = new AddressImpl();
                if (plAddress.getProvince() != null && StringUtils.isNotBlank(plAddress.getProvince().getCode())) {
                    advisorAddress.setProvince(plAddress.getProvince().getCode());
                }

                advisorAddress.setMunicipality(StringUtils.trimToNull(plAddress.getMunicipality()));
                addresses.add(advisorAddress);
            }

            advisor.setTheAddress(addresses);
        }

    }

    protected void mapPolicyVersion(com.ing.canada.plp.domain.policyversion.PolicyVersion plPolicyVersion, PolicyVersion somPolicyVersion) {
        this.setAttributesByReflectionFromPLtoSOM(DMConstants.plPolicyVersionAttributeNames, DMConstants.somPolicyVersionAttributeNames, plPolicyVersion, somPolicyVersion);
        PriorCarrierPolicyInfo pvPriorCarrierPolicyInfo = plPolicyVersion.getPriorCarrierPolicyInfo();
        if (pvPriorCarrierPolicyInfo != null && pvPriorCarrierPolicyInfo.getReasonDeclinedOrCancelledIndicator() != null) {
            if (pvPriorCarrierPolicyInfo.getReasonDeclinedOrCancelledIndicator()) {
                somPolicyVersion.setPriorCarrierDeclinedOrCancelledInd("Y");
            } else {
                somPolicyVersion.setPriorCarrierDeclinedOrCancelledInd("N");
            }
        }

        if (!StringUtils.isEmpty(plPolicyVersion.getClientOfBrokerSinceResCode())) {
            String nbMonthResCd = plPolicyVersion.getClientOfBrokerSinceResCode();
            GregorianCalendar now = (GregorianCalendar)Calendar.getInstance();
            now.add(2, -Integer.parseInt(nbMonthResCd));
            somPolicyVersion.setClientOfBrokerSinceRes(now);
        }

    }

    protected void setAttributesByReflectionFromPLtoSOM(String[] plAttributeNames, String[] somAttributeNames, BaseEntity plBean, BusinessModel somBean, Boolean... ignorePersistenceUniqueId) {
        if (ignorePersistenceUniqueId.length == 0) {
            somBean.setPersistenceUniqueId(plBean.getId().toString());
        }

        List<String> plList = Arrays.asList(plAttributeNames);
        List<String> somList = Arrays.asList(somAttributeNames);
        String className = DataMediatorUtils.getRealClassName(plBean);
        if (log.isTraceEnabled()) {
            log.trace(className);
        }

        Class<?> plBeanRealClass = DataMediatorUtils.getRealClass(plBean);
        Field[] fields = plBeanRealClass.getDeclaredFields();
        List<String> fieldNameList = new ArrayList();
        List<Field> fieldList = new ArrayList();

        for(Field field : fields) {
            Column column = (Column)field.getAnnotation(Column.class);
            if (column != null && plList.contains(column.name())) {
                fieldNameList.add(column.name());
                fieldList.add(field);
            }
        }

        if (plBeanRealClass.getSuperclass() != BaseEntity.class) {
            Field[] superclassFields = plBeanRealClass.getSuperclass().getDeclaredFields();

            for(Field field : superclassFields) {
                Column column = (Column)field.getAnnotation(Column.class);
                if (column != null && plList.contains(column.name())) {
                    fieldNameList.add(column.name());
                    fieldList.add(field);
                }
            }
        }

        if (plBeanRealClass != InsuranceRiskOffer.class && !fieldNameList.containsAll(plList)) {
        }

        for(Field field : fieldList) {
            Method plGetterMethod;
            try {
                plGetterMethod = plBeanRealClass.getMethod(String.format("get%s", StringUtils.capitalize(field.getName())));
            } catch (NoSuchMethodException nsme) {
                log.error("Could not find the approriate getter for the field conversion from PL to SOM. Make sure the getter exists!", nsme);
                throw new SystemException("Could not find the approriate getter for the field " + StringUtils.capitalize(field.getName()) + " conversion from PL to SOM. Make sure the getter exists!", nsme);
            }

            String somFieldName = null;

            try {
                Object value = plGetterMethod.invoke(plBean);
                if (value != null) {
                    Column column = (Column)field.getAnnotation(Column.class);
                    String plFieldName = column.name();
                    int index = plList.indexOf(plFieldName);
                    somFieldName = (String)somList.get(index);
                    Method somGetterMethod = somBean.getClass().getMethod(DataMediatorUtils.accessorMethodName("get", somFieldName));
                    Method somSetterMethod = somBean.getClass().getMethod(DataMediatorUtils.accessorMethodName("set", somFieldName), somGetterMethod.getReturnType());
                    somSetterMethod.invoke(somBean, getTranslatedValueFromPLtoSOM(value, somGetterMethod.getReturnType()));
                }
            } catch (NoSuchMethodException nsme) {
                String message = MessageFormat.format("Could not find the approriate getter for the field [ {0} ] conversion from PL to SOM. Make sure the getter exists!", somFieldName);
                log.error(message, nsme);
                throw new SystemException(message, nsme);
            } catch (InvocationTargetException ex) {
                log.error("Error while invoking the getter/setter method", ex);
                throw new SystemException("Error while invoking the getter/setter method", ex);
            } catch (IllegalAccessException ex) {
                log.error("Access error on method", ex);
                throw new SystemException("Access error on method", ex);
            }
        }

    }

    public static Object getTranslatedValueFromPLtoSOM(Object value, Class<?> somFieldClass) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Object objectToSet = null;
        if (somFieldClass.equals(String.class)) {
            if (value instanceof Boolean) {
                objectToSet = BooleanUtils.isTrue((Boolean)value) ? "Y" : "N";
            } else if (value.getClass().isEnum()) {
                objectToSet = value.getClass().getMethod("getCode").invoke(value);
            } else {
                objectToSet = value.toString();
            }
        } else if (somFieldClass.equals(Integer.class)) {
            if (value.getClass().isEnum()) {
                objectToSet = Integer.valueOf(value.getClass().getMethod("getCode").invoke(value).toString());
            } else {
                objectToSet = Integer.valueOf(value.toString());
            }
        } else if (somFieldClass.equals(Double.class)) {
            if (value instanceof Integer) {
                objectToSet = Double.parseDouble(value.toString());
            } else if (value instanceof BigDecimal) {
                objectToSet = ((BigDecimal)value).doubleValue();
            } else if (value instanceof Double) {
                objectToSet = (Double)value;
            }
        } else if (somFieldClass.equals(Long.class)) {
            if (value instanceof Integer) {
                objectToSet = ((Integer)value).longValue();
            }
        } else {
            if (!somFieldClass.equals(GregorianCalendar.class)) {
                log.error("No appropriate conversion has been found for the {SOM field class, PL field class} pair.");
                throw new SystemException("Error in the PL >> SOM conversion algorithm");
            }

            GregorianCalendar gc = new GregorianCalendar();
            if (value instanceof Date) {
                gc.setTime((Date)value);
            } else {
                gc.setTime(new Date());
            }

            objectToSet = gc;
        }

        return objectToSet;
    }

    static {
        NO_CREDIT_SCORE_MANUFACTURERS = new ArrayList(Arrays.asList(ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION));
        SAVERS_MANUFACTURERS = new ArrayList(Arrays.asList(ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION, ManufacturerCompanyCodeEnum.ING_WESTERN_REGION));
        PolicyHolderComparator = new Comparator<PolicyHolder>() {
            public int compare(PolicyHolder object1, PolicyHolder object2) {
                return object2.getPolicyHolderType().getCode().compareTo(object1.getPolicyHolderType().getCode());
            }
        };
    }
}
