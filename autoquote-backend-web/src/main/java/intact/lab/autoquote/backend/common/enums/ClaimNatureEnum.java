package intact.lab.autoquote.backend.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Getter
@RequiredArgsConstructor
public enum ClaimNatureEnum {
    AT_FAULT_ACCIDENT("AA"),
    NOT_AT_FAULT_ACCIDENT("NA"),
    AT_FAULT_ACCIDENT_FOR_50_PCT("AAF"),
    AT_FAULT_ACCIDENT_WITH_THIRD_PARTY_LIABILITY_PAYOUT("AWP"),
    SINGLE_VEHICLE_ACCIDENT_WITH_NO_DAMAGE_TO_A_THIRD_PARTYS_VEHICLE_OR_OBJECT("SWD"),
    HIT_AND_RUN("HR"),
    FIRE_VANDALISM("FV"),
    WINDSHIELD_REPAIR("GR"),
    WINDSHIELD_REPLACEMENT("GW"),
    THEFT("TH"),
    WINDSTORM_OR_HAIL("WN"),
    INSURANCE_OF_PERSON("AP"),
    ACCIDENT_BENEFITS("AB"),
    BODILY_INJURY("BI"),
    FIRE("FIR"),
    VANDALISM("VAN"),
    IMPACT_WITH_ANIMAL("IA"),
    FIRE_THEFT_VANDALISM_STORM("ONC");

    private final String code;

    public static ClaimNatureEnum valueOfCode(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        for (ClaimNatureEnum v : values()) {
            if (v.code.equals(value)) {
                return v;
            }
        }

        throw new IllegalArgumentException("No enum value found for code: " + value);
    }

    @JsonValue
    public String getCode() {
        return code;
    }
}
