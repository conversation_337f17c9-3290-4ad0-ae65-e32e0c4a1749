/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.intact.rating.IPremiumDerivationService;
import com.intact.rating.exception.RatingException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.services.business.offer.IRateManagerService;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.IRatingServiceHelper;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

@Service
public abstract class RateManagerService implements IRateManagerService {

	private static final Logger LOG = ESAPI.getLogger(RateManagerService.class);

	@AutowiredLocal
	protected IPremiumDerivationService premiumDeviationService;

	@Autowired
	protected IRatingServiceHelper helper;

	@Autowired
	protected IInsuranceRiskOfferHelper insuranceRiskOfferHelper;

	@Autowired
	@Qualifier("plpCoverageHelper")
	protected ICoverageHelper coverageHelper;

	@AutowiredLocal
	protected IRatingService ratingService;

	@Autowired
	protected IDataMediatorToPL dataMediatorToPL;

	@Override
	@Transactional
	public PolicyVersion rateTheWholeQuotation(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled)
			throws AutoquoteRatingException {

		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		performanceWatch.start("    >> rateTheWholeQuotation.rateOffer");
		PolicyVersion policyVersion = this.getRatingService().rateOffer(aPolicyVersion, isAgent, isUbiEnabled);
		performanceWatch.stop();
		if (LOG.isTraceEnabled()) {
			try {
				this.helper.printPolicyCoverage(policyVersion, true, LOG, this.premiumDeviationService.getReferenceOffer(policyVersion.getInsurancePolicy().getApplicationMode().getCode()));
			} catch (Exception ignored) {
				LOG.warning(Logger.EVENT_FAILURE, ">> failed silently on call printPolicyCoverage : " + ignored.getMessage());
			}
		}

		performanceWatch.start("    >> rateTheWholeQuotation.rateService");
		try {
			this.premiumDeviationService.rateService(policyVersion);
			// Update market segmentation for ubi eligibility where needed
			this.updateSegmentation(policyVersion);
			// Add UBI endorsement if eligible and apply discount (re-rate) if selected
			if (isUbiEnabled) {
				this.ratingService.manageUbi(policyVersion, null, performanceWatch);
			}

		} catch (RatingException e) {
			LOG.error(Logger.EVENT_FAILURE, "A rating excepytion occured: " + e.getMessage());
			throw new AutoquoteRatingException(e.getMessage(), e);
		} finally {
			if (LOG.isTraceEnabled()) {
				try {
					this.helper.printPolicyCoverage(policyVersion, false, LOG, this.premiumDeviationService.getReferenceOffer(policyVersion.getInsurancePolicy().getApplicationMode().getCode()));
					this.helper.printFactors(policyVersion, LOG);
				} catch (Exception ignored) {
					LOG.warning(Logger.EVENT_FAILURE, ">> failed silently on call printPolicyCoverage|printFactors : " + ignored.getMessage());
				}
			}
		}
		performanceWatch.stop();

		if (LOG.isTraceEnabled()) {
			LOG.trace(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
		}

		return policyVersion;
	}

	/**
	 * Update Segmentation
	 */
	public void updateSegmentation(PolicyVersion aPolicyVersion) {
		// implemented locally where needed
	}

}
