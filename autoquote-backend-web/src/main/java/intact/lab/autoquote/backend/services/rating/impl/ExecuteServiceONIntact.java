package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.base.SSDC;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IGenericPegaService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ExecuteServiceONIntact implements IExecuteService {

    private static final Logger LOG = ESAPI.getLogger(ExecuteServiceABIntact.class);

    @Autowired
    @Qualifier("determineQuoteInfos_0x00")
    private IGenericPegaService determineQuoteInfos;

    @Autowired
    @Qualifier("manageProducts0x00")
    private IGenericPegaService manageProducts0x00;

    @Autowired
    @Qualifier("codePlPolicyCar0x00")
    private IGenericPegaService codePlPolicyCar0x00;

    @Autowired
    @Qualifier("ratePlPolicyCar0x00")
    private IGenericPegaService ratePlPolicyCar0x00;

    @Override
    public PolicyVersion determineQuoteInfos(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Pega::determineQuoteInfos_0x00");
        }
        return (PolicyVersion) this.determineQuoteInfos.executeService(aCtxt, aPolicy);
    }

    /**
     * Calls manageProducts
     *
     * @param aPolicy policyVersion
     * @param ilParams the a params
     *
     * @throws BaseException the base exception
     */
    public PolicyVersion manageProducts(PolicyVersion aPolicy, Map<String, Object> ilParams) throws BaseException {
        PolicyVersion response = (PolicyVersion) this.manageProducts0x00.executeService(ilParams, aPolicy);
        String traceId = SSSUtils.getTraceId(ilParams);
        String serviceStatus = SSSUtils.getServiceStatus(ilParams);
        if (!SSDC.SERVICE_STATUS_VALUE_OK.equalsIgnoreCase(serviceStatus)) {
            LOG.error(Logger.EVENT_SUCCESS, "manageProducts0x00 Service status > " + serviceStatus + " traceId > " + traceId);
        }
        return response;
    }

    public PolicyVersion codePlPolicyCar(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {
        LOG.debug(Logger.EVENT_SUCCESS, "Pega:codePlPolicyCar0x00"); //#########
        return (PolicyVersion) this.codePlPolicyCar0x00.executeService(aCtxt, aPolicy);
    }

    public PolicyVersion ratePlPolicyCar(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {
        LOG.debug(Logger.EVENT_SUCCESS, "Pega:ratePlPolicyCar0x00"); //#########
        return (PolicyVersion) this.ratePlPolicyCar0x00.executeService(aCtxt, aPolicy);
    }
}
