package intact.lab.autoquote.backend.services.business.vehicle.impl;

import com.ing.canada.common.domain.VehicleDetail;
import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.services.api.vehicle.IVehicleDetailService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.AntiTheftDeviceRequiredCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OwnerTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.VehicleCategoryCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRole;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.service.IAdditionalInterestRoleService;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Component
@AllArgsConstructor
public class VehicleBusinessProcess implements IVehicleBusinessProcess {

    private static final Logger LOG = ESAPI.getLogger(VehicleBusinessProcess.class);

    private final VehicleMakesByYearService vehicleMakesByYearService;
    private final VehicleModelsService vehicleModelsService;
    protected IDateManagerService dateManagerService;
    private final IVehicleDetailService vehicleDetailService;
    private final ICommonBusinessProcess commonBusinessProcess;
    private IPartyRoleInRiskService partyRoleInRiskService;
    private IPartyService partyService;
    private IAdditionalInterestRoleService additionalInterestRoleService;
    private IInsuranceRiskService insuranceRiskService;

    @Override
    public int getVehicleCount(PolicyVersion aPolicyVersion) {
        Set<InsuranceRisk> insuranceRiskSet = aPolicyVersion.getInsuranceRisks();
        return insuranceRiskSet.size();
    }


    @Override
    public List<String> getVehicleManufacturerListByYear(String year, DistributionChannelCodeEnum distributionchannel, InsuranceBusinessCodeEnum insuranceBusiness, ProvinceCodeEnum provinceCode) {
        return this.vehicleMakesByYearService.getVehicleManufacturerListByYear(year, distributionchannel, insuranceBusiness, provinceCode);
    }

    @Override
    public List<VehicleModel> getVehicleModelList(Language language, String make, String year, DistributionChannelCodeEnum distributionchannel, InsuranceBusinessCodeEnum insuranceBusiness, ProvinceCodeEnum provinceCode) {
        return this.vehicleModelsService.getVehicleModelList(language, make, year, distributionchannel, insuranceBusiness, provinceCode);
    }

    @Override
    public VehicleModel getVehicleModelEnglishByModelCode(String modelCode, String make, String year,
                                                          DistributionChannelCodeEnum distributionchannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                                          ProvinceCodeEnum provinceCode) {
        return this.getModel(modelCode, make, year, Language.ENGLISH, distributionchannel, insuranceBusiness, provinceCode);
    }

    @Override
    public VehicleModel getVehicleModelFrenchByModelCode(String modelCode, String make, String year,
                                                         DistributionChannelCodeEnum distributionchannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                                         ProvinceCodeEnum provinceCode) {
        return this.getModel(modelCode, make, year, Language.FRENCH, distributionchannel, insuranceBusiness, provinceCode);
    }

    @Override
    public void setVehicleDetail(Vehicle aVehicle, Locale locale, Long aPolicyVersionId, DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness) {
        VehicleDetail details = this.getVehicleDetailToVehicleDetailSpecificationRepositoryEntry(locale, aVehicle, aPolicyVersionId, distributionChannel, insuranceBusiness);
        loadVehicleDetailToVehicleDetailSpecificationRepositoryEntry(details, aVehicle);
    }

    /**
     * Obtain the vehicle detail specification repository
     * @param aLocale {@link Locale}
     * @param aVehicle {@link VehicleDetail}
     * @param aPolicyVersion {@link Long}
     * @param distributionChannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @return {@link VehicleDetail}
     */
    public VehicleDetail getVehicleDetailToVehicleDetailSpecificationRepositoryEntry(Locale aLocale, Vehicle aVehicle, Long aPolicyVersion,
                                                                                     DistributionChannelCodeEnum distributionChannel,
                                                                                     InsuranceBusinessCodeEnum insuranceBusiness ) {

        Language language = Language.fromLocale(aLocale);
        Province province = Province.fromLocale(aLocale);

        Integer vehicleYear = aVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleYear();
        String vehicleCode = aVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry().getVehicleCode();
        Date ratingDate = this.dateManagerService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, aPolicyVersion);

        return this.vehicleDetailService.getVehicleDetailService(
                province, language, ratingDate, vehicleCode, vehicleYear.toString(), distributionChannel, insuranceBusiness
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void detachVehicle(Vehicle aVehicle, PolicyVersion aPolicyVersion, ApplicationEnum application) throws Exception {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format(">> detach vehicle={insuranceRisk.id=[%d], insuranceRisk.sequence=[%d],vehicle.id=[%d], policyVersion.id=[%d]} from application=[%s]",
                    aVehicle.getInsuranceRisk().getId(), aVehicle.getInsuranceRisk().getInsuranceRiskSequence(), aVehicle.getId(), aPolicyVersion.getId(), application));
        }

        // The vehicle deletion must be perform now (Can't be executed on the persistCascadeAll).
        // When deleting a vehicle, if the persistCascade fails later the deleted vehicle is not restored.
        this.removePersistedPartyRoleInRisk(aVehicle.getInsuranceRisk());
        this.removePersistedAdditionalInterestRoles(aVehicle);
        this.insuranceRiskService.delete(aVehicle.getInsuranceRisk());
        aPolicyVersion.removeInsuranceRisk(aVehicle.getInsuranceRisk());
    }

    /*
     * Remove a persisted party role in risk from an insuranceRisk (vehicle)
     */
    private void removePersistedPartyRoleInRisk(InsuranceRisk insuranceRisk) {

        Set<PartyRoleInRisk> partyRoleInRiskList = insuranceRisk.getPartyRoleInRisks();
        List<PartyRoleInRisk> lst = new ArrayList<PartyRoleInRisk>(partyRoleInRiskList);

        if (partyRoleInRiskList != null) {
            for (PartyRoleInRisk partyRoleInRisk : lst) {
                if (partyRoleInRisk.getId() != null && !OwnerTypeCodeEnum.SECOND_REGISTERED_OWNER.equals(partyRoleInRisk.getOwnerType())) {
                    Party party = partyRoleInRisk.getParty();
                    // May be null if the owner of a vehicle equal to 'Other' - BR467
                    if (party != null) {
                        party.removePartyRoleInRisk(partyRoleInRisk);
                    }
                    insuranceRisk.removePartyRoleInRisk(partyRoleInRisk);
                    this.partyRoleInRiskService.delete(partyRoleInRisk);
                }
            }
        }
    }

    /*
     * Remove a vehicle's additional interest roles (lessor, lienholder) prior to saving it.
     */
    private void removePersistedAdditionalInterestRoles(Vehicle vehicle) {
        Party roleParty = null;
        Set<AdditionalInterestRole> roles = vehicle.getAdditionalInterestRoles();
        if (roles != null) {
            for (AdditionalInterestRole role : roles) {
                // detach related party
                roleParty = role.getParty();
                if (roleParty != null) {
                    // attached party should be of type PartyTypeCodeEnum.CORPORATION for LIENHOLDER or LESSOR
                    // and have no other reference in the policy version
                    role.setParty(null);
                    roleParty.removeAdditionalInterestRole(role);
                    // detach party from the policy version itself
                    roleParty.setPolicyVersion(null);
                    vehicle.getInsuranceRisk().getPolicyVersion().removeParty(roleParty);

                    this.partyService.delete(roleParty);
                }
                // detach related vehicle
                if (role.getVehicle() != null) {
                    role.setVehicle(null);
                }
                this.additionalInterestRoleService.delete(role);
            }
        }
    }

    /**
     * Get vehicle model by language.
     *
     * @param modelCode the model code
     * @param make the make
     * @param year the year
     * @param language the language
     * @param distributionchannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param provinceCode {@link ProvinceCodeEnum}
     *
     * @return the model
     */
    private VehicleModel getModel(String modelCode, String make, String year,
                                  Language language,
                                  DistributionChannelCodeEnum distributionchannel,
                                  InsuranceBusinessCodeEnum insuranceBusiness,
                                  ProvinceCodeEnum provinceCode) {
        VehicleModel model = new VehicleModel();
        List<VehicleModel> vehicleModelList = this.vehicleModelsService.getVehicleModelList(language, make, year, distributionchannel, insuranceBusiness, provinceCode);
        for (VehicleModel vehicleModel : vehicleModelList) {
            if (vehicleModel.getCode().equals(modelCode)) {
                model = vehicleModel;
                break; // exit when found
            }
        }
        return model;
    }

    /**
     * Load vehicle detail to vehicle detail specification repository entry.
     *
     * @param vehicleDetail the a locale
     * @param aVehicle the a vehicle
     */
    private static void loadVehicleDetailToVehicleDetailSpecificationRepositoryEntry(final VehicleDetail vehicleDetail, final Vehicle aVehicle) {
        ManufacturingContext manufacturingContext = aVehicle.getInsuranceRisk().getPolicyVersion().getInsurancePolicy().getManufacturingContext();

        if(vehicleDetail!=null){
            VehicleDetailSpecificationRepositoryEntry detailSpecs = aVehicle.getVehicleDetailSpecificationRepositoryEntry();
            detailSpecs.setAntiTheftDeviceRequired(AntiTheftDeviceRequiredCodeEnum.valueOfCode(vehicleDetail.getAntiTheftDeviceRequired()));
            detailSpecs.setCommercialClassEligibilityIndicator(vehicleDetail.getCommercialClassEligibilityIndicator());
            detailSpecs.setFacilityRiskSharingPoolScoring(vehicleDetail.getFacilityRiskSharingPoolScoring());
            detailSpecs.setManufacturingContext(manufacturingContext);
            detailSpecs.setMinimumDeductibleMandatoryIndB3B4Indicator(vehicleDetail.getMinimumDeductibleMandatoryIndicatorB3B4());
            detailSpecs.setMinimumDeductibleMandatoryIndC2C3Indicator(vehicleDetail.getMinimumDeductibleMandatoryIndicatorC2C3());
            detailSpecs.setNumberOfCylinders(vehicleDetail.getNumberOfCylinders());
            detailSpecs.setRateGroupClearAccidentBenefit(vehicleDetail.getVehicleRateGroupClearAccidentBenefits());
            detailSpecs.setRateGroupClearCollision(vehicleDetail.getVehicleRateGroupClearCollision());
            detailSpecs.setRateGroupClearCombined(vehicleDetail.getVehicleRateGroupClearCombined());
            detailSpecs.setRateGroupClearComprehensive(vehicleDetail.getVehicleRateGroupClearComprehensive());
            detailSpecs.setRateGroupClearDcpd(vehicleDetail.getVehicleRateGroupClearDCPD());
            detailSpecs.setRateGroupClearLiability(vehicleDetail.getVehicleRateGroupClearLiability());
            detailSpecs.setRateGroupModClearCombined(vehicleDetail.getVehicleRateGroupModifiedClearCombined());
            detailSpecs.setRateGroupModClearCombinedNewBusiness(vehicleDetail.getVehicleRateGroupModifiedClearCombinedNewBusiness());
            detailSpecs.setRateGroupMsrp(vehicleDetail.getVehicleRateGroupMSRP());
            detailSpecs.setReplacementCostIndicator(vehicleDetail.getReplacementCostIndicator());
            detailSpecs.setRestrictedVehicleIndicator(vehicleDetail.getRestrictedVehicleIndicator());
            detailSpecs.setRspAdjustmentScenarioVehicleNewBusiness(vehicleDetail.getRSPAdjustmentScenarioVehicleNewBusiness());
            detailSpecs.setRspAdjustmentScenarioVehicleStandard(vehicleDetail.getRSPAdjustmentScenarioVehicleStandard());
            detailSpecs.setVehicleCategory(VehicleCategoryCodeEnum.valueOfCode(vehicleDetail.getVehicleCategory()));
            detailSpecs.setVehicleInspectionIndicator(vehicleDetail.getVehicleInspectionIndicator());
            // RNAS-16 fields
            aVehicle.setVehicleBodyType(vehicleDetail.getBodyStyle());
            aVehicle.setVehicleNetWeight(vehicleDetail.getWeight());
            detailSpecs.setHorsePower(vehicleDetail.getHorsePower());
            detailSpecs.setRetailPriceWithGst(vehicleDetail.getRetailPriceWithGst());
            // RAI (Rate Adequacy Index) fields
            detailSpecs.setWheelbaseQty( getWheelBaseQty(vehicleDetail) );
            detailSpecs.setAirbagsCd(vehicleDetail.getAirbagsCd());
            detailSpecs.setAbsBrakesCd(vehicleDetail.getAbsBrakesCd());
            detailSpecs.setAudibleAlarmCd(vehicleDetail.getAudibleAlarmCd());
            detailSpecs.setCutOffSystemCd(vehicleDetail.getCutOffSystemCd());
            detailSpecs.setSecurityKeySystemCd(vehicleDetail.getSecurityKeySystemCd());
            detailSpecs.setIbcApprovedCd(vehicleDetail.getIbcApprovedCd());
            detailSpecs.setEngineCylinderCd(vehicleDetail.getEngineCylinderCd());
            detailSpecs.setIbcMarketCd(vehicleDetail.getIbcMarketCd());
            detailSpecs.setVehicleSizeCd(vehicleDetail.getVehicleSizeCd());
            detailSpecs.setVehicleGenerationCd(vehicleDetail.getVehicleGenerationCd());
            detailSpecs.setFuelUsedByVehicleCd(vehicleDetail.getFuelUsedByVehicleCd());
            detailSpecs.setEngineForceInductionCd(vehicleDetail.getEngineForceInductionCd());
            detailSpecs.setEngineHybridCd(vehicleDetail.getEngineHybridCd());
            detailSpecs.setTractionControlCd(vehicleDetail.getTractionControlCd());
            detailSpecs.setStabitlityControlCd(vehicleDetail.getStabitlityControlCd());
            detailSpecs.setDriveTrainCd(vehicleDetail.getDriveTrainCd());
            String fwdLowSpeed = vehicleDetail.getForwardCollisionMitigationLowSpeed() != null ? vehicleDetail.getForwardCollisionMitigationLowSpeed().toString(): null;
            detailSpecs.setForwardCollisionMitigationLowSpeed(fwdLowSpeed);
            String fwdAllSpeed = vehicleDetail.getForwardCollisionMitigationAllSpeed() != null ? vehicleDetail.getForwardCollisionMitigationAllSpeed().toString(): null;
            detailSpecs.setForwardCollisionMitigationAllSpeed(fwdAllSpeed);

        }
    }

    private static Integer getWheelBaseQty(VehicleDetail vd) {
        Integer wheelbaseQty = null;
        if (StringUtils.isNotEmpty(vd.getWheelbaseQty())) {
            try {
                wheelbaseQty = Integer.parseInt(vd.getWheelbaseQty());
            }catch(NumberFormatException nfe) {
                LOG.error(Logger.EVENT_FAILURE, String.format("Unable to set numeric value of wheelbase qty (value=%s) : %s", vd.getWheelbaseQty(), nfe.getMessage()));
            }
        }
        return wheelbaseQty;
    }

    /**
     * Gets the PolicyVersion.
     *
     * @param aPolicyVersionId the PolicyVersion id
     *
     * @return the PolicyVersion
     */
    public PolicyVersion getPolicyVersion(final Long aPolicyVersionId) {
        return this.commonBusinessProcess.loadPolicyVersion(aPolicyVersionId);
    }

    @Override
    public RuleExceptionResult validateHardRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage) {
        return null;
    }

    @Override
    public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage) {
        return null;
    }
}
