package intact.lab.autoquote.backend.facade.impl;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.dto.ContextDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteOfferException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.QuoteValidationException;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.facade.ICommonFacade;
import intact.lab.autoquote.backend.facade.IOfferFacade;
import intact.lab.autoquote.backend.facade.offer.IAutoQuoteOfferFacade;
import intact.lab.autoquote.backend.facade.offer.impl.AutoQuoteOfferFacade;
import intact.lab.autoquote.backend.validation.IQuoteDTOValidator;
import intact.lab.autoquote.backend.validation.IValidatorFactory;
import intact.lab.autoquote.backend.validation.impl.GeneralValidator;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


import jakarta.annotation.Resource;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;

@Component
@Transactional
public class OfferFacade implements IOfferFacade {

    private static final Logger LOG = ESAPI.getLogger(OfferFacade.class);

    @Resource(name = "comQuoteConverter")
    private ICOMConverter<QuoteDTO, CommunicationObjectModel> comQuoteConverter;

    private final IValidatorFactory validatorFactory;

    public OfferFacade(IValidatorFactory validatorFactory) {
        this.validatorFactory = validatorFactory;
    }

    @Override
    @Transactional(value = "transactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseDTO calculate(QuoteDTO quoteDTO, String apiKey, String province, String language, String subBroker, String organizationSource) {
        this.validateQuote(quoteDTO, AutoQuoteConstants.STR_INTACT, province, language);
        return this.getPrice(quoteDTO, null, apiKey, province, language, subBroker, organizationSource);
    }

    @Override
    @Transactional(value = "transactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ResponseDTO recalculate(QuoteDTO quoteDTO, String uuid, String apiKey, String province, String language, String subBroker, String organizationSource) {
        this.validateQuote(quoteDTO, AutoQuoteConstants.STR_INTACT, province, language);
        return this.getPrice(quoteDTO, uuid, apiKey, province, language, subBroker, organizationSource);
    }

    public void validateQuote(QuoteDTO quoteDTO, String company, String province, String language) throws AutoQuoteApiParametersException, QuoteValidationException {
        BindingResult errors = new BeanPropertyBindingResult(quoteDTO, "quoteDTO");
        GeneralValidator.validateApiParameter(language, province);

        IQuoteDTOValidator quoteValidator = validatorFactory.getQuoteDTOValidator(new ValidationContext(province, language, company, quoteDTO), errors);
        quoteValidator.validateQuote(quoteDTO, errors);

        if (errors.hasErrors()) {
            throw new QuoteValidationException(QuoteValidationException.VALIDATION_ERROR, quoteDTO, errors);
        }
    }

    private ResponseDTO getPrice(QuoteDTO quoteDTO, String uuid, String apiKey, String province, String language, String subBroker, String organizationSource) {

        QuoteDTO responseQuoteDTO;
        CommunicationObjectModel initialCom = null;
        try {
            ContextDTO contextDTO = ContextUtil.buildUserContext(language, province, apiKey, subBroker, organizationSource);
            quoteDTO.setContextDTO(contextDTO);
            ComContext comContext = ContextUtil.loadInitialComContext(null,
                    quoteDTO.getContextDTO().getBusinessContext().getProvince(),
                    quoteDTO.getContextDTO().getLanguage().getIsoCode(), quoteDTO.getContextDTO().getSubBroker(),
                    quoteDTO.getContextDTO().getOrigin(), null);

            if (StringUtils.isEmpty(uuid)) {
                initialCom = this.instantiateCommonFacade(comContext).createQuote(comContext);
            } else {
                initialCom = this.instantiateCommonFacade(comContext).retrievePolicyByUUID(comContext, uuid);
            }
            CommunicationObjectModel com = this.comQuoteConverter.toCOM(quoteDTO, initialCom);
            com = this.instantiateCommonFacade(comContext).save(com, false);
            this.verifyRoadBlock(com, quoteDTO);

            com = this.instantiateCommonOfferFacade(comContext).retrieveOffer(com);
            this.verifyRoadBlock(com, quoteDTO);

            responseQuoteDTO = this.comQuoteConverter.toDTO(com);
            ResponseDTO responseDTO = new ResponseDTO();
            responseDTO.setData(responseQuoteDTO);
            return responseDTO;

        } catch (AutoQuoteRoadBlockException e) {
            LOG.error(Logger.EVENT_SUCCESS, ">> Facade : Exception occurred while getPrice", e);
            throw e;
        } catch (Exception e) {
            LOG.error(Logger.EVENT_FAILURE, ">> Facade : Exception occurred while getPrice", e);
            throw new AutoQuoteOfferException(AutoQuoteOfferException.EXEC_GET_PRICE_ERROR, new Object[]{e, initialCom != null ? initialCom.getAgreementNumber() : "no AgreementNumber", apiKey, province, language});
        }
    }

    /**
     * Instantiate the right class for the Interface according to the context.
     *
     * @param aComContext {@link ComContext}
     * @return {@link ICommonFacade}
     *
     * @throws AutoQuoteException throws {@link AutoQuoteException} on exception
     */
    public ICommonFacade instantiateCommonFacade(ComContext aComContext) throws AutoQuoteException {
        return AutoQuoteCommonFacade.getInstance(aComContext);
    }

    /**
     * Instantiate the right class for the Interface according to the context.
     *
     * @param aComContext {@link ComContext}
     * @return {@link ICommonFacade}
     *
     * @throws AutoQuoteException throws {@link AutoQuoteException} on exception
     */
    public IAutoQuoteOfferFacade instantiateCommonOfferFacade(ComContext aComContext) throws AutoQuoteException {
        return AutoQuoteOfferFacade.getInstance(aComContext);
    }

    private void verifyRoadBlock(CommunicationObjectModel com, QuoteDTO quoteDTO) throws AutoQuoteRoadBlockException {
        //TODO : pour les roadblock, dans le futur on doit passer par la mediation COM To DTO
        if (com.getRoadblock() != null && com.getRoadblock().size() > 0) {
            quoteDTO.setNumber(com.getAgreementNumber());
            quoteDTO.setId(com.getUuId());
            quoteDTO.setPvId(String.valueOf(com.getPolicyVersionId()));
            if (com.getContext().getBrokerInfo() != null) {
                quoteDTO.setSubBrokerNbr(com.getContext().getBrokerInfo().getSubBrokerNumber());
            }
            throw new AutoQuoteRoadBlockException(AutoQuoteRoadBlockException.ROAD_BLOCK_ERROR, quoteDTO, com.getRoadblock());
        }
    }
}
