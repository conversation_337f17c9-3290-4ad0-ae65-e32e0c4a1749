package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.common.services.api.policydate.IManageDatesService;
import com.ing.canada.plp.dao.policyversion.IProducerRepositoryEntryDAO;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.enums.AgreementTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationFlowStateCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.AssignmentOriginatorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;
import com.ing.canada.plp.domain.enums.BranchCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.InitialTransactionCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.RatingBasisCodeEnum;
import com.ing.canada.plp.domain.enums.SpfCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionStatusCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.partnership.Partnership;
import com.ing.canada.plp.domain.policyversion.DirectChanDistRepEntry;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.policyversion.Producer;
import com.ing.canada.plp.domain.policyversion.ProducerRepositoryEntry;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.service.IDirectChanDistRepEntryService;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPartnershipService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.INewQuoteBusinessProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;

@Component
@Slf4j
public class NewQuoteBusinessProcessService implements INewQuoteBusinessProcessService {

    private static final String INTACT_AGENT_NBR = "9243";

    private final IDirectChanDistRepEntryService directChanDistRepEntryService;
    private final IInsurancePolicyService insurancePolicyService;
    private final IPolicyVersionService policyVersionService;
    private final IPartnershipService partnershipService;
    private final DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;
    private final IManageDatesService dateService;
    private final IDataMediatorToPL dataMediatorToPL;
    private final IProducerRepositoryEntryDAO producerRepositoryEntryDAO;

    public NewQuoteBusinessProcessService(IDirectChanDistRepEntryService directChanDistRepEntryService,
                                          IInsurancePolicyService insurancePolicyService,
                                          IPolicyVersionService policyVersionService,
                                          IPartnershipService partnershipService,
                                          DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory,
                                          @Qualifier("capiManageDatesService1x00") IManageDatesService dateService,
                                          IDataMediatorToPL dataMediatorToPL,
                                          IProducerRepositoryEntryDAO producerRepositoryEntryDAO) {
        this.directChanDistRepEntryService = directChanDistRepEntryService;
        this.insurancePolicyService = insurancePolicyService;
        this.policyVersionService = policyVersionService;
        this.partnershipService = partnershipService;
        this.dataMediatorToSOMServiceFactory = dataMediatorToSOMServiceFactory;
        this.dateService = dateService;
        this.dataMediatorToPL = dataMediatorToPL;
        this.producerRepositoryEntryDAO = producerRepositoryEntryDAO;
    }

    @Override
    @Transactional()
    public PolicyVersion newQuote(ComContext aComContext, ManufacturingContext mCtxt, String subBrokerId,
                                  ApplicationModeEnum appModeEnum, ApplicationFlowStateCodeEnum appFlowState,
                                  ComBrokerWebSiteOriginEnum origin, LineOfBusinessCodeEnum lineOfBusiness) {

        ExternalSystemOriginCodeEnum externalSystemOriginCode = null;
        if (ComBrokerWebSiteOriginEnum.BROKER.equals(origin)) {
            externalSystemOriginCode = ExternalSystemOriginCodeEnum.WEB_BROKER;
        }

        PolicyVersion policyVersion = new PolicyVersion();

        InsurancePolicy insurancePolicy = this.createInsurancePolicy(ProvinceCodeEnum.valueOfCode(aComContext.getProvince().getCode().toUpperCase()),
                mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness(), mCtxt.getManufacturerCompany(),
                ApplicationModeEnum.QUICK_QUOTE, aComContext.isTestDataInd(), lineOfBusiness, null);

        /*
         * Support for IntactQuickQuote Skip the broker assignation if the
         * SUB_BROKER_ID is null.
         */
        if (StringUtils.isNotEmpty(subBrokerId)) {
            this.assignBrokerToQuote(insurancePolicy, policyVersion, subBrokerId);
        }

        policyVersion = this.setQuoteInformation(aComContext.getLanguage().getCode(),
                aComContext.getMarketingPromotionCode(), null, insurancePolicy, policyVersion, appFlowState,
                null, aComContext.getClientIPNumber(), aComContext.getClientXForwardIPNbr(), lineOfBusiness);

        this.addDefaultAgentToPolicyVersion(policyVersion);

        //if(ComBrokerWebSiteOriginEnum.BROKER.equals(origin)){
        if (ExternalSystemOriginCodeEnum.WEB_BROKER.equals(origin)) {
            policyVersion.getInsurancePolicy().setExternalSystemOrigin(externalSystemOriginCode);
        }

        return policyVersion;
    }

    protected InsurancePolicy createInsurancePolicy(ProvinceCodeEnum provinceCodeEnum, DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                                    ManufacturerCompanyCodeEnum manufacturerCompany, ApplicationModeEnum applicationMode, boolean testDataIndicator, LineOfBusinessCodeEnum lineOfBusiness, String sessionNumber) {

        ManufacturingContext manufacturingContext = this.policyVersionService.findManufacturingContext(provinceCodeEnum, distributionChannel, insuranceBusiness, manufacturerCompany);
        InsurancePolicy insurancePolicy = new InsurancePolicy();
        insurancePolicy.setAgreementType(AgreementTypeCodeEnum.QUOTATION);
        insurancePolicy.setSpfCode(SpfCodeEnum.STANDARD_OWNERS_AUTOMOBILE_POLICY.getCode());
        insurancePolicy.setRatingBasis(RatingBasisCodeEnum.INDIVIDUALLY_RATED);
        insurancePolicy.setLineOfBusinessCode(lineOfBusiness!=null ? lineOfBusiness : LineOfBusinessCodeEnum.PERSONAL_LINES);
        insurancePolicy.setManufacturingContext(manufacturingContext);

        insurancePolicy.setAgreementNumber(this.insurancePolicyService.generateQuotationNumber(applicationMode, lineOfBusiness));
        if (log.isDebugEnabled()){
            log.debug("Quotation number = {}", insurancePolicy.getAgreementNumber());
        }

        insurancePolicy.setUuId(this.insurancePolicyService.generateUUID(applicationMode, lineOfBusiness));
        if (log.isDebugEnabled()){
            log.debug("Quotation uuid = {}", insurancePolicy.getUuId());
        }

        insurancePolicy.setCreditScoreInfoClientConfirmationInd(Boolean.FALSE);
        insurancePolicy.setTestDataIndicator(testDataIndicator);
        insurancePolicy.setManufacturerCompany(manufacturerCompany);
        insurancePolicy.setApplicationMode(applicationMode);
        return insurancePolicy;
    }

    @Override
    public void assignBrokerToQuote(InsurancePolicy insurancePolicy, PolicyVersion policyVersion, String subBrokerId) {
        SubBrokerAssignment subBrokerAssignment = new SubBrokerAssignment();
        subBrokerAssignment.setCifSubBrokerId(Long.parseLong(subBrokerId));
        subBrokerAssignment.setEffectiveDate(new Date());
        subBrokerAssignment.setAssignmentOriginatorType(AssignmentOriginatorTypeCodeEnum.SYSTEM);
        subBrokerAssignment.setAssignmentReason(AssignmentReasonCodeEnum.SYSTEM_DEFAULT_ASSIGNMENT);
        insurancePolicy.addSubBrokerAssignment(subBrokerAssignment);
    }

    public void addDefaultAgentToPolicyVersion(PolicyVersion policyVersion) {

        ProducerRepositoryEntry producerRepositoryEntry = new ProducerRepositoryEntry();
        producerRepositoryEntry.setBranch(BranchCodeEnum.BELAIR_DIRECT);

        String agentNb = this.getAgentCode(); // agent number is a constant for intact
        producerRepositoryEntry.setAgentNumber(agentNb);

        // Populate the producer
        ProducerRepositoryEntry producerRepositoryEntryFromDAO = this.producerRepositoryEntryDAO.findByBranchMarketingTerritoryUnderwritingTeamAndAgentNumber(
                producerRepositoryEntry.getBranch().getCode(), producerRepositoryEntry.getMarketingTerritory(), producerRepositoryEntry.getUnderwritingTeam(),
                producerRepositoryEntry.getAgentNumber());
        if (producerRepositoryEntryFromDAO != null) {
            producerRepositoryEntry = producerRepositoryEntryFromDAO;
        }

        Producer producer = new Producer(policyVersion, producerRepositoryEntry);
        if (log.isDebugEnabled()){
            ProducerRepositoryEntry proPre = producer.getProducerRepositoryEntry();
            log.debug("Assigning producer to policyVersion[id={}]: branch={}, marketingTerritory={}, agent={} (pre.id={}) ]",
                    policyVersion.getId(), proPre.getBranch(), proPre.getMarketingTerritory(), proPre.getAgentNumber(), proPre.getId());
        }
    }

    protected String getAgentCode() {
        return INTACT_AGENT_NBR;
    }

    public PolicyVersion setQuoteInformation(final String language,
                                                final String marketingPromotionCode,
                                                final String partnershipId,
                                                final InsurancePolicy insurancePolicy,
                                                final PolicyVersion aPolicyVersion,
                                                final ApplicationFlowStateCodeEnum currentApplicationFlowState,
                                                final DistributorCodeEnum distributor,
                                                final String clientIPNumber,
                                                final String clientXForwardIPNumber, final LineOfBusinessCodeEnum lineOfBusiness) {

        try {

            // Populate the business transaction Activity
            BusinessTransaction businessTransaction = new BusinessTransaction();
            businessTransaction.setTransactionCode(TransactionCodeEnum.NEW_BUSINESS_QUOTE);
            businessTransaction.setTransactionStatus(TransactionStatusCodeEnum.SUSPENDED);
            final Date now = Calendar.getInstance().getTime();
            businessTransaction.setCreationDateTime(now);

            // QP-5619 Contact requires the transaction effective date when uploading, defaulting to now timestamp
            // The value needs to be overwritten when the inception date is captured
            businessTransaction.setTransactionEffectiveDateTime(now);

            businessTransaction.setTransactionSequence((short) 1);
            businessTransaction.setCurrentApplicationFlowState(currentApplicationFlowState);
            businessTransaction.setLastRatingSequence((short) 0);

            if (log.isDebugEnabled()) {
                log.debug("businessTransaction = {}", ToStringBuilder.reflectionToString(businessTransaction));
            }

            // Populate the policy version
            PolicyVersion policyVersion = aPolicyVersion;
            policyVersion.setPolicyVersionType(
                    LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(lineOfBusiness)
                            ? PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL
                            : PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_PERSONAL);

            if (language.equalsIgnoreCase("fr")) {
                policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
            } else {
                policyVersion.setLanguageOfCommunication(LanguageCodeEnum.ENGLISH);
            }

            policyVersion.setInitialTransactionCode(InitialTransactionCodeEnum.NEW_BUSINESS);
            policyVersion.setBusinessTransaction(businessTransaction);
            policyVersion.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);

            // Populate the insurance policy
            insurancePolicy.addPolicyVersion(policyVersion);

            // Set a new ReferenceDate
            policyVersion.setMarketingPromotionCode(marketingPromotionCode);

            // Set the direct channel distributor when provided
            if (distributor != null) {
                DirectChanDistRepEntry dcdre = this.directChanDistRepEntryService.find(distributor);
                policyVersion.setDirectChanDistRepEntry(dcdre);
            }

            // Set the incoming IP address
            policyVersion.setClientIPNbr(clientIPNumber);
            policyVersion.setClientXForwardIPNbr(clientXForwardIPNumber);

            // Partnership
            if (StringUtils.isNotBlank(partnershipId) && !StringUtils.equals("null", partnershipId)) {
                Partnership partnership = this.partnershipService.findById(Long.parseLong(partnershipId));
                if (log.isDebugEnabled()) {
                    log.debug("partnership = {}", ToStringBuilder.reflectionToString(partnership));
                }
                policyVersion.addPartnership(partnership);
            }

            if (log.isDebugEnabled()) {
                log.debug("insurancePolicy before saving = {}", ToStringBuilder.reflectionToString(insurancePolicy));
            }

            // Save the insurance policy
            this.insurancePolicyService.persist(insurancePolicy);
            if (log.isDebugEnabled()) {
                log.debug("policyVersion before saving = {}", ToStringBuilder.reflectionToString(policyVersion));

            }

            // Save the policy version
            policyVersion = this.policyVersionService.persist(policyVersion);
            if (log.isDebugEnabled()) {
                log.debug("policyVersion after saving = {}", ToStringBuilder.reflectionToString(policyVersion));
            }

            // Set a new ReferenceDate
            policyVersion = this.initializeReferenceDate(insurancePolicy, policyVersion);
            if (log.isDebugEnabled()) {
                log.debug("policyVersion after initializeReferenceDate = {}", ToStringBuilder.reflectionToString(
                                policyVersion));
            }

            return policyVersion;
        } catch (Exception exp) {
            log.error("ERROR At setQuoteInformation: \nMessage: {} \nStackTrace: {}", exp.getMessage(), exp.getStackTrace());
        }
        return null;
    }

    private PolicyVersion initializeReferenceDate(final InsurancePolicy aInsurancePolicy, final PolicyVersion aPolicy) {


        com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = dataMediatorToSOMServiceFactory
                .getService("dataMediatorToSOM").convertTo_SOM(aPolicy);
        if (log.isDebugEnabled()) {
            log.debug("somPolicyVersion after convertTo_SOM = " + ToStringBuilder.reflectionToString(somPolicyVersion));
        }

        // Call the date manager service (PEGA) if the ReferenceDate Table is not linked to the PolicyVersion.
        // IMPORTANT NOTE = It's MANDATORY to call the manage dates service before calling the others PEGA
        // service because the others services need to have access to ReferenceDate object.

        try {
            if (aPolicy.getReferenceDate() == null) {
                boolean isBc = aInsurancePolicy.getManufacturingContext().getProvince()
                        == ProvinceCodeEnum.BRITISH_COLUMBIA;
                if (isBc) {
                    aInsurancePolicy.getManufacturingContext().setProvince(ProvinceCodeEnum.ALBERTA);
                }
                somPolicyVersion = this.dateService.callManageDates(
                        aInsurancePolicy.getManufacturingContext(), somPolicyVersion);
                if (isBc) {
                    aInsurancePolicy.getManufacturingContext().setProvince(ProvinceCodeEnum.BRITISH_COLUMBIA);
                }
                if (log.isDebugEnabled()) {
                    log.debug("somPolicyVersion when referenceDate is null = {}", ToStringBuilder.reflectionToString(
                                    somPolicyVersion));
                }
            }
        } catch (Exception exp) {
            log.error("Failed to call PEGA service's ManageDatesService: \nMessage: {} \nStackTrace: {}",
                    exp.getMessage(), exp.getStackTrace());
        }
        // Convert back to PL
        PolicyVersion policyVersion = this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPolicy, false);
        if (log.isDebugEnabled()) {
            log.debug("policyVersion after convertTo_PL = {}", ToStringBuilder.reflectionToString(policyVersion));
        }
        return policyVersion;
    }
}
