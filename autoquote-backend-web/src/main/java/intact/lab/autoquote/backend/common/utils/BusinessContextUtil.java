package intact.lab.autoquote.backend.common.utils;

import intact.lab.autoquote.backend.common.enums.ProvinceEnum;
import intact.lab.autoquote.backend.common.model.BusinessContext;
import org.springframework.beans.factory.annotation.Value;

public class BusinessContextUtil {

    @Value("${application.id.ontario}")
    private static String ontarioApplicationId;

    @Value("${application.id.others}")
    private static String otherProvinceApplicationId;

    public static BusinessContext create(ProvinceEnum province) {
        BusinessContext context = new BusinessContext();
        context.setApplication(province == ProvinceEnum.ONTARIO ? ontarioApplicationId : otherProvinceApplicationId);
        context.setDescription("Intact Auto QuickQuote IRCA C/L");
        context.setDistributionChannel("B");
        context.setUnderwritingCompany("001");
        context.setInsuranceBusiness("R");
        context.setCountry("CA");
        context.setLineOfBusiness("COMMERCIAL_LINES");
        context.setCompany("INTACT");
        return context;
    }
}
