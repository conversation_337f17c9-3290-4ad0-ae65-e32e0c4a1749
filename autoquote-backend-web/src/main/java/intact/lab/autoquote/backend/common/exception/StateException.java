/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.exception;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.io.Serial;

public class StateException extends RuntimeException {

	private static final Logger LOG = ESAPI.getLogger(StateException.class);

	@Serial
	private static final long serialVersionUID = -4379521679148832252L;


	/**
	 * Instantiates a new state exception.
	 *
	 * @param msg the msg
	 */
	public StateException(String msg) {
		super(msg);
		if (LOG.isDebugEnabled()) {
			// log the exception message to the console
			LOG.debug(Logger.EVENT_SUCCESS, msg);
		}
	}
}
