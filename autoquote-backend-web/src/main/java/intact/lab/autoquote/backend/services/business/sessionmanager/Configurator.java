package intact.lab.autoquote.backend.services.business.sessionmanager;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.stereotype.Component;

@Slf4j
@Component("config")
public class Configurator implements IConfigurator {

    private final ReloadableResourceBundleMessageSource config;

    Configurator(@Qualifier("reloadableResourceBundleMessageSource") ReloadableResourceBundleMessageSource config) {
        this.config = config;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isUbiVersion2(String province, String distributor) {
        String upProvince = StringUtils.isBlank(province)? "" : province.toUpperCase();
        String upDistrib = StringUtils.isBlank(distributor)? "" : distributor.toUpperCase();
        StringBuilder sb = new StringBuilder("config.ubi2.").append(upProvince);
        // append distributor only for BNA
        if (!StringUtils.isBlank(upDistrib) && !"BEL".equals(upDistrib)) {
            sb.append(".").append(upDistrib);
        }
        // if by chance province was null (or empty), or no config found: will return false
        return this.getValue(sb.toString(), false);
    }

    @Override
    public boolean isUbi(String province, String distributor) {
        if (distributor == null || "".equals(distributor)) {
            return this.isUbi(province);
        }
        return this.getValue("config.ubi." + province.toUpperCase() + "." + distributor, false);
    }

    @Override
    public boolean isUbi(String province) {

        return this.getValue("config.ubi." + province.toUpperCase(), false);
    }

    /**
     * Reads from the configuration the value for the key given in parameter.
     *
     * @param aKey the key
     * @param aDefault default value if key is missing in file
     * @return Boolean value of the key
     */
    private boolean getValue(String aKey, Boolean aDefault) {
        boolean rc = false;
        try {
            String strValue = this.config.getMessage(aKey, null, null);
            if (!StringUtils.isBlank(strValue)) {
                rc = Boolean.parseBoolean(strValue);
                if (log.isTraceEnabled()){
                    log.trace("Value of :{} is = {}", aKey, rc);
                }
            }
        } catch (NoSuchMessageException nsex) {
            // Default value
            if (aDefault != null) {
                if (log.isWarnEnabled()){
                    log.warn("Unable to read property, using default: {}={}", aKey, rc);
                }
                rc = aDefault;
                // No default
            } else {
                if (log.isErrorEnabled()){
                    log.error("Unable to read property: {}", aKey, nsex);
                }
            }
        }
        return rc;
    }
}
