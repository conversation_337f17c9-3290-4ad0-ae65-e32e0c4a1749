/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.webattack;

import com.ing.canada.plp.domain.ManufacturingContext;

/**
 * 
 * <AUTHOR>
 */
public interface IWebAttackBusinessProcess {

	void keepTraceOfThisCall(ManufacturingContext context, String applicationId, String clientIp, String ipType,
			String eventType, String parameterKey1, String trackingNbr) throws Exception;

}
