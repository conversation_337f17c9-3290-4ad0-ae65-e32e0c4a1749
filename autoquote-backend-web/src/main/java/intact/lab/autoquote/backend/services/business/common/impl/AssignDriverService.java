/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.services.impl.pegaservices.PegaBaseService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.delegate.services.GenericDelegate;
import intact.lab.autoquote.backend.services.business.common.IAssignDriverService;
import org.springframework.stereotype.Service;

/**
 * The Class AssignDriverService.
 */
@Service
public class AssignDriverService extends PegaBaseService implements IAssignDriverService {

	/** Service properties */
	private static final String COMPONENT_NAME = "UNDERWRITING";

	private static final String SERVICE_NAME = "ASSIGN_DRIVERS";

	private static final String SERVICE_VERSION = "0.00";

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PolicyVersion assignDrivers(PolicyVersion aSomPolicyVersion, ManufacturingContext aContext) {
		try {		
			// Create the assignation service.
			GenericDelegate createAssignDriverService = new GenericDelegate(COMPONENT_NAME, SERVICE_NAME, SERVICE_VERSION,
					aSomPolicyVersion, getDelegateParameters(aContext));

			// call the service

            return (PolicyVersion) createAssignDriverService.executeService();

		} catch (BaseException ex) {
			throw new SystemException("", ex);
		} catch (Exception ex) {
			throw new SystemException("", ex);
		}
	}
}
