/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.plp.domain.enums.ActionTakenCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageSelectedTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.impl.InsuranceRiskOfferHelper;
import intact.lab.autoquote.backend.common.utils.RatingUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Some conversions are made on the SOM, others are made on PL. The reason is that when the coverageEligibileInd is set
 * to 'N', the coverageOffer will be flushed by the DataMediatorToPL. For the custom offer, it shouldn't happen. So we
 * process these on PL.
 *
 * <AUTHOR>
 */
@Component
public class RatingExternalizationEligibilityTranslator {

    @Autowired
    private InsuranceRiskOfferHelper insuranceRiskOfferHelper;

    /**
     * Translates the selections and eligibility on the custom offer after a simple CodePl call or a combination of
     * CodePl and RatePl
     * <p>
     * Post code pl policy process on custom offer.
     *
     * @param plPolicyVersion the pl policy version
     */
    public void postCodePlAndOrRatePlPolicyProcessOnCustomOffer(PolicyVersion plPolicyVersion) {
        Set<com.ing.canada.plp.domain.insurancerisk.InsuranceRisk> irSet = plPolicyVersion.getInsuranceRisks();

        for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk : irSet) {
            InsuranceRiskOffer insuranceRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForOfferType(insuranceRisk, OfferTypeCodeEnum.CUSTOM);

            if (insuranceRiskOffer.getCoverageOffers() != null) {
                for (CoverageOffer coverageOffer : insuranceRiskOffer.getCoverageOffers()) {
                    // on endorsements or discounts
                    if (CoverageTypeCodeEnum.ENDORSEMENT.equals(coverageOffer.getCoverageRepositoryEntry().getCoverageType())
                            || CoverageTypeCodeEnum.DISCOUNT_SURCHARGE.equals(coverageOffer.getCoverageRepositoryEntry().getCoverageType())) {

                        // In Autoquote, discounts are not selectable
                        // Every action about UE05 (intact ontario coverage) is managed by a manageUBI
                        // in RatingServiceON, because the selection is determined by a customer decision
                        // and eligibility by codePL
                        if ("UE05".equalsIgnoreCase(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
                            if (coverageOffer.getActionTaken() != null && "D".equals(coverageOffer.getActionTaken().getCode())) {
                                coverageOffer.setCoverageSelectedIndicator(Boolean.FALSE);
                            }
                            if (coverageOffer.getCoverageSelectableIndicator() == null) {
                                coverageOffer.setCoverageSelectableIndicator(Boolean.TRUE);
                            }
                            coverageOffer.setCoverageSelectedType(CoverageSelectedTypeCodeEnum.COVERAGE_HAS_BEEN_ADDED_BY_THE_SYSTEM);
                        }

                        // discounts are not selectable. we set them as selected and eligible when returned by CodePl and RatePl
                        // discounts never have ActionTaken='D', since we always flush the discounts before calling Pega
                        // 13D is an exception and might have an actionTaken = D (Intact Alberta)
                        else if (CoverageTypeCodeEnum.DISCOUNT_SURCHARGE.equals(coverageOffer.getCoverageRepositoryEntry().getCoverageType())
                                && !EndorsementCodeEnum._13D_SAV.getCode().equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {

                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);
                            coverageOffer.setCoverageSelectableIndicator(Boolean.FALSE);
                            coverageOffer.setCoverageSelectedIndicator(Boolean.TRUE);
                            coverageOffer.setCoverageSelectedType(CoverageSelectedTypeCodeEnum.COVERAGE_HAS_BEEN_ADDED_BY_THE_SYSTEM);

                        } else if (EndorsementCodeEnum._20_SAV.getCode().equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())
                                && ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())
                                && RatingUtil.isPlusPacEligibleOnPl(insuranceRiskOffer)) {

                            // 20 stays eligible, since a custom offer has a mutually exclusive selection between the Plus Pac and OPCF20
                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);

                        }else if (EndorsementCodeEnum.E33_SAV.getCode().equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())
                                && ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())
                                && RatingUtil.isPlusPacEligibleOnPl(insuranceRiskOffer)) {

                            // E33 stays eligible, since a custom offer has a mutually exclusive selection between the Plus Pac and OPCF20
                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);

                        } else if (EndorsementCodeEnum._033_SAV.getCode().equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())
                                && ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())
                                && RatingUtil.isPlusPacEligibleOnPl(insuranceRiskOffer)) {

                            // 033 stays eligible, since a custom offer has a mutually exclusive selection between the Plus Pac and Roadside Assistance
                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);

                        } else if (RatingUtil.isCoverageOfCodeOnPl(coverageOffer,
                                EndorsementCodeEnum._27A_SAV,
                                EndorsementCodeEnum._35_SAV,
                                EndorsementCodeEnum._033_SAV,
                                EndorsementCodeEnum._43_SAV,
                                EndorsementCodeEnum._43A_SAV,
                                EndorsementCodeEnum._43R_SAV,
                                EndorsementCodeEnum._43L_SAV)) {
                            // other plus-pac components are never offered individually better safe than sorry
                            coverageOffer.setCoverageEligibleIndicator(Boolean.FALSE);

                        } else if (ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())) {
                            coverageOffer.setCoverageEligibleIndicator(Boolean.FALSE);

                        } else if (BooleanUtils.isFalse(coverageOffer.getCoverageSelectableIndicator())
                                && !ActionTakenCodeEnum.LOGICAL_DELETE.equals(coverageOffer.getActionTaken())) {
                            // not selectable coverages must be selected if eligible. This is where it is done in the translation.
                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);
                            coverageOffer.setCoverageSelectedIndicator(Boolean.TRUE);
                        } else {
                            coverageOffer.setCoverageEligibleIndicator(Boolean.TRUE);
                            if (coverageOffer.getCoverageSelectableIndicator() == null) {
                                coverageOffer.setCoverageSelectableIndicator(Boolean.TRUE);
                            }
                            if (coverageOffer.getCoverageSelectedIndicator() == null) {
                                coverageOffer.setCoverageSelectedIndicator(Boolean.FALSE);
                            }
                        }
                        RatingUtil.logCoverageOfferSelectionData(coverageOffer);
                    }
                }
            } // else there are no coverages...
        }
    }
}
