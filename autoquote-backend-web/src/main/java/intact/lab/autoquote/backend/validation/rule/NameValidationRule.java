package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public abstract class NameValidationRule {

	private static final String patternExpName = "[a-zA-ZÀÁÂÄÇÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜâàáäçèéêëìíîïòóôöùúûü' \\-]*";
	private static final String patternExpUnstructName = "[a-zA-Z0-9ÀÁÂÄÇÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜâàáäçèéêëìíîïòóôöùúûü' \\-.&()+]*";

	public static void validate(String firstName, String lastName, Errors errors) {

		if (StringUtils.isEmpty(firstName)) {
			errors.rejectValue("firstName", BRulesExceptionEnum.NotBlank.getErrorCode(), "[firstName]");
		} else if (firstName.trim().length() < 2 || firstName.length() > 30) {
			errors.rejectValue("firstName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode(), "[firstName]");
		} else {
			if (!match(patternExpName, firstName)) {
				errors.rejectValue("firstName", BRulesExceptionEnum.ERR_DRIVER_FIRSTNAME_BR7714.getErrorCode(), "[firstName]");
			}
		}
		if (StringUtils.isEmpty(lastName)) {
			errors.rejectValue("lastName", BRulesExceptionEnum.NotBlank.getErrorCode(), "[lastName]");
		} else if (lastName.trim().length() < 2 || lastName.length() > 30) {
			errors.rejectValue("lastName", BRulesExceptionEnum.ERR_DRIVER_LASTNAME_BR7714.getErrorCode(), "[lastName]");
		} else {
			if (!match(patternExpName, lastName)) {
				errors.rejectValue("lastName", BRulesExceptionEnum.ERR_DRIVER_LASTNAME_BR7714.getErrorCode(), "[lastName]");
			}
		}
	}

	public static void validateUnstructured(String unstructuredName, Errors errors) {
		if (StringUtils.isEmpty(unstructuredName)) {
			errors.rejectValue("unstructuredName", BRulesExceptionEnum.NotBlank.getErrorCode(), "[unstructuredName]");
		} else if (unstructuredName.trim().length() < 2 || unstructuredName.length() > 64) {
			errors.rejectValue("unstructuredName", "UNSTRUCT_LENGTH", "[unstructuredName]");
		} else {
			if (!match(patternExpUnstructName, unstructuredName)) {
				errors.rejectValue("unstructuredName", "UNSTRUCT_PATTERN", "[unstructuredName]");
			}
		}

	}

	/**
	 * Match.
	 *
	 * @param patternExp the pattern exp
	 * @param value      the value
	 * @return true, if successful
	 */
	protected static boolean match(String patternExp, String value) {
		Pattern pattern = Pattern.compile(patternExp);
		Matcher matcher = pattern.matcher(value);
		return matcher.matches();
	}

}
