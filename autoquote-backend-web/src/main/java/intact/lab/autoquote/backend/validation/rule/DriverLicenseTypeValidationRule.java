package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.enums.ContentsMappingEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.services.impl.AutoQuoteServiceCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.List;

@Component
public class DriverLicenseTypeValidationRule {

	private final AutoQuoteServiceCache autoQuoteServiceCache;

	public DriverLicenseTypeValidationRule(AutoQuoteServiceCache autoQuoteServiceCache) {
		this.autoQuoteServiceCache = autoQuoteServiceCache;
	}

	public void validate(final String driverLicenseType, final String province, final String language, Errors errors, String fieldName) {
		final List<ValidValueBO> licenseTypeList = this.autoQuoteServiceCache.getListByProvinceAndLocaleBO(ContentsMappingEnum.TYPE_OF_LICENCE.name(),
				province, language);
		if (StringUtils.isEmpty(driverLicenseType)) {
			errors.rejectValue(fieldName, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(fieldName));
		} else {
			if (!ValidationUtilities.isExistValueInList(licenseTypeList, driverLicenseType)) {
				errors.rejectValue(fieldName, BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), ValidationUtilities.bracket(fieldName));
			}
		}
	}
}
