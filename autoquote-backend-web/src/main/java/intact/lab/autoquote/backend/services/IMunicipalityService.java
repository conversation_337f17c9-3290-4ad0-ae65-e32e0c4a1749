/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services;

import com.ing.canada.common.domain.Municipality;
import com.ing.canada.plp.domain.ManufacturingContext;

import java.util.Locale;

/**
 * Defines a municipality service
 * 
 * <AUTHOR> pabonnea
 * 
 */
public interface IMunicipalityService {

	/**
	 * Gets the first municipality for postal code.
	 * 
	 * @param locale the locale
	 * @param postalCode the postal code
	 * @param context the context
	 * 
	 * @return the first municipality for postal code
	 */
	Municipality getFirstMunicipalityForPostalCode(Locale locale, String postalCode, ManufacturingContext context);


}
