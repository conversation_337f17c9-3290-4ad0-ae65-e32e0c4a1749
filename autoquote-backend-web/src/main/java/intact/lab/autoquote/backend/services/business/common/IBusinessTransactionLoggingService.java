
package intact.lab.autoquote.backend.services.business.common;

import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;

public interface IBusinessTransactionLoggingService {


	/**
	 * This method is used to create a BusinessTransactionActivity.
	 * 
	 * @param code the code
	 * @param policyId the policy id
	 * @param userId the user id
	 * @param mainframeUserId the mainframe user id
	 * @param userTypeCode the user type code
	 */
	void createActivity(BusinessTransactionActivityCodeEnum code, long policyId, String userId, String mainframeUserId,
						UserTypeCodeEnum userTypeCode);

	/**
	 * This method can be called by any controller in the application. it create a sub activity with the
	 * BusinessTransactionSubActivityCodeEnum passed in parameter
	 *
	 * @param event BusinessTransactionSubActivityCodeEnum
	 * @param policy the policy
	 * @param rootReferenceId the root reference id
	 *
	 * @return the long
	 */
	long createSubActivity(BusinessTransactionSubActivityCodeEnum event, long policy, long rootReferenceId);

	/**
	 * this method can be called by any controller in the application to create a compl. info on defined sub activity
	 * code. (e.g roadBlock)
	 *
	 * @param complInfoCode the code representing the compl info
	 * @param complInfoValue a value attach to the code
	 * @param businessTransSubActId the id of the parent sub activity
	 */
	void createSubActivityComplInfo(String complInfoCode, String complInfoValue, long businessTransSubActId);


}
