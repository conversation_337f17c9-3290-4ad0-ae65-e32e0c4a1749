package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

public abstract class GenderValidationRule {

	public static void validate(String gender, Errors errors) {
		if (StringUtils.isBlank(gender)) {
			errors.rejectValue("gender", BRulesExceptionEnum.NotBlank.getErrorCode(), "[gender]");
		} else if (!gender.equals("M") && !gender.equals("F")) {
			errors.rejectValue("gender", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), "[gender]");
		}
	}
}
