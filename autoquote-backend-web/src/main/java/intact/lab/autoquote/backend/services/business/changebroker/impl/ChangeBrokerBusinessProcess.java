/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2011, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.changebroker.impl;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.enums.AssignmentOriginatorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.service.ISubBrokerAssignmentService;
import intact.lab.autoquote.backend.services.business.changebroker.IChangeBrokerBusinessProcess;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Component
public class ChangeBrokerBusinessProcess implements IChangeBrokerBusinessProcess {

	private static final Logger log = ESAPI.getLogger(ChangeBrokerBusinessProcess.class);

	@Autowired
	@Qualifier("subBrokersServiceOPC")
	private ISubBrokersService subBrokersService;

	@Autowired
	private ISubBrokerAssignmentService subBrokerAssignmentService;

	@Autowired
	private ICommonBusinessProcess commonBusinessProcess;

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void changeClientBroker(Long aPolicyVersionId, String aSubBrokerNumber, String aCompanyNumber, AssignmentReasonCodeEnum aReason) {
		this.changeClientBroker(aPolicyVersionId, aSubBrokerNumber, aCompanyNumber, aReason, AssignmentOriginatorTypeCodeEnum.CLIENT);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void changeClientBroker(Long aPolicyVersionId, String aSubBrokerNumber, String aCompanyNumber, AssignmentReasonCodeEnum aReason, AssignmentOriginatorTypeCodeEnum assignmentOriginator) {
		log.debug(Logger.EVENT_SUCCESS, String.format("broker number : %s  company : %s", aSubBrokerNumber, aCompanyNumber));
		// get the subBroker defined in CIF that match the subbrokernumber and company
		ISubBrokers subBroker = this.subBrokersService.getSubBroker(aSubBrokerNumber, aCompanyNumber);
		Long cifSubBrokerId = subBroker.getSubBrokerId();

		// find the current subBroker assign to the client and set the expiry date
		SubBrokerAssignment previousSubBrokerAssignment = this.subBrokerAssignmentService.getLastSubBrokerAssignmentForPolicy(aPolicyVersionId);
		previousSubBrokerAssignment.setExpiryDate(new Date());

		if (previousSubBrokerAssignment.getCifSubBrokerId() != cifSubBrokerId) {
			// create a new subBrokerAssignment with the ID defined in CIF
			SubBrokerAssignment subBrokerAssignment = new SubBrokerAssignment();
			subBrokerAssignment.setInsurancePolicy(previousSubBrokerAssignment.getInsurancePolicy());
			subBrokerAssignment.setAssignmentOriginatorType(assignmentOriginator);
			if (aReason == null) {
				subBrokerAssignment.setAssignmentReason(AssignmentReasonCodeEnum.OTHER);
			} else {
				subBrokerAssignment.setAssignmentReason(aReason);
			}
			subBrokerAssignment.setCifSubBrokerId(cifSubBrokerId);
			subBrokerAssignment.setEffectiveDate(new Date());
			subBrokerAssignment.setPreviousSubBrokerAssignment(previousSubBrokerAssignment);

			// add the subBrokerAssignment to the insurancePolicy
			PolicyVersion policyVersion = this.commonBusinessProcess.loadPolicyVersion(aPolicyVersionId);
			policyVersion.getInsurancePolicy().addSubBrokerAssignment(subBrokerAssignment);
		}
	}
}
