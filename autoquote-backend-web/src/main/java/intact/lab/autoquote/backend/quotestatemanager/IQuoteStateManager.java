package intact.lab.autoquote.backend.quotestatemanager;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.state.ComState;

public interface IQuoteStateManager {

    /**
     * Update State
     *
     * @param aComState {@link ComState}
     * @param aPolicyVersion {@link PolicyVersion}
     */
    void updateState(ComState aComState, PolicyVersion aPolicyVersion);
}
