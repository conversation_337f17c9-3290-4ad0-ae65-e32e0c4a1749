/*
 * Important notice: This software is the sole property of Intact Insurance Inc.
 * and cannot be distributed and/or copied without the written permission of Intact Insurance Inc.
 *
 * Copyright (c) 2009, Intact Insurance Inc., All rights reserved.
 */
package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.party.Party;
import com.ing.canada.ss.base.BaseException;

/**
 * The purpose of this service is to determine client eligibility level on a Party SOM graph
 * 
 * <AUTHOR>
 */
public interface IDetermineClientEligibilityLevel {

	/**
	 * Determine client eligibility level.
	 * 
	 * @param aParty SOM Party
	 * @param ctxt the ctxt
	 * 
	 * @return Updated SOM party
	 * 
	 * @throws BaseException If an error occurs within PEGA execution
	 */
	Party determineClientEligibilityLevel(ManufacturingContext ctxt, Party aParty) throws BaseException;
}
