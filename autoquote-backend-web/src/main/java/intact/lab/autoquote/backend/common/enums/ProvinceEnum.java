package intact.lab.autoquote.backend.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum ProvinceEnum {

	ONTARIO("ON"),
	QUEBEC("QC"),
	ALBERTA("AB");

	private static Map<String, ProvinceEnum> valuesMap = null;
	private String code = null;

	private ProvinceEnum(String code) {
		this.code = code;
	}

	public static synchronized ProvinceEnum fromCode(String code) {
		if (code != null) {
			code = code.toUpperCase();
		}


		if (ProvinceEnum.valuesMap == null) {
			ProvinceEnum.valuesMap = new HashMap<String, ProvinceEnum>();

			for (ProvinceEnum province : ProvinceEnum.values())
				ProvinceEnum.valuesMap.put(province.getCode().toUpperCase(), province);
		}

		return ProvinceEnum.valuesMap.get(code);
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String toString() {
		return "[code=" + this.getCode() + "]";
	}

}
