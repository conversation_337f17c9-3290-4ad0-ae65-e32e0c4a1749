/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.rating.exception.RatingException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.impl.RatingServiceQCIntactCL;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class RateManagerServiceQCIntactCL extends RateManagerService {
	private static final Logger log = ESAPI.getLogger(RateManagerServiceQCIntactCL.class);

	@AutowiredLocal
	private RatingServiceQCIntactCL ratingService;

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public PolicyVersion rateTheWholeQuotation(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled)
			throws AutoquoteRatingException {

		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		performanceWatch.start("    >> rateTheWholeQuotation.rateOffer");
		PolicyVersion policyVersion = this.getRatingService().rateOffer(aPolicyVersion, isAgent, isUbiEnabled);
		performanceWatch.stop();
		if (log.isDebugEnabled()) {
			try {
				this.helper.printPolicyCoverage(policyVersion, true, log, this.premiumDeviationService.getReferenceOffer(policyVersion.getInsurancePolicy().getApplicationMode().getCode()));
			} catch (Exception ignored) {
				log.warning(Logger.EVENT_FAILURE, ">> failed silently on call printPolicyCoverage : " + ignored.getMessage());
			}
		}

		performanceWatch.start("    >> rateTheWholeQuotation.rateService");
		try {
			this.premiumDeviationService.rateService(policyVersion);

			// Update market segmentation for ubi eligibility where needed
			this.updateSegmentation(policyVersion);

			// Add UBI endorsement if eligible and apply discount (re-rate) if
			// selected
			if (isUbiEnabled) {
				this.ratingService.manageUbi(policyVersion, null, performanceWatch);
			}

		} catch (RatingException e) {
			log.error(Logger.EVENT_FAILURE, "A rating excepytion occured: " + e.getMessage());
			throw new AutoquoteRatingException(e.getMessage(), e);
		} finally {
			if (log.isDebugEnabled()) {
				try {
					this.helper.printPolicyCoverage(policyVersion, false, log, this.premiumDeviationService.getReferenceOffer(policyVersion.getInsurancePolicy().getApplicationMode().getCode()));
					this.helper.printFactors(policyVersion, log);
				} catch (Exception ignored) {
					log.warning(Logger.EVENT_FAILURE, ">> failed silently on call printPolicyCoverage|printFactors : " + ignored.getMessage());
				}
			}
		}
		performanceWatch.stop();

		if (log.isTraceEnabled()) {
			log.trace(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
		}

		return policyVersion;
	}

	@Override
	public IRatingService getRatingService() {
		return this.ratingService;
	}

	@Transactional(propagation = Propagation.REQUIRED)
	@Override
	public void selectOffer(PolicyVersion aPolicyVersion, StopWatch performanceWatch, boolean isUbiEnabled)
			throws AutoquoteRatingException {
		this.premiumDeviationService.saveOfferSelectionsOnThePolicyOfferRating(aPolicyVersion);

		if (!LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(aPolicyVersion.getInsurancePolicy().getLineOfBusiness()) && isUbiEnabled) {
			// Add UBI endorsement if eligible and apply discount (re-rate) if selected
			this.ratingService.manageUbi4SelectOffer(aPolicyVersion, performanceWatch);
		}
	}
}
