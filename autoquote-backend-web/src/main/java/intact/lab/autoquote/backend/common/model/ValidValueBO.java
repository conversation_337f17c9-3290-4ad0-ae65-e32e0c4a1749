/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.model;

import org.apache.commons.lang3.builder.CompareToBuilder;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Collection;

/**
 * Valid values Businness Object.
 *
 * <AUTHOR>
 */
public class ValidValueBO implements Comparable<ValidValueBO> {

	private static final int _780924249 = 780924249;

	private static final int _1482699103 = 1482699103;

	private final String value;

	private final String label;

	private final int index;

	/**
	 * Creates a new ValidValueBO object.
	 *
	 * @param aKey The key
	 *
	 * @param aLabel The label
	 *
	 */
	public ValidValueBO(String aKey, String aLabel) {
		this(aKey, aLabel, 0);
	}

	/**
	 * Creates a new ValidValueBO object.
	 *
	 * @param aKey The key
	 * @param aLabel The label
	 * @param aIndex The index
	 */
	public ValidValueBO(String aKey, String aLabel, int aIndex) {
		this.value = aKey;
		this.label = aLabel;
		this.index = aIndex;
	}

	/**
	 * Util method to gets a label for value into a collection of ValidValueBO
	 *
	 * @param validValues the valid value collection to search
	 * @param value the value that we want the label
	 *
	 * @return the label for value
	 */
	public static String getLabelForValue(Collection<ValidValueBO> validValues, String value) {
		for (ValidValueBO validValueBO : validValues) {
			if (validValueBO.getValue().equals(value)) {
				return validValueBO.getLabel();
			}
		}
		return null;
	}

	/**
	 *
	 * the value
	 *
	 * @return The value
	 */
	public String getValue() {
		return this.value;
	}

	/**
	 *
	 * The label
	 *
	 * @return The label
	 */
	public String getLabel() {
		return this.label;
	}

	/**
	 *
	 * The index
	 *
	 * @return the index
	 */
	public int getIndex() {
		return this.index;
	}

	/**
	 * @see Object#equals(Object)
	 */
	@Override
	public boolean equals(Object object) {
		if (!(object instanceof ValidValueBO)) {
			return false;
		}

		ValidValueBO rhs = (ValidValueBO) object;

		return new EqualsBuilder().appendSuper(super.equals(object)).append(this.value, rhs.value).isEquals();
	}

	/**
	 * @see Comparable#compareTo(Object)
	 */
	public int compareTo(ValidValueBO aValidValueBO) {
		return new CompareToBuilder().append(this.index, aValidValueBO.index).toComparison();
	}

	/**
	 * @see Object#hashCode()
	 */
	@Override
	public int hashCode() {
		return new HashCodeBuilder(-_1482699103, _780924249).appendSuper(super.hashCode()).append(this.value).append(
				this.index).append(this.label).toHashCode();
	}

	/**
	 * @see Object#toString()
	 */
	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this, ToStringStyle.MULTI_LINE_STYLE);
	}
}
