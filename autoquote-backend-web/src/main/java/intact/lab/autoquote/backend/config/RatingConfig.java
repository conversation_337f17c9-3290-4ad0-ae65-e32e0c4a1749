package intact.lab.autoquote.backend.config;

import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class RatingConfig {

    private final Logger log = ESAPI.getLogger(RatingConfig.class);

    private final ReloadableResourceBundleMessageSource config;

    public RatingConfig(@Qualifier("reloadableRatingConfiguration") ReloadableResourceBundleMessageSource config) {
        this.config = config;
    }

    /**
     * Reads from the configuration the value for the key given
     * in parameter.
     *
     * @param aKey the key
     * @return Boolean value of the key
     */
    private Date getDateValue(String aKey) {
        Date rc = null;
        try {
            String strValue = this.config.getMessage(aKey, null, null);
            if (!StringUtils.isBlank(strValue)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                rc = sdf.parse(strValue);
                this.log.trace(Logger.EVENT_SUCCESS, String.format("Value of %s: is = %s", aKey, rc.toString()));
            }
        } catch (NoSuchMessageException nsex) {
            this.log.error(Logger.EVENT_FAILURE, String.format("Unable to read property: %s", aKey), nsex);
        } catch (ParseException epe) {
            this.log.error(Logger.EVENT_FAILURE, String.format("Unable to parse the date (yyyyMMdd): %s", aKey), epe);
        }
        return rc;
    }

    /**
     * Get overriding rating date
     *
     * @param aProvince
     * @return the date
     */
    public Date getOverridingRatingDate(String aProvince) {
        Date overridingRatingDate = this.getDateValue("date.overridingRatingDate." + aProvince.toUpperCase());
        // Double check : if overridingRatingDate is in the past, wipe it!
        if (overridingRatingDate != null && overridingRatingDate.before(new Date())) {
            this.log.error(Logger.EVENT_SUCCESS, String.format("Overriding Rating Date < %s > is in the past!", overridingRatingDate));
            overridingRatingDate = null;
        }
        return overridingRatingDate;
    }
}
