/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.common.domain.Group;
import com.ing.canada.common.domain.SubGroup;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.party.IGroupDescriptionService;
import com.ing.canada.common.services.api.party.ISubGroupDescriptionService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import intact.lab.autoquote.backend.services.business.common.IOccupationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Service to retrieve a list of occupation and domain of occupation from classic.
 *
 * <AUTHOR> Brochu, xpham
 */
@Component
public class OccupationService implements IOccupationService {

	private static final String OTHER_CD = "0006";

	@Autowired
	private ISubGroupDescriptionService subGroupDescriptionService;

	@Autowired
	@Qualifier("capiGroupDescriptionService")
	private IGroupDescriptionService groupDescriptionService;

	@Override
	public List<ValidValueBO> getOccupations(Locale aLocale, String domain, ManufacturingContext context, DistributorCodeEnum distributor) {

		List<ValidValueBO> validValues = new ArrayList<>();

		List<SubGroup> occupations = this.subGroupDescriptionService.getOccupations(Language.fromLocale(aLocale), domain, context, distributor);

		// Other must be at end
		occupations.sort((sg1, sg2) -> OTHER_CD.equals(sg1.getCode()) ? 1 : OTHER_CD.equals(sg2.getCode()) ? -1 : sg1.getDescription().compareTo(sg2.getDescription()));

		int index = 1;
		for (SubGroup occupation : occupations) {
			validValues.add(new ValidValueBO(occupation.getCode(), occupation.getDescription(), index));
			index++;
		}
		return validValues;
	}


	//	From service PARTY . LIST_GROUP_DESC_FROM_INSURED_GROUP_TABLE_FOR_GROUP_TYPE . 3.00
	private final static String classicRetiredCode = "0038";
	private final static String classicRetiredDescEn = "Retreat";
	private final static String classicRetiredDescFr = "Retraite";
	private final static String webRetiredDescEn = "Retired";
	// in french display as "Retraité" on the web
	private final static String webRetiredDescFr = "Retrait\u00e9";

	@Override
	public List<ValidValueBO> getDomains(Locale aLocale,
										 DistributionChannelCodeEnum distributionChannel,
										 InsuranceBusinessCodeEnum insuranceBusiness,
										 DistributorCodeEnum distributor) {

		List<ValidValueBO> validValues = new ArrayList<>();

		List<Group> domains = this.groupDescriptionService.getDomains(Language.fromLocale(aLocale), Province.fromLocale(aLocale), distributionChannel, insuranceBusiness, distributor);

		int i = 1;
		for (Group domain : domains) {
			if (classicRetiredCode.equals(domain.getCode())){
				validValues.add(new ValidValueBO(domain.getCode(), getRetiredDescription(domain.getDescription()), i));
			}else{
				validValues.add(new ValidValueBO(domain.getCode(), domain.getDescription(), i));
			}
			i++;
		}

		return validValues;
	}


	private String getRetiredDescription(String domainDesc) {
		String webDescription;
		switch(domainDesc){
			case classicRetiredDescEn:
				webDescription = webRetiredDescEn;
				break;
			case classicRetiredDescFr:
				webDescription = webRetiredDescFr;
				break;
			default:
				webDescription = domainDesc;
		}
		return webDescription;
	}

}
