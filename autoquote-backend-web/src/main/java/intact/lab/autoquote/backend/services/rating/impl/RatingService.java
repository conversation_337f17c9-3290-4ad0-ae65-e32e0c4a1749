/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.enums.AddressTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyScenarioCodeEnum;
import com.ing.canada.plp.domain.enums.DriverTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.RatingRiskTypeCodeEnum;
import com.ing.canada.plp.domain.enums.UBIStatusCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.insuranceriskoffer.RatingRiskOffer;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.ing.canada.plp.service.ICoverageService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.som.interfaces.agreement.PriorCarrierPolicyInfo;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.moneyProvisionPremium.CoveragePremium;
import com.ing.canada.som.interfaces.moneyProvisionPremium.SubCoveragePremium;
import com.ing.canada.som.interfaces.physicalObject.Vehicle;
import com.ing.canada.som.interfaces.registration.Conviction;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;
import com.ing.canada.som.rootclasses.GenericRootObject;
import com.ing.canada.ss.base.BaseException;
import com.intact.rating.IPremiumDerivationService;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import intact.lab.autoquote.backend.services.rating.ISelectOfferType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jvnet.hk2.annotations.Service;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * The Class RatingService.
 */
@Service
public abstract class RatingService implements IRatingService {

  protected static final String SOM_PHYSICAL_DELETION = "P";
  private static final Logger LOG = ESAPI.getLogger(RatingService.class);
  @Autowired
  protected IPartyHelper partyHelper;
  @Autowired
  protected IPolicyVersionHelper policyVersionHelper;
  @Autowired
  protected IPolicyVersionService policyVersionService;
  @Autowired
  protected IQuotationService quotationService;
  @Autowired
  protected IVehicleHelper vehicleHelper;
  @Autowired
  protected ICoverageService coverageService;
  @Autowired
  protected IDataMediatorToPL dataMediatorToPL;
  @Autowired
  protected IPolicyVersionHelper plpPolicyVersionHelper;
  @Autowired
  @Qualifier("plpCoverageHelper")
  protected ICoverageHelper coverageHelper;
  /**
   * The data mediator to som factory.
   */

  @Autowired
  private ISelectOfferType selectOfferType;

  /*
   * Creates the sub coverage premium.
   */
  private static void createSubCoveragePremium(Set<BasicCoverageCodeEnum> basicCoverageCodes, CoveragePremium aCoveragePremium) {
    for (BasicCoverageCodeEnum bcCode : basicCoverageCodes) {
      SubCoveragePremium scp = aCoveragePremium.addTheSubCoveragePremium();
      scp.setBasicCoverageCode(bcCode.getCode());
    }
  }

  private static void putGarbageInCVIScores(InsuranceRisk somInsuranceRisk, StringBuilder printMsg) {
    printMsg.append("\n");
    somInsuranceRisk.setCustomerValueIndexPureWithoutCredit(0);
    somInsuranceRisk.setCustomerValueIndexPureWithCredit(0);
    somInsuranceRisk.setRetentionScorePureWithoutCredit(0);
    somInsuranceRisk.setRetentionScorePureWithCredit(0);
    somInsuranceRisk.setCompetitivityScoreWithoutCredit(0);
    somInsuranceRisk.setCompetitivityScoreWithCredit(0);
  }

  private static void putGarbageInRatingDeviation(InsuranceRisk somInsuranceRisk, StringBuilder printMsg) {
    printMsg.append("\n");
    somInsuranceRisk.setCappingPercentage(0);
    somInsuranceRisk.setCappingPercentageSystem(0);
    somInsuranceRisk.setCompetitivityAdjustmentPercentage(0);
    somInsuranceRisk.setCustomerValueIndexAdjustedWithCredit(0);
    somInsuranceRisk.setFlexMaximumPercentage(0);
    somInsuranceRisk.setFlexPercentage(0);
    somInsuranceRisk.setFlexPercentageSystem(0);
    // somInsuranceRisk.setQualityRiskLevel("Z");
    // somInsuranceRisk.setRetentionBand("RT1");
    somInsuranceRisk.setScoringAdjustmentPercentage(0);
  }

  private static void printCVIScores(InsuranceRisk somInsuranceRisk, StringBuilder printMsg) {
    printMsg.append("\n").append("\n%%% InsuranceRiskSequence:\t").append(somInsuranceRisk.getInsuranceRiskSequence())
        .append("\n%%% OfferType:\t").append(somInsuranceRisk.getOfferType())
        .append("\n%%% CustomerValueIndexPureWithoutCredit:\t").append(somInsuranceRisk.getCustomerValueIndexPureWithoutCredit())
        .append("\n%%% CustomerValueIndexPureWithCredit:\t").append(somInsuranceRisk.getCustomerValueIndexPureWithCredit())
        .append("\n%%% RetentionScorePureWithoutCredit:\t").append(somInsuranceRisk.getRetentionScorePureWithoutCredit())
        .append("\n%%% RetentionScorePureWithCredit:\t").append(somInsuranceRisk.getRetentionScorePureWithCredit())
        .append("\n%%% CompetitivityScoreWithoutCredit:\t").append(somInsuranceRisk.getCompetitivityScoreWithoutCredit())
        .append("\n%%% CompetitivityScoreWithCredit:\t").append(somInsuranceRisk.getCompetitivityScoreWithCredit());
  }

  private static void validateRatingDeviation(InsuranceRisk somInsuranceRisk, StringBuilder printMsg) {
    printMsg.append("\n")
        .append("\n%%% InsuranceRiskSequence:\t").append(somInsuranceRisk.getInsuranceRiskSequence())
        .append("\n%%% OfferType:\t").append(somInsuranceRisk.getOfferType())
        .append(validateRatingDeviationValue("\n%%% CappingPercentage (Intact QC, Belair QC, Belair ON, GreyPower AB):\t", somInsuranceRisk.getCappingPercentage()))
        .append(validateRatingDeviationValue("\n%%% CappingPercentageSystem:\t", somInsuranceRisk.getCappingPercentageSystem()))
        .append(validateRatingDeviationValue("\n%%% CompetitivityAdjustmentPercentage (Belair QC, Belair ON, GreyPower AB):\t", somInsuranceRisk.getCompetitivityAdjustmentPercentage()))
        .append(validateRatingDeviationValue("\n%%% CustomerValueIndexAdjustedWithCredit:\t", somInsuranceRisk.getCustomerValueIndexAdjustedWithCredit()))
        .append(validateRatingDeviationValue("\n%%% FlexMaximumPercentage:\t", somInsuranceRisk.getFlexMaximumPercentage()))
        .append(validateRatingDeviationValue("\n%%% FlexPercentage (Intact QC):\t", somInsuranceRisk.getFlexPercentage()))
        .append(validateRatingDeviationValue("\n%%% FlexPercentageSystem:\t", somInsuranceRisk.getFlexPercentageSystem()))
        .append("\n%%% QualityRiskLevel:\t").append(somInsuranceRisk.getQualityRiskLevel())
        .append("\n%%% RetentionBand:\t").append(somInsuranceRisk.getRetentionBand())
        .append(validateRatingDeviationValue("\n%%% ScoringAdjustmentPercentage (Belair QC, Belair ON, GreyPower AB):\t", somInsuranceRisk.getScoringAdjustmentPercentage()));
  }

  private static String validateRatingDeviationValue(String description, Integer value) {
    StringBuilder printMsg = new StringBuilder();
    if ((value == null) || value.equals(0)) {
      printMsg.append(description).append(value).append("\t(Zero or null)");
    } else {
      printMsg.append(description).append(value).append("\t(NOT ZERO OR NULL)");
    }
    return printMsg.toString();
  }

  private static InsuranceRisk cloneInsuranceRisk(
      com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionClone, InsuranceRisk insuranceRisk) {
    InsuranceRisk insuranceRiskClone = policyVersionClone.addTheInsuranceRisk();
    insuranceRiskClone.setInsuranceRiskSequence(insuranceRisk.getInsuranceRiskSequence());
    insuranceRiskClone.setOfferType(insuranceRisk.getOfferType());

    // Livrable 4
    insuranceRiskClone.setCustomerValueIndexPureWithoutCredit(insuranceRisk
        .getCustomerValueIndexPureWithoutCredit());
    insuranceRiskClone.setCustomerValueIndexPureWithCredit(insuranceRisk.getCustomerValueIndexPureWithCredit());
    insuranceRiskClone.setRetentionScorePureWithoutCredit(insuranceRisk.getRetentionScorePureWithoutCredit());
    insuranceRiskClone.setRetentionScorePureWithCredit(insuranceRisk.getRetentionScorePureWithCredit());
    insuranceRiskClone.setCompetitivityScoreWithoutCredit(insuranceRisk.getCompetitivityScoreWithoutCredit());
    insuranceRiskClone.setCompetitivityScoreWithCredit(insuranceRisk.getCompetitivityScoreWithCredit());

    // Livrables 5 et 6
    insuranceRiskClone.setFlexPercentage(insuranceRisk.getFlexPercentage());
    insuranceRiskClone.setCappingPercentage(insuranceRisk.getCappingPercentage());
    insuranceRiskClone.setScoringAdjustmentPercentage(insuranceRisk.getScoringAdjustmentPercentage());
    insuranceRiskClone.setCompetitivityAdjustmentPercentage(insuranceRisk.getCompetitivityAdjustmentPercentage());

    return insuranceRiskClone;
  }

  private static void comparePolicyVersionOffers(
      com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersion1,
      com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersion2,
      boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations, StringBuilder printMsg) {
    Iterator<InsuranceRisk> insuranceRisk2Iterator = policyVersion2.getTheInsuranceRisk().iterator();
    for (InsuranceRisk insuranceRisk1 : policyVersion1.getTheInsuranceRisk()) {

      InsuranceRisk insuranceRisk2 = insuranceRisk2Iterator.next();

      printMsg.append("\n");
      printMsg.append("\n%%% InsuranceRiskSequence:\t").append(insuranceRisk1.getInsuranceRiskSequence());
      if (insuranceRisk1.getInsuranceRiskSequence() != insuranceRisk2.getInsuranceRiskSequence()) {
        printMsg.append("\n%%% insuranceRisk1.getInsuranceRiskSequence()=").append(
            insuranceRisk1.getInsuranceRiskSequence());
        printMsg.append(" != insuranceRisk2.getInsuranceRiskSequence()=")
            .append(insuranceRisk2.getInsuranceRiskSequence()).append("!!!!!");
      }

      printMsg.append("\n%%% OfferType:\t").append(insuranceRisk1.getOfferType());
      if (!insuranceRisk1.getOfferType().equals(insuranceRisk2.getOfferType())) {
        printMsg.append("\n%%% insuranceRisk1.getOfferType()=").append(insuranceRisk1.getOfferType());
        printMsg.append(" != insuranceRisk2.getOfferType()=").append(insuranceRisk2.getOfferType())
            .append("!!!!!");
      }

      // Livrable 4
      if (getRegularAndPreferredRspScoring) {
        testEquality("CustomerValueIndexPureWithoutCredit",
            insuranceRisk1.getCustomerValueIndexPureWithoutCredit(),
            insuranceRisk2.getCustomerValueIndexPureWithoutCredit(), printMsg);
        testEquality("CustomerValueIndexPureWithCredit", insuranceRisk1.getCustomerValueIndexPureWithCredit(),
            insuranceRisk2.getCustomerValueIndexPureWithCredit(), printMsg);
        testEquality("RetentionScorePureWithoutCredit", insuranceRisk1.getRetentionScorePureWithoutCredit(),
            insuranceRisk2.getRetentionScorePureWithoutCredit(), printMsg);
        testEquality("RetentionScorePureWithCredit", insuranceRisk1.getRetentionScorePureWithCredit(),
            insuranceRisk2.getRetentionScorePureWithCredit(), printMsg);
        testEquality("CompetitivityScoreWithoutCredit", insuranceRisk1.getCompetitivityScoreWithoutCredit(),
            insuranceRisk2.getCompetitivityScoreWithoutCredit(), printMsg);
        testEquality("CompetitivityScoreWithCredit", insuranceRisk1.getCompetitivityScoreWithCredit(),
            insuranceRisk2.getCompetitivityScoreWithCredit(), printMsg);
      }

      // Livrables 5 et 6
      if (getRatingDeviations) {
        testEquality("FlexPercentage", insuranceRisk1.getFlexPercentage(), insuranceRisk2.getFlexPercentage(),
            printMsg);
        testEquality("CappingPercentage", insuranceRisk1.getCappingPercentage(),
            insuranceRisk2.getCappingPercentage(), printMsg);
        testEquality("ScoringAdjustmentPercentage", insuranceRisk1.getScoringAdjustmentPercentage(),
            insuranceRisk2.getScoringAdjustmentPercentage(), printMsg);
        testEquality("CompetitivityAdjustmentPercentage",
            insuranceRisk1.getCompetitivityAdjustmentPercentage(),
            insuranceRisk2.getCompetitivityAdjustmentPercentage(), printMsg);
      }
    }
  }

  private static void testEquality(String fieldName, Integer i1, Integer i2, StringBuilder printMsg) {
    printMsg.append("\n");
    printMsg.append("\n%%% GET_REGULAR_AND_PREFERRED_RSP_SCORING, ").append(fieldName).append(" = ").append(i1);
    printMsg.append("\n%%% RATE_PL_POLICY,                        ").append(fieldName).append(" = ").append(i2);
    if (isZeroOrNull(i1) && isZeroOrNull(i2)) {
      printMsg.append("\n%%% Are equivalent");
    } else if (isZeroOrNull(i1) && !isZeroOrNull(i2)) {
      printMsg.append("\n%%% Are not equivalent !!!!!");
    } else if (!isZeroOrNull(i1) && isZeroOrNull(i2)) {
      printMsg.append("\n%%% Are not equivalent !!!!!");
    } else if (i1.equals(i2)) {
      printMsg.append("\n%%% Are equivalent");
    } else {
      printMsg.append("\n%%% Are not equivalent !!!!!");
    }
  }

  private static boolean isZeroOrNull(Integer i1) {
    return ((i1 == null) || i1.equals(0));
  }

  protected abstract IPremiumDerivationService getPremiumDeviationService();

  /**
   * Updates the epiry date and the policyTerm. This can only be done once we know the insured group used so we can
   * claculate the payment plans
   *
   * @param aPlPolicyVersion the a pl policy version
   * @param somPolicyVersion the som policy version
   */
  protected void setPolicyTermAndExpiry(PolicyVersion aPlPolicyVersion,
                                        com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {

    // call to determine if plan is 1 year or 2 years
    // we set the policyversion term following the credit score call
    PolicyTermInMonthsEnum aPolicyTerm = this.quotationService.calculatePolicyTermInMonths(aPlPolicyVersion);
    aPlPolicyVersion.setPolicyTermInMonths(aPolicyTerm);
    somPolicyVersion.setPolicyTermInMonths(aPolicyTerm.getCode());

    // Set the policy version expiry date to be the inception date + the
    // duration in months of the policy term
    Date inceptionDate = aPlPolicyVersion.getPolicyInceptionDate();
    Calendar aCal = Calendar.getInstance();
    aCal.setTime(inceptionDate);
    aCal.add(Calendar.MONTH, aPolicyTerm.getCode());
    aPlPolicyVersion.setPolicyExpiryDate(aCal.getTime());
    somPolicyVersion.setPolicyExpiryDate((GregorianCalendar) aCal);
  }

  /**
   * @param aPlPolicyVersion
   * @param somPolicyVersion
   */
  protected void setInsuredGroup(PolicyVersion aPlPolicyVersion,
                                 com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {
    String insuredGroup = null;

    for (InsuranceRisk insuranceRisk : somPolicyVersion.getTheInsuranceRisk()) {
      insuredGroup = insuranceRisk.getTheRatingRiskPrincipal().getTheGroupRepositoryEntry().getInsuredGroup();
      break;
    }

    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {
      for (RatingRiskOffer ratingRisk : ir.getSelectedInsuranceRiskOffer().getRatingRiskOffers()) {
        if (ratingRisk.getRatingRiskType().equals(RatingRiskTypeCodeEnum.PRINCIPAL_RISK)) {
          ratingRisk.setInsuredGroupRated(insuredGroup);
        }
      }
    }
  }

  /**
   * Select offer type.
   *
   * @param performanceWatch  the performance watch
   * @param aCtxt             the a ctxt
   * @param aSomPolicyVersion the a som policy version
   * @return the com.ing.canada.som.interfaces.agreement. policy version
   * @throws BaseException the base exception
   */
  protected com.ing.canada.som.interfaces.agreement.PolicyVersion selectOfferType(StopWatch performanceWatch,
                                                                                  ManufacturingContext aCtxt, com.ing.canada.som.interfaces.agreement.PolicyVersion aSomPolicyVersion)
      throws BaseException {

    // PEGA - Select the offer to use by default
    performanceWatch.start(" >> selectOfferType");
    com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = this.selectOfferType.selectOfferType(
        aCtxt, aSomPolicyVersion);
    performanceWatch.stop();

    List<com.ing.canada.som.interfaces.agreement.PolicyVersion> policyVersionOffersList = new ArrayList<>();
    policyVersionOffersList.addAll(somPolicyVersion.getThePolicyVersionOffer());
    policyVersionOffersList.add(somPolicyVersion);

    for (com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOffer : policyVersionOffersList) {
      List<InsuranceRisk> somRisks = policyVersionOffer.getTheInsuranceRisk();
      if (somRisks != null) {
        for (InsuranceRisk somRisk : somRisks) {
          if ("N".equals(somRisk.getRiskSelectedInd())) {
            somRisk.setRiskSelectedInd(null);
          }
        }
      }
    }

    return somPolicyVersion;
  }

  /**
   * This method select in the SOM policy graph, all the insurance risk that match the offer type given in parameter.
   * This method is used to select the right offer risk for all the sub-sequent rating services.
   *
   * @param aSomPolicyVersion SOM Policy Version
   * @param aOfferType        Offer type to be selected
   */
  protected void selectOfferForRating(com.ing.canada.som.interfaces.agreement.PolicyVersion aSomPolicyVersion,
                                      OfferTypeCodeEnum aOfferType) {

    List<com.ing.canada.som.interfaces.agreement.PolicyVersion> policyVersionOffersList = new ArrayList<com.ing.canada.som.interfaces.agreement.PolicyVersion>();
    policyVersionOffersList.addAll(aSomPolicyVersion.getThePolicyVersionOffer());
    policyVersionOffersList.add(aSomPolicyVersion);

    for (com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOffer : policyVersionOffersList) {
      List<InsuranceRisk> somRisks = policyVersionOffer.getTheInsuranceRisk();
      if (somRisks != null) {
        for (InsuranceRisk somRisk : somRisks) {
          if (aOfferType.getCode().equals(somRisk.getOfferType())) {
            somRisk.setRiskSelectedInd("Y");
          } else {
            somRisk.setRiskSelectedInd(null);
          }
        }
      }
    }

  }

  /**
   * Preprocess needed to PL before calling the rating sequence. These operations are needed by the DM2SOM or by the
   * SSServices.
   *
   * @param aPlPolicyVersion the pl policy version
   */
  protected void preprocessPL(PolicyVersion aPlPolicyVersion) {

    com.ing.canada.plp.domain.policyversion.PolicyHolder policyHolder = this.policyVersionHelper
        .getPrincipalInsuredPolicyHolder(aPlPolicyVersion);
    Party plpParty = this.partyHelper.getNamedInsured(aPlPolicyVersion);
    com.ing.canada.plp.domain.party.Address currentAddress = null;
    if (plpParty != null) {
      currentAddress = this.partyHelper
          .getCurrentResidentialAddress(plpParty);
    }
    PolicyOfferRating por = new PolicyOfferRating(aPlPolicyVersion);

    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {

      ir.setRatingDate(aPlPolicyVersion.getRatingDate());
      // Set effective dates based on the inception date of the policy
      ir.setEffectiveDate(aPlPolicyVersion.getBusinessTransaction().getTransactionEffectiveDateTime());

      // Create the RatingRisks depending on the drivers assignation
      for (PartyRoleInRisk prir : ir.getPartyRoleInRisks()) {
        if (DriverTypeCodeEnum.PRINCIPAL.equals(prir.getDriverType())) {
          new com.ing.canada.plp.domain.insurancerisk.RatingRisk(ir, RatingRiskTypeCodeEnum.PRINCIPAL_RISK);
        } else if (DriverTypeCodeEnum.OCCASIONAL.equals(prir.getDriverType())) {
          new com.ing.canada.plp.domain.insurancerisk.RatingRisk(ir, RatingRiskTypeCodeEnum.OCCASIONAL_RISK);
        }
      }

      // Create the Premium InsuranceRiskOffer and RatingRiskOffers
      InsuranceRiskOffer iro = this.createRiskOffer(por, ir, OfferTypeCodeEnum.CUSTOM, Boolean.FALSE);

      // Set a default InsuranceRiskOffer for DM2SOM
      // PEGA will set the SelectedInsuranceRiskOffer later in the rating
      // sequence
      ir.setSelectedInsuranceRiskOffer(iro, true);

      // Create the association to the policyHolder
      if (currentAddress != null) {
        if (ir.getAddress() == null) {
          MunicipalityDetailSpecification spec = currentAddress.getMunicipalityDetailSpecification();
          ir.setAddress(currentAddress);
          if (spec != null && !StringUtils.isBlank(spec.getAutomobileTerritoryStat()) && !StringUtils.isBlank(spec.getMunicipalAdjustmentAuto())) {
            String territoryStat = spec.getAutomobileTerritoryStat();
            ir.setTerritoryStat(territoryStat);
            ir.setMunicipalAdjustment(spec.getMunicipalAdjustmentAuto());
          }
        }
      }
    }

    // Synchronize values for the services need
    // Not done in the driver section because we can skip the driver page
    // when we add a vehicule
    Boolean licenceRevokedInd = aPlPolicyVersion.getLicenseSuspendedOrRevokedIndicator();
    Short numberStabilityMonths = null;
    if (policyHolder.getNumberStabilityMonths() != null) {
      numberStabilityMonths = policyHolder.getNumberStabilityMonths().shortValue();
    }
    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {
      ir.setLicenseSuspensionPrincipalIndicator(licenceRevokedInd);
      ir.setNumberOfStabilityMonths(numberStabilityMonths);
    }

    this.policyVersionService.persistCascadeAll(aPlPolicyVersion);
  }

  /**
   * Preprocess needed to PL before calling the rating sequence. Use only
   * for Alberta and Ontario.
   *
   * @param aPlPolicyVersion the pl policy version
   */
  protected void preprocessPLForAlbertaAndOntario(PolicyVersion aPlPolicyVersion) {

    Party plpParty = this.partyHelper.getNamedInsured(aPlPolicyVersion);
    com.ing.canada.plp.domain.party.Address currentAddress = this.partyHelper
        .getCurrentResidentialAddress(plpParty);

    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {

      ir.setRatingDate(aPlPolicyVersion.getRatingDate());
      // Set effective dates based on the inception date of the policy
      ir.setEffectiveDate(aPlPolicyVersion.getBusinessTransaction().getTransactionEffectiveDateTime());

      // Create the association to the policyHolder
      if (ir.getAddress() == null && currentAddress != null) {
        MunicipalityDetailSpecification spec = currentAddress.getMunicipalityDetailSpecification();
        ir.setAddress(currentAddress);
        if (spec != null && !StringUtils.isBlank(spec.getAutomobileTerritoryStat()) && !StringUtils.isBlank(spec.getMunicipalAdjustmentAuto())) {
          String territoryStat = spec.getAutomobileTerritoryStat();
          ir.setTerritoryStat(territoryStat);
          ir.setMunicipalAdjustment(spec.getMunicipalAdjustmentAuto());
        }
      }
    }

    this.policyVersionService.persistCascadeAll(aPlPolicyVersion);
  }

  /**
   * Creates all the branch InsuranceRisk of SOM in PL.
   *
   * @param plPolicyVersion  the PL policy version
   * @param somPolicyVersion the SOM policy version
   */
  protected void createRiskOffers(PolicyVersion plPolicyVersion,
                                  com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {

    PolicyOfferRating plPolicyOfferRating = plPolicyVersion.getLatestPolicyOfferRating();

    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {

      // Loop on the policy version offers
      for (com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersionOffer : somPolicyVersion.getThePolicyVersionOffer()) {

        // Determine the offer type of the current policy version offer
        String offerType = null;
        for (InsuranceRisk somInsuranceRisk : somPolicyVersionOffer.getTheInsuranceRisk()) {
          offerType = somInsuranceRisk.getOfferType();
          break;
        }

        // Create the InsuranceRiskOffer and their RatingRiskOffer in PL
        // for the current offer type
        this.createRiskOffer(plPolicyOfferRating, plInsuranceRisk, OfferTypeCodeEnum.valueOfCode(offerType), Boolean.TRUE);
      }
    }

    this.policyVersionService.persistCascadeAll(plPolicyVersion);
  }

  /**
   * Creates the risk offer.
   *
   * @param por                         the por
   * @param ir                          the ir
   * @param offerType                   the offer type
   * @param aRecalculableOfferIndicator the a recalculable offer indicator
   * @return the insurance risk offer
   */
  protected InsuranceRiskOffer createRiskOffer(PolicyOfferRating por,
                                               com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir, OfferTypeCodeEnum offerType,
                                               Boolean aRecalculableOfferIndicator) {

    InsuranceRiskOffer iro = new InsuranceRiskOffer(ir, offerType);
    iro.setRecalculatableOfferIndicator(aRecalculableOfferIndicator);
    por.addInsuranceRiskOffer(iro);

    for (PartyRoleInRisk prir : ir.getPartyRoleInRisks()) {
      if (DriverTypeCodeEnum.PRINCIPAL.equals(prir.getDriverType())) {
        RatingRiskOffer ratingRiskOffer = new RatingRiskOffer();
        ratingRiskOffer.setInsuranceRiskOffer(iro);
        ratingRiskOffer.setRatingRiskType(RatingRiskTypeCodeEnum.PRINCIPAL_RISK);

      } else if (DriverTypeCodeEnum.OCCASIONAL.equals(prir.getDriverType())) {
        new RatingRiskOffer(iro, RatingRiskTypeCodeEnum.OCCASIONAL_RISK);
      }
    }
    return iro;
  }

  /**
   * Get the insured group inserted on the policyversion.
   *
   * @param policyVersion the policy version
   * @return the insured group
   */
  protected String getInsuredGroup(PolicyVersion policyVersion) {
    if (policyVersion.getAffinityGroupRepositoryEntry() != null) {

      GroupRepositoryEntry groupRepositoryEntry = this.policyVersionService
          .getGroupRepositoryEntry(policyVersion);
      if (groupRepositoryEntry != null) {
        return groupRepositoryEntry.getInsuredGroup();
      }
      return this.getInsuredGroupBeforeOffer(policyVersion);
    }

    return null;
  }

  /**
   * Get the insured group inserted on the policyversion.
   *
   * @param policyVersion the policy version
   * @return the insured group
   */
  protected String getInsuredGroupBeforeOffer(PolicyVersion policyVersion) {
    if (policyVersion.getAffinityGroupRepositoryEntry() != null) {

      GroupRepositoryEntry groupRepositoryEntry = this.policyVersionService
          .getGroupRepositoryEntryBeforeOffer(policyVersion);
      if (groupRepositoryEntry != null) {
        return groupRepositoryEntry.getInsuredGroup();
      }
    }

    return null;
  }

  /**
   * Postprocess needed to PL after the rating sequence. These operations couldn't be done by the DM2PL.
   *
   * @param aPlPolicyVersion the pl policy version
   */
  protected void postprocessPL(PolicyVersion aPlPolicyVersion) {

    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk ir : aPlPolicyVersion.getInsuranceRisks()) {
      ir.setInsuranceRiskOfferSystemSelectedIndicator(true);
    }

    Short seq = aPlPolicyVersion.getBusinessTransaction().getLastRatingSequence();
    aPlPolicyVersion.getBusinessTransaction().setLastRatingSequence(++seq);

    this.policyVersionService.persistCascadeAll(aPlPolicyVersion);
  }

  /**
   * Preprocess som.
   *
   * @param somPolicyVersion the som policy version
   * @param aPolicyVersion   the a policy version
   */
  protected void preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
                               PolicyVersion aPolicyVersion) {

    GregorianCalendar inceptionDate = somPolicyVersion.getPolicyInceptionDate();

    // Not in Autoquote, use the policy inceptionDate - info from CEthier
    somPolicyVersion.getTheInsurancePolicy().setOriginalInceptionDate(inceptionDate);
    // Not in Autoquote, use the policy inceptionDate - info from CEthier
    somPolicyVersion.setClientOfBrokerSinceAuto(inceptionDate);

    // Set the origine
    somPolicyVersion.getTheInsurancePolicy().setExternalSystemOrigin("E");

    // .
    PriorCarrierPolicyInfo prior = somPolicyVersion.getThePriorCarrierPolicyInfo();
    if (prior != null && prior.getNumberOfYearsContinuouslyInsuredWithPriorCarrier() == null) {
      prior.setNumberOfYearsContinuouslyInsuredWithPriorCarrier(0);
    }

    for (InsuranceRisk ir : somPolicyVersion.getTheInsuranceRisk()) {
      for (com.ing.canada.som.interfaces.partyRoleInRisk.Driver d : ir.getTheDriver()) {
        if ("P".equals(d.getTypeOfDriver())) {
          ir.setNumberOfMinorConvictionsPrincipalDriver3Years(d.getTheParty().getTheDriverComplementInfo()
              .getNumberOfMinorConvictions3Years());
        } else if ("O".equals(d.getTypeOfDriver())) {
          ir.setNumberOfMinorConvictionsOccasionalDriver3Years(d.getTheParty().getTheDriverComplementInfo()
              .getNumberOfMinorConvictions3Years());
        }
      }
    }

    // Set all the counters the services need
    Set<String> princDrivers = new HashSet<String>();
    int nbOfOccMaleDriverUnder25YearsOldOnPpv = 0;
    for (InsuranceRisk ir : somPolicyVersion.getTheInsuranceRisk()) {
      for (com.ing.canada.som.interfaces.partyRoleInRisk.Driver d : ir.getTheDriver()) {
        if ("P".equals(d.getTypeOfDriver())) {
          princDrivers.add(d.getTheParty().getPersistenceUniqueId());
        } else if ("O".equals(d.getTypeOfDriver()) && d.getAge() < 25 && "M".equals(d.getTheParty().getSex())) {
          nbOfOccMaleDriverUnder25YearsOldOnPpv++;
        }
      }
    }
    somPolicyVersion.setNumberOfPrincipalDriverOnPpv(princDrivers.size());
    somPolicyVersion.setNumberOfOccasionalMaleDriverUnder25YearsOldOnPpv(nbOfOccMaleDriverUnder25YearsOldOnPpv);

    Date refDate = somPolicyVersion.getTheReferenceDate().getClaimReferenceDate().getTime();
    for (InsuranceRisk ir : somPolicyVersion.getTheInsuranceRisk()) {
      this.calculateCounters(ir, refDate);
      for (com.ing.canada.som.interfaces.partyRoleInRisk.Driver driver : ir.getTheDriver()) {
        this.calculateCounters(driver, refDate);
      }
    }

    // Hard coded values from AQ4-Charlie
    somPolicyVersion.setMvrsaaqAuthorizationInd("Y");
    somPolicyVersion.setSignedApplicationInd("Y");
    if (somPolicyVersion.getTheProducer() != null) {
      somPolicyVersion.getTheProducer().setCreditScoreEligibilityInd("Y");
    }

    int months = somPolicyVersion.getPolicyTermInMonths();
    GregorianCalendar endDate = (GregorianCalendar) inceptionDate.clone();
    endDate.add(Calendar.MONTH, months);
    final int MILLSECS_PER_DAY = 24 * 60 * 60 * 1000;
    long deltaDays = (endDate.getTimeInMillis() - inceptionDate.getTimeInMillis()) / MILLSECS_PER_DAY;
    somPolicyVersion.setPolicyTermInDays(Math.round(deltaDays));

    Vehicle vehicle;
    for (InsuranceRisk ir : somPolicyVersion.getTheInsuranceRisk()) {
      ir.setNumberOfAdditionalInterests(0);

      vehicle = ir.getTheVehicle();
      vehicle.setRatingTableIdentification("P");
      vehicle.setActionTaken("A");
      vehicle.setPurchaseOdometerReading(vehicle.getOdometerReading());
    }

    this.preprocessSOMParties(somPolicyVersion);

    if (CombinedPolicyCodeEnum.MONOLINE_POLICY.equals(aPolicyVersion.getCombinedPolicyCode())
        && (aPolicyVersion.getCombinedPolicyScenarioCode() != null && aPolicyVersion
        .getCombinedPolicyScenarioCode().equals(CombinedPolicyScenarioCodeEnum.COMBO_POLICY))) {
      somPolicyVersion.setCombinedPolicyCode(aPolicyVersion.getCombinedPolicyScenarioCode().getCode());
    }

    this.setPolicyTermAndExpiry(aPolicyVersion, somPolicyVersion);

  }

  /**
   * Pre-process all the parties which are present on the quote.
   *
   * @param somPolicyVersion SOM Policy version
   */
  protected void preprocessSOMParties(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {
    GregorianCalendar convictionDate = somPolicyVersion.getTheReferenceDate().getClaimReferenceDate();
    convictionDate.add(Calendar.YEAR, -1);
    for (com.ing.canada.som.interfaces.party.Party p : somPolicyVersion.getTheParty()) {

      // Modify only parties of type "IN" (individual): not all of the
      // parties of SOM's policy version are
      // associated to a DriverComplementInfo.
      if (PartyTypeCodeEnum.INDIVIDUAL.getCode().equals(p.getPartyType())) {

        // . Needed by the Get_Rating_Company service
        p.setIngStaffInd("N");

        com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo dci = p.getTheDriverComplementInfo();
        if (dci != null) {
          dci.setNormalLicenseProgressionInd("Y");

          dci.setNumberOfNonPaymentCancellationsIn3Years(0);

          if (dci.getNumberOfMinorConvictions3Years() != null) {
            for (int i = 0; i < dci.getNumberOfMinorConvictions3Years(); i++) {
              Conviction conviction = dci.addTheConviction();
              conviction.setConvictionSequence(i + 1);
              conviction.setConvictionCode("MINOR");
              conviction.setConvictionDate(convictionDate);
              conviction.setConvictionType("MIN");
              conviction.setConvictionTypeRsp("MIN");
              conviction.setConvictionChargeabilityInd("Y");
            }

            int seq = dci.getNumberOfMinorConvictions3Years() + 1;
            for (int i = 0; i < dci.getNumberOfMajorConvictions3Years(); i++) {
              Conviction conviction = dci.addTheConviction();
              conviction.setConvictionSequence(i + seq);
              conviction.setConvictionCode("MAJOR");
              conviction.setConvictionDate(convictionDate);
              conviction.setConvictionType("MAJ");
              conviction.setConvictionTypeRsp("MAJ");
              conviction.setConvictionChargeabilityInd("Y");
            }
          }
        }
      }
    }
  }

  /**
   * Calculate counters.
   *
   * @param insuranceRisk the insurance risk
   * @param refDate       the ref date
   */
  protected void calculateCounters(InsuranceRisk insuranceRisk, Date refDate) {

    int numberOfClaims3Years = 0;
    int numberOfClaims5Years = 0;
    int numberOfClaims6Years = 0;
    int numberOfClaims10Years = 0;
    int numberOfClaimsExcludingGlassBreakage3Years = 0;
    int numberOfClaimsLiablePrincipalAndNonRatedDriver6Years = 0;
    int numberOfClaimsLiableOccasionalDriver6Years = 0;
    int numberOfClaimsLiable1Year = 0;
    int numberOfClaimsLiable2Years = 0;
    int numberOfClaimsLiable3Years = 0;
    int numberOfClaimsLiable4Years = 0;
    int numberOfClaimsLiable5Years = 0;
    int numberOfClaimsLiable6Years = 0;
    int numberOfClaimsLiable10Years = 0;

    for (Claim claim : insuranceRisk.getTheClaim()) {
      if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 1)) {
        numberOfClaimsLiable1Year++;
      } else if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 2)) {
        numberOfClaimsLiable2Years++;
      } else if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 3)) {
        numberOfClaims3Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          numberOfClaimsLiable3Years++;
        }
      } else if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 4)) {
        numberOfClaimsLiable4Years++;
      } else if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 5)) {
        numberOfClaims5Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          numberOfClaimsLiable5Years++;
        }
      } else if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 6)) {
        numberOfClaims6Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          numberOfClaimsLiable6Years++;
        }
      } else if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 10)) {
        numberOfClaims10Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          numberOfClaimsLiable10Years++;
        }
      }

      if (!"WS".equals(claim.getClaimType()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 3)) {
        numberOfClaimsExcludingGlassBreakage3Years++;
      }

      if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 6)) {
        for (com.ing.canada.som.interfaces.partyRoleInRisk.Driver driver : insuranceRisk.getTheDriver()) {
          if (driver.getTheParty().equals(claim.getTheParty())) {
            if ("O".equals(driver.getTypeOfDriver())) {
              numberOfClaimsLiableOccasionalDriver6Years++;
            } else if ("P".equals(driver.getTypeOfDriver()) || "N".equals(driver.getTypeOfDriver())) {
              numberOfClaimsLiablePrincipalAndNonRatedDriver6Years++;
            }
            break;
          }
        }
      }
    }

    insuranceRisk.setNumberOfClaims3Years(numberOfClaims3Years);
    numberOfClaims5Years += numberOfClaims3Years;
    insuranceRisk.setNumberOfClaims5Years(numberOfClaims5Years);
    numberOfClaims6Years += numberOfClaims5Years;
    insuranceRisk.setNumberOfClaims6Years(numberOfClaims6Years);
    numberOfClaims10Years += numberOfClaims6Years;
    insuranceRisk.setNumberOfClaims10Years(numberOfClaims10Years);
    insuranceRisk.setNumberOfClaimsLiable1Year(numberOfClaimsLiable1Year);
    numberOfClaimsLiable2Years += numberOfClaimsLiable1Year;
    insuranceRisk.setNumberOfClaimsLiable2Years(numberOfClaimsLiable2Years);
    numberOfClaimsLiable3Years += numberOfClaimsLiable2Years;
    insuranceRisk.setNumberOfClaimsLiable3Years(numberOfClaimsLiable3Years);
    numberOfClaimsLiable4Years += numberOfClaimsLiable3Years;
    insuranceRisk.setNumberOfClaimsLiable4Years(numberOfClaimsLiable4Years);
    numberOfClaimsLiable5Years += numberOfClaimsLiable4Years;
    insuranceRisk.setNumberOfClaimsLiable5Years(numberOfClaimsLiable5Years);
    numberOfClaimsLiable6Years += numberOfClaimsLiable5Years;
    insuranceRisk.setNumberOfClaimsLiable6Years(numberOfClaimsLiable6Years);
    numberOfClaimsLiable10Years += numberOfClaimsLiable6Years;
    insuranceRisk.setNumberOfClaimsLiable10Years(numberOfClaimsLiable10Years);
    insuranceRisk.setNumberOfClaimsExcludingGlassBreakage3Years(numberOfClaimsExcludingGlassBreakage3Years);
    insuranceRisk
        .setNumberOfClaimsLiablePrincipalAndNonRatedDriver6Years(numberOfClaimsLiablePrincipalAndNonRatedDriver6Years);
    insuranceRisk.setNumberOfClaimsLiableOccasionalDriver6Years(numberOfClaimsLiableOccasionalDriver6Years);
  }

  /**
   * Calculate counters.
   *
   * @param driver  the driver
   * @param refDate the ref date
   */
  protected void calculateCounters(com.ing.canada.som.interfaces.partyRoleInRisk.Driver driver, Date refDate) {

    int nbOfClaims3Years = 0;
    int nbOfClaims5Years = 0;
    int nbOfLiableClaims3Years = 0;
    int nbOfLiableClaims5Years = 0;
    int nbOfClaimsAtFault6Years = 0;
    int nbOfLiableClaims10Years = 0;

    for (Claim claim : driver.getTheParty().getTheClaim()) {
      if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 3)) {
        nbOfClaims3Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          nbOfLiableClaims3Years++;
        }
      } else if (this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 5)) {
        nbOfClaims5Years++;
        if ("Y".equals(claim.getClaimAtFaultInd())) {
          nbOfLiableClaims5Years++;
        }
      } else if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 6)) {
        nbOfClaimsAtFault6Years++;
      } else if ("Y".equals(claim.getClaimAtFaultInd()) && this.isInTheLastNbYears(claim.getDateOfLoss(), refDate, 10)) {
        nbOfLiableClaims10Years++;
      }
    }

    com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo dci = driver.getTheParty()
        .getTheDriverComplementInfo();
    dci.setNumberOfClaims3Years(nbOfClaims3Years);
    nbOfClaims5Years += nbOfClaims3Years;
    dci.setNumberOfClaims5Years(nbOfClaims5Years);
    dci.setNumberOfLiableClaims3Years(nbOfLiableClaims3Years);
    nbOfLiableClaims5Years += nbOfLiableClaims3Years;
    dci.setNumberOfLiableClaims5Years(nbOfLiableClaims5Years);
    nbOfClaimsAtFault6Years += nbOfLiableClaims5Years;
    dci.setNumberOfClaimsAtFault6Years(nbOfClaimsAtFault6Years);
    nbOfLiableClaims10Years += nbOfClaimsAtFault6Years;
    dci.setNumberOfLiableClaims10Years(nbOfLiableClaims10Years);
  }

  /**
   * Checks if the date is in the last nb years.
   *
   * @param nbYears    the nb years
   * @param dateOfLoss the date of loss
   * @param refDate    the reference date
   * @return true, if is in the last nb years
   */
  protected boolean isInTheLastNbYears(GregorianCalendar dateOfLoss, Date refDate, int nbYears) {

    GregorianCalendar nbYearsCal = new GregorianCalendar();
    nbYearsCal.setTime(refDate);
    nbYearsCal.roll(Calendar.YEAR, -nbYears);

    return dateOfLoss.after(nbYearsCal);
  }

  /**
   * Postprocess som.
   *
   * @param somPolicyVersion        the som policy version
   * @param originalInceptionDate   the original inception date
   * @param clientOfBrokerSinceAuto the client of broker since auto
   * @param combinedPolicyCode      the combined policy code
   */
  protected void postprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
                                GregorianCalendar originalInceptionDate, GregorianCalendar clientOfBrokerSinceAuto,
                                String combinedPolicyCode) {

    somPolicyVersion.getTheInsurancePolicy().setOriginalInceptionDate(originalInceptionDate);
    somPolicyVersion.setClientOfBrokerSinceAuto(clientOfBrokerSinceAuto);
    somPolicyVersion.setCombinedPolicyCode(combinedPolicyCode);
  }

  /**
   * Postprocess som.
   *
   * @param somPolicyVersion        the som policy version
   * @param originalInceptionDate   the original inception date
   * @param clientOfBrokerSinceAuto the client of broker since auto
   * @param coverageIds             the coverage ids
   * @param combinedPolicyCode      the combined policy code
   */
  protected void postprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
                                GregorianCalendar originalInceptionDate, GregorianCalendar clientOfBrokerSinceAuto,
                                List<String> coverageIds, String combinedPolicyCode) {

    this.postprocessSOM(somPolicyVersion, originalInceptionDate, clientOfBrokerSinceAuto, combinedPolicyCode);

    // Reset the not covered basic coverage selected indicator
    boolean found;
    for (String id : coverageIds) {
      found = false;
      for (InsuranceRisk ir : somPolicyVersion.getTheInsuranceRisk()) {
        for (Coverage co : ir.getTheCoverage()) {
          if (id.equals(co.getPersistenceUniqueId())) {
            co.setCoverageSelectedInd("Y");
            found = true;
            break;
          }
        }
        if (found) {
          break;
        }
      }
    }
  }

  /**
   * Returns a set of BasicCoverageCodeEnum as a list of codes.
   *
   * @param basicCoverageCodes the basic coverage codes
   * @return the list< string>
   */
  protected List<String> basicCoverageCodesAsList(Set<BasicCoverageCodeEnum> basicCoverageCodes) {

    List<String> codes = new ArrayList<String>();
    for (BasicCoverageCodeEnum bcc : basicCoverageCodes) {
      codes.add(bcc.getCode());
    }
    return codes;
  }

  /**
   * Creates the CoveragePremiums for all the Endorsments of aInsuranceRisk.
   *
   * @param aInsuranceRisk     the a insurance risk
   * @param basicCoverageCodes the basic coverage codes
   */
  protected void createEndorsmentCoveragePremiums(InsuranceRisk aInsuranceRisk,
                                                  Set<BasicCoverageCodeEnum> basicCoverageCodes) {

    for (Coverage aCoverage : aInsuranceRisk.getTheCoverage()) {

      if ("EN".equals(aCoverage.getCoverageType()) && "Y".equals(aCoverage.getCoverageEligibleInd())) {

        CoveragePremium cp = null;
        if ("P".equals(aCoverage.getRatingRiskTypeApply())) {
          cp = aCoverage.getTheCoveragePremiumPrincipal();
          if (cp == null) {
            cp = aCoverage.createTheCoveragePremiumPrincipal();
          }
        } else if ("O".equals(aCoverage.getRatingRiskTypeApply())) {
          cp = aCoverage.getTheCoveragePremiumOccasional();
          if (cp == null) {
            cp = aCoverage.createTheCoveragePremiumOccasional();
          }
        }

        if (cp != null) {
          for (BasicCoverageCodeEnum bcCode : basicCoverageCodes) {
            SubCoveragePremium scp = cp.addTheSubCoveragePremium();
            scp.setBasicCoverageCode(bcCode.getCode());
          }
        }
      }
    }
  }

  /**
   * Creates the CoveragePremiums for all the Coverages of aInsuranceRisk.
   *
   * @param aInsuranceRisk        the a insurance risk
   * @param basicCoverageCodes    the basic coverage codes
   * @param basicCoverageOccCodes the basic coverage occ codes
   */
  protected void createCoveragePremiums(InsuranceRisk aInsuranceRisk, Boolean hasOccasionnal,
                                        Set<BasicCoverageCodeEnum> basicCoverageCodes, Set<BasicCoverageCodeEnum> basicCoverageOccCodes) {

    for (Coverage aCoverage : aInsuranceRisk.getTheCoverage()) {

      CoveragePremium cp = null;
      // For basic coverages, the same coverage will have a
      // CoveragePremiumPrincipal and a
      // CoveragePremiumOccasional.
      if ("P".equals(aCoverage.getRatingRiskTypeApply()) || "B".equals(aCoverage.getRatingRiskTypeApply())) {
        cp = aCoverage.createTheCoveragePremiumPrincipal();
        if ("EN".equals(aCoverage.getCoverageType())) {
          createSubCoveragePremium(basicCoverageCodes, cp);
        }
      }

      if (hasOccasionnal) {
        if ("O".equals(aCoverage.getRatingRiskTypeApply()) || "B".equals(aCoverage.getRatingRiskTypeApply())) {
          cp = aCoverage.createTheCoveragePremiumOccasional();
          if ("EN".equals(aCoverage.getCoverageType())) {
            createSubCoveragePremium(basicCoverageOccCodes, cp);
          }
        }
      }

      // Must be set to yes for the PEGA service
      // selectCoveragesOnMultipleOffers
      aCoverage.setCoverageEligibleInd("Y");
    }
  }

  /**
   * Creates the CoveragePremiums for a coverage Retrieves the rating risk type apply from PLP
   *
   * @param coverage                the coverage
   * @param hasOccasionnal          indicates if there is an occasional rating risk
   * @param basicCoverageCodes      the basic coverage codes
   * @param basicCoverageOccCodes   the basic coverage occasional codes
   * @param coverageRepositoryEntry the coverage repository entry
   */
  protected void createCoveragePremiums(Coverage coverage, boolean hasOccasionnal,
                                        Set<BasicCoverageCodeEnum> basicCoverageCodes, Set<BasicCoverageCodeEnum> basicCoverageOccCodes,
                                        CoverageRepositoryEntry coverageRepositoryEntry) {

    // Set the rating risk type apply of the coverage according to the
    // coverage repository entry
    if (coverageRepositoryEntry != null && coverageRepositoryEntry.getRatingRiskTypeApply() != null) {
      coverage.setRatingRiskTypeApply(coverageRepositoryEntry.getRatingRiskTypeApply().getCode());
    }

    // The same coverage may have a CoveragePremiumPrincipal and a
    // CoveragePremiumOccasional.
    CoveragePremium coveragePremium = null;

    // Create the CoveragePremiumPrincipal. Do not create if already present
    if ("P".equals(coverage.getRatingRiskTypeApply()) || "B".equals(coverage.getRatingRiskTypeApply())) {
      if (coverage.getTheCoveragePremiumPrincipal() == null) {
        coveragePremium = coverage.createTheCoveragePremiumPrincipal();
        if ("EN".equals(coverage.getCoverageType())) {
          createSubCoveragePremium(basicCoverageCodes, coveragePremium);
        }
      }
    }

    // Create the CoveragePremiumOccasional. Do not create if already
    // present
    if (hasOccasionnal) {
      if ("O".equals(coverage.getRatingRiskTypeApply()) || "B".equals(coverage.getRatingRiskTypeApply())) {
        if (coverage.getTheCoveragePremiumOccasional() == null) {
          coveragePremium = coverage.createTheCoveragePremiumOccasional();
          if ("EN".equals(coverage.getCoverageType())) {
            createSubCoveragePremium(basicCoverageOccCodes, coveragePremium);
          }
        }
      }
    }
  }

  /**
   * Creates the CoveragePremiums for all the Coverages of aInsuranceRisk. (version that retrieves the rating risk
   * type apply from plp)
   *
   * @param aInsuranceRisk             the a insurance risk
   * @param basicCoverageCodes         the basic coverage codes
   * @param basicCoverageOccCodes      the basic coverage occ codes
   * @param aCoverageRepositoryEntries the coverage repository entries
   */
  protected void createCoveragePremiums(InsuranceRisk aInsuranceRisk, Boolean hasOccasionnal,
                                        Set<BasicCoverageCodeEnum> basicCoverageCodes, Set<BasicCoverageCodeEnum> basicCoverageOccCodes,
                                        Map<String, CoverageRepositoryEntry> aCoverageRepositoryEntries) {

    for (Coverage aCoverage : aInsuranceRisk.getTheCoverage()) {

      CoverageRepositoryEntry coverageRepositoryEntry = aCoverageRepositoryEntries.get(aCoverage
          .getCoverageCode());

      if (coverageRepositoryEntry != null && coverageRepositoryEntry.getRatingRiskTypeApply() != null) {
        aCoverage.setRatingRiskTypeApply(coverageRepositoryEntry.getRatingRiskTypeApply().getCode());
      }

      CoveragePremium cp = null;
      // For basic coverages, the same coverage will have a
      // CoveragePremiumPrincipal and a
      // CoveragePremiumOccasional.
      if ("P".equals(aCoverage.getRatingRiskTypeApply()) || "B".equals(aCoverage.getRatingRiskTypeApply())) {
        cp = aCoverage.createTheCoveragePremiumPrincipal();
        if ("EN".equals(aCoverage.getCoverageType())) {
          createSubCoveragePremium(basicCoverageCodes, cp);
        }
      }

      if (hasOccasionnal) {
        if ("O".equals(aCoverage.getRatingRiskTypeApply()) || "B".equals(aCoverage.getRatingRiskTypeApply())) {
          cp = aCoverage.createTheCoveragePremiumOccasional();
          if ("EN".equals(aCoverage.getCoverageType())) {
            createSubCoveragePremium(basicCoverageOccCodes, cp);
          }
        }
      }

      // Must be set to yes for the PEGA service
      // selectCoveragesOnMultipleOffers
      aCoverage.setCoverageEligibleInd("Y");
    }
  }

  /**
   * Creates the CoveragePremiums for all the endorsement Coverages of aInsuranceRisk. IPORTANT: This method might be
   * called when the endorsements are generated by PEGA instead of Classic. Because the 'rating risk type apply' is
   * not present in PEGA
   *
   * @param aInsuranceRisk            the a insurance risk
   * @param basicCoverageCodes        the basic coverage codes
   * @param basicCoverageOccCodes     the basic coverage occ codes
   * @param coverageRepositoryEntries
   */
  protected void createEndorsmentCoveragePremiums(InsuranceRisk aInsuranceRisk,
                                                  Set<BasicCoverageCodeEnum> basicCoverageCodes, Set<BasicCoverageCodeEnum> basicCoverageOccCodes,
                                                  Map<String, CoverageRepositoryEntry> coverageRepositoryEntries) {

    // PEGA can't access the classic to get data to setup the rating risk
    // type
    for (Coverage aCoverage : aInsuranceRisk.getTheCoverage()) {
      CoverageRepositoryEntry coverageRepositoryEntry = coverageRepositoryEntries
          .get(aCoverage.getCoverageCode());

      if (coverageRepositoryEntry != null && coverageRepositoryEntry.getRatingRiskTypeApply() != null) {
        aCoverage.setRatingRiskTypeApply(coverageRepositoryEntry.getRatingRiskTypeApply().getCode());
      }

    }

    this.createEndorsmentCoveragePremiums(aInsuranceRisk, basicCoverageCodes, basicCoverageOccCodes);
  }

  /**
   * Creates the CoveragePremiums for all the endorsement Coverages of aInsuranceRisk.
   *
   * @param aInsuranceRisk        the a insurance risk
   * @param basicCoverageCodes    the basic coverage codes
   * @param basicCoverageOccCodes the basic coverage occ codes
   */
  protected void createEndorsmentCoveragePremiums(InsuranceRisk aInsuranceRisk,
                                                  Set<BasicCoverageCodeEnum> basicCoverageCodes, Set<BasicCoverageCodeEnum> basicCoverageOccCodes) {

    for (Coverage aCoverage : aInsuranceRisk.getTheCoverage()) {

      if ("EN".equals(aCoverage.getCoverageType()) && "Y".equals(aCoverage.getCoverageEligibleInd())) {
        if ("P".equals(aCoverage.getRatingRiskTypeApply())) {
          CoveragePremium cp = aCoverage.getTheCoveragePremiumPrincipal();
          if (cp == null) {
            cp = aCoverage.createTheCoveragePremiumPrincipal();
            createSubCoveragePremium(basicCoverageCodes, cp);
          }
        } else if ("O".equals(aCoverage.getRatingRiskTypeApply())) {
          CoveragePremium cp = aCoverage.getTheCoveragePremiumOccasional();
          if (cp == null) {
            cp = aCoverage.createTheCoveragePremiumOccasional();
            createSubCoveragePremium(basicCoverageOccCodes, cp);
          }
        }
      }
    }
  }

  /**
   * Gets the root insurance risk.
   *
   * @param branchInsuranceRisk the branch insurance risk
   * @param rootPolicyVersion   the root policy version
   * @return the root insurance risk
   */
  protected InsuranceRisk getRootInsuranceRisk(InsuranceRisk branchInsuranceRisk,
                                               com.ing.canada.som.interfaces.agreement.PolicyVersion rootPolicyVersion) {

    List<InsuranceRisk> insuranceRiskList = rootPolicyVersion.getTheInsuranceRisk();
    for (InsuranceRisk aRootInsuranceRisk : insuranceRiskList) {
      if (branchInsuranceRisk.getInsuranceRiskSequence().equals(aRootInsuranceRisk.getInsuranceRiskSequence())) {
        return aRootInsuranceRisk;
      }
    }
    return null;
  }

  /**
   * Copy all the attributes from the root InsuranceRisk to the branch InsuranceRisks.
   *
   * @param somPolicyVersion the som policy version
   * @throws BaseException the base exception
   */
  protected void copyInsuranceRiskToOffers(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion)
      throws BaseException {

    try {
      Map<Method, Method> methodList = this.getMethodList(somPolicyVersion.getTheInsuranceRisk().get(0));
      List<com.ing.canada.som.interfaces.agreement.PolicyVersion> policyVersionOfferList = somPolicyVersion
          .getThePolicyVersionOffer();

      for (com.ing.canada.som.interfaces.agreement.PolicyVersion branchPolicyVersion : policyVersionOfferList) {

        List<InsuranceRisk> insuranceRiskList = branchPolicyVersion.getTheInsuranceRisk();
        for (InsuranceRisk branchInsuranceRisk : insuranceRiskList) {
          InsuranceRisk rootInsuranceRisk = this.getRootInsuranceRisk(branchInsuranceRisk, somPolicyVersion);

          for (Entry<Method, Method> entry : methodList.entrySet()) {
            Object objRoot = entry.getKey().invoke(rootInsuranceRisk);
            Object objBranch = entry.getKey().invoke(branchInsuranceRisk);
            if (objRoot != null || objBranch != null) {
              entry.getValue().invoke(branchInsuranceRisk, objRoot);
            }
          }
        }
      }
    } catch (Exception e) {
      BaseException be = new BaseException(
          "Unable to copy the root InsuranceRisk Data in the branch InsuranceRisks");
      be.setRealException(e);
      throw be;
    }
  }

  /**
   * Returns the list of get/set methods to invoke.
   *
   * @param aInsuranceRisk the a insurance risk
   * @return the list of get/set methods to invoke.
   * @throws Exception the exception
   */
  protected Map<Method, Method> getMethodList(InsuranceRisk aInsuranceRisk) throws Exception {

    // Do not exclude de persistenceUniqueId because we want the
    // persistenceUniqueId of the InsuranceRisk
    // on all the offers for the dataMediator
    List<String> exclSetterList = Arrays.asList(new String[]{"setTestDataTrace", "setOfferType",
        "setInsuranceRiskSequence"});

    Class<?> clazz = aInsuranceRisk.getClass();
    List<Method> methods = new ArrayList<Method>();
    CollectionUtils.addAll(methods, clazz.getDeclaredMethods());
    CollectionUtils.addAll(methods, clazz.getSuperclass().getDeclaredMethods());

    Map<Method, Method> methodList = new HashMap<Method, Method>();
    for (Method method : methods) {
      String methodName = method.getName();
      if (methodName.startsWith("set") && !methodName.startsWith("setThe")
          && !exclSetterList.contains(methodName)) {

        methodList.put(clazz.getMethod("g" + methodName.substring(1), (Class[]) null), method);
      }
    }
    return methodList;
  }

  /**
   * Removes all the endorsments of the policy version.
   *
   * @param anInsuranceRisk the an insurance risk
   */
  protected void removeEndorsments(InsuranceRisk anInsuranceRisk) {

    List<Coverage> lst = new ArrayList<Coverage>(anInsuranceRisk.getTheCoverage());
    for (Coverage co : lst) {
      if ("EN".equals(co.getCoverageType())) {
        co.setCoverageEligibleInd("N");
      }
    }
  }

  /**
   * Gets the real exception.
   *
   * @param e the BaseException
   * @return the real exception
   */
  protected Exception getRealException(BaseException e) {
    Exception cause = e;
    if (e.getRealException() != null) {
      cause = e.getRealException();
    }
    return cause;
  }

  // TODO ylaberge BEGIN Please do not remove what is below this comment
  protected boolean getBugToAvoid() {
    return false;
  }

  protected boolean getYankeeZulu() {
    return false;
  }

  protected boolean getBeatles() {
    return true;
  }

  protected boolean getCranberries() {
    return false;
  }

  protected boolean getCranberriesWithAutoQuoteRollback() {
    return false;
  }

  protected void printTestDescription() {
    StringBuilder toPrint = new StringBuilder("\n");
    toPrint.append(this.printCurrentDate());
    toPrint.append("\n");
    if (this.getBugToAvoid()) {
      toPrint.append("\n%%% Testing Bug to avoid").append("\n%%% * Pega Environment: DEV")
          .append("\n%%% * Not using EIS values for KindOfLoss.coverageCode")
          .append("\n%%% * Using Pega deliverables 4-6")
          .append("\n%%% * Using GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% * Using GET_RATING_DEVIATIONS");
    } else if (this.getYankeeZulu()) {
      toPrint.append("\n%%% Testing Yankee-Zulu").append("\n%%% * Pega Environment: INTG")
          .append("\n%%% * Not using EIS values for KindOfLoss.coverageCode")
          .append("\n%%% * Not using Pega deliverables 4-6")
          .append("\n%%% * Using GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% * Using GET_RATING_DEVIATIONS");
    } else if (this.getBeatles()) {
      toPrint.append("\n%%% Testing Beatles").append("\n%%% * Pega Environment: INTG")
          .append("\n%%% * Using EIS values for KindOfLoss.coverageCode")
          .append("\n%%% * Not using Pega deliverables 4-6")
          .append("\n%%% * Using GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% * Using GET_RATING_DEVIATIONS");
    } else if (this.getCranberries()) {
      toPrint.append("\n%%% Testing Cranberries without AutoQuote rollback")
          .append("\n%%% * Pega Environment: DEV")
          .append("\n%%% * Using EIS values for KindOfLoss.coverageCode")
          .append("\n%%% * Using Pega deliverables 4-6")
          .append("\n%%% * Not using GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% * Not using GET_RATING_DEVIATIONS");
    } else if (this.getCranberriesWithAutoQuoteRollback()) {
      toPrint.append("\n%%% Testing Cranberries with AutoQuote rollback").append("\n%%% * Pega Environment: DEV")
          .append("\n%%% * Using EIS values for KindOfLoss.coverageCode")
          .append("\n%%% * Using Pega deliverables 4-6")
          .append("\n%%% * Using GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% * Using GET_RATING_DEVIATIONS");
    }
    toPrint.append("\n");
    LOG.info(Logger.EVENT_SUCCESS, toPrint.toString());
  }

  protected String printCurrentDate() {
    GregorianCalendar dt = new GregorianCalendar();
    return new StringBuilder("\n%%% Date:\t").append(dt.get(Calendar.YEAR)).append("/")
        .append((dt.get(Calendar.MONTH) + 1)).append("/").append(dt.get(Calendar.DAY_OF_MONTH)).toString();
  }

  protected void putGarbage(String header,
                            com.ing.canada.som.interfaces.agreement.PolicyVersion modifiedSomPolicyVersion,
                            boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations) {
    this.putGarbage(header, modifiedSomPolicyVersion, getRegularAndPreferredRspScoring, getRatingDeviations, null);
  }

  protected void putGarbage(String header,
                            com.ing.canada.som.interfaces.agreement.PolicyVersion modifiedSomPolicyVersion,
                            boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations, String offerType) {

    StringBuilder printMsg = new StringBuilder("\n");
    if (header != null) {
      printMsg.append("\n%%% ").append(header);
    }

    for (InsuranceRisk somInsuranceRisk : modifiedSomPolicyVersion.getTheInsuranceRisk()) {
      if ((offerType == null) || (offerType.equals(somInsuranceRisk.getOfferType()))) {
        this.putGarbage(somInsuranceRisk, getRegularAndPreferredRspScoring, getRatingDeviations, printMsg);
      }
    }
    for (com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOffer : modifiedSomPolicyVersion
        .getThePolicyVersionOffer()) {
      for (InsuranceRisk somInsuranceRisk : policyVersionOffer.getTheInsuranceRisk()) {
        if ((offerType == null) || (offerType.equals(somInsuranceRisk.getOfferType()))) {
          this.putGarbage(somInsuranceRisk, getRegularAndPreferredRspScoring, getRatingDeviations, printMsg);
        }
      }
    }
    LOG.info(Logger.EVENT_SUCCESS, printMsg.toString());
  }

  protected void putGarbage(InsuranceRisk somInsuranceRisk, boolean getRegularAndPreferredRspScoring,
                            boolean getRatingDeviations, StringBuilder printMsg) {

    if (getRegularAndPreferredRspScoring) {
      putGarbageInCVIScores(somInsuranceRisk, printMsg);
    }

    if (getRatingDeviations) {
      putGarbageInRatingDeviation(somInsuranceRisk, printMsg);
    }
  }

  protected void printInfoFromPegaRating(String header,
                                         com.ing.canada.som.interfaces.agreement.PolicyVersion modifiedSomPolicyVersion,
                                         boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations) {
    this.printInfoFromPegaRating(header, modifiedSomPolicyVersion, getRegularAndPreferredRspScoring,
        getRatingDeviations, null);
  }

  protected void printInfoFromPegaRating(String header,
                                         com.ing.canada.som.interfaces.agreement.PolicyVersion modifiedSomPolicyVersion,
                                         boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations, String offerType) {

    StringBuilder printMsg = new StringBuilder("\n");
    if (header != null) {
      printMsg.append("\n%%% ").append(header);
    }

    for (InsuranceRisk somInsuranceRisk : modifiedSomPolicyVersion.getTheInsuranceRisk()) {
      if ((offerType == null) || (offerType.equals(somInsuranceRisk.getOfferType()))) {
        this.printInfoFromPegaRating(null, somInsuranceRisk, getRegularAndPreferredRspScoring, getRatingDeviations,
            printMsg);
      }
    }
    for (com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOffer : modifiedSomPolicyVersion
        .getThePolicyVersionOffer()) {
      for (InsuranceRisk somInsuranceRisk : policyVersionOffer.getTheInsuranceRisk()) {
        if ((offerType == null) || (offerType.equals(somInsuranceRisk.getOfferType()))) {
          this.printInfoFromPegaRating(null, somInsuranceRisk, getRegularAndPreferredRspScoring,
              getRatingDeviations, printMsg);
        }
      }
    }
    LOG.info(Logger.EVENT_SUCCESS, printMsg.toString());
  }

  protected void printInfoFromPegaRating(String header, InsuranceRisk somInsuranceRisk,
                                         boolean getRegularAndPreferredRspScoring, boolean getRatingDeviations, StringBuilder printMsg) {

    printMsg.append("\n");
    if (header != null) {
      printMsg.append("\n%%% ").append(header);
    }

    if (getRegularAndPreferredRspScoring) {
      printMsg.append("\n%%% Project Rating Ext Classic Auto PPV As-is, deliverable 4")
          .append("\n%%% Remove second call to GET_REGULAR_AND_PREFERRED_RSP_SCORING")
          .append("\n%%% Check customer value indexes, retention scores and competitivity scores");
      printCVIScores(somInsuranceRisk, printMsg);
    }

    if (getRatingDeviations) {
      printMsg.append("\n")
          //
          .append("\n%%% Project Rating Ext Classic Auto PPV As-is, deliverables 5 et 6")
          .append("\n%%% Remove call to GET_RATING_DEVIATIONS")
          .append("\n%%% Check deviation data: flex, capping, scoring adjustment, competitivity adjustment");
      validateRatingDeviation(somInsuranceRisk, printMsg);
    }
  }

  protected com.ing.canada.som.interfaces.agreement.PolicyVersion clonePolicyVersion(
      com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersion) {

    GenericRootObject groList = new GenericRootObject(com.ing.canada.som.interfaces.agreement.PolicyVersion.class);
    com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionClone = (com.ing.canada.som.interfaces.agreement.PolicyVersion) groList
        .createTheRootObject();

    for (InsuranceRisk insuranceRisk : policyVersion.getTheInsuranceRisk()) {
      cloneInsuranceRisk(policyVersionClone, insuranceRisk);
    }
    for (com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOffer : policyVersion
        .getThePolicyVersionOffer()) {
      com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersionOfferClone = policyVersionClone
          .addThePolicyVersionOffer();
      for (InsuranceRisk insuranceRisk : policyVersionOffer.getTheInsuranceRisk()) {
        cloneInsuranceRisk(policyVersionOfferClone, insuranceRisk);
      }
    }
    return policyVersionClone;
  }

  /**
   * {@inheritDoc}
   *
   * @param aPlPolicyVersion     -- UNUSED
   * @param ubiSelectedByRiskSeq -- UNUSED
   * @param performanceWatch     -- UNUSED
   */
  @Override
  public void manageUbi(PolicyVersion aPlPolicyVersion, Map<Integer, Boolean> ubiSelectedByRiskSeq,
                        StopWatch performanceWatch) throws AutoquoteRatingException {
    // Empty method
  }


  /**
   * Gets the ammount that as no taxes on a policyOffer.
   *
   * @param currentPolicyOfferRating the current policy offer rating
   * @return the untaxable amount
   */
  @Override
  public double getUntaxableAmount(PolicyOfferRating currentPolicyOfferRating) {
    return this.getPremiumDeviationService().getUntaxableAmount(currentPolicyOfferRating);
  }

  @Override
  public OfferTypeCodeEnum getReferenceOfferType(String applicationMode) throws AutoquoteRatingException {

    OfferTypeCodeEnum offerType = null;

    try {
      offerType = this.getPremiumDeviationService().getReferenceOffer(applicationMode);
    } catch (Exception ex) {
      throw new AutoquoteRatingException(ex.getMessage(), ex);
    }

    return offerType;
  }

  @Override
  public void manageUbiStatus(PolicyVersion aPlPolicyVersion) {

    Collection<com.ing.canada.plp.domain.insurancerisk.InsuranceRisk> insuranceRisks = aPlPolicyVersion.getInsuranceRisks();
    InsuranceRiskOffer selectedOffer = null;

    // Iterate on each Vehicle
    for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk : insuranceRisks) {
      selectedOffer = insuranceRisk.getSelectedInsuranceRiskOffer();
      boolean isNotSelected = selectedOffer == null;
      OfferTypeCodeEnum type = isNotSelected ? null : selectedOffer.getOfferType();

      if (type != null) {
        BaseCoverage ubi = this.coverageHelper.getSelectedEndorsement(selectedOffer.getCoverageOffers(), EndorsementCodeEnum.UBI);
        Party principalDriver = this.partyHelper.getPrincipalDriver(selectedOffer.getInsuranceRisk());
        if (ubi != null && ubi.getCoverageSelectedIndicator()) {
          principalDriver.getDriverComplementInfo().setUBIStatusCode(UBIStatusCodeEnum.ENROLLED);
        } else if (ubi == null) {
          principalDriver.getDriverComplementInfo().setUBIStatusCode(null);
        }
      }
    }
  }

  protected boolean isQuickQuote(PolicyVersion aPlPolicyVersion) {
    return ApplicationModeEnum.QUICK_QUOTE.equals(aPlPolicyVersion.getInsurancePolicy().getApplicationMode());
  }

  protected void processProhibitedPostalCode(com.ing.canada.som.interfaces.agreement.PolicyVersion policyVersion,
                                             PolicyVersion plpPolicyVersion) {

    String prohibited = null;
    if (policyVersion.getTheInsuranceRisk(0) != null
        && policyVersion.getTheInsuranceRisk(0).getTheAddress() != null) {
      prohibited = policyVersion.getTheInsuranceRisk(0).getTheAddress().getProhibitedPostalCodeIndicator();
    }

    boolean prohibitedIndicator = prohibited != null && "Y".equals(prohibited) ? true : false;

    if (plpPolicyVersion.getParties() != null
        && plpPolicyVersion.getParties().iterator().next() != null
        && plpPolicyVersion.getParties().iterator().next().getAddress(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS) != null) {
      plpPolicyVersion.getParties().iterator().next().getAddress(AddressTypeCodeEnum.RESIDENTIAL_ADDRESS).setProhibitedPostalCodeIndicator(
          prohibitedIndicator);
    }

  }
}
