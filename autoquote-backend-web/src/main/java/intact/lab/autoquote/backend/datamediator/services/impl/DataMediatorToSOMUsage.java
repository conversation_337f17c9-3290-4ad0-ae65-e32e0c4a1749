package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.plp.domain.coverage.Coverage;
import com.ing.canada.plp.domain.driver.Conviction;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DriverTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyRoleInRiskTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.ing.canada.plp.service.IBusinessTransactionActivityService;
import com.ing.canada.plp.service.ICarrierRepositoryEntryService;
import com.ing.canada.som.interfaces.agreement.InsurancePolicy;
import com.ing.canada.som.interfaces.businessModel.ManufacturingContext;
import com.ing.canada.som.interfaces.businessTransaction.BusinessTransaction;
import com.ing.canada.som.interfaces.partyRoleInRisk.Driver;
import com.ing.canada.som.interfaces.partyRoleInRisk.Insured;
import com.ing.canada.som.interfaces.partyRoleInRisk.Owner;
import com.ing.canada.som.interfaces.product.CoverageRepositoryEntry;
import java.util.Set;

import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOMLegacyRatingInfo;
import intact.lab.autoquote.backend.datamediator.utils.CipherUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component("dataMediatorToSOMUsage")
@Scope("prototype")
public class DataMediatorToSOMUsage extends DataMediatorToSOM {
    private static final Log log = LogFactory.getLog(DataMediatorToSOMUsage.class);

    DataMediatorToSOMUsage(@Qualifier("application-id") String applicationId, IInsuranceRiskOfferHelper insuranceRiskOfferHelper,
                           ICarrierRepositoryEntryService carrierRepositoryEntryService, IBusinessTransactionActivityService businessTransactionActivityService,
                           IDataMediatorToSOMLegacyRatingInfo dmLegacyRatingInfoByPostalCode, CipherUtils cipherUtils) {
        super(applicationId, insuranceRiskOfferHelper, carrierRepositoryEntryService, businessTransactionActivityService,
                dmLegacyRatingInfoByPostalCode, cipherUtils);
    }

    protected void createPolicyVersionGraph(PolicyVersion plPolicyVersion, com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, Boolean isPrior) {
        this.mapPolicyVersion(plPolicyVersion, somPolicyVersion);
        if (plPolicyVersion.getReferenceDate() != null) {
            this.mapReferenceDate(plPolicyVersion.getReferenceDate(), somPolicyVersion.createTheReferenceDate());
        }

        InsurancePolicy somInsurancePolicy = somPolicyVersion.createTheInsurancePolicy();
        this.mapInsurancePolicy(plPolicyVersion, somInsurancePolicy);
        ManufacturingContext somManufacturingContext = somInsurancePolicy.createTheManufacturingContext();
        this.mapManufacturingContext(plPolicyVersion.getInsurancePolicy().getManufacturingContext(), somManufacturingContext);
        if (plPolicyVersion.getBusinessTransaction() != null) {
            BusinessTransaction somBusinessTransaction = somPolicyVersion.createTheBusinessTransaction();
            this.mapBusinessTransaction(plPolicyVersion.getBusinessTransaction(), somBusinessTransaction);
        }

        this.setClaimsAndKindOfLossTreeViaPolicyVersion(plPolicyVersion, somPolicyVersion);
        this.setInsuranceRiskTree(plPolicyVersion, somPolicyVersion, isPrior);
        this.setThePartyTree(plPolicyVersion, somPolicyVersion);
    }

    protected void setInsuranceRiskTree(PolicyVersion plPolicyVersion, com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, Boolean isPrior) {
        if (plPolicyVersion.getInsuranceRisks() != null) {
            for(InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRisk.getId());
                if (somInsuranceRisk != null) {
                    somPolicyVersion.addTheInsuranceRisk(somInsuranceRisk);
                } else {
                    somInsuranceRisk = somPolicyVersion.addTheInsuranceRisk();
                    this.mapInsuranceRisk(plInsuranceRisk, somInsuranceRisk);
                    this.insuranceRiskCache.put(plInsuranceRisk.getId(), somInsuranceRisk);
                }

                this.setClaimsAndKindOfLossTreeViaInsuranceRisk(plInsuranceRisk, somInsuranceRisk);
                Vehicle plVehicle = plInsuranceRisk.getVehicle();
                if (plVehicle != null) {
                    com.ing.canada.som.interfaces.physicalObject.Vehicle somVehicle = somInsuranceRisk.createTheVehicle();
                    this.mapVehicle(plVehicle, somVehicle, plInsuranceRisk, somInsuranceRisk);
                    this.setTheVehicleDetailsSpec(plVehicle, somVehicle);
                }

                if (isPrior) {
                    Set<Coverage> coverageSet = plInsuranceRisk.getCoverages();
                    if (coverageSet != null) {
                        for(Coverage plCoverage : coverageSet) {
                            com.ing.canada.som.interfaces.risk.Coverage somCoverage = somInsuranceRisk.addTheCoverage();
                            this.mapCoverage(plCoverage, somCoverage);
                            CoverageRepositoryEntry somCoverageRepositoryEntry = somCoverage.createTheCoverageProduct().createTheCoverageRepositoryEntryBase();
                            this.mapCoverageRepositoryEntry(plCoverage, somCoverageRepositoryEntry);
                        }
                    }
                } else {
                    InsuranceRiskOffer plInsuranceRiskOffer = plInsuranceRisk.getSelectedInsuranceRiskOffer();
                    if (plInsuranceRiskOffer != null) {
                        this.mapInsuranceRiskOffer(plInsuranceRiskOffer, somInsuranceRisk);
                        Set<CoverageOffer> coverageOfferSet = plInsuranceRiskOffer.getCoverageOffers();
                        if (coverageOfferSet != null) {
                            for(CoverageOffer plCoverageOffer : coverageOfferSet) {
                                com.ing.canada.som.interfaces.risk.Coverage somCoverageOffer = somInsuranceRisk.addTheCoverage();
                                this.mapCoverageOffer(plCoverageOffer, somCoverageOffer);
                                CoverageRepositoryEntry somCoverageRepositoryEntry = somCoverageOffer.createTheCoverageProduct().createTheCoverageRepositoryEntryBase();
                                this.mapCoverageRepositoryEntry(plCoverageOffer, somCoverageRepositoryEntry);
                            }
                        }
                    }
                }
            }
        }

    }

    protected void setThePartyTree(PolicyVersion plPolicyVersion, com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {
        if (plPolicyVersion.getParties() != null) {
            for(Party plParty : plPolicyVersion.getParties()) {
                com.ing.canada.som.interfaces.party.Party somParty = (com.ing.canada.som.interfaces.party.Party)this.partyCache.get(plParty.getId());
                if (somParty != null) {
                    somPolicyVersion.addTheParty(somParty);
                } else {
                    somParty = somPolicyVersion.addTheParty();
                    this.mapParty(plParty, somParty);
                    this.partyCache.put(plParty.getId(), somParty);
                }

                this.setClaimsAndKindOfLossTreeViaParty(plParty, somParty);
                DriverComplementInfo plDriverComplementInfo = plParty.getDriverComplementInfo();
                if (plDriverComplementInfo != null) {
                    com.ing.canada.som.interfaces.partyRoleInRisk.DriverComplementInfo somDriverComplementInfo = somParty.createTheDriverComplementInfo();
                    this.mapDriverComplementInfo(plDriverComplementInfo, somDriverComplementInfo);
                    if (plDriverComplementInfo.getConvictions() != null) {
                        for(Conviction plConviction : plDriverComplementInfo.getConvictions()) {
                            this.mapConviction(plConviction, somDriverComplementInfo.addTheConviction());
                        }
                    }

                    if (plDriverComplementInfo.getDriverLicenseClasses() != null) {
                        for(DriverLicenseClass plDriverLicenseClass : plDriverComplementInfo.getDriverLicenseClasses()) {
                            this.mapDriverLicenceClass(plDriverLicenseClass, somDriverComplementInfo.addTheDriverLicenseClass());
                        }
                    }
                }

                if (plParty.getPartyRoleInRisks() != null) {
                    for(PartyRoleInRisk plPartyRoleInRisk : plParty.getPartyRoleInRisks()) {
                        if (PartyRoleInRiskTypeCodeEnum.IS_DRIVER_OF.equals(plPartyRoleInRisk.getPartyRoleInRiskType())) {
                            Driver somDriver = somParty.addTheDriver();
                            this.mapPartyRoleInRiskOnDriver(plPartyRoleInRisk, somDriver);
                            InsuranceRisk plInsuranceRiskViaPartyRoleInRisk = plPartyRoleInRisk.getInsuranceRisk();
                            if (plInsuranceRiskViaPartyRoleInRisk != null) {
                                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRiskViaPartyRoleInRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRiskViaPartyRoleInRisk.getId());
                                if (somInsuranceRiskViaPartyRoleInRisk != null) {
                                    somDriver.setTheInsuranceRisk(somInsuranceRiskViaPartyRoleInRisk);
                                    if (plPartyRoleInRisk.getDriverType() == DriverTypeCodeEnum.PRINCIPAL) {
                                        Insured insured = somInsuranceRiskViaPartyRoleInRisk.addTheInsured();
                                        insured.setInsuredType("P");
                                        if (!plParty.getCreditScores().isEmpty()) {
                                            for(CreditScore creditScore : plParty.getCreditScores()) {
                                                com.ing.canada.som.interfaces.party.CreditScore cs = insured.addTheCreditScore();
                                                cs.setCreditScore(creditScore.getCreditScore());
                                                cs.setCreditScoreCategory("RATED");
                                            }
                                        }

                                        if (!plParty.getConsents().isEmpty()) {
                                            for(Consent consent : plParty.getConsents()) {
                                                if (consent.getConsentType() == ConsentTypeCodeEnum.CREDIT_SCORE) {
                                                    com.ing.canada.som.interfaces.party.Consent ct = insured.addTheConsent();
                                                    ct.setConsentInd(consent.getConsentIndicator() ? "Y" : "N");
                                                    ct.setConsentType("CS");
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    log.error("An impossible situation has occured... a PartyRoleInRisk is linked to a InsuranceRisk of another PolicyVersion");
                                }
                            }
                        } else if (PartyRoleInRiskTypeCodeEnum.IS_OWNER_OF.equals(plPartyRoleInRisk.getPartyRoleInRiskType())) {
                            Owner somOwner = somParty.addTheOwner();
                            this.mapPartyRoleInRiskOnOwner(plPartyRoleInRisk, somOwner);
                            InsuranceRisk plInsuranceRiskViaPartyRoleInRisk = plPartyRoleInRisk.getInsuranceRisk();
                            if (plInsuranceRiskViaPartyRoleInRisk != null) {
                                com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRiskViaPartyRoleInRisk = (com.ing.canada.som.interfaces.risk.InsuranceRisk)this.insuranceRiskCache.get(plInsuranceRiskViaPartyRoleInRisk.getId());
                                if (somInsuranceRiskViaPartyRoleInRisk != null) {
                                    somOwner.setTheInsuranceRisk(somInsuranceRiskViaPartyRoleInRisk);
                                } else {
                                    log.error("An impossible situation has occured... a PartyRoleInRisk is linked to a InsuranceRisk of another PolicyVersion");
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}

