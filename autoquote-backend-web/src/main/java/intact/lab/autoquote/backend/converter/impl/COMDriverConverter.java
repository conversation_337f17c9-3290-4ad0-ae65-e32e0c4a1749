package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.address.ComAddress;
import com.intact.com.driver.ComDriver;
import com.intact.com.driver.ComDriverClaim;
import com.intact.com.driver.ComDriverConviction;
import com.intact.com.util.ComDate;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.ClaimDTO;
import intact.lab.autoquote.backend.common.dto.ConvictionDTO;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.LocalDate;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("comDriverConverter")
public class COMDriverConverter implements ICOMConverter<DriverDTO, ComDriver> {

	@Resource(name = "comAddressConverter")
	private ICOMConverter<AddressDTO, ComAddress> comAddressConverter;

	@Resource(name = "comClaimConverter")
	private ICOMConverter<ClaimDTO, ComDriverClaim> comClaimConverter;

	@Resource(name = "comConvictionConverter")
	private ICOMConverter<ConvictionDTO, ComDriverConviction> comConvictionConverter;

	@Override
	public DriverDTO toDTO(ComDriver comDriver) {
		if (comDriver.getIsDriver() == null || (comDriver.getIsDriver() != null && !comDriver.getIsDriver())) {
			return null;
		}
		DriverDTO driverDTO = new DriverDTO();
		driverDTO.setLicenseNbr(comDriver.getLicenseNumber());
		driverDTO.setPartyId(comDriver.getWebMsgId() != null ? comDriver.getWebMsgId() : null);
		driverDTO.setNumberOfMinorInfractions(comDriver.getMinorInfractionCount() != null ? Integer.parseInt(comDriver.getMinorInfractionCount()) : 0); //SV
		driverDTO.setNumberOfMajorInfractions(comDriver.getMajorInfractionCount() != null ? Integer.parseInt(comDriver.getMajorInfractionCount()) : 0); //SV
		driverDTO.setNumberOfRelevantClaims(CollectionUtils.isNotEmpty(comDriver.getDriverClaims()) ? comDriver.getDriverClaims().size() : 0);//SV
		driverDTO.setPrincipalInsuredSinceCode(comDriver.getPrincipalInsuredSinceCode()); //SV
		if (comDriver.getCustomerInterestedByUBI() != null) {
			driverDTO.setInterestedByUbiInd(BooleanUtils.toBoolean(comDriver.getCustomerInterestedByUBI()) ? Boolean.TRUE : Boolean.FALSE);//SV
		}

		//Claims
		List<ComDriverClaim> driverClaims = comDriver.getDriverClaims() != null ? comDriver.getDriverClaims() : new ArrayList<>();
		for (ComDriverClaim comDriverClaim : driverClaims) {
			driverDTO.getClaims().add(this.comClaimConverter.toDTO(comDriverClaim));
		}

		//Convictions
		List<ComDriverConviction> driverConvictions = comDriver.getDriverConvictions() != null ? comDriver.getDriverConvictions() : new ArrayList<>();
		for (ComDriverConviction comDriverConviction : driverConvictions) {
			driverDTO.getConvictions().add(this.comConvictionConverter.toDTO(comDriverConviction));
		}

		driverDTO.setDriverLicenseType(comDriver.getDriverLicenseType());
		driverDTO.setLicenseObtentionDate(this.convertCOMDateToDate(comDriver.getDateLicenseObtained()));
		return driverDTO;
	}

	@Override
	public ComDriver toCOM(DriverDTO dto, ComDriver initialComDriver) {
		ComDriver driver = initialComDriver == null ? new ComDriver() : initialComDriver;
		driver.setCreateProfileConsentInd(Boolean.FALSE); // TODO: change this once it is ready
		if (dto != null) {
			driver.setMinorInfractionCount(dto.getNumberOfMinorInfractions() != null ? dto.getNumberOfMinorInfractions().toString() : null);//SV
			driver.setMajorInfractionCount(dto.getNumberOfMajorInfractions() != null ? dto.getNumberOfMajorInfractions().toString() : null);//SV
			driver.setAlreadyBeenPrincipalDriverInd(dto.getPrincipalInsuredSinceCode() != null ? true : false);
			driver.setPrincipalInsuredSinceCode(dto.getPrincipalInsuredSinceCode());//SV - QC
			driver.setCustomerInterestedByUBI(dto.getInterestedByUbiInd());//SV
			driver.setDriverLicenseType(dto.getDriverLicenseType());
			if (dto.getLicenseObtentionDate() != null) {
				driver.setDateLicenseObtained(this.convertDatetoCOMDate(dto.getLicenseObtentionDate()));
			}
			this.prepareClaims(dto, driver);
			this.prepareConvictions(dto, driver);
		}


		return driver;
	}


	/**
	 * Adds the claims to com driver.
	 *
	 * @param dto       the dto
	 * @param comDriver the com driver
	 */
	private void prepareClaims(DriverDTO dto, ComDriver comDriver) {

		if (comDriver.getDriverClaims() == null) {
			comDriver.setDriverClaims(new ArrayList<>());
		} else {
			comDriver.getDriverClaims().clear();
		}
		List<ClaimDTO> claimsDTO = dto.getClaims();
		short claimSeq = 0;
		if (CollectionUtils.isNotEmpty(claimsDTO)) {
			comDriver.setLossesYearsInd(true);

			for (ClaimDTO claimDTO : claimsDTO) {
				ComDriverClaim comDriverClaim = this.comClaimConverter.toCOM(claimDTO, null);
				comDriverClaim.setClaimSequence(claimSeq++);
				comDriver.getDriverClaims().add(comDriverClaim);
			}
		} else {
			comDriver.setLossesYearsInd(false);
		}
	}


	/**
	 * Adds the convictions to com driver.
	 *
	 * @param dto       the dto
	 * @param comDriver the com driver
	 */
	private void prepareConvictions(DriverDTO dto, ComDriver comDriver) {

		if (comDriver.getDriverConvictions() == null) {
			comDriver.setDriverConvictions(new ArrayList<>());
		} else {
			comDriver.getDriverConvictions().clear();
		}
		List<ConvictionDTO> convictionsDTO = dto.getConvictions();
		short convictionSeq = 0;
		if (CollectionUtils.isNotEmpty(convictionsDTO)) {
			for (ConvictionDTO convictionDTO : convictionsDTO) {
				ComDriverConviction comDriverConviction = this.comConvictionConverter.toCOM(convictionDTO, null);
				comDriverConviction.setConvictionSequence(convictionSeq++);
				comDriver.getDriverConvictions().add(comDriverConviction);
			}
		}
	}

	/**
	 * Convert dateto COM date.
	 *
	 * @param localDate the local date
	 * @return the com date
	 */
	private ComDate convertDatetoCOMDate(LocalDate localDate) {
		ComDate comDate = new ComDate();
		comDate.setDay(String.valueOf(localDate.getDayOfMonth()));
		comDate.setMonth(String.valueOf(localDate.getMonthOfYear()));
		comDate.setYear(String.valueOf(localDate.getYear()));
		return comDate;
	}

	/**
	 * Convert COM date to date.
	 *
	 * @param date the date
	 * @return the local date
	 */
	private LocalDate convertCOMDateToDate(ComDate date) {
		LocalDate localDate = null;
		if (date != null) {
			localDate = new LocalDate(Integer.valueOf(date.getYear()),
					Integer.valueOf(date.getMonth()), Integer.valueOf(date.getDay()));
		}
		return localDate;
	}

}
