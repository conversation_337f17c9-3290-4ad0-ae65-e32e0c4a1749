package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.partyRoleInAgreement.PolicyHolder;
import com.ing.canada.ss.base.BaseException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.rating.ICreditScoreService;
import intact.lab.autoquote.backend.services.rating.IDetermineClientEligibilityLevel;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Map;

@Component
public class CreditScoreService implements ICreditScoreService {

	private static final String APPLICATION_MODE = "APPLICATION_MODE";

	private final IDetermineClientEligibilityLevel determineClientEligibilityLevel;

	private final String applicationId;

	private final ExecuteService executeService;

	public CreditScoreService(IDetermineClientEligibilityLevel determineClientEligibilityLevel,
			@Qualifier("application-id") String applicationId, ExecuteService executeService) {
		this.determineClientEligibilityLevel = determineClientEligibilityLevel;
		this.applicationId = applicationId;
		this.executeService = executeService;
	}


	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void callTheCreditScoreWithoutClientEligibility(
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, ManufacturingContext aCtxt,
			StopWatch performanceWatch) throws AutoquoteRatingException {

		try {
			Map<String, Object> ilParams = SSSUtils.getDelegateParameters(aCtxt, this.applicationId);
			ilParams.put(APPLICATION_MODE, somPolicyVersion.getTheInsurancePolicy().getApplicationMode());
			for (PolicyHolder policyHolder : somPolicyVersion.getThePolicyHolder()) {
				if ("P".equals(policyHolder.getPolicyHolderType())) {
					com.ing.canada.som.interfaces.party.Party somParty = preProcessSOM(somPolicyVersion, policyHolder);

					// 1. Call the IL Credit Score service
					performanceWatch.start("    >> getCreditScore");
					this.executeService.getCreditScore(somParty, ilParams);
					performanceWatch.stop();

					break;
				}
			}

		} catch (BaseException bex) {
			throw new AutoquoteRatingException("The credit scoring call has failed!", this.getRealException(bex));
		}

	}

	/**
	 * Call the credit score.
	 * 
	 * @param somPolicyVersion the som policy version
	 * @param aCtxt the a ctxt
	 * @param performanceWatch {@link StopWatch}
	 * @throws AutoquoteRatingException the rating exception
	 *
	 */
	@Override
	@Transactional
	public void callTheCreditScore(com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
			ManufacturingContext aCtxt, StopWatch performanceWatch) throws AutoquoteRatingException {

		try {
			Map<String, Object> ilParams = SSSUtils.getDelegateParameters(aCtxt, this.applicationId);
			for (PolicyHolder policyHolder : somPolicyVersion.getThePolicyHolder()) {
				if ("P".equals(policyHolder.getPolicyHolderType())) {
					com.ing.canada.som.interfaces.party.Party somParty = preProcessSOM(somPolicyVersion, policyHolder);

					// 1. Call the IL Credit Score service
					performanceWatch.start("    >> getCreditScore");
					ilParams.put(APPLICATION_MODE, somPolicyVersion.getTheInsurancePolicy().getApplicationMode());
					this.executeService.getCreditScore(somParty, ilParams);
					performanceWatch.stop();

					// 2. PEGA - Determine Client Eligibility Level
					performanceWatch.start("    >> determineClientEligibilityLevel");
					com.ing.canada.som.interfaces.party.Party modifiedSomParty = this.determineClientEligibilityLevel
							.determineClientEligibilityLevel(aCtxt, somParty);
					performanceWatch.stop();

					somParty.setClientEligibilityLevel(modifiedSomParty.getClientEligibilityLevel());

					break;
				}
			}

		} catch (BaseException bex) {
			throw new AutoquoteRatingException("The credit scoring call has failed!", this.getRealException(bex));
		}

	}

	private static com.ing.canada.som.interfaces.party.Party preProcessSOM(
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion, PolicyHolder policyHolder) {
		com.ing.canada.som.interfaces.party.Party somParty = policyHolder.getTheParty();

		// Set Age of the policyHolder in service pega IDetermineClientEligibilityLevel
		GregorianCalendar calrefDate = somPolicyVersion.getTheReferenceDate().getDriverAgeReferenceDate();

		GregorianCalendar cal = somParty.getDateOfBirth();
		int age = calrefDate.get(Calendar.YEAR) - cal.get(Calendar.YEAR);
		if ((cal.get(Calendar.MONTH) > calrefDate.get(Calendar.MONTH))
				|| (cal.get(Calendar.MONTH) == calrefDate.get(Calendar.MONTH) && cal.get(Calendar.DAY_OF_MONTH) > calrefDate
						.get(Calendar.DAY_OF_MONTH))) {
			age--;
		}

		policyHolder.setAge(age);
		return somParty;
	}

	/**
	 * Gets the real exception.
	 * 
	 * @param e the BaseException
	 * @return the real exception
	 */
	protected Exception getRealException(BaseException e) {
		Exception cause = e;
		if (e.getRealException() != null) {
			cause = e.getRealException();
		}
		return cause;
	}
}
