package intact.lab.autoquote.backend.services.business.driver.impl;

import com.ing.canada.cif.domain.IPostalCode;
import com.ing.canada.cif.service.IPostalCodeService;
import com.ing.canada.common.domain.AffinityGroupCode;
import com.ing.canada.common.domain.Municipality;
import com.ing.canada.common.domain.MunicipalityDetails;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.common.services.api.municipality.IMunicipalitiesByPostalCodeService;
import com.ing.canada.common.services.api.municipality.IMunicipalityDetailService;
import com.ing.canada.common.services.api.party.IInsuredGroupService;
import com.ing.canada.common.services.api.party.PartyGroupType;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.AffinityGroupRepositoryEntry;
import com.ing.canada.plp.domain.driver.AffinityGroupSpecialConditionRepositoryEntry;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CountryCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.GroupRepositoryEntryEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.KindOfLossCodeEnum;
import com.ing.canada.plp.domain.enums.OwnerTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyGroupTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.GroupRepositoryEntry;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyGroup;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyGroupHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.common.IOccupationService;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.singleid.IProfileService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

@Component
public abstract class DriverBusinessProcess implements IDriverBusinessProcess {

	/** The insured group affinity code. */
	private static String INSURED_GROUP_AFFINITY_CODE = "Aff";
	private static final Logger LOG = ESAPI.getLogger(DriverBusinessProcess.class);

	private final IPolicyVersionHelper policyVersionHelper;
	private final IMunicipalityDetailService municipalityDetailsService;
	protected IMunicipalitiesByPostalCodeService municipalityByPostalCodeService;
	protected IPostalCodeService postalCodeService;
	protected IPartyHelper partyHelper;
	protected IFormFieldValidatorService formFieldValidatorService;
	private final IAffinityGroupService affinityGroupService;
	private final IInsuredGroupService insuredGroupService;
	protected IPartyGroupHelper partyGroupHelper;
	private final IPartyRoleInRiskService partyRoleInRiskService;
	private final IPartyService partyService;
	private final IPartyGroupHelper plpPartyGroupHelper;
	private final IOccupationService occupationService;

	@AutowiredLocal
	protected IProfileService profileService;

	DriverBusinessProcess(IPolicyVersionHelper policyVersionHelper,
						  IMunicipalityDetailService municipalityDetailsService, IMunicipalitiesByPostalCodeService municipalityByPostalCodeService,
						  IPostalCodeService postalCodeService, IPartyHelper partyHelper, IFormFieldValidatorService formFieldValidatorService,
						  IPartyGroupHelper partyGroupHelper, IAffinityGroupService affinityGroupService, IInsuredGroupService insuredGroupService,
						  IPartyRoleInRiskService partyRoleInRiskService, IPartyService partyService, IOccupationService occupationService) {
		this.policyVersionHelper = policyVersionHelper;
		this.municipalityDetailsService = municipalityDetailsService;
		this.municipalityByPostalCodeService = municipalityByPostalCodeService;
		this.postalCodeService = postalCodeService;
		this.partyHelper = partyHelper;
		this.formFieldValidatorService = formFieldValidatorService;
		this.affinityGroupService = affinityGroupService;
		this.insuredGroupService = insuredGroupService;
		this.partyGroupHelper = partyGroupHelper;
		this.partyRoleInRiskService = partyRoleInRiskService;
		this.partyService = partyService;
		this.plpPartyGroupHelper = partyGroupHelper;
		this.occupationService = occupationService;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public int getDriversCount(final PolicyVersion aPolicyVersion) {
		// NOTE - we assume that 1 party = 1 driver AND of individual type
		Set<Party> drivers = this.policyVersionHelper.getIndividualParties(aPolicyVersion);
		return drivers == null ? 0 : drivers.size();
	}

	/**
	 * Gets the province for municipality.
	 *
	 * @param aMunicipality the a municipality
	 * @param postalCode the postal code
	 * @param context the context
	 *
	 * @return the province for municipality
	 *
	 */
	@Override
	public ProvinceCodeEnum getProvinceForMunicipality(final Municipality aMunicipality, final String postalCode, final ManufacturingContext context) {

		MunicipalityDetails municipalityDetails = this.municipalityDetailsService.getMunicipalityDetailedInfos(Language.FRENCH, aMunicipality.getId(), new Date(), postalCode, context);

		String prov = municipalityDetails.getProvince();

		// "valueOf" method are specific concerning the param (QC or QUÉBEC).
		// This is done to avoid problem.
		return prov.length() > 2 ? ProvinceCodeEnum.valueOfCode(prov) : ProvinceCodeEnum.valueOfCode(prov);
	}

	/**
	 * Returns a list of Municipality instances rather than a list of ValidValueBO instances.
	 *
	 * BR0427 The City/municipality available is/are determined and limited by the list of city generated by a specific
	 * Postal Code
	 *
	 * @param locale the locale
	 * @param postalCode the postal code
	 * @param context the context
	 * @return the municipalities from postal code
	 */
	@Override
	public List<Municipality> getMunicipalitiesFromPostalCode(final Locale locale, final String postalCode, final ManufacturingContext context) {
		return this.municipalityByPostalCodeService.getMunicipalitiesListForPostalCode(Language.fromLocale(locale), postalCode, context);
	}

	/**
	 * Gets the street name from the CIF service. <br>
	 * BR333 When there is only one (1) street named associated to the postal code, then the street name should be
	 * defaulted to that one. Otherwise the street name should remain blank.
	 *
	 * @param locale the locale
	 * @param postalCode the postal code
	 *
	 * @return the street name
	 */
	@Override
	public IPostalCode getPostalCodeInfo(final Locale locale, final String postalCode) {

		List<IPostalCode> listPostalCode = this.postalCodeService.findByPostalCode(postalCode.toUpperCase(locale));

		if (listPostalCode == null || listPostalCode.isEmpty() || CollectionUtils.size(listPostalCode) > 1) {
			return null;

		}
		return listPostalCode.getFirst();
	}

	/**
	 * Gets the province.
	 *
	 * @param postalCode the postal code
	 * @return the province
	 */
	@Override
	public ProvinceCodeEnum getProvince(String postalCode) {
		ProvinceCodeEnum found = null;
		String province = this.postalCodeService.getProvince(postalCode);
		if (province != null) {
			found = ProvinceCodeEnum.valueOfCode(province);
		}
		return found;
	}

	/**
	 * Gets the cities valid values.
	 *
	 * @param postalCode the postal code
	 * @return the cities valid values
	 */
	@Override
	public List<ValidValueBO> getCitiesValidValues(String postalCode) {
		List<ValidValueBO> validValues = new ArrayList<>();
		List<String> cities = this.getCities(postalCode);

		if (cities.size() > 0) {
			int i = 1;
			for (String city : cities) {
				final String normalizedCity = Normalizer.normalize(city, Normalizer.Form.NFD);
				String unnacentedCity = normalizedCity.replaceAll("[^\\p{ASCII}]", "");
				validValues.add(new ValidValueBO(unnacentedCity, unnacentedCity, i));
				i++;
			}
		}

		return validValues;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<String> getCities(String postalCode) {
		return this.postalCodeService.getCities(postalCode);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void setModelForMunicipality(PolicyVersion aPolicyVersion, Party party, ManufacturingContext context, Locale locale) {
		Assert.notNull(aPolicyVersion, "PollicyVersion parameter cannot be null");
		Assert.notNull(aPolicyVersion, "Party parameter cannot be null");
		Assert.notNull(aPolicyVersion, "ManufacturingContext parameter cannot be null");
		Assert.notNull(aPolicyVersion, "Locale parameter cannot be null");

		Address address = this.partyHelper.getCurrentResidentialAddress(party);
		if (address != null) {
			String postalCode = this.formFieldValidatorService.getFormattedPostalCode(address.getPostalCode()).toUpperCase();

			this.manageMunicipalityRepositoryEntry(locale, address, postalCode, context);
		}
	}

	/**
	 * Manage municipality repository entry.
	 *
	 * @param aLocale the a locale
	 * @param address the address
	 * @param postalCode the postal code
	 * @param context the context
	 *
	 */
	@Override
	public void manageMunicipalityRepositoryEntry(final Locale aLocale, final Address address, final String postalCode, final ManufacturingContext context) {
		MunicipalityDetails details = this.municipalityDetailsService.getMunicipalityDetailedInfos(Language.fromLocale(aLocale), address.getMunicipalCode(), new Date(), postalCode, context);

		MunicipalityDetailSpecification mds = setMunicipalityDetailSpecification(address, details);
		MunicipalityRepositoryEntry mre = setMunicipalityRepositoryEntry(mds, details);

		// Link tables and set them in Address
		mds.setMunicipalityRepositoryEntry(mre);
		address.setMunicipality(mre.getMunicipality());

		address.setPostalCode(postalCode);
		CountryCodeEnum countryCodeFromLocale = getCountryCode(aLocale);
		if (countryCodeFromLocale != null){
			address.setCountry(countryCodeFromLocale);
		}
		address.setProvince(context.getProvince());

		address.setMunicipalityDetailSpecification(mds);
	}

	/**
	 * Sets the MunicipalityDetailSpecification.
	 *
	 * @param address the Address
	 * @param details the MunicipalityDetails
	 *
	 * @return the MunicipalityDetailSpecification
	 */
	private static MunicipalityDetailSpecification setMunicipalityDetailSpecification(final Address address, final MunicipalityDetails details) {
		MunicipalityDetailSpecification mds = address.getMunicipalityDetailSpecification();

		if (mds == null) {
			mds = new MunicipalityDetailSpecification();
		}

		mds.setAutomobileTerritoryRating(details.getAutomobileTerritoryRating());
		mds.setAutomobileTerritoryStat(details.getAutomobileTerritoryStatistical());
		mds.setFacilityRiskSharingPoolScoring(Byte.parseByte(details.getFacilityRiskSharingPoolScoring()));
		mds.setMunicipalAdjustmentAuto(details.getAutomobileMunicipalAdjustment());
		mds.setProhibitedMunicipalityAutoIndicator(Boolean.parseBoolean(details.getAutomobileProhibitedMunicipalityIndicator()));
		return mds;
	}

	/**
	 * Sets the MunicipalityRepositoryEntry.
	 *
	 * @param mds the MunicipalityDetailSpecification
	 * @param details the details
	 *
	 * @return the MunicipalityRepositoryEntry
	 */
	private static MunicipalityRepositoryEntry setMunicipalityRepositoryEntry(final MunicipalityDetailSpecification mds, final MunicipalityDetails details) {

		MunicipalityRepositoryEntry mre = mds.getMunicipalityRepositoryEntry();
		if (mre == null) {
			mre = new MunicipalityRepositoryEntry();
		}

		mds.setMunicipalityRepositoryEntry(mre);

		mre.setProvince(details.getProvince().length() > 2 ? ProvinceCodeEnum.valueOfCode(details.getProvince()) : ProvinceCodeEnum.valueOfCode(details.getProvince()));
		mre.setMunicipality(details.getMunicipality());
		mre.setMunicipalCode(details.getMunicipalCodeReplaced());
		mre.setMunicipalityStatus(details.getMunicipalityStatus());

		return mre;
	}

	protected CountryCodeEnum getCountryCode(Locale locale){
		CountryCodeEnum code = null;
		if ("CA".equals(locale.getCountry())) {
			code = CountryCodeEnum.CANADA;
		} else if ("US".equals(locale.getCountry())) {
			code = CountryCodeEnum.UNITED_STATES;
		} else if ("OT".equals(locale.getCountry())) {
			code = CountryCodeEnum.OTHER;
		}
		return code;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void setModelForAffinityGroup(PolicyVersion policyVersion, Party party, ManufacturingContext context, Map<String, String> insuredGroups) {

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> set affinityGroup. policyVersion.id='").append(policyVersion.getId())
					.append("', party.id='").append(party.getId())
					.append("', context={").append(ReflectionToStringBuilder.toString(context))
					.append("}, insuredGroups={").append(ReflectionToStringBuilder.toString(insuredGroups))
					.append("}").toString());
		}

		Province province = Province.fromDbCode(context.getProvince()); // match province from plp provinceEnum
		DistributorCodeEnum distributor = policyVersion.getDistributorCode();
		AffinityGroupCode affGroupCode = null;
		String insuredGroupAff = null;
		String pvAffinityGroupCode = policyVersion.getAffinityGroupCode();
		if (StringUtils.isNotEmpty(pvAffinityGroupCode)) {

			Date now = Calendar.getInstance().getTime();
			affGroupCode = this.affinityGroupService.getAffinityGroupCode(province, pvAffinityGroupCode.toUpperCase(), now,
					context.getDistributionChannel(), context.getInsuranceBusiness(),
					distributor);

			if (affGroupCode != null) { // only manage code if matched from CLASSIC

				PartyGroupType partyGroupType = PartyGroupType.valueOf(PartyGroupTypeCodeEnum.valueOfCode(affGroupCode.getPartyGroupType()).name());
				insuredGroupAff = this.insuredGroupService.getInsuredGroup(province, partyGroupType, affGroupCode.getPartyGroupCode(),
						context.getDistributionChannel(), context.getInsuranceBusiness(),
						distributor);

				if (LOG.isDebugEnabled()) {
					LOG.debug(Logger.EVENT_SUCCESS, String.format("found insuredGroupAff=[%s] on getInsuredGroup(%s, %s, %s, %s, %s, %s).", insuredGroupAff,
							province, partyGroupType, affGroupCode.getPartyGroupCode(), context.getDistributionChannel(),
							context.getInsuranceBusiness(), distributor));
				}

			}
		}
		insuredGroups.put(INSURED_GROUP_AFFINITY_CODE, insuredGroupAff);
		this.manageGroupCode(policyVersion, party, affGroupCode, insuredGroups.get(INSURED_GROUP_AFFINITY_CODE));
	}

	/**
	 * manages the group code. Form to PLP
	 *
	 * @param policyVersion the policy version
	 * @param aParty the a party
	 * @param anAffinityGroupCode the affinity group code we want to insert
	 * @param insuredGroupAff the insured group aff
	 */
	private void manageGroupCode(final PolicyVersion policyVersion, final Party aParty, final AffinityGroupCode anAffinityGroupCode, final String insuredGroupAff) {

		// DE107
		AffinityGroupRepositoryEntry agre = policyVersion.getAffinityGroupRepositoryEntry();

		// remove the group code from PL we will add the new one and recreate the special conditions if needed
		if (agre != null) {
			policyVersion.setAffinityGroupRepositoryEntry(null);
			PartyGroup partyGroup = null;
			// find the proper party group
			for (PartyGroup pg : aParty.getPartyGroups()) {
				if (pg.getGroupRepositoryEntry() != null && pg.getAffinityGroupRepositoryEntry() != null) {
					// group codes are set as EMPLOYER type
					if (PartyGroupTypeCodeEnum.EMPLOYER.equals(pg.getGroupRepositoryEntry().getPartyGroupType())) {
						partyGroup = pg;
						break;
					}
				}
			}
			if (partyGroup != null) {
				partyGroup.setAffinityGroupRepositoryEntry(null);
				aParty.removePartyGroup(partyGroup);
			}
		}

		// add the group code if provided
		if (anAffinityGroupCode != null) {
			PartyGroupTypeCodeEnum partyGroupType = PartyGroupTypeCodeEnum.valueOfCode(anAffinityGroupCode.getPartyGroupType());
			PartyGroup partyGroup = this.partyGroupHelper.getPartyGroup(aParty, partyGroupType);

			// No current group code in PL.. create a new one
			agre = new AffinityGroupRepositoryEntry();
			policyVersion.setAffinityGroupRepositoryEntry(agre);

			if (partyGroup == null) {
				partyGroup = new PartyGroup();
				partyGroup.setGroupRepositoryEntry(new GroupRepositoryEntry());
				aParty.addPartyGroup(partyGroup);
			}

			agre.setAffinityGroupCode(anAffinityGroupCode.getCode());
			agre.setAffinityGroupEnglishDescription(anAffinityGroupCode.getDescriptionEnglish());
			agre.setAffinityGroupFrenchDescription(anAffinityGroupCode.getDescriptionFrench());

			GroupRepositoryEntry gre = partyGroup.getGroupRepositoryEntry();
			gre.setInsuredGroup(insuredGroupAff);
			gre.setPartyGroupCode(anAffinityGroupCode.getPartyGroupCode());
			gre.setPartyGroupType(partyGroupType);
			gre.setPartySubGroupDescriptionEnglish(anAffinityGroupCode.getDescriptionEnglish());
			gre.setPartySubGroupDescriptionFrench(anAffinityGroupCode.getDescriptionFrench());
			partyGroup.setAffinityGroupRepositoryEntry(agre);

			for (String categories : anAffinityGroupCode.getCategories()) {
				AffinityGroupSpecialConditionRepositoryEntry agscre = new AffinityGroupSpecialConditionRepositoryEntry();
				agscre.setAffinityGroupSpecialConditionCode(categories);
				agre.addAffinityGroupSpecialConditionRepositoryEntry(agscre);
			}
		}
	}

	/**
	 * Extract the KindOfLoss from the Claim. If not found, create a new one
	 *
	 * @param aClaim the claim
	 * @return the KindOfLoss
	 */
	protected KindOfLoss extractKindOfLoss(final Claim aClaim) {
		KindOfLoss kindOfLoss;
		if (aClaim.getKindOfLosses().isEmpty()) {
			kindOfLoss = new KindOfLoss(aClaim, null);
		} else {
			kindOfLoss = (KindOfLoss) CollectionUtils.get(aClaim.getKindOfLosses(), 0);
		}

		return kindOfLoss;
	}

	/**
	 * Sets the kind of loss.
	 *
	 * @param kol the kol
	 * @param aClaim the a claim
	 * @param code the KindOfLossCodeEnum
	 * @param coverageTypeCode the CoverageTypeCodeEnum
	 * @param basicCoverageCode the BasicCoverageCodeEnum
	 * @param percentageOfLiability the percentage of liability
	 * @param isClaimAtFault the is claim at fault flag
	 * @param claimTypeCode the ClaimTypeCodeEnum
	 * @param amountPaid the amount paid
	 */
	protected void setKindOfLoss(final KindOfLoss kol, final Claim aClaim,
								 final KindOfLossCodeEnum code,
								 final CoverageTypeCodeEnum coverageTypeCode,
								 final BasicCoverageCodeEnum basicCoverageCode,
								 final short percentageOfLiability,
								 final Boolean isClaimAtFault,
								 final ClaimTypeCodeEnum claimTypeCode,
								 final BigDecimal amountPaid) {

		kol.setKindOfLoss(code.getCode());
		kol.setCoverageTypeCode(coverageTypeCode);
		kol.setCoverageCode(basicCoverageCode);
		kol.setAmountPaid(amountPaid);
		aClaim.setPercentageOfLiability(percentageOfLiability);
		aClaim.setClaimAtFaultIndicator(isClaimAtFault);
		aClaim.setClaimType(claimTypeCode);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void detachDriver(DriverComplementInfo aDriver, PolicyVersion aPolicyVersion, ApplicationEnum application) throws AccessManagerException {
		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder().append(">> detach driver={").append("driverComplementInfo.id'=").append(aDriver.getId()).append("', driverComplementInfo.driverSequence=")
					.append(aDriver.getDriverSequence()).append("', party.id=").append(aDriver.getParty().getId()).append("', policyVersion.id=").append(aPolicyVersion.getId()).append("'").toString());
		}
		// Remove relationship
		this.profileService.removeRelationShip(aPolicyVersion, application, aDriver.getDriverSequence());

		this.removePersistedPartyRoleInRisk(aPolicyVersion);

		aPolicyVersion.removeParty(aDriver.getParty());

		this.partyService.delete(aDriver.getParty());

		this.resetDriverSequences(aDriver, aPolicyVersion);
		// NOTE this method does not persist data. Persistence should be done by the caller
	}

	@Override
	public RuleExceptionResult validateQuickQuoteHardRoadblock(DriverComplementInfo aDriver, Locale locale) {
		// current no current common roadblocks
		return null;
	}

	/**
	 * {@inheritDoc}
	 * */
	@Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(PolicyVersion policyVersion, ProvinceCodeEnum aProvince) {
		return new ArrayList<>();
	}

	/**
	 * {@inheritDoc}
	 * */
	@Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(DriverComplementInfo driver, ProvinceCodeEnum aProvince) {
		return new ArrayList<>();
	}

	@Override
	public void manageUniversityDegree(Party party, final String insuredGroupUni) {
		PartyGroup partyGroup = this.plpPartyGroupHelper.getPartyGroup(party, PartyGroupTypeCodeEnum.UNIVERSITY);

		if (BooleanUtils.isTrue(party.getHolderOfDiplomaFromCanadianUniversity())) {
			// No party is alreay present, create one
			if (partyGroup == null) {
				GroupRepositoryEntry aGroupRepositoryEntry = new GroupRepositoryEntry();
				aGroupRepositoryEntry.setPartyGroupTypeCode(PartyGroupTypeCodeEnum.UNIVERSITY);
				aGroupRepositoryEntry.setPartyGroupCode(GroupRepositoryEntryEnum.UNIVERSITY.getCode());
				aGroupRepositoryEntry.setInsuredGroup(insuredGroupUni);

				PartyGroup newPartyGroup = new PartyGroup();
				newPartyGroup.setGroupRepositoryEntry(aGroupRepositoryEntry);
				party.addPartyGroup(newPartyGroup);
			}

			// Don't need to update anything
		} else if (partyGroup != null) {
			party.removePartyGroup(partyGroup);
		}
	}

	/**
	 * Remove all the persisted PartyRoleInRisk for a policyVersion.
	 *
	 * @param aPolicyVersion the a policy version
	 *
	 */
	private void removePersistedPartyRoleInRisk(final PolicyVersion aPolicyVersion) {

		for (InsuranceRisk insuranceRisk : aPolicyVersion.getInsuranceRisks()) {
			Set<PartyRoleInRisk> partyRoleInRiskList = insuranceRisk.getPartyRoleInRisks();
			List<PartyRoleInRisk> lst = new ArrayList<>(partyRoleInRiskList);

			for (PartyRoleInRisk partyRoleInRisk : lst) {
				if (partyRoleInRisk.getId() != null) {
					Party party = partyRoleInRisk.getParty();

					if (OwnerTypeCodeEnum.SECOND_REGISTERED_OWNER.equals(partyRoleInRisk.getOwnerType())) {
						insuranceRisk.setOtherRegisteredOwnerIndicator(null);
					}

					party.removePartyRoleInRisk(partyRoleInRisk);
					insuranceRisk.removePartyRoleInRisk(partyRoleInRisk);
					this.partyRoleInRiskService.delete(partyRoleInRisk);
				}
			}
		}
	}

	/**
	 * Manage profile. Added for quickquote needs.
	 *
	 * @param aPolicyVersion the a policy version
	 * @param application - {@link ApplicationEnum}
	 * @throws SingleIdActiveProductException the single id active product exception
	 * @throws AccessManagerException the access manager exception
	 */
	@Override
	public void manageProfile(PolicyVersion aPolicyVersion, ApplicationEnum application) throws SingleIdActiveProductException, AccessManagerException {
		RoadBlockExceptionContextEnum context = RoadBlockExceptionContextEnum.SAVE_DRIVER;

		this.profileService.manageProfile(aPolicyVersion, false, context, application, null);

	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String getInsuredGroupFromService(Province province, PartyGroupType partyGroupType, String partyGroupCode, ManufacturingContext context, final DistributorCodeEnum distributor) {
		return this.insuredGroupService.getInsuredGroup(province, partyGroupType, partyGroupCode, context.getDistributionChannel(), context.getInsuranceBusiness(), distributor);
	}

	@Override
	public List<ValidValueBO> getDomainsList(Locale aLocale, DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness, DistributorCodeEnum distributor) {
		return this.occupationService.getDomains(aLocale, distributionChannel, insuranceBusiness, distributor);
	}

	@Override
	public List<ValidValueBO> getOccupationsList(final Locale locale, final String aDomain, final ManufacturingContext context, DistributorCodeEnum distributor) {
		return this.occupationService.getOccupations(locale, aDomain, context, distributor);
	}

	/**
	 * Scan the existing parties and reset the sequence if it's after a party set to be deleted.
	 *
	 * @param aDriver - {@link DriverComplementInfo} to be deleted
	 * @param aPolicyVersion - {@link PolicyVersion}
	 */
	private void resetDriverSequences(DriverComplementInfo aDriver, PolicyVersion aPolicyVersion) {
		// Loop over the parties and decrement the sequence if it was after the deleted party
		for (Party party : this.policyVersionHelper.getIndividualParties(aPolicyVersion)) {
			if (party.getDriverComplementInfo().getDriverSequence() > aDriver.getDriverSequence()) {
				party.getDriverComplementInfo().setDriverSequence(party.getDriverComplementInfo().getDriverSequence() - 1);
			}
		}
	}
}
