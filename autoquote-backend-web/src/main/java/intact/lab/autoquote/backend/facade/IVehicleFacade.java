package intact.lab.autoquote.backend.facade;


import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteVehicleException;

import java.util.List;

public interface IVehicleFacade {

	List<MakeDTO> getVehicleMakeList(String year, String province, String language) throws AutoQuoteVehicleException;

	List<ModelDTO> getVehicleModels(String year, String makeId, String province, String language) throws AutoQuoteVehicleException;
}
