package intact.lab.autoquote.backend.facade;

import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;

public interface IDistributorFacade {

	DistributorDTO retrieveBrokerInfo(final String apiKey,
                                      final String language, final String province,
                                      final String postalCode, final String subBrokerNo,
                                      final String origin) throws AutoQuoteException, AutoquoteFacadeException;
}
