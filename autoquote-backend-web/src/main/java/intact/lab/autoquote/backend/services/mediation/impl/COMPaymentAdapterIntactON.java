package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.services.mediation.ICOMPaymentAdapter;

@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT)
public class COMPaymentAdapterIntactON implements ICOMPaymentAdapter {

	@Override
	public void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails, PolicyVersion policyVersion) {
		//noop
	}
	
}
