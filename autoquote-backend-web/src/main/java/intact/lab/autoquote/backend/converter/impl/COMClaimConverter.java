package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.driver.ComDriverClaim;
import intact.lab.autoquote.backend.common.dto.ClaimDTO;
import intact.lab.autoquote.backend.common.enums.ClaimNatureEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.springframework.stereotype.Component;

@Component("comClaimConverter")
public class COMClaimConverter implements ICOMConverter<ClaimDTO, ComDriverClaim> {

	@Override
	public ClaimDTO toDTO(ComDriverClaim comClaim) {
		ClaimDTO claimDTO = new ClaimDTO();
		if (comClaim != null) {
			claimDTO.setClaimSequence(comClaim.getClaimSequence().toString());
			claimDTO.setNature(ClaimNatureEnum.valueOfCode(comClaim.getClaimNature()));
			claimDTO.setDateOfLoss(comClaim.getClaimNatureYear());
		}
		return claimDTO;
	}

	@Override
	public ComDriverClaim toCOM(ClaimDTO dto, ComDriverClaim initialComDriverClaim) {
		ComDriverClaim comDriverClaim = new ComDriverClaim();
		if (dto != null) {
			comDriverClaim.setClaimNature(dto.getNature().getCode());
			comDriverClaim.setClaimNatureYear(dto.getDateOfLoss());
		}
		return comDriverClaim;
	}

}
