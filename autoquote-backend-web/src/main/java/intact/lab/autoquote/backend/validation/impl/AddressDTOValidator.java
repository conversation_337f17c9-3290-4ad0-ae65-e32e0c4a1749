package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IAddressDTOValidator;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("AddressDTOValidator")
public class AddressDTOValidator implements IAddressDTOValidator {

	@Override
	public void validate(AddressDTO addressDTO, Errors errors, ValidationContext context) {
		validateProvince(addressDTO, context, errors);
		validatePostalCode(addressDTO.getPostalCode(), context.getProvince(), context.getLanguage(), context.getCompany(), errors);
	}

	private void validateProvince(AddressDTO addressDTO, ValidationContext context, Errors errors) {
		String addressProvince = addressDTO.getProvince();
		if (addressProvince == null || !addressProvince.equalsIgnoreCase(context.getProvince()) || addressProvince.equals("")) {
			errors.rejectValue("province", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), "[province]");
		}
	}

	private void validatePostalCode(String postalCode, String province, String language, String company, Errors errors) {
		if (null != postalCode) {
			final String exp = "[A-Za-z].*[0-9]";
			postalCode = postalCode.replaceAll("[_.,-/ ]", "");

			// BR768: Postal Code Input Format =>Displays message : MSG213
			if (postalCode.trim().length() < 6 || postalCode.trim().length() > 6) {
				errors.rejectValue("postalCode", BRulesExceptionEnum.ERR_DRIVER_POSTALCODE_BR768.getErrorCode(), "[postalCode]");
			} else if (!postalCode.substring(0, 2).matches(exp) || !postalCode.substring(2, 4).matches(exp)
					|| !postalCode.substring(4, 6).matches(exp)) {
				errors.rejectValue("postalCode", BRulesExceptionEnum.Pattern.getErrorCode(), "[postalCode]");
			}
		} else {
			errors.rejectValue("postalCode", BRulesExceptionEnum.NotBlank.getErrorCode(), "[postalCode]");
		}
	}
}
