package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.CommunicationObjectModel;
import com.intact.com.state.ComState;

public class COMInitializer {

	private CommunicationObjectModel baseCommunicationObjectModel;

	public COMInitializer(CommunicationObjectModel baseCommunicationObjectModel) {
		this.baseCommunicationObjectModel = baseCommunicationObjectModel;
	}

	public CommunicationObjectModel initialize() {
		if (this.baseCommunicationObjectModel == null) {
			CommunicationObjectModel com = new CommunicationObjectModel();
			ComState comState = new ComState();
			com.setState(comState);
			return com;
		} else {
			//TODO
		}
		return this.baseCommunicationObjectModel;
	}

	public CommunicationObjectModel getBaseCommunicationObjectModel() {
		return baseCommunicationObjectModel;
	}

	public void setBaseCommunicationObjectModel(CommunicationObjectModel baseCommunicationObjectModel) {
		this.baseCommunicationObjectModel = baseCommunicationObjectModel;
	}
}
