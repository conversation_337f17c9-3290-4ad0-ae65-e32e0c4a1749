/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.impl;

import intact.lab.autoquote.backend.common.model.ValidValueBO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * This resource bundle extend the spring ResourceBundleMessageSource.<br>
 * So we get the advantage of the resource localisation and caching.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 13-Mar-08
 */
public final class ResourceBundleHelper {

	/**
	 * Private constructor
	 */
	private ResourceBundleHelper() {
		// Do nothing
	}

	/**
	 * Return a list of ValidValueBO contains in the bundle name.<br>
	 * Must respect this format key.index=value<br>
	 * Ex: SPO.1=Spouse<br>
	 * The first element of the list can derogate to this rule .0=Select
	 *
	 * @param aBundleName
	 *            The bundle name
	 * @param aLocale
	 *            The locale
	 *
	 * @return list of ValidValueBO
	 */
	public static List<ValidValueBO> getBundleContent(String aBundleName, Locale aLocale) {
		return getBundleContent(aBundleName, aLocale, true);
	}

	/**
	 * Return a list of ValidValueBO contains in the bundle name.<br>
	 * Must respect this format key.index=value<br>
	 * Ex: SPO.1=Spouse<br>
	 * The first element of the list can derogate to this rule .0=Select
	 *
	 * @param aBundleName
	 *            The bundle name
	 * @param aLocale
	 *            The locale
	 *
	 * @return list of ValidValueBO
	 */
	public static List<ValidValueBO> getBundleContent(String aBundleName, Locale aLocale, Boolean ascending) {

		ResourceBundle bundle = ResourceBundle.getBundle(aBundleName, aLocale);

		// Create a valid list with the bundle contain
		List<ValidValueBO> validValuesList = new ArrayList<ValidValueBO>();
		Enumeration<String> keysEnumeration = bundle.getKeys();
		int index = 0;
		while (keysEnumeration.hasMoreElements()) {
			String originalKey = keysEnumeration.nextElement();
			String fixedKey = originalKey;
			// Look if we have Index information in the key, i.e, insert.key
			int dotIndex = originalKey.indexOf(".");
			if (dotIndex > -1) {
				// extract the index and fix the key
				index = NumberUtils.toInt(StringUtils.left(originalKey, dotIndex));
				fixedKey = StringUtils.substring(originalKey, dotIndex + 1);
			}

			validValuesList.add(new ValidValueBO(fixedKey, bundle.getString(originalKey), index));
		}

		// default ascending
		if (ascending == null || Boolean.TRUE.equals(ascending)) {
			Collections.sort(validValuesList);
		}
		// otherwise descending
		else {
			Collections.sort(validValuesList, Collections.reverseOrder());
		}

		return validValuesList;
	}

	/**
	 * Gets the value of a key from a bundle.
	 *
	 * @param aBundleName
	 *            the a bundle name
	 * @param aLocale
	 *            the a locale
	 * @param key
	 *            the key
	 *
	 * @return the value if founded otherwise return null
	 */
	public static String getValue(String aBundleName, Locale aLocale, String key) {
		ResourceBundle bundle = ResourceBundle.getBundle(aBundleName, aLocale);
		return bundle != null ? bundle.getString(key) : null;
	}
}
