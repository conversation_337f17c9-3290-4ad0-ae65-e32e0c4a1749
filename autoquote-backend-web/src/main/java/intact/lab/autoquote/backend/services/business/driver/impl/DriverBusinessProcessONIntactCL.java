package intact.lab.autoquote.backend.services.business.driver.impl;

import com.ing.canada.cif.service.IPostalCodeService;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.common.services.api.municipality.IMunicipalitiesByPostalCodeService;
import com.ing.canada.common.services.api.municipality.IMunicipalityDetailService;
import com.ing.canada.common.services.api.party.IInsuredGroupService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.KindOfLossCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.MunicipalityDetailSpecification;
import com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyGroupHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.driver.BR11237_ClaimAtFaultRecentlyLicensed;
import com.intact.business.rules.driver.BR15475_MinorInfractionsRecentlyLicensed;
import com.intact.business.rules.driver.BR2623_MajorOrSeriousConviction;
import com.intact.business.rules.driver.BR2625_CombineConvictionAndClaim;
import com.intact.business.rules.driver.BR7996_TooManyAtFaultClaims6years;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.business.rules.usage.BR8719_FourOrMoreMinorInfractions3Years;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.common.IOccupationService;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class DriverBusinessProcessONIntactCL extends DriverBusinessProcess {

	private static final Logger log = ESAPI.getLogger(DriverBusinessProcessONIntactCL.class);

	protected BR15475_MinorInfractionsRecentlyLicensed br15475;
	protected BR8719_FourOrMoreMinorInfractions3Years br8719;
	protected BR2623_MajorOrSeriousConviction br2623;
	protected BR11237_ClaimAtFaultRecentlyLicensed br11237;
	protected BR2625_CombineConvictionAndClaim br2625;
	protected BR7996_TooManyAtFaultClaims6years br7996;

	public DriverBusinessProcessONIntactCL(IPolicyVersionHelper policyVersionHelper, IMunicipalityDetailService municipalityDetailsService,
										   IMunicipalitiesByPostalCodeService municipalityByPostalCodeService,
										   IPostalCodeService postalCodeService, IPartyHelper partyHelper,
										   IFormFieldValidatorService formFieldValidatorService, IPartyGroupHelper partyGroupHelper,
										   IAffinityGroupService affinityGroupService, IInsuredGroupService insuredGroupService,
										   IPartyRoleInRiskService partyRoleInRiskService, IPartyService partyService,
										   BR15475_MinorInfractionsRecentlyLicensed br15475, BR8719_FourOrMoreMinorInfractions3Years br8719,
										   BR2623_MajorOrSeriousConviction br2623, BR11237_ClaimAtFaultRecentlyLicensed br11237,
										   BR2625_CombineConvictionAndClaim br2625, BR7996_TooManyAtFaultClaims6years br7996,
										   IOccupationService occupationService) {
		super(policyVersionHelper, municipalityDetailsService, municipalityByPostalCodeService, postalCodeService, partyHelper,
				formFieldValidatorService, partyGroupHelper, affinityGroupService, insuredGroupService, partyRoleInRiskService,
				partyService, occupationService);
		this.br15475 = br15475;
		this.br8719 = br8719;
		this.br2623 = br2623;
		this.br11237 = br11237;
		this.br2625 = br2625;
		this.br7996 = br7996;
	}


	@Override
	public void manageMunicipalityRepositoryEntry(Locale aLocale, Address address, String postalCode, ManufacturingContext context) {
		// For intact Ontario we manage the repository entry for rating revolution

		MunicipalityDetailSpecification mds = address.getMunicipalityDetailSpecification();
		if (mds == null) {
			mds = new MunicipalityDetailSpecification();
			address.setMunicipalityDetailSpecification(mds);
		}

		MunicipalityRepositoryEntry mre = mds.getMunicipalityRepositoryEntry();
		if (mre == null) {
			mre = new MunicipalityRepositoryEntry();
			mds.setMunicipalityRepositoryEntry(mre);
		}
		mre.setProvince(address.getProvince());
		mre.setCountry(address.getCountry());
		mre.setMunicipality(address.getMunicipality());
		mre.setMunicipalCode(address.getMunicipalCode());
	}

	@Override
	public Claim setClaimKindOfLoss(Claim aClaim, String claimNatureAmount) {

		/*
		 * BR455: The "claims paid Amount" must be always defaulted to 1, and should never be requested to the eBTP user
		 * whatever of the "nature of the claims" provided.
		 */
		BigDecimal amountPaid = new BigDecimal(1);
		if (StringUtils.isNotBlank(claimNatureAmount)) {
			try {
				amountPaid = new BigDecimal(claimNatureAmount);
			} catch (NumberFormatException nfe) {
				log.error(Logger.EVENT_SUCCESS, new StringBuilder(">> Can't parse the claim nature amount=").append(claimNatureAmount).toString());
			}
		}

		KindOfLoss kol = this.extractKindOfLoss(aClaim);
		this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A1_A2_B_C2_C3_C4_AUTRES, CoverageTypeCodeEnum.COLLISION, BasicCoverageCodeEnum.COLLISION_PD20, (short) 100, Boolean.TRUE,
				ClaimTypeCodeEnum.COLLISION, amountPaid);

		return aClaim;
	}

	/**
	 * {@inheritDoc}
	 * */
	@Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(DriverComplementInfo driver, ProvinceCodeEnum aProvince) {
		List<RuleExceptionResult> roadblocks = new ArrayList<>();

		// BR8719: A roadblock must be raised when the driver has 4 or more (4+) minor infractions
		// ("Minor infractions in the last 3 years") in the last 3 years.
		roadblocks.add(this.br8719.validate(driver.getParty().getPolicyVersion()));

		// BR2623: When a driver has had 1 major or serious conviction
		// ("Serious or major infractions in the last 3 years"), he must be referred to his broker.
		roadblocks.add(this.br2623.validateBR2623(driver.getParty()));

		// BR15475: A roadblock must be raised when the driver has 2 minor infractions and got is licence
		// ("First driver's licence (including G1 or G2)") since less than 4 years ago.
		roadblocks.add(this.br15475.validate(driver.getParty()));

		// BR11237: A roadblock must be raised when the driver has 1 claim ("Any at-fault accidents in the last 9 years")
		// in the last 9 years and got is licence ("First driver's licence (including G1 or G2)") since less than 4
		// years ago.
		roadblocks.add(this.br11237.validate(driver.getParty()));

		// BR2625 : When a driver has been licensed for 4 years or more, he must be referred to his broker if he has had 2 minor
		// convictions or more in the past 3 years AND 1 at-fault claim or more in the past 6 years.
		roadblocks.add(this.br2625.validateBR2625(driver.getParty()));

		// BR7996: A roadblock must be raised when the driver has 2 claims in the last 6 years.
		roadblocks.add(this.br7996.validateBR(driver.getParty()));

		return roadblocks;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void manageProfile(PolicyVersion aPolicyVersion, ApplicationEnum application) throws SingleIdActiveProductException, AccessManagerException {

		RoadBlockExceptionContextEnum context = RoadBlockExceptionContextEnum.SAVE_DRIVER;
		try {
			this.profileService.manageProfile(aPolicyVersion.getId(), context, application);
		} catch (AutoQuoteRoadBlockException e) {
			throw new SingleIdActiveProductException(AutoquoteRoadBlockExceptionEnum.BR551, context);
		}
	}

}
