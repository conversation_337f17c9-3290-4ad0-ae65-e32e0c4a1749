/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.vehicle.impl;

import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.services.api.vehicle.IVehicleDetailService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.service.IAdditionalInterestRoleService;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.business.rules.vehicle.BR0616_BusinessAnnualKmVsAnnualKm;
import com.intact.business.rules.vehicle.BR15064_NormalRadiusKmLimit;
import com.intact.business.rules.vehicle.BR16878_BlacklistedVehicleModels;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class VehicleBusinessProcessQCIntactCL extends VehicleBusinessProcess {
	
	protected BR15064_NormalRadiusKmLimit br15064;

	@Value("${blacklistedModels}")
	private String BR16878_blacklistedvehicleModelCodes;

	@Value("${vehicle.BR16878.yearlimit:2017}")
	private Integer BR16878_yearlimit;

	private final BR16878_BlacklistedVehicleModels br16878;
	protected BR0616_BusinessAnnualKmVsAnnualKm br0616;

	public VehicleBusinessProcessQCIntactCL(VehicleMakesByYearService vehicleMakesByYearService, VehicleModelsService vehicleModelsService,
											IDateManagerService dateManagerService, IVehicleDetailService vehicleDetailService,
											ICommonBusinessProcess commonBusinessProcess, IPartyRoleInRiskService partyRoleInRiskService,
											IPartyService partyService, IAdditionalInterestRoleService additionalInterestRoleService,
											IInsuranceRiskService insuranceRiskService, BR15064_NormalRadiusKmLimit br15064,
											BR16878_BlacklistedVehicleModels br16878, BR0616_BusinessAnnualKmVsAnnualKm br0616) {
		super(vehicleMakesByYearService, vehicleModelsService, dateManagerService, vehicleDetailService, commonBusinessProcess,
				partyRoleInRiskService, partyService, additionalInterestRoleService, insuranceRiskService);
		this.br15064 = br15064;
		this.br16878 = br16878;
		this.br0616 = br0616;
	}

	@Override
	public RuleExceptionResult validateHardRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage) {
		RuleExceptionResult result = super.validateHardRoadblock(aVehicle, aProvince, aLanguage);
		if (result != null && result.hasFailed()) {
			return result;
		}

		List<String> listOfVehicles = BR16878_blacklistedvehicleModelCodes != null ? Arrays.asList(BR16878_blacklistedvehicleModelCodes.split(",")) : new ArrayList<>();
		result = this.br16878.validateBR(aVehicle, listOfVehicles, BR16878_yearlimit);
		//result = this.br16878.validateBR(aVehicle, Arrays.asList("0271", "1031", "1949"), 2017);
		if (result != null && result.hasFailed()) {
			return result;
		}
		return null;
	}

	@Override
	public  List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(
			Vehicle aVehicle, Province aProvince, Language aLanguage) {

		List<RuleExceptionResult> result = new ArrayList<>();

		RuleExceptionResult br0616 = this.br0616.validate(aVehicle);
		if (br0616.hasFailed()) {
			result.add(br0616);
		}
		result.add(this.br15064.validate(aVehicle));
		return result;
	}

}
