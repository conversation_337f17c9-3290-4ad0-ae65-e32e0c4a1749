package intact.lab.autoquote.backend.services.business.usage.impl;

import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * The CloneService have been override for Intact, now the risk associated with claims are not removed automatically
 * after a clone. This change is needed by intactAB where the risk is associated with the claim by the client.
 * For the other market(ON and QC) When processing the usage page, just before the assign claims, the risk is removed
 * from the claims.
 * 
 * 
 * <AUTHOR>
 * 
 */
@Component
public class UsageBusinessProcessHelperIntact {

	private final IPolicyVersionHelper policyVersionHelper;

	public UsageBusinessProcessHelperIntact(IPolicyVersionHelper policyVersionHelper) {
		this.policyVersionHelper = policyVersionHelper;
	}

	/**
	 * Remove any reference of claims to insurance risks.
	 * 
	 * @param aPolicyVersion the policy version
	 */
	public void clearInsuranceRiskOnClaims(PolicyVersion aPolicyVersion) {
		Set<Party> parties = this.policyVersionHelper.getIndividualParties(aPolicyVersion);

		for (Party aParty : parties) {
			Set<Claim> claims = aParty.getClaims();

			for (Claim aClaim : claims) {
				aClaim.setInsuranceRisk(null);
			}
		}
	}
}
