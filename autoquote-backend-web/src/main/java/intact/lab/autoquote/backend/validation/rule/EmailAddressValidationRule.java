package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.validation.impl.GeneralValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.Errors;

public abstract class EmailAddressValidationRule {

	public static void validate(Errors errors, String emailAddress) {
		// story 54440 either phone or email address must be entered
		// story 62307 now phone is mandatory and email is not
		if (!StringUtils.isBlank(emailAddress) && !"undefined".equals(emailAddress)) {
			if (!GeneralValidator.isValidEmailAddress(emailAddress)) {
				errors.rejectValue("emailAddress", "PATTERN",
						"[emailAddress]");
			} else if (emailAddress.contains("+")) {
				errors.rejectValue("emailAddress", "PATTERN",
						"[emailAddress]");
			}
		}
	}

}
