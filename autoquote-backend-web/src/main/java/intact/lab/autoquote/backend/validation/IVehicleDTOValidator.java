package intact.lab.autoquote.backend.validation;


import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.springframework.validation.Errors;

public interface IVehicleDTOValidator {

	void validate(VehicleDTO vehicleDTO, Errors errors, ValidationContext context);

	void validateKmPerYear(Integer kmPerYear, String province, String language, Errors errors);

}
