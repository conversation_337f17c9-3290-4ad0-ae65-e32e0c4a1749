/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid;

import com.ing.canada.cif.domain.IAddress;
import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.domain.impl.IClientCreationParameters;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.singleid.accessmanager.domain.SecureDomain;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.ing.canada.singleid.accessmanager.service.IClientAccountService;
import com.ing.canada.singleid.accessmanager.service.IUserAccountService;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;

public interface IProfileService {

	/**
	 * Manage relationship between the driver and the policy holder.
	 *
	 * @param aPolicyVersionId the a policy version id
	 * @param aapplication {@link ApplicationEnum}
	 * @param driverSequence the driver sequence
	 * @throws AccessManagerException
	 */
	void removeRelationShip(Long aPolicyVersionId, ApplicationEnum aapplication, Integer driverSequence)
			throws AccessManagerException;

	/**
	 * Remove a driver relationship in a policy version based on the driver' sequence.
	 *
	 * @param aPolicyVersion - {@link PolicyVersion}
	 * @param application - {@link ApplicationEnum}
	 * @param aDriverSequence - {@link Integer}
	 * @throws AccessManagerException
	 */
	void removeRelationShip(final PolicyVersion aPolicyVersion, final ApplicationEnum application,
			final Integer aDriverSequence) throws AccessManagerException;

	/**
	 * Manage profile.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 * @param isAgent - boolean indicator if is an agent
	 * @param contextEnum {@link RoadBlockExceptionContextEnum}
	 * @param application {@link ApplicationEnum}
	 * @param partner as {@link String}
	 * @throws AccessManagerException
	 * @throws SingleIdActiveProductException
	 */
	void manageProfile(final PolicyVersion aPolicyVersion, final boolean isAgent,
					   final RoadBlockExceptionContextEnum contextEnum, final ApplicationEnum application, String partner)
			throws AccessManagerException, SingleIdActiveProductException;

	/**
	 * Manage the profile(client) to be linked to the quote.
	 *
	 * @param aPolicyVersionId the a policy version id
	 * @param contextEnum the context enum
	 * @param application {@link ApplicationEnum}
	 * @throws AutoQuoteRoadBlockException
	 */
	void manageProfile(Long aPolicyVersionId, RoadBlockExceptionContextEnum contextEnum, ApplicationEnum application)
            throws AutoQuoteRoadBlockException, SingleIdActiveProductException;

	/**
	 * Populate the ClientCreationParameters from PLP objects.
	 *
	 * @param aPolicyVersion the a policy version
	 * @param application {@link ApplicationEnum}
	 * @param driverSequence the driver sequence
	 *
	 *
	 * @return the i client creation parameters
	 *
	 * @throws Exception the exception
	 */
	IClientCreationParameters populateForCreateProfile(PolicyVersion aPolicyVersion, ApplicationEnum application,
													   Long driverSequence);

	/**
	 * THIS METHOD IS IN THE INTERFACE BECAUSE WE WANT TO CALL IT FOR THE MONITORING.
	 */
	IClientAccountService getClientAccountService();

	/**
	 * Verify if is a policy version from quickquote
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 */
	boolean isQuickQuote(PolicyVersion aPolicyVersion);

	/**
	 * This method format the addressLine1 and addressLine2 base on the value include in the param anAddress. The fields
	 * civicNo and StreetName are mandatory, appartment is not.
	 *
	 * @param anAddress The party address
	 * @param aLanguage The party Language
	 */
	void formatCifAddress(IAddress anAddress, LanguageCodeEnum aLanguage);

	/**
	 * Populate the ClientCreationParameters from PLP objects.
	 *
	 * @param aPolicyVersion the a policy version
	 * @param aClient the a client
	 *
	 * @throws Exception the exception IClientCreationParameters populateForCreateProfile(PolicyVersion aPolicyVersion)
	 *             throws Exception;
	 */
	void populateCifForUpdateProfile(PolicyVersion aPolicyVersion, IClient aClient, Long driverSequence);

	/**
	 * THIS METHOD IS IN THE INTERFACE BECAUSE WE WANT TO CALL IT FOR THE MONITORING.
	 */
	IUserAccountService getUserAccountService();

	/**
	 * Verify product on first validation. Throws a SingleIdActiveProductException to redirect the user.
	 * BR545.1 The system must not redirect the User when his profile contains no active quote, no active policy, and no
	 * critical cancelled policy (within the last 3 years). It may contain a non-critical cancelled policy (within the
	 * last 2 years) and a user account may exist.
	 *
	 * @param clientId the client id
	 * @param userId the user id
	 * @param contextEnum the context enum
	 * @param isAgent the is agent
	 * @throws SingleIdActiveProductException
	 * @throws AccessManagerException
	 */
	void verifyProductOnFirstValidation(SecureDomain secureDomain, Long clientId, String userId, RoadBlockExceptionContextEnum contextEnum,
										boolean isAgent) throws SingleIdActiveProductException, AccessManagerException;

	/**
	 * Verify for if the email address has a prefixed partner email
	 *
	 * @param secureDomain the requested {@link SecureDomain}
	 * @param emailAdress as {@link String}
	 * @return true when has prefixed partner email address
	 */
	boolean hasPrefixedPartnerEmailAdress(SecureDomain secureDomain, String emailAdress);

	/**
	 * Update profile data - used for a quick quote.
	 * @param aPolicyVersion - the policy version
	 * @param cifClientId - the CIF client ID
	 * @throws AccessManagerException
	 */
	void updateProfile(PolicyVersion aPolicyVersion, Long cifClientId) throws AccessManagerException;
}
