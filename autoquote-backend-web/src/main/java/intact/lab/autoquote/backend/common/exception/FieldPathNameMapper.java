package intact.lab.autoquote.backend.common.exception;

import org.apache.commons.lang3.StringUtils;

import java.util.StringTokenizer;

class FieldPathNameMapper {

	private static final String DOT = ".";
	final String fieldPath;
	final String fieldName;
	FieldPathNameMapper(String objectName, String field) {
		// build the logical path to the field
		StringBuilder sb = new StringBuilder();
		if (StringUtils.isNotBlank(objectName)) {
			sb.append(objectName);
		}
		if (StringUtils.isNotBlank(field)) {
			// append a dot to separate object and rest of path, but only if there is something on both sides
			if (StringUtils.isNotBlank(objectName)) {
				sb.append(DOT);
			}
			sb.append(field);
		}
		StringTokenizer st = new StringTokenizer(sb.toString(), DOT);
		if (st.countTokens() > 0) {
			StringBuilder sbPath = new StringBuilder();
			while (st.countTokens() > 1) { // keep last token for fieldName
				if (sbPath.length() != 0) {
					sbPath.append(DOT); // only append dot between tokens, not at the start
				}
				sbPath.append(st.nextToken());
			}
			fieldName = st.nextToken();
			fieldPath = sbPath.toString();
		} else {
			fieldName = "";
			fieldPath = "";
		}
	}
}
