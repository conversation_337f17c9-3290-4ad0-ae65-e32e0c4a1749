package intact.lab.autoquote.backend.facade.offer;

import com.intact.com.CommunicationObjectModel;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;

public interface IAutoQuoteOfferFacade {

    /**
     * Get an offer.
     *
     * @param aCom {@link CommunicationObjectModel}
     * @return {@link CommunicationObjectModel}
     * @throws AutoquoteFacadeException
     */
    CommunicationObjectModel retrieveOffer(CommunicationObjectModel aCom) throws AutoquoteFacadeException;

    /**
     * Performs roadblock validation on the policy version.
     *
     * A CommunicationObjectModel instance can be considered valid if both 'ValidationErrors' and 'RoadBlock'
     * collections are empty.
     *
     * Does not perform basic UI validation.
     *
     * @param aCom {@link CommunicationObjectModel}
     * @throws Exception
     */
    void validateRoadblocks(CommunicationObjectModel aCom) throws Exception;
}
