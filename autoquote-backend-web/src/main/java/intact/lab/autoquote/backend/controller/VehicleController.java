package intact.lab.autoquote.backend.controller;

import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/irca/v2")
@RequiredArgsConstructor
public class VehicleController {


    private final IVehicleFacade vehicleFacade;

    @Operation(summary = "Get the vehicle makes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved a list of vehicule maker"),
            @ApiResponse(responseCode = "401", description = "You are not authorized to view the resource"),
            @ApiResponse(responseCode = "403", description = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(responseCode = "404", description = "The resource you were trying to reach is not found")
    })
    @GetMapping(value = "/vehicles/{year}/makes")
    public ResponseEntity<List<MakeDTO>> getVehicleMakeList(@RequestParam("province") String province,
                                                            @RequestParam("language") String language,
                                                            @PathVariable Integer year) {

        return ResponseEntity.ok(this.vehicleFacade.getVehicleMakeList(year.toString(), province, language));
    }

    @Operation(summary = "Get the vehicule models")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved a list of vehicule models"),
            @ApiResponse(responseCode = "401", description = "You are not authorized to view the resource"),
            @ApiResponse(responseCode = "403", description = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(responseCode = "404", description = "The resource you were trying to reach is not found")
    })
    @GetMapping(value = "/vehicles/{year}/{makeId}/models", produces = "application/json")
    public ResponseEntity<List<ModelDTO>> getVehicleModels(@PathVariable("year") String year, @PathVariable("makeId") String makeId, @RequestParam("province") String province,
                                                @RequestParam("language") String language) {
        return ResponseEntity.ok(this.vehicleFacade.getVehicleModels(year, makeId, province, language));
    }
}
