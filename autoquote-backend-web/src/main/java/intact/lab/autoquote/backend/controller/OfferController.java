package intact.lab.autoquote.backend.controller;

import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.ResponseDTO;
import intact.lab.autoquote.backend.facade.IOfferFacade;
import intact.lab.security.recaptcha.api.annotations.CheckRecaptcha;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/irca/v2")
@RequiredArgsConstructor
public class OfferController {

    private final IOfferFacade offerFacade;

    @CheckRecaptcha
    @Operation(summary = "Get the rate quote")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved ResponseEntity"),
            @ApiResponse(responseCode = "401", description = "You are not authorized to view the resource"),
            @ApiResponse(responseCode = "403", description = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(responseCode = "404", description = "The resource you were trying to reach is not found")
    })
    @PostMapping(value = "/quotes/rates", produces = "application/json")
    public ResponseEntity<ResponseDTO> rateQuote(@RequestBody QuoteDTO quoteDTO,
                                                 @RequestParam("actionToken") String actionToken,
                                                 @RequestParam("apiKey") String apiKey,
                                                 @RequestParam("province") String province,
                                                 @RequestParam("language") String language,
                                                 @RequestParam(value = "subBroker", required = false) String subBroker,
                                                 @RequestParam(value = "organizationSource", required = false) String organizationSource) {

        ResponseDTO responseDTO = offerFacade.calculate(quoteDTO, apiKey, province, language, subBroker, organizationSource);

        return ResponseEntity.ok(responseDTO);
    }

    @CheckRecaptcha
    @Operation(summary = "Get the rate quote")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved ResponseEntity"),
            @ApiResponse(responseCode = "401", description = "You are not authorized to view the resource"),
            @ApiResponse(responseCode = "403", description = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(responseCode = "404", description = "The resource you were trying to reach is not found")
    })
    @PutMapping(value = "/quotes/{uuid}/rates", produces = "application/json")
    public ResponseEntity<ResponseDTO> rateQuotes(@RequestBody QuoteDTO quoteDTO, @PathVariable("uuid") String uuid, @RequestParam("apiKey") String apiKey, @RequestParam("province") String province,
                                                  @RequestParam("language") String language, @RequestParam(value = "subBroker", required = false) String subBroker, @RequestParam(value = "organizationSource", required = false) String organizationSource) {
        ResponseDTO responseDTO = offerFacade.recalculate(quoteDTO, uuid, apiKey, province, language, subBroker, organizationSource);

        return ResponseEntity.ok(responseDTO);
    }

}
