package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IAddressDTOValidator;
import intact.lab.autoquote.backend.validation.IConsentDTOValidator;
import intact.lab.autoquote.backend.validation.IPartyDTOValidator;
import intact.lab.autoquote.backend.validation.IPartyRoleDTOValidator;
import intact.lab.autoquote.backend.validation.rule.DateOfBirthValidationRule;
import intact.lab.autoquote.backend.validation.rule.EmailAddressValidationRule;
import intact.lab.autoquote.backend.validation.rule.GenderValidationRule;
import intact.lab.autoquote.backend.validation.rule.NameValidationRule;
import intact.lab.autoquote.backend.validation.rule.PhoneNumberValidationRule;
import intact.lab.autoquote.backend.validation.rule.ValidationUtilities;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.List;

@Component("PartyDTOValidator")
public class PartyDTOValidator implements IPartyDTOValidator {

	private final IAddressDTOValidator addressDTOValidator;
	private final IPartyRoleDTOValidator partyRoleDTOValidator;
	private final IConsentDTOValidator consentDTOValidator;

	public PartyDTOValidator(
			@Qualifier("AddressDTOValidator") IAddressDTOValidator addressDTOValidator,
			@Qualifier("PartyRoleDTOValidator") IPartyRoleDTOValidator partyRoleDTOValidator,
			@Qualifier("ConsentDTOValidator") IConsentDTOValidator consentDTOValidator) {
		this.addressDTOValidator = addressDTOValidator;
		this.partyRoleDTOValidator = partyRoleDTOValidator;
		this.consentDTOValidator = consentDTOValidator;
	}

	@Override
	public void validate(PartyDTO partyDTO, Errors errors, ValidationContext context) {
		PartyTypeEnum partyType = partyDTO.getPartyType();
		this.validatePartyType(partyType, errors); // validate early: needed for some of the validations below
		this.validateId(partyDTO.getId(), errors);

		if (null != partyType) { // will not be able to validate other fields without partyTpe (null pointers)
			this.validateDateOfBirth(partyDTO.getDateOfBirth(), partyType, errors);
			this.validateName(partyDTO.getFirstName(), partyDTO.getLastName(), partyType, errors);
			this.validateGender(partyDTO.getGender(), partyType, errors);
			this.validateUnstructuredName(partyDTO.getUnstructuredName(), partyType, errors);
			this.validateAddress(partyDTO.getAddress(), partyType, errors, context);
			this.validateEmailAddress(partyDTO.getEmailAddress(), errors);
			this.validatePhoneNumber(partyDTO.getPhoneNumber(), partyType, errors);
			this.validatePartyRoles(partyDTO.getPartyRoles(), partyType, errors);
		}
	}

	private void validateGender(String gender, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (!PartyTypeEnum.COMPANY.equals(partyTypeEnum)) { // no gender required for company party
			GenderValidationRule.validate(gender, errors);
		}
	}

	// TODO update validation rules for consent when the story is implemented; for now, be relatively permissive
	public void validateConsents(List<ConsentDTO> consents, PartyTypeEnum partyTypeEnum, Errors errors) {

		if (null != consents && !consents.isEmpty()) {
			int index = 0;
			for (ConsentDTO consent : consents) {
				String nestedPath = ValidationUtilities.buildNestedPath(index, "consents");
				if (null != consent) {
					errors.pushNestedPath(nestedPath);
					this.consentDTOValidator.validate(consent, errors);
					errors.popNestedPath();
				} else {
					errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
				}
				++index;
			}
		} else if (partyTypeEnum.equals(PartyTypeEnum.COMPANY)) {
			errors.rejectValue("consents", BRulesExceptionEnum.NotBlank.getErrorCode(), "[consents]");
		}
	}

	private void validatePartyRoles(List<PartyRoleDTO> partyRoles, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (!partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { // party roles do not apply to company
			if (null != partyRoles && !partyRoles.isEmpty()) {
				int index = 0;
				for (PartyRoleDTO partyRole : partyRoles) {
					String nestedPath = ValidationUtilities.buildNestedPath(index, "partyRoles");
					if (null != partyRole) {
						errors.pushNestedPath(nestedPath);
						this.partyRoleDTOValidator.validate(partyRole, errors);
						errors.popNestedPath();
					} else {
						errors.rejectValue(nestedPath, BRulesExceptionEnum.NotBlank.getErrorCode(), ValidationUtilities.bracket(nestedPath));
					}
					++index;
				}
			} else {
				errors.rejectValue("partyRoles", BRulesExceptionEnum.NotBlank.getErrorCode(), "[partyRoles]");
			}
		}
	}

	private void validatePhoneNumber(String phoneNumber, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { // phoneNumber only used used for company party
			PhoneNumberValidationRule.validate(phoneNumber, errors, "phoneNumber");
		}
	}

	private void validateEmailAddress(String emailAddress, Errors errors) {
		if (!StringUtils.isEmpty(emailAddress)) {
			if (emailAddress.length() <= 64) {
				EmailAddressValidationRule.validate(errors, emailAddress);
			} else {
				errors.rejectValue("emailAddress", "LENGTH_64", "[emailAddress]");
			}
		}
		// else no error: email is optional
	}

	private void validateAddress(AddressDTO address, PartyTypeEnum partyTypeEnum, Errors errors, ValidationContext context) {
		if (partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { // address only used used for company party
			if (null != address) {
				errors.pushNestedPath("address");
				this.addressDTOValidator.validate(address, errors, context);
				errors.popNestedPath();
			} else {
				errors.rejectValue("address", BRulesExceptionEnum.NotBlank.getErrorCode(), "[address]");
			}
		}
	}

	private void validateId(Integer id, Errors errors) {
		if (null == id) {
			errors.rejectValue("id", BRulesExceptionEnum.NotBlank.getErrorCode(), "[id]");
		}
	}

	private void validateUnstructuredName(String unstructuredName, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { //unstructuredName used for company party
			NameValidationRule.validateUnstructured(unstructuredName, errors);
		}
	}

	private void validatePartyType(PartyTypeEnum partyType, Errors errors) {
		if (null == partyType) {
			errors.rejectValue("partyType", BRulesExceptionEnum.NotBlank.getErrorCode(), "[partyType]");
		}
	}

	private void validateName(String firstName, String lastName, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (!partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { //names used for persons-type party
			NameValidationRule.validate(firstName, lastName, errors);
		}
	}

	private void validateDateOfBirth(LocalDate dateOfBirth, PartyTypeEnum partyTypeEnum, Errors errors) {
		if (!partyTypeEnum.equals(PartyTypeEnum.COMPANY)) { // birth dates do not apply to companies
			if (null != dateOfBirth) {
				// using existing Personal-line logic for consistency; could rework and modernize to Joda
				String year = Integer.toString(dateOfBirth.getYear());
				String month = Integer.toString(dateOfBirth.getMonthOfYear());
				String day = Integer.toString(dateOfBirth.getDayOfMonth());
				DateOfBirthValidationRule.validate(year, month, day, "dateOfBirth", errors);
			} else {
				errors.rejectValue("dateOfBirth", BRulesExceptionEnum.NotBlank.getErrorCode(), "[dateOfBirth]");
			}
		}
	}

}
