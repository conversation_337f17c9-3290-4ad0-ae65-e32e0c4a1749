/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.exception;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;

/**
 * The Class SingleIdActiveProductException is a AutoquoteRoadBlockException but only for singleId.
 * 
 * <AUTHOR>
 */
public class SingleIdActiveProductException extends RoadBlockException {

	/** The Constant serialVersionUID. */
	private static final long serialVersionUID = -9141869209562198325L;

	private AutoquoteRoadBlockExceptionEnum autoquoteRoadBlockExceptionEnum;

	/**
	 * The Constructor.
	 * 
	 * @param aAutoquoteRoadBlockExceptionEnum the autoquote road block exception enum
	 * @param aContext the a context
	 */
	public SingleIdActiveProductException(AutoquoteRoadBlockExceptionEnum aAutoquoteRoadBlockExceptionEnum,
                                          RoadBlockExceptionContextEnum aContext) {
		super(aAutoquoteRoadBlockExceptionEnum.getCode(), aContext.name());
		this.autoquoteRoadBlockExceptionEnum = aAutoquoteRoadBlockExceptionEnum;
	}

	/**
	 * Gets the autoquote road block exception enum.
	 * 
	 * @return the autoquote road block exception enum
	 */
	public AutoquoteRoadBlockExceptionEnum getAutoquoteRoadBlockExceptionEnum() {
		return this.autoquoteRoadBlockExceptionEnum;
	}

	/**
	 * Sets the single id active product exception business rule.
	 * 
	 * @param aAutoquoteRoadBlockExceptionEnum the new single id active product exception business rule
	 */
	public void setSingleIdActiveProductExceptionBusinessRule(
			AutoquoteRoadBlockExceptionEnum aAutoquoteRoadBlockExceptionEnum) {
		this.autoquoteRoadBlockExceptionEnum = aAutoquoteRoadBlockExceptionEnum;
	}
}
