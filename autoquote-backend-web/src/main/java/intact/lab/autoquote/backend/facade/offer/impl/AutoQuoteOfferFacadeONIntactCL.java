package intact.lab.autoquote.backend.facade.offer.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.coverage.BaseCoverage;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.business.rules.exception.BusinessRuleException;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.business.rules.offer.BR5799_EligibilityToBindON;
import com.intact.business.rules.vehicle.BR16593_MissingRateGroup;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.enums.ComRoadBlockTypeEnum;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.com.util.ComRoadBlock;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AutoQuoteOfferFacadeONIntactCL extends AutoQuoteOfferFacade {

    @Autowired
    private BR16593_MissingRateGroup br16593;

    @Autowired
    protected IPolicyVersionHelper plpPolicyVersionHelper;

    @Autowired
    protected IPolicyVersionService policyVersionService;

    @Autowired
    private BloomMQHandlerService bloomMQHandlerService;

    @Autowired
    private BR5799_EligibilityToBindON br5799;

    @Override
    @Transactional
    public CommunicationObjectModel retrieveOffer(CommunicationObjectModel aCom) throws AutoquoteFacadeException {
        CommunicationObjectModel quote = super.retrieveOffer(aCom);

        if(CollectionUtils.isNotEmpty(quote.getRoadblock())){
            bloomMQHandlerService.sendMessageToMQ(aCom.getUuId(), MediatorUtils.convertContext(aCom.getContext()).getManufacturerCompany());
            this.validateRoadblocks(quote);
            return quote;
        }

        // Create roadblock on CVI
        try {
            this.br5799.validate(this.getPolicyVersion(quote.getPolicyVersionId()));
        } catch (BusinessRuleException e) {
            ComRoadBlock rb = new ComRoadBlock("BR256_1", ComRoadBlockTypeEnum.HARD);
            List<ComRoadBlock> roadblocks = new ArrayList<ComRoadBlock>();

            roadblocks.add(rb);
            quote.setRoadblock(roadblocks);
            quote.getState().setMonthlyPaymentsEligible(false);
        }

        try {
            // We simulate an offer selection made by the user,
            // then to proceed, we unselect the systemSelectedInd flag
            // and apply a selection over the only offer we have.
            undoInsuranceRiskOfferSystemSelectedInd(quote);

            PolicyVersion pv = this.getPolicyVersion(quote.getPolicyVersionId());
            this.offerBusinessProcess.selectOffer(pv, true);
            this.updateUBI(quote, pv);
            this.addSurcharge(quote);
            this.transactionHistoryService.updateTransactionHistory(ComEventEnum.SAVE_OFFER, pv);
            bloomMQHandlerService.sendMessageToMQ(pv.getInsurancePolicy().getUuId(), pv.getInsurancePolicy().getManufacturerCompany());
        } catch (Exception e) {
            throw new AutoquoteFacadeException(e);
        }
        this.validateRoadblocks(quote);
        return quote;
    }

    @Override
    public void validateRoadblocks(CommunicationObjectModel aCom) {
        PolicyVersion policyVersion = this.getPolicyVersion(aCom.getPolicyVersionId());
        Vehicle vehicle = policyVersion.getRisk(1).getVehicle();
        RuleExceptionResult result = this.br16593.validateBR16593(vehicle);

        if (result != null && result.hasFailed()) {
            ComRoadBlock rb = new ComRoadBlock("BR16593", ComRoadBlockTypeEnum.HARD);
            List<ComRoadBlock> roadblocks = new ArrayList<ComRoadBlock>();

            roadblocks.add(rb);
            aCom.setRoadblock(roadblocks);
        }
    }

    private void undoInsuranceRiskOfferSystemSelectedInd(CommunicationObjectModel quote) {
        PolicyVersion pv = this.getPolicyVersion(quote.getPolicyVersionId());
        Set<InsuranceRisk> irs = pv.getInsuranceRisks();
        for (InsuranceRisk insuranceRisk : irs) {
            insuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(Boolean.FALSE);
        }
    }

    private void updateUBI(CommunicationObjectModel com, PolicyVersion policyVersion) {
        InsuranceRisk ir = policyVersion.getInsuranceRisks().iterator().next();
        if (ir == null) {
            return;
        }

        InsuranceRiskOffer selectedOffer = policyVersion.getInsuranceRisks().iterator().next().getSelectedInsuranceRiskOffer();
        boolean isNotSelected = selectedOffer == null || ir.getInsuranceRiskOfferSystemSelectedIndicator();
        OfferTypeCodeEnum type = isNotSelected ? null : selectedOffer.getOfferType();

        if (type != null) {
            BaseCoverage ubi = this.coverageHelper.getSelectedEndorsement(selectedOffer.getCoverageOffers(), EndorsementCodeEnum.UE05);
            if (ubi != null && ubi.getCoverageSelectedIndicator()) {
                com.getState().setCanDisplayUBI(Boolean.TRUE);
            }
        }
    }

    protected void addSurcharge(CommunicationObjectModel aCom) {
        if (aCom.getVehicles().getFirst().getCurrentOffer() != null) {
            aCom.getVehicles().getFirst().getCurrentOffer().setSurcharge(!this.plpPolicyVersionHelper.isCombinedPolicy(this.policyVersionService.findById(aCom.getPolicyVersionId())));
        }
    }
}
