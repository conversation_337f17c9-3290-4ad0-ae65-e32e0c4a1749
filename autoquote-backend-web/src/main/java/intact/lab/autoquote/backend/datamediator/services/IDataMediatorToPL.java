package intact.lab.autoquote.backend.datamediator.services;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

public interface IDataMediatorToPL {

    PolicyVersion convertTo_PL(com.ing.canada.som.interfaces.agreement.PolicyVersion var1, PolicyVersion var2, boolean var3);

    PolicyVersion convertTo_PL(com.ing.canada.som.interfaces.agreement.PolicyVersion var1, PolicyVersion var2, boolean var3, boolean var4);

    void setReferenceOffer(OfferTypeCodeEnum var1);

    void setIsKeepNonEligibleCoverages(boolean var1);
}
