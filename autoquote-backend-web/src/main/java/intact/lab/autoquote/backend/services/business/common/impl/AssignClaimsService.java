/*
 * Important notice: This software is the sole property of Intact Insurance Inc.
 * and cannot be distributed and/or copied without the written permission of Intact Insurance Inc.
 *
 * Copyright (c) 2009, Intact Insurance Inc., All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.services.impl.pegaservices.PegaBaseService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.delegate.services.GenericDelegate;
import intact.lab.autoquote.backend.services.business.common.IAssignClaimsService;
import org.springframework.stereotype.Service;

/**
 * The Class AssignClaimsService.
 * 
 * <AUTHOR>
 */
@Service
public class AssignClaimsService extends PegaBaseService implements IAssignClaimsService {

	/** Service properties */
	private static final String COMPONENT_NAME = "UNDERWRITING";

	private static final String SERVICE_NAME = "ASSIGN_CLAIMS";

	private static final String SERVICE_VERSION = "0.00";

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PolicyVersion assignClaims(PolicyVersion aSomPolicyVersion, ManufacturingContext aContext) {
		try {

			// Create the assignation service.
			GenericDelegate createAssignClaimService = new GenericDelegate(COMPONENT_NAME, SERVICE_NAME,
					SERVICE_VERSION, aSomPolicyVersion, getDelegateParameters(aContext));

			// Call the service
			PolicyVersion policyVersion = (PolicyVersion) createAssignClaimService.executeService();

			// Handle the response
			return policyVersion;

		} catch (BaseException ex) {
			throw new SystemException("", ex);
		} catch (Exception ex) {
			throw new SystemException("", ex);
		}
	}
}
