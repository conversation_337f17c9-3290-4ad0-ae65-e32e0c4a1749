package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteVehicleException;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.Iterator;
import java.util.List;

@Component
public class ModelValidationRule {

	private final IVehicleFacade vehicleFacade;

	public ModelValidationRule(IVehicleFacade vehicleFacade) {
		this.vehicleFacade = vehicleFacade;
	}

	public void validate(int year, String make, String model, String company, String province, String language, Errors errors) {
		try {
			List<ModelDTO> listModel = vehicleFacade.getVehicleModels(String.valueOf(year), make, province, language);
			Iterator<ModelDTO> itr = listModel.iterator();
			while (itr.hasNext()) {
				if (itr.next().getCode().equals(model)) {
					return;
				}
			}
		} catch (AutoQuoteVehicleException e) {
		} catch (Error e) {
			throw new AutoQuoteException(e.getMessage(), e);
		}
		errors.rejectValue("model", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode(), "[model]");
	}

}
