package intact.lab.autoquote.backend.config;

import org.apache.catalina.Context;
import org.apache.tomcat.util.descriptor.web.ContextResource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jmx.export.annotation.AnnotationMBeanExporter;

import javax.sql.DataSource;


/**
 * Configuration for persistence context (used for plp - see plp-services.properties).
 */
@Configuration
@ImportResource({"classpath:plp-services-beans.xml", "classpath:cif-services-beans.xml"})
public class TomcatJndiConfig {

    @Value("${database.plp.jdbc.jndi-name}")
    private String jndiName;

    @Value("${database.plp.jdbc.initialSize}")
    private String initialSize;

    @Value("${database.plp.jdbc.maxTotal}")
    private String maxTotal;

    @Value("${database.plp.jdbc.maxIdle}")
    private String maxIdle;

    @Value("${database.plp.user}")
    private String plpUser;

    @Value("${database.plp.password}")
    private String plpPassword;

    @Value("${database.plp.url}")
    private String plpUrl;

    @Value("${database.cif.jdbc.jndi-name}")
    private String cifJndiName;

    @Value("${database.cif.jdbc.initialSize}")
    private String cifInitialSize;

    @Value("${database.cif.jdbc.maxTotal}")
    private String cifMaxTotal;

    @Value("${database.cif.jdbc.maxIdle}")
    private String cifMaxIdle;

    @Value("${database.cif.user}")
    private String cifUser;

    @Value("${database.cif.password}")
    private String cifPassword;

    @Value("${database.cif.url}")
    private String cifUrl;

    @Bean
    public TomcatServletWebServerFactory tomcatFactory() {
        return new TomcatServletWebServerFactory() {
            @Override
            protected TomcatWebServer getTomcatWebServer(org.apache.catalina.startup.Tomcat tomcat) {
                tomcat.enableNaming();
                return super.getTomcatWebServer(tomcat);
            }

            @Override
            protected void postProcessContext(Context context) {

                getContextResource(context, jndiName, plpUrl, plpUser, plpPassword, initialSize, maxTotal, maxIdle);
                getContextResource(context, cifJndiName, cifUrl, cifUser, cifPassword, cifInitialSize, cifMaxTotal, cifMaxIdle);
            }
        };
    }

    private void getContextResource(Context context, String cifJndiName, String cifUrl, String cifUser, String cifPassword, String cifInitialSize, String cifMaxTotal, String cifMaxIdle) {
        ContextResource cifResource = new ContextResource();
        cifResource.setName(cifJndiName);
        cifResource.setType("javax.sql.DataSource");
        cifResource.setProperty("driverClassName", "oracle.jdbc.OracleDriver");
        cifResource.setAuth("Container");
        cifResource.setProperty("url", cifUrl);
        cifResource.setProperty("username", cifUser);
        cifResource.setProperty("password", cifPassword);
        cifResource.setProperty("initialSize", cifInitialSize);
        cifResource.setProperty("maxTotal", cifMaxTotal);
        cifResource.setProperty("maxIdle", cifMaxIdle);
        cifResource.setProperty("validationQuery", "SELECT 1 FROM DUAL");

        context.getNamingResources().addResource(cifResource);
    }

    @Bean
    public AnnotationMBeanExporter annotationMBeanExporter() {
        AnnotationMBeanExporter annotationMBeanExporter = new AnnotationMBeanExporter();
        annotationMBeanExporter.addExcludedBean("plpDataSource");
        return annotationMBeanExporter;
    }

    @Bean(name = "cifJdbcTemplate")
    public JdbcTemplate cifJdbcTemplate(@Qualifier("cifDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
