package intact.lab.autoquote.backend.facade;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;

import java.util.List;

// FIXME RENAME IBaseFacade to avoid confusion with BaseFacade these are not the same classes.

/**
 * <AUTHOR> L.
 *
 */
public interface IBaseFacade {

	/**
	 * Segmentation - Update Pattern Behaviors
	 * 
	 * @param aPolicyVersion {@link PolicyVersion}
	 * @param com {@link CommunicationObjectModel}
	 */
	void updatePatternBehaviors(PolicyVersion aPolicyVersion, CommunicationObjectModel com);
	
	/**
	 * Returns a list of types of offer that should be mediated.
	 * @param comContext 
	 * @return a list of {@link OfferTypeCodeEnum}
	 * */
	List<OfferTypeCodeEnum> getOfferTypes(ComContext comContext);
}
