package intact.lab.autoquote.backend.config;

import com.intactfc.bloom.mq.api.ApiClient;
import com.intactfc.bloom.mq.api.MqEventServiceApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class BloomConfig {

    @Bean
    public ApiClient apiClient(@Value("${bloom-mq-handler-service.url}") String basePath) {
        ApiClient client = new ApiClient();
        client.setBasePath(basePath);
        return client;
    }

    @Bean(name = "mqEventServiceApi")
    public MqEventServiceApi mqEventServiceApi(ApiClient apiClient) {
        MqEventServiceApi api = new MqEventServiceApi();
        api.setApiClient(apiClient);
        return api;
    }

    @Bean(name = "MQThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor mqThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setWaitForTasksToCompleteOnShutdown(false);
        return executor;
    }
}
