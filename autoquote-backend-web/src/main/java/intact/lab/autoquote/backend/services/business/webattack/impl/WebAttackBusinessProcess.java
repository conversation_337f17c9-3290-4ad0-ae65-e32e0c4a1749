/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.webattack.impl;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.intact.globaladmin.domain.MonitoredEventLog;
import com.intact.globaladmin.domain.enums.ApplicationIdEnum;
import com.intact.globaladmin.domain.enums.EventTypeEnum;
import com.intact.globaladmin.domain.enums.IpTypeEnum;
import com.intact.globaladmin.domain.enums.Province;
import com.intact.globaladmin.domain.enums.UnderwritingCompanyEnum;
import com.intact.globaladmin.service.IWebAttackController;
import intact.lab.autoquote.backend.services.business.webattack.IWebAttackBusinessProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * 
 */
@Component
public class WebAttackBusinessProcess implements IWebAttackBusinessProcess {
	
	@Autowired
	protected IWebAttackController webAttackController;
	
	@Override
	public void keepTraceOfThisCall(ManufacturingContext context , String applicationId, String clientIp, String ipType, String eventType, String parameterKey1, String trackingNbr) {
		MonitoredEventLog eventLog = new MonitoredEventLog();
		
		eventLog.setApplicationId(ApplicationIdEnum.valueOfCode(applicationId));
		
		//ParameterKey1 can be null (In the case of a rate)
		//ParameterKey1 can contains the driver licence number (In the case of a retrieve report)
		eventLog.setEventParameterKey1(parameterKey1);
		
		eventLog.setEventType(EventTypeEnum.valueOfCode(eventType));
		eventLog.setIpAddressNumber(clientIp);
		eventLog.setIpType(IpTypeEnum.valueOfCode(ipType));
		eventLog.setProvince(Province.valueOfCode(context.getProvince().getCode()));
		eventLog.setTrackingNumber(trackingNbr);
		
		//todo remove this line before prod
		//eventLog.setUnderwritingCompany(UnderwritingCompanyEnum.B);
		eventLog.setUnderwritingCompany(UnderwritingCompanyEnum.valueOfCode(context.getManufacturerCompany().getCode()));
		
		this.webAttackController.createMonitoredEventLog(eventLog);
	}
}
