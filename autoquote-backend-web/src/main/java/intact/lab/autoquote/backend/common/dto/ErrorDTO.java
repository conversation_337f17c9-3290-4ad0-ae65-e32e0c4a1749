package intact.lab.autoquote.backend.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import intact.lab.autoquote.backend.common.enums.ErrorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ErrorDTO {

    private String code;
    private String description;
    private ErrorTypeEnum type;
    private List<ErrorSourceDTO> errorSources;

    public void addErrorSource(String field, String path) {
        ErrorSourceDTO error = new ErrorSourceDTO(field, path);
        errorSources.add(error);
    }
}
