package intact.lab.autoquote.backend.facade.impl;

import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.common.enums.WebSiteOriginEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.facade.IDistributorFacade;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class DistributorFacade implements IDistributorFacade {

	private static final Logger LOG = ESAPI.getLogger(DistributorFacade.class);

	@Resource(name = "comDistributorConverter")
	private ICOMConverter<DistributorDTO, ComBrokerInfo> comDistributorConverter;
	private final BrokerService brokerService;

	DistributorFacade(BrokerService brokerService) {
		this.brokerService = brokerService;
	}

	@Override
	public DistributorDTO retrieveBrokerInfo(final String apiKey, final String language, final String province,
											 final String postalCode, final String subBrokerNo, final String origin) {

		DistributorDTO distributor = new DistributorDTO();
		final ComContext comCtx = ContextUtil.loadInitialComContext(postalCode, province, language, subBrokerNo, WebSiteOriginEnum.valueOf(origin), null);
		comCtx.setLineOfBusiness(ComLineOfBusinessCodeEnum.COMMERCIAL_LINES);

		ContextUtil.validateInitialisation(comCtx);

		try {
			ComBrokerInfo comBrokerInfo = this.brokerService.getBrokerInfo(comCtx);
			if (comBrokerInfo != null) {

				distributor = comDistributorConverter.toDTO(comBrokerInfo);
			}

		} catch (AutoQuoteException e) {
			LOG.error(Logger.EVENT_FAILURE, ">> Facade : Exception occurred while retrieveBrokerInfo...", e);
			throw e;
		}

		return distributor;
	}
}
