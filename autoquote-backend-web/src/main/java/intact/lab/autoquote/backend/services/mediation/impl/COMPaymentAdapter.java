/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2014, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.services.mediation.ICOMPaymentAdapter;
import org.springframework.stereotype.Component;

@Component
public abstract class COMPaymentAdapter implements ICOMPaymentAdapter {

	protected static final int FIRST_PAYMENT_RATIO = 2;

	protected IQuotationService quotationService;

	public COMPaymentAdapter(IQuotationService quotationService) {
		this.quotationService = quotationService;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails, PolicyVersion policyVersion) {
		this.quotationService.roundUpTheQuoteNumber(quotationDetails);
	}
}
