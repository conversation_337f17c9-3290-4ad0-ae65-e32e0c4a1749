package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.driver.ComDriver;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.enums.ObjectTypeEnum;
import intact.lab.autoquote.backend.common.enums.RoleTypeEnum;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("comPartyRoleConverter")
public class COMPartyRoleConverter implements ICOMConverter<QuoteDTO, CommunicationObjectModel> {

	public QuoteDTO toDTO(CommunicationObjectModel com, QuoteDTO quote) {
		// If driver is not a driver, it is a owner
		for (ComDriver driver : com.getDrivers()) {
			PartyDTO party = (PartyDTO) this.findObjectById(driver.getWebMsgId(), ObjectTypeEnum.PARTY, quote);
			if (party != null) {
				PartyRoleDTO partyRole = new PartyRoleDTO();
				partyRole.setRoleType(driver.getIsDriver() ? RoleTypeEnum.PRINCIPAL_DRIVER : RoleTypeEnum.BUSINESS_OWNER);
				partyRole.setPartyId(driver.getWebMsgId());
				party.getPartyRoles().add(partyRole);
			}
		}

		// If vehicle has a principalDriver --> set RoleTypeEnum.PRINCIPAL_DRIVER with vehicle and party reference
		for (ComVehicle vehicle : com.getVehicles()) {
			Integer principalDriverId = vehicle.getPrincipalDriver();
			if (principalDriverId != null) {
				PartyDTO principalDriver = this.findPrincipalDriver(vehicle, com, quote);
				if (principalDriver != null) {
					for (PartyRoleDTO partyRole : principalDriver.getPartyRoles()) {
						if (partyRole.getRoleType().equals(RoleTypeEnum.PRINCIPAL_DRIVER)) {
							partyRole.setRiskId(vehicle.getWebMsgId());
						}
					}
				}
			}
		}
		return quote;
	}

	@Override
	public QuoteDTO toDTO(CommunicationObjectModel com) {
		throw new NotImplementedException("This method must not be used. Please use toDTO with two parameters instead.");
	}

	@Override
	public CommunicationObjectModel toCOM(QuoteDTO dto,
										  CommunicationObjectModel initialCOM) {
		for (PartyDTO party : dto.getParties()) {
			if (party.getPartyRoles() != null && party.getPartyRoles().size() > 0) {
				for (PartyRoleDTO partyRole : party.getPartyRoles()) {
					// set principalDriver
					if (RoleTypeEnum.PRINCIPAL_DRIVER.equals(partyRole.getRoleType())) {
						ComDriver driver = (ComDriver) this.findObjectById(party.getId(), ObjectTypeEnum.PARTY, initialCOM);
						ComVehicle vehicle = (ComVehicle) this.findObjectById(partyRole.getRiskId(), ObjectTypeEnum.VEHICLE, initialCOM);
						if (driver != null && vehicle != null) {
							driver.setIsDriver(Boolean.TRUE);
							driver.setDriverId(1);
							vehicle.setPrincipalDriver(1);
						}
					}
					if (RoleTypeEnum.BUSINESS_OWNER.equals(partyRole.getRoleType())) {
						if (partyRole.getPartyId() == null) continue;
						ComDriver driver = (ComDriver) this.findObjectById(partyRole.getPartyId(), ObjectTypeEnum.PARTY, initialCOM);
						if (driver != null) {
							driver.setPolicyHolderInd(Boolean.TRUE);
						}
					}

				}
			} else {
				// no partyRoles
				ComDriver driver = (ComDriver) this.findObjectById(party.getId(), ObjectTypeEnum.PARTY, initialCOM);
				if (driver != null) {
					driver.setIsDriver(Boolean.FALSE);
				}
			}
		}
		return initialCOM;
	}


	private Object findObjectById(int id, ObjectTypeEnum dtoType, CommunicationObjectModel com) {
		switch (dtoType) {
			case PARTY:
				for (ComDriver driver : com.getDrivers()) {
					if (driver.getWebMsgId() == id) {
						return driver;
					}
				}
				return null;
			case VEHICLE:
				for (ComVehicle vehicle : com.getVehicles()) {
					if (vehicle.getWebMsgId() == id) {
						return vehicle;
					}
				}
				return null;
		}
		return null;
	}

	private Object findObjectById(Integer partyId, ObjectTypeEnum objectType, QuoteDTO quoteDTO) {
		for (PartyDTO party : quoteDTO.getParties()) {
			if (party.getId() == partyId) {
				return party;
			}
		}
		return null;
	}

	private ComDriver findDriverById(List<ComDriver> drivers, int principalDriverId) {
		for (ComDriver driver : drivers) {
			if (driver.getDriverId() == principalDriverId) {
				return driver;
			}
		}
		return null;
	}

	private PartyDTO findPrincipalDriver(ComVehicle vehicle, CommunicationObjectModel com, QuoteDTO quote) {
		Integer principalDriverId = vehicle.getPrincipalDriver();
		PartyDTO principalDriver = null;

		if (principalDriverId != null) {
			ComDriver comDriver = this.findDriverById(com.getDrivers(), principalDriverId);
			if (comDriver != null) {
				principalDriver = (PartyDTO) this.findObjectById(comDriver.getWebMsgId(), ObjectTypeEnum.PARTY, quote);
			}
		}
		return principalDriver;
	}
}
