package intact.lab.autoquote.backend.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class QuoteDTO  {

    @JsonIgnore
    private ContextDTO contextDTO;

    @JsonIgnore
    private String clientXForwardIPNbr;

    private String id;
    private String number;
    private String pvId;
    private String subBrokerNbr;

    @Valid
    private List<PartyDTO> parties = new ArrayList<>();

    private List<PolicyHolderDTO> policyHolders = new ArrayList<>();
    private List<VehicleDTO> risks = new ArrayList<>();
    private List<DriverDTO> drivers = new ArrayList<>();

    private String policyDiscountCode;
}
