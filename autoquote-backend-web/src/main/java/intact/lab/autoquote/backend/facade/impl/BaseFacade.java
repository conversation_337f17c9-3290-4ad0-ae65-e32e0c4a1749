package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.util.SystemUtils;
import com.ing.canada.common.util.holiday.HolidayManager;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ApplicationIdEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PartyTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.ICoverageHelper;
import com.ing.canada.plp.helper.enums.PaymentPlanEnum;
import com.ing.canada.plp.helper.impl.InsuranceRiskOfferHelper;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComDistributor;
import com.intact.com.enums.ComOfferTypeCodeEnum;
import com.intact.com.enums.ComRoadBlockTypeEnum;
import com.intact.com.enums.ComUrlEnum;
import com.intact.com.state.ComState;
import com.intact.com.state.enums.ComStateEnum;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.com.util.ComRoadBlock;
import com.intact.com.util.ComValidationError;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorPaymentPlp;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import com.intact.common.web.security.webattack.WebAttackAnalyser;
import com.intact.moneris.enums.ClientTypeEnum;
import com.intact.moneris.services.api.externalpayment.IExternalPaymentService;
import com.intact.moneris.services.api.externalpayment.PaymentStoreEnum;
import com.intact.moneris.services.impl.java.externalpayment.MonerisConfig;
import com.intact.moneris.services.impl.java.externalpayment.MonerisConfigHelper;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.utils.QuoteValidationUtil;
import intact.lab.autoquote.backend.services.ICloneService;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.offer.IOfferBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.facade.IBaseFacade;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import intact.lab.autoquote.backend.services.business.usage.IUsageBusinessProcess;
import intact.lab.autoquote.backend.services.business.webattack.IWebAttackBusinessProcess;
import intact.lab.autoquote.backend.services.mediation.ICOMPaymentAdapter;
import intact.lab.autoquote.backend.services.mediation.ICOMtoPLAdapter;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Set;


@Slf4j
public class BaseFacade {

    private static final Logger LOG = ESAPI.getLogger(BaseFacade.class);

    public static final String SUB_ACTIVITY_ROAD_BLOCK_CODE = "ROAD_BLOCK";
    protected static final String EVENT_TYPE_RATINGREQ = "RATINGREQ";

    @AutowiredLocal
    protected IBaseFacade baseFacade;

    @Autowired
    protected IMediatorComPlp mediatorCom;

    @AutowiredLocal
    protected IQuoteStateManager quoteStateManager;

    @Autowired
    protected ICoverageHelper coverageHelper;

    @Autowired
    private InsuranceRiskOfferHelper insuranceRiskOfferHelper;

    @Autowired
    protected ICommonBusinessProcess commonBusinessProcess;

    @Autowired
    protected IConfigurator configurator;

    @AutowiredLocal
    protected ICOMPaymentAdapter comPaymentAdapter;

    @AutowiredLocal
    protected ICOMtoPLAdapter comToPLAdapter;

    @Autowired
    protected IMediatorPaymentPlp mediatorPaymentPlp;

    @Autowired
    private IExternalPaymentService externalPaymentService;

    @Autowired
    private HolidayManager holiday;

    @Autowired
    protected IPolicyVersionService policyVersionService;

    @Autowired
    protected IFormFieldValidatorService formFieldValidatorService;

    @Autowired
    protected ICloneService cloneService;

    @AutowiredLocal
    protected IUsageBusinessProcess usageBusinessProcess;

    @Autowired
    protected ITransactionHistoryService transactionHistoryService;

    @Autowired
    protected WebAttackAnalyser webAttackAnalyser;

    @AutowiredLocal
    protected IOfferBusinessProcess offerBusinessProcess;

    @AutowiredLocal
    protected IDriverBusinessProcess driverBusinessProcess;

    @Autowired
    protected IWebAttackBusinessProcess webAttackBusinessProcess;

    /**
     * Populate communication object model from a provide policy version.
     *
     * @param aPolicyVersion {@link PolicyVersion} the PL policy version to convert
     * @param origCom        {@link CommunicationObjectModel} to populate
     * @return {@link CommunicationObjectModel}
     */
    protected CommunicationObjectModel buildCom(PolicyVersion aPolicyVersion, CommunicationObjectModel origCom){
        CommunicationObjectModel com = null;

        List<OfferTypeCodeEnum> offerType = this.defineOfferTypes(origCom.getState(), origCom.getContext());
        com = this.mediatorCom.convertPLPtoCOM(aPolicyVersion, origCom.getContext(), offerType);
        com.setPolicyAssignDate(origCom.getPolicyAssignDate()); // pass back DE2287 to new com for roadblock validation

        try {
            BeanUtils.copyProperties(com.getState(), origCom.getState());

            this.quoteStateManager.updateState(com.getState(), aPolicyVersion);

            // updatestate sets roadblock indicator to null, so do this after calling updateState
            this.copyRoadblocks(com, origCom);

            this.baseFacade.updatePatternBehaviors(aPolicyVersion, com);

            if (com.getState() != null && BooleanUtils.isTrue(com.getState().isCanBind())) {
                this.initializeBind(com, aPolicyVersion);
            }
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("An error occured while copying ComState properties. {}", e);
        }

        com.getState().setCanDisplayUBI(this.isDisplayUBISection(com, aPolicyVersion));
        this.populateUbiVersion(com);

        if (com.getState() != null && ComStateEnum.PURCHASE.equals(com.getState().getCurrentState())) {
            this.mediatorPaymentPlp.fillPostPaymentDetails(com, aPolicyVersion);
        }

        String provinceCode = com.getContext().getProvince().getCode();
        com.setOnHolidayOpeningHours(this.holiday.isOnHolidayOpeningHours(ProvinceCodeEnum.fromCode(provinceCode)));
        com.setHolidays(this.holiday.getHolidays(ProvinceCodeEnum.fromCode(provinceCode)));

        return com;
    }

    /**
     * Retrieves the PolicyVersion from the database or from the persistence cache (Hibernate).
     *
     * @return the current PolicyVersion
     * @throws IllegalArgumentException an {@link IllegalArgumentException}
     */
    protected PolicyVersion getPolicyVersion(Long policyVersionId) throws IllegalArgumentException {
        Assert.notNull(policyVersionId, "policyVersionId is required to retrieve a policy version");

        PolicyVersion policyVersion = this.commonBusinessProcess.loadPolicyVersion(policyVersionId);

        return policyVersion;
    }

    /**
     * QQ Belair: the types of offers returned are decided by the the frontend via the ComState object, WHAT??!!!!
     * <p>
     * AQ Belair: the types of offers returned are decided by the backend (BaseFacade) QQ Intact: the types of offers
     * returned are decided by the backend (BaseFacade)
     *
     * @param quoteState {@link ComState}
     * @param comContext {@link ComContext}
     * @return list of {@link OfferTypeCodeEnum}
     */
    protected List<OfferTypeCodeEnum> defineOfferTypes(final ComState quoteState, ComContext comContext) {

        // 1- try to retrieve offer types from the provided ComState
        List<ComOfferTypeCodeEnum> comOfferTypes = quoteState != null ? quoteState.getOfferTypes() : null;
        if (!CollectionUtils.isEmpty(comOfferTypes)) {
            return this.convertOfferType(comOfferTypes);
        }

        // 2- otherwise return default
        return this.baseFacade.getOfferTypes(comContext);
    }

    private List<OfferTypeCodeEnum> convertOfferType(List<ComOfferTypeCodeEnum> comOfferTypeCodeEnum) {
        List<OfferTypeCodeEnum> offerTypeCodeEnum = new ArrayList<>();
        for (ComOfferTypeCodeEnum comOfferTypeEnum : comOfferTypeCodeEnum) {
            offerTypeCodeEnum.add(OfferTypeCodeEnum.valueOf(comOfferTypeEnum.name()));
        }
        return offerTypeCodeEnum;
    }

    private void copyRoadblocks(CommunicationObjectModel com, CommunicationObjectModel origCom) {
        com.getRoadblock().addAll(origCom.getRoadblock()); // getRoadblock returns a new list if there was none
        com.getState().setRoadblockInd(!com.getRoadblock().isEmpty());
    }

    protected void populateUbiVersion(CommunicationObjectModel com) {
        ProvinceCodeEnum province = ProvinceCodeEnum.valueOfCode(com.getContext().getProvince().getCode());
        ComDistributor comDistributor = com.getContext().getDistributor();
        // for Belair and Intact, use province-only configuration (empty distrib); for BNA distributor specification is needed
        String distributor = null == comDistributor || ComDistributor.BEL.equals(comDistributor.getCode()) ? "" : comDistributor.getCode();
        com.getState().setUseUBI20(this.configurator.isUbiVersion2(province.getCode(), distributor));
    }

    /**
     * Initiliaze bind
     *
     * @param com {@link CommunicationObjectModel}
     * @param policyVersion {@link PolicyVersion}
     */
    protected void initializeBind(final CommunicationObjectModel com, PolicyVersion policyVersion) {
        if (com != null && com.getPolicyVersionId() != null) {
            if (log.isDebugEnabled()) {
                log.debug("Start Initializing bind for policy : {}", com.getPolicyVersionId());
            }
            // PolicyVersion policyVersion = this.commonBusinessProcess.loadPolicyVersion(com.getPolicyVersionId());

            // Get quote pricing
            ProvinceCodeEnum province = ProvinceCodeEnum.valueOfCode(com.getContext().getProvince().getCode());
            QuoteCalculationDetails quoteCalculationDetails = this.getQuotationCalculationDetails(policyVersion,
                    province);
            this.comPaymentAdapter.applySpecificCalculationRules(quoteCalculationDetails, policyVersion);

            // Payment details
            this.mediatorPaymentPlp.initPaymentDetails(com, quoteCalculationDetails, policyVersion);

            PaymentStoreEnum paymentStore = this.externalPaymentService.getTheProperStoreForPolicy(policyVersion);
            if (paymentStore == null) {
                return;
            }
            String profileIdPlanB = fillPlan(paymentStore, PaymentPlanEnum.PLAN_B, com);
            String profileIdPlanE = fillPlan(paymentStore, PaymentPlanEnum.PLAN_E, com);
            String monerisProfileURL = MonerisConfigHelper.retrieveMonerisHostedCardTokenizationProfileURL();

            this.mediatorPaymentPlp.initPaymentMonerisDetails(com.getComPayment(), profileIdPlanB, profileIdPlanE, monerisProfileURL);

            if (log.isDebugEnabled()) {
                log.debug("End Initializing bind for policy : {}", com.getPolicyVersionId());
            }
        } else {
            log.error("Missing required information to initialize bind process !!! ");
        }
    }

    /**
     * Get quote calculation details
     *
     * @param policyVersion policy details
     * @param aProvinceCode province code
     * @return quote calculation details
     */
    protected QuoteCalculationDetails getQuotationCalculationDetails(final PolicyVersion policyVersion,
                                                                     final ProvinceCodeEnum aProvinceCode) {
        return this.commonBusinessProcess.getQuoteCalculationDetails(policyVersion, aProvinceCode);
    }

    /**
     * Fill plan B details
     *
     * @param paymentStore {@link PaymentStoreEnum}
     * @param paymentPlanEnum {@link PaymentPlanEnum}
     * @param com {@link CommunicationObjectModel}
     * @return the fill plan
     */
    private static String fillPlan(PaymentStoreEnum paymentStore, PaymentPlanEnum paymentPlanEnum, CommunicationObjectModel com) {
        MonerisConfig config = MonerisConfigHelper.getHPPTtokenProperMonerisData(ClientTypeEnum.CUSTOMER,
                PaymentStoreEnum.valueOfCode(paymentStore.getCode()), ApplicationIdEnum.BELAIR_WEB_AUTO_QUOTE,
                paymentPlanEnum.getCode(), ComUrlEnum.getLang(com.getContext().getRefererUrl()));

        if (log.isDebugEnabled()) {
            if (config != null && com.getContext() != null) {
                log.debug("Moneris Logs {'host': {}, 'api_token': {}, 'cryptCode': {}, 'externalHostedCardTokenizationProfile': " +
                        "{}, 'externalHostedCardTokenizationProfileUrl': {}, 'externalPaymentHost': {}, 'externalPaymentUrl': {}, " +
                        "'store_id': {}}", com.getContext() != null ? com.getContext().getRefererUrl() : "",
                        config.getApi_token(), config.getCryptCode(), config.getExternalHostedCardTokenizationProfile(),
                        config.getExternalHostedCardTokenizationProfileUrl(), config.getExternalPaymentHost(),
                        config.getExternalPaymentUrl(), config.getStore_id());
            }
        }

        return config.getExternalHostedCardTokenizationProfile();
    }

    /**
     * Display UBI section.
     *
     * @param aCom           {@link CommunicationObjectModel}
     * @param aPolicyVersion {@link PolicyVersion}
     * @return {@link Boolean}
     */
    protected boolean isDisplayUBISection(CommunicationObjectModel aCom, PolicyVersion aPolicyVersion) {
        if (!aCom.getState().isCanRate()) {
            return false;
        }
        if (ApplicationModeEnum.QUICK_QUOTE.equals(aPolicyVersion.getInsurancePolicy().getApplicationMode())) {
            return this.verifyIfThereIsAtLeastOneDriverEligibleToUbi(aPolicyVersion);
        }
        return this.coverageHelper.verifyIfThereIsAtLeastOneDriverEligibleToUbi(aPolicyVersion);
    }

    protected CommunicationObjectModel getPolicyByUuid(ComContext context, String uuid) {
        Assert.notNull(context, "ComContext parameter must be provided.");
        Assert.notNull(uuid, "policyNumber parameter must be provided.");

        CommunicationObjectModel retCOM = new CommunicationObjectModel();
        retCOM.setContext(context);

        PolicyVersion policyVersion = this.policyVersionService.findLatestQuoteByUUIDAndTransactionActivityCodes(uuid,
                BusinessTransactionActivityCodeEnum.INITIAL_QUOTE_OR_POLICY_CREATION,
                BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED);
        QuoteValidationUtil.validatePolicyVersion(uuid, policyVersion, retCOM);
        List<ComValidationError> comValidationErrorList = retCOM.getValidationErrors();
        ComState.ComTransactionStatus comTransactionStatus = retCOM.getState().getTransactionStatus();

        if (comValidationErrorList.isEmpty() && comTransactionStatus.equals(ComState.ComTransactionStatus.FOUND)) {
            retCOM = this.buildCom(policyVersion, retCOM);
            this.quoteStateManager.updateState(retCOM.getState(), policyVersion);
        }

        this.determineRoadBlockIndFromActivities(policyVersion, retCOM);

        return retCOM;
    }

    private void determineRoadBlockIndFromActivities(PolicyVersion policyVersion, CommunicationObjectModel retCOM) {
        Set<BusinessTransactionActivity> businessTransactionActivities = policyVersion.getBusinessTransaction().getBusinessTransactionActivities();
        policyVersion.getBusinessTransaction().getLastBusinessTransactionActivity();
        // For each sub activities of each activities
        for(BusinessTransactionActivity bta : businessTransactionActivities){
            if(bta.getBusinessTransactionSubActivities() != null
                    && !bta.getBusinessTransactionSubActivities().isEmpty()
                    && bta.getLatestSubActivity() != null
                    && SUB_ACTIVITY_ROAD_BLOCK_CODE.equals(bta.getLatestSubActivity().getSubActivityCode().getCode())){
                retCOM.getState().setRoadblockInd(true);
                return;
            }
        }
        retCOM.getState().setRoadblockInd(false);
    }

    private boolean verifyIfThereIsAtLeastOneDriverEligibleToUbi(PolicyVersion pv) {

        List<OfferTypeCodeEnum> coverageAdvisorOffers = new ArrayList<>();
        coverageAdvisorOffers.add(OfferTypeCodeEnum.CUSTOM);
        coverageAdvisorOffers.add(OfferTypeCodeEnum.COVERAGEADVISOR_PEOPLELIKEYOU);
        coverageAdvisorOffers.add(OfferTypeCodeEnum.COVERAGEADVISOR_RECOMMENDED);

        for (InsuranceRisk ir : pv.getInsuranceRisks()) {
            for (OfferTypeCodeEnum offer : coverageAdvisorOffers) {
                InsuranceRiskOffer insuranceRiskOffer = this.insuranceRiskOfferHelper
                        .getInsuranceRiskOfferForOfferType(ir, offer);
                if (insuranceRiskOffer != null) {
                    if (this.coverageHelper.verifyUbiDriverEligibility(insuranceRiskOffer)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Check if the policyVersion needs to be clone, if so we clone it.
     *
     * @param aCom          {@link CommunicationObjectModel}
     * @param policyVersion {@link PolicyVersion}
     * @return {@link PolicyVersion}
     */
    protected PolicyVersion clonePolicyVersion(CommunicationObjectModel aCom, PolicyVersion policyVersion) {
        PolicyVersion returnPV;

        if (mustClonePolicyVersion(aCom)) {
            // Clones the current policy and saves it in the DB for the transaction history.
            String cifClientIdStr = null;
            if (aCom.getCifClientId() != null) {
                cifClientIdStr = Long.toString(aCom.getCifClientId());
            }
            returnPV = this.cloneService.clone(policyVersion, cifClientIdStr, aCom.getContext()
                    .getMarketingPromotionCode());
            aCom.setPolicyVersionId(returnPV.getId());
        } else {
            returnPV = policyVersion;
        }

        return returnPV;
    }

    /**
     * Checks whether or not the provided instance of the policy Version should be clone.
     *
     * @param aCom {@link CommunicationObjectModel}
     */
    private static boolean mustClonePolicyVersion(CommunicationObjectModel aCom) {
        Assert.notNull(aCom.getState().getDataChanged(), "Com.state.dateChanged must be provided");
        Assert.notNull(aCom.getState().getHasOffer(), "Com.state.hasOffer must be provided");

        boolean isSaveBindEvent = false;

        if (aCom.getComEvent() != null && (ComEventEnum.SAVE_BIND.equals(aCom.getComEvent().getEventCode()))) {
            isSaveBindEvent = true;
        }

        boolean mustClone = BooleanUtils.isTrue(aCom.getState().getDataChanged())
                && BooleanUtils.isTrue(aCom.getState().getHasOffer()) && !isSaveBindEvent;

        aCom.getState().setDataChanged(Boolean.FALSE);
        aCom.getState().setHasOffer(Boolean.FALSE);

        return mustClone;
    }

    /**
     * Performs roadblock validation on all vehicles in the policy version.
     * <p>
     * A CommunicationObjectModel instance can be considered valid if both 'ValidationErrors' and 'RoadBlock'
     * collections are empty.
     * <p>
     * Does not perform basic UI validation.
     *
     * @param policyVersion {@link PolicyVersion}
     * @param com           {@link CommunicationObjectModel}
     * @return list of {@link RoadBlockException}
     */
    protected List<RoadBlockException> validateRoadblocksVehicles(final PolicyVersion policyVersion, CommunicationObjectModel com) {
        List<RoadBlockException> roadblocks = new ArrayList<>();
        Province province = Province.fromDbCode(com.getContext().getProvince().getCode());
        Language language = Language.fromLocaleCode(com.getContext().getLanguage().getCode());

        for (InsuranceRisk ir : policyVersion.getInsuranceRisks()) {
            if (LOG.isDebugEnabled()) {
                LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder().append(">> validate roadblocks for vehicle={")
                        .append("insuranceRisk.id=").append(ir.getId()).append("', insuranceRisk.sequence=")
                        .append(ir.getInsuranceRiskSequence()).append("', vehicle.id=").append(ir.getVehicle().getId())
                        .append("', policyVersion.id=").append(policyVersion.getId()).append("'").toString());
            }

            RuleExceptionResult hardrb = this.validateVehiclesHARDRB(ir.getVehicle(), province, language);

            if (hardrb != null) {
                com.getRoadblock().add(new ComRoadBlock(hardrb.getBusinessRuleCode(), ComRoadBlockTypeEnum.HARD));
                // This list is used later to link the roadblock with their corresponding entity
                // @see com.intact.autoquote.business.roadblock.RoadblockBusinessProcess.persistRoadblock
                roadblocks.add(this.convertRuleExceptionResultInRoadBlockException(ir, hardrb));
                addRoadblockDebugInfos(com, ComRoadBlockTypeEnum.HARD, hardrb);

            } else {
                // -- Check for soft.
                List<RuleExceptionResult> softrb = this.validateVehiclesSOFTRB(ir.getVehicle(), province, language, policyVersion);

                if (CollectionUtils.isNotEmpty(softrb)) {
                    for (RuleExceptionResult result : softrb) {
                        if (result.hasFailed()) {
                            com.getRoadblock().add(new ComRoadBlock(result.getBusinessRuleCode(), ComRoadBlockTypeEnum.SOFT));

                            // This list is used later to link the roadblock with their corresponding entity
                            // @see com.intact.autoquote.business.roadblock.RoadblockBusinessProcess.persistRoadblock
                            roadblocks.add(this.convertRuleExceptionResultInRoadBlockException(ir, result));
                        }
                        addRoadblockDebugInfos(com, ComRoadBlockTypeEnum.SOFT, result);
                    }
                }
            }
        }
        return roadblocks;
    }

    /**
     * Performs roadblock validation on all Parties in the policy version.
     * <p>
     * A CommunicationObjectModel instance can be considered valid if both 'ValidationErrors' and 'RoadBlock'
     * collections are empty.
     * <p>
     * Does not perform basic UI validation.
     *
     * @param policyVersion {@link PolicyVersion}
     * @param com    {@link CommunicationObjectModel}
     * @return {@link RoadBlockException}
     */
    protected List<RoadBlockException> validateRoadblocksDrivers(final PolicyVersion policyVersion,
                                                                 CommunicationObjectModel com)  {
        List<RoadBlockException> roadblocks = new ArrayList<>();

        for (Party party : policyVersion.getParties()) {
            if (!PartyTypeCodeEnum.INDIVIDUAL.equals(party.getPartyType())) {
                // This is to ensure we only process parties that are people, not Lessor or LienHolder
                continue;
            }

            if (LOG.isDebugEnabled()) {
                LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> validate roadblocks for driver={")
                        .append("party.id='" + party.getId()).append("' ,driverComplementInfo.id=")
                        .append(party.getDriverComplementInfo().getId()).append("' ,policyVersion.id=")
                        .append(policyVersion.getId()).append("'").toString());
            }
            final DriverComplementInfo driver = party.getDriverComplementInfo();

            // Validate hard roadblocks
            RuleExceptionResult hardrb = this.validateHardRoadblocks(com, driver);

            // No point in validating soft roadblocks if there's at least one hard roadblock
            if (hardrb != null) {
                com.getRoadblock().add(new ComRoadBlock(hardrb.getBusinessRuleCode(), ComRoadBlockTypeEnum.HARD));

                // This list is used later to link the roadblock with their corresponding entity
                // @see com.intact.autoquote.business.roadblock.RoadblockBusinessProcess.persistRoadblock
                roadblocks.add(this.convertRuleExceptionResultInRoadBlockException(party, hardrb));

                addRoadblockDebugInfos(com, ComRoadBlockTypeEnum.HARD, hardrb);
            } else {
                // -- Check for soft.
                List<RuleExceptionResult> softrb = this.validateDriversSOFTRB(driver, ProvinceCodeEnum.valueOfCode(com.getContext().getProvince().getCode()));
                this.addSoftRoadblock(roadblocks, softrb, party, com);
            }
        }

        List<RuleExceptionResult> softrb = this.validateDriversSOFTRB(policyVersion,
                ProvinceCodeEnum.valueOfCode(com.getContext().getProvince().getCode()));
        this.addSoftRoadblock(roadblocks, softrb, policyVersion.getParties().iterator().next(), com);

        return roadblocks;
    }

    /**
     * Proceed to encountered roadblocks
     *
     * @param policyVersion {@link PolicyVersion}
     * @param roadblocks    {@link List} of {@link RoadBlockException}
     */
    protected void persistRoadblocks(final PolicyVersion policyVersion, List<RoadBlockException> roadblocks,
                                     ComState currentState) {
        Assert.notNull(policyVersion, "PolicyVersion cannot be null on persistRoadblocks");

        if (!roadblocks.isEmpty()) {
            // persist roadblocks
            for (RoadBlockException curRoadBlock : roadblocks) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> The roadblock is caused by : ").append(
                            curRoadBlock.getBusinessRuleInvolved()).toString());
                }

                ComStateEnum stateEnum = null;
                if (currentState != null) {
                    stateEnum = currentState.getCurrentState();
                }

                this.transactionHistoryService.updateTransHistoryOnRoadBlock(curRoadBlock, stateEnum, policyVersion);
            }

            if (currentState != null) {
                currentState.setRoadblockInd(true);
            }
        }
    }

    /**
     * Move this method to BaseFacade classes when quickquote intact is ready to use it.
     *
     * @param inQuote       incoming {@link CommunicationObjectModel}
     * @param eventTypeCode the event type code
     * @param trackingNbr   the tracking number
     * @throws AutoquoteFacadeException thrown after a web attack
     */
    protected void verifyWebAttack(CommunicationObjectModel inQuote, String eventTypeCode, String trackingNbr) throws AutoquoteFacadeException {

        // Do not validate webattack if no ip found
        if (StringUtils.isNotEmpty(inQuote.getContext().getClientXForwardIPNbr())) {
            ManufacturingContext ctx = MediatorUtils.convertContext(inQuote.getContext());

            ApplicationModeEnum applicationMode = ApplicationModeEnum.REGULAR_QUOTE;
            if (inQuote.getContext().getApplication().equals(ComApplicationEnum.QUICKQUOTE)) {
                applicationMode = ApplicationModeEnum.QUICK_QUOTE;
            }

            String applicationId = this.getApplicationId(applicationMode);

            try {
                // Check if it's an attack
                this.webAttackAnalyser.verifyWebAttacks(ctx, inQuote.getContext().getClientXForwardIPNbr(), eventTypeCode, applicationMode);
            } catch (RoadBlockException e) {
                LOG.error(Logger.SECURITY_FAILURE, String.format("Detected a web attack from quote=[%s] ip=[%s] evenTypeCode=[%s]. %s",
                        inQuote.getAgreementNumber(), inQuote.getContext().getClientXForwardIPNbr(), eventTypeCode,
                        e.getMessage()));
                throw new AutoquoteFacadeException("WAT");
            }

            // Log this call
            try {
                this.webAttackBusinessProcess.keepTraceOfThisCall(ctx, applicationId, inQuote.getContext().getClientXForwardIPNbr(), "XFWD", eventTypeCode,
                        inQuote.getDriver(0).getLicenseNumber(), trackingNbr);
            } catch (Exception e) {
                LOG.error(Logger.SECURITY_FAILURE, String.format("Exception occured logging the web attack. %s", e.getMessage()));
            }
        } else {
            LOG.info(Logger.SECURITY_AUDIT, String.format("Client ip address is empty.  EventType: %s, AgreementNumber: %s. TrackingNbr: %s",
                    eventTypeCode, inQuote.getAgreementNumber(), trackingNbr));
        }

    }

    /**
     * Validate hard roadkblock.
     *
     * @param com    {@link CommunicationObjectModel}
     * @param driver {@link DriverComplementInfo}
     * @return {@link RuleExceptionResult}
     */
    private RuleExceptionResult validateHardRoadblocks(CommunicationObjectModel com, DriverComplementInfo driver) {
        RuleExceptionResult hardrb = this.usageBusinessProcess.validateHoneypotTrap(com.getPolicyAssignDate());
        if (hardrb.hasFailed()) {
            // cleanUp com
            com.setPolicyAssignDate("");
        } else { // if honeypot has not failed verify other roadblocks
            hardrb = this.validateDriversHARDRB(driver, MediatorUtils.getLocale(com.getContext()));
        }

        return hardrb;
    }

    protected void addSoftRoadblock(List<RoadBlockException> roadblocks, List<RuleExceptionResult> softrb, Party party,
                                    CommunicationObjectModel com) {
        if (CollectionUtils.isNotEmpty(softrb)) {
            for (RuleExceptionResult result : softrb) {
                if (result.hasFailed()) {
                    com.getRoadblock().add(new ComRoadBlock(result.getBusinessRuleCode(), ComRoadBlockTypeEnum.SOFT));

                    // This list is used later to link the roadblock with their corresponding entity
                    // @see com.intact.autoquote.business.roadblock.RoadblockBusinessProcess.persistRoadblock
                    roadblocks.add(this.convertRuleExceptionResultInRoadBlockException(party, result));
                }

                addRoadblockDebugInfos(com, ComRoadBlockTypeEnum.SOFT, result);
            }
        }
    }

    /**
     * Validate vehicles HARD roadblock.
     *
     * @param aVehicle  {@link Vehicle}
     * @param aProvince {@link ProvinceCodeEnum}
     * @param aLanguage {@link Language}
     * @return {@link RuleExceptionResult}
     */
    public RuleExceptionResult validateVehiclesHARDRB(Vehicle aVehicle, Province aProvince, Language aLanguage) {
        throw new NotImplementedException("Validate Vehicles HARD ROADBLOCK");
    }

    /**
     * Validate vehicles SOFT roadblock.
     *
     * @param aVehicle       {@link Vehicle}
     * @param aProvince      {@link Province}
     * @param aLanguage      {@link Language}
     * @param aPolicyVersion {@link PolicyVersion}
     * @return {@link List} of {@link RuleExceptionResult}
     */
    public List<RuleExceptionResult> validateVehiclesSOFTRB(Vehicle aVehicle, Province aProvince, Language aLanguage,
                                                            PolicyVersion aPolicyVersion) {
        throw new NotImplementedException("Validate Vehicles SOFT ROADBLOCK");
    }

    protected RoadBlockException convertRuleExceptionResultInRoadBlockException(InsuranceRisk ir,
                                                                                RuleExceptionResult hardrb) {
        return null;
    }

    protected RoadBlockException convertRuleExceptionResultInRoadBlockException(Party party, RuleExceptionResult hardrb) {
        return null; // do nothing in common code.
    }

    private static void addRoadblockDebugInfos(CommunicationObjectModel aCommunicationObjectModel,
                                               ComRoadBlockTypeEnum aComRoadBlockTypeEnum, RuleExceptionResult aResult) {
        if (!SystemUtils.isSpoeTestMode()) {
            // SPOE mode not activated, nothing to do
            return;
        }

        String previousInfo = aCommunicationObjectModel.getRoadblockDebugInfos();

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(previousInfo)) {
            sb.append(previousInfo).append("-");
        }

        sb.append(String.valueOf(aComRoadBlockTypeEnum).toLowerCase()).append("_")
                .append(aResult.getBusinessRuleCode());

        aCommunicationObjectModel.setRoadblockDebugInfos(sb.toString());
    }

    /**
     * Return the harmonized applicationId for autoquote and quickquote
     *
     * @param applicationMode {@link ApplicationModeEnum}
     * @return the application id
     */
    public String getApplicationId(ApplicationModeEnum applicationMode) {
        String applicationId = "AQRG";
        if (ApplicationModeEnum.QUICK_QUOTE.equals(applicationMode)) {
            applicationId = "AQQK";
        }

        return applicationId;
    }

    /**
     * Validate drivers HARD roadblock.
     *
     * @param driver {@link DriverComplementInfo}
     * @param locale {@link Locale}
     * @return {@link RuleExceptionResult}
     */
    public RuleExceptionResult validateDriversHARDRB(DriverComplementInfo driver, Locale locale) {
        throw new NotImplementedException("Validate Drivers HARD ROADBLOCK");
    }

    /**
     * Validate drivers SOFT roadblock.
     *
     * @param policyVersion    {@link PolicyVersion}
     * @param provinceCodeEnum {@link ProvinceCodeEnum}
     * @return {@link List} of {@link RuleExceptionResult}
     */
    public List<RuleExceptionResult> validateDriversSOFTRB(PolicyVersion policyVersion, ProvinceCodeEnum provinceCodeEnum) {
        throw new NotImplementedException("Validate Drivers SOFT ROADBLOCK must be implemented in child class.");
    }

    /**
     * Validate drivers SOFT roadblock.
     *
     * @param driver           {@link DriverComplementInfo}
     * @param provinceCodeEnum {@link ProvinceCodeEnum}
     * @return {@link List} of {@link RuleExceptionResult}
     */
    public List<RuleExceptionResult> validateDriversSOFTRB(DriverComplementInfo driver, ProvinceCodeEnum provinceCodeEnum) {
        throw new NotImplementedException("Validate Drivers SOFT ROADBLOCK");
    }
}
