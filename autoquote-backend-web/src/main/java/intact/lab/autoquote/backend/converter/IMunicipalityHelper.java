package intact.lab.autoquote.backend.converter;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.address.ComMunicipalityInfo;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;

public interface IMunicipalityHelper {

	ComMunicipalityInfo getMunicipalityInfo(CommunicationObjectModel com, AddressDTO addressDTO) throws AutoQuoteException;

}
