package intact.lab.autoquote.backend.services;

import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ApplicationFlowStateCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;

public interface INewQuoteBusinessProcessService {

    /**
     * Creates a new quote.
     * <p>
     * If the subbroker is empty then the postal code and company are required
     * to fetch the assigned subbroker to the given postal code and company.
     *
     * @return the policy version.
     */
    PolicyVersion newQuote(ComContext aComContext, ManufacturingContext mCtxt, String subBrokerId,
                           ApplicationModeEnum appModeEnum, ApplicationFlowStateCodeEnum appFlowState,
                           ComBrokerWebSiteOriginEnum origin, LineOfBusinessCodeEnum lineOfBusiness);

    /**
     * This method complete the quote creation process by adding province #
     * company specific stuff to the plp object (insurance company # policy
     * version)
     *
     * @param insurancePolicy Insurance policy
     * @param policyVersion Policy version
     */
    void assignBrokerToQuote(InsurancePolicy insurancePolicy, PolicyVersion policyVersion, String subBrokerId);
}
