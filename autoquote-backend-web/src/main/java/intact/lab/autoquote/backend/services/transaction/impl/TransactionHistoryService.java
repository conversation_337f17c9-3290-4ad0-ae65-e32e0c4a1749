/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2015, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.transaction.impl;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity;
import com.ing.canada.plp.domain.enums.ApplicationFlowStateCodeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionStatusCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.state.enums.ComStateEnum;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import intact.lab.autoquote.backend.common.exception.StateException;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.roadblock.IRoadblockBusinessProcess;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Calendar;
import java.util.Set;

/**
 * Transaction History Service
 */
@Component
public class TransactionHistoryService implements ITransactionHistoryService {

	protected ICommonBusinessProcess commonBusinessProcess;
	private final IRoadblockBusinessProcess roadblockBusinessProcess;
	private final IPolicyVersionHelper policyVersionHelper;

	public TransactionHistoryService(ICommonBusinessProcess commonBusinessProcess, IPolicyVersionHelper policyVersionHelper,
									 IRoadblockBusinessProcess roadblockBusinessProcess) {
		this.commonBusinessProcess = commonBusinessProcess;
		this.policyVersionHelper = policyVersionHelper;
		this.roadblockBusinessProcess = roadblockBusinessProcess;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void updateTransactionHistory(ComEventEnum eventEnum, PolicyVersion aPolicyVersion) {
		if (eventEnum != null && aPolicyVersion != null) {
			ComEvent comEvent = new ComEvent();
			comEvent.setEventCode(eventEnum);
			updateTransactionHistory(comEvent, aPolicyVersion);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void updateTransHistoryOnRoadBlock(RoadBlockException roadblockEx, ComStateEnum currentState,
											  PolicyVersion policyVersion) {
		if (roadblockEx != null && policyVersion != null) {
			this.roadblockBusinessProcess.persistRoadblock(policyVersion.getId(), roadblockEx);
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), ComEventEnum.ROADBLOCK, currentState);
		}
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void updateTransactionHistory(ComEvent event, PolicyVersion policyVersion) {
		if (event != null && event.getEventCode() != null && policyVersion != null) {
			if (ComEventEnum.ADD_DRIVER.equals(event.getEventCode())) {
				handleAddDriverTransHistory(event, ComStateEnum.DRIVER, policyVersion);
			} else if (ComEventEnum.MODIFY_DRIVER.equals(event.getEventCode())) {
				handleModifyDriverTransHistory(event, ComStateEnum.DRIVER, policyVersion);
			} else if (ComEventEnum.DELETE_DRIVER.equals(event.getEventCode())) {
				handleDeleteDriverTransHistory(event, ComStateEnum.DRIVER, policyVersion);
			} else if (ComEventEnum.ADD_VEHICLE.equals(event.getEventCode())) {
				handleAddVehicleTransHistory(event, ComStateEnum.VEHICLE, policyVersion);
			} else if (ComEventEnum.MODIFY_VEHICLE.equals(event.getEventCode())) {
				handleModifyVehicleTransHistory(event, ComStateEnum.VEHICLE, policyVersion);
			} else if (ComEventEnum.DELETE_VEHICLE.equals(event.getEventCode())) {
				handleDeleteVehicleTransHistory(event, ComStateEnum.VEHICLE, policyVersion);
			} else if (ComEventEnum.VIEW_USAGE.equals(event.getEventCode())) {
				handleUsageTransHistory(event, ComStateEnum.VEHICLE_USAGE, policyVersion);
			} else if (ComEventEnum.SAVE_USAGE.equals(event.getEventCode())) {
				handleUsageTransHistory(event, ComStateEnum.VEHICLE_USAGE, policyVersion);
			} else if (ComEventEnum.VIEW_OFFER.equals(event.getEventCode())) {
				handleOfferTransHistory(event, ComStateEnum.OFFER, policyVersion);
			} else if (ComEventEnum.SAVE_OFFER.equals(event.getEventCode())) {
				handleOfferTransHistory(event, ComStateEnum.OFFER, policyVersion);
			} else if (ComEventEnum.RECALCULATE.equals(event.getEventCode())) {
				handleRecalculateOfferTransHistory(event, ComStateEnum.OFFER, policyVersion);
			} else if (ComEventEnum.UPDATE_SAVINGS.equals(event.getEventCode())) {
				handleUpdateSavingsTransHistory(event, ComStateEnum.OFFER, policyVersion);
			} else if (ComEventEnum.VIEW_PROFILE.equals(event.getEventCode())) {
				handleViewBindProfileTransHistory(event, ComStateEnum.BIND, policyVersion);
			} else if (ComEventEnum.VIEW_BIND.equals(event.getEventCode())) {
				handleViewBindVehDriverTransHistory(event, ComStateEnum.BIND, policyVersion);
			} else if (ComEventEnum.VIEW_PAYMENT.equals(event.getEventCode())) {
				handleViewPaymentTransHistory(event, ComStateEnum.BIND, policyVersion);
			} else if (ComEventEnum.PURCHASE.equals(event.getEventCode())) {
				handlePurchaseTransHistory(event, ComStateEnum.PURCHASE, policyVersion);
			}
			this.commonBusinessProcess.savePolicyVersion(policyVersion);
		}
	}

	/**
	 * Handle purchase transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handlePurchaseTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			PolicyOfferRating offerRating = policyVersion.getLatestPolicyOfferRating();
			if (offerRating != null && offerRating.getId() != null
					&& policyVersion.getBusinessTransaction().getCurrentApplicationFlowState() == ApplicationFlowStateCodeEnum.BIND) {
				addBusinessTransactionSubActivity(policyVersion,
						BusinessTransactionSubActivityCodeEnum.PURCHASE_REQUESTED, 0L);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			} else {
				throw new StateException("invalid state transition in transaction history - purchase");
			}
		}
	}

	/**
	 * Handle view payment transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleViewPaymentTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			PolicyOfferRating offerRating = policyVersion.getLatestPolicyOfferRating();
			if (offerRating != null && offerRating.getId() != null) {
				addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.GET_PURCHASE,
                        0L);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}
		}
	}

	/**
	 * Handle view bind profile transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleViewBindProfileTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			PolicyOfferRating offerRating = policyVersion.getLatestPolicyOfferRating();
			if (offerRating != null && offerRating.getId() != null) {
				addBusinessTransactionSubActivity(policyVersion,
						BusinessTransactionSubActivityCodeEnum.GET_BIND_PROFILE, 0L);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}
		}
	}

	/**
	 * Handle view Bind transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleViewBindVehDriverTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (isValidParameters(comEvent, policyVersion)) {
			PolicyOfferRating offerRating = policyVersion.getLatestPolicyOfferRating();
			if (isValidOfferRating(offerRating) && isValidBINDFlowState(policyVersion)) {
				addBusinessTransactionSubActivity(policyVersion,
						BusinessTransactionSubActivityCodeEnum.GET_BIND_VEH_DRVR, 0L);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}else {
				throw new StateException("invalid state transition in transaction history - bind");
			}
		}
	}

	private static boolean isValidParameters(ComEvent comEvent, PolicyVersion policyVersion) {
		return comEvent != null && policyVersion != null;
	}

	private static boolean isValidBINDFlowState(PolicyVersion policyVersion) {
		return policyVersion.getBusinessTransaction().getCurrentApplicationFlowState() == ApplicationFlowStateCodeEnum.BIND
				|| policyVersion.getBusinessTransaction().getCurrentApplicationFlowState() == ApplicationFlowStateCodeEnum.OFFER;
	}

	private static boolean isValidOfferRating(PolicyOfferRating offerRating) {
		return offerRating != null && offerRating.getId() != null;
	}


	/**
	 * Handle view offer transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleOfferTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			PolicyOfferRating offerRating = policyVersion.getLatestPolicyOfferRating();
			if (offerRating != null && offerRating.getId() != null) {
				addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.GET_OFFER,
						offerRating.getId());
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}
		}
	}

	/**
	 * Handle recalculate offer transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleUpdateSavingsTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.UPDATE_SAVINGS,
					Long.valueOf(0));
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle recalculate offer transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleRecalculateOfferTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			// The business transaction sub activity is not created because this has already been done in the
			// OfferBusinessProcess.recalculateCustomOffer
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle modify usage transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleUsageTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.MODIFY_USAGE,
					Long.valueOf(0));
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle delete driver transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleDeleteDriverTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null && comEvent.getComId() != null
				&& comEvent.getComId().getPlpId() != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.DELETE_DRIVER,
					comEvent.getComId().getPlpId());
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle delete vehicle transaction activity
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleDeleteVehicleTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null && comEvent.getComId() != null
				&& comEvent.getComId().getPlpId() != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.DELETE_VEHICLE,
					comEvent.getComId().getPlpId());
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle modify driver transaction history
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleModifyDriverTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null && comEvent.getComId() != null
				&& comEvent.getComId().getPlpId() != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.MODIFY_DRIVER,
					comEvent.getComId().getPlpId());
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle modify vehicle transaction history
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleModifyVehicleTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null && comEvent.getComId() != null
				&& comEvent.getComId().getPlpId() != null) {
			addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.MODIFY_VEHICLE,
					comEvent.getComId().getPlpId());
			updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
		}
	}

	/**
	 * Handle add driver transaction history
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private void handleAddDriverTransHistory(ComEvent comEvent, ComStateEnum currentState, PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			Long referenceId = this.findLastAddedDriverId(policyVersion);
			if (referenceId != null) {
				addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.ADD_DRIVER,
						referenceId);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}
		}
	}

	/**
	 * Handle add vehicle transaction history
	 *
	 * @param comEvent {@link CommunicationObjectModel}
	 * @param policyVersion {@link PolicyVersion}
	 */
	private static void handleAddVehicleTransHistory(ComEvent comEvent, ComStateEnum currentState,
			PolicyVersion policyVersion) {
		if (comEvent != null && policyVersion != null) {
			Long referenceId = findLastAddedVehicleId(policyVersion);
			if (referenceId != null) {
				addBusinessTransactionSubActivity(policyVersion, BusinessTransactionSubActivityCodeEnum.ADD_VEHICLE,
						referenceId);
				updateBusinessTransaction(policyVersion.getBusinessTransaction(), comEvent.getEventCode(), currentState);
			}
		}
	}

	/**
	 * Add business transaction sub activity code
	 *
	 * @param aPolicyVersion PolicyVersion
	 * @param subActivityCodeEnum Sub activity code enum
	 * @param referenceId reference id
	 */
	private static void addBusinessTransactionSubActivity(PolicyVersion aPolicyVersion,
			BusinessTransactionSubActivityCodeEnum subActivityCodeEnum, Long referenceId) {
		Assert.notNull(aPolicyVersion, "PolicyVersion cannot be null.");
		Assert.notNull(subActivityCodeEnum, "BusinessTransactionSubActivityCodeEnum cannot be null.");

		BusinessTransactionSubActivity btsa = new BusinessTransactionSubActivity();
		btsa.setSubActivityCode(subActivityCodeEnum);
		btsa.setRootReferenceId(referenceId);

		BusinessTransaction bt = aPolicyVersion.getBusinessTransaction();
		bt.getLastBusinessTransactionActivity().addBusinessTransactionSubActivity(btsa);
	}

	/**
	 * Update the business transaction according to the transaction sub activity
	 *
	 * @param busTransaction
	 * @param event
	 */
	private static void updateBusinessTransaction(BusinessTransaction busTransaction, ComEventEnum event,
			ComStateEnum currentState) {
		if (busTransaction != null && event != null && currentState != null) {
			// if we are in a roadblock we need to update the transactionstatus for CRM display
			if (ComEventEnum.ROADBLOCK.equals(event)
					&& (ComStateEnum.BIND.equals(currentState) || ComStateEnum.OFFER.equals(currentState) || ComStateEnum.INVALID_OFFER
							.equals(currentState))) {
				// this is done to match DE376/VD180 in CRM <Roadblock>
				updateBusinessTransaction(busTransaction,
						ApplicationFlowStateCodeEnum.valueOfCode(currentState.getCode()),
						TransactionStatusCodeEnum.INTERVENTION_REQUIRED_PREMIUM_AVAILABLE,
						busTransaction.getCurrentApplicationFlowState());
			} else if (ComEventEnum.ROADBLOCK.equals(event) && !ComStateEnum.BIND.equals(currentState)
					&& !ComStateEnum.OFFER.equals(currentState) && !ComStateEnum.INVALID_OFFER.equals(currentState)) {
				// this is done to match DE376/VD180 in CRM <Roadblock>
				updateBusinessTransaction(busTransaction,
						ApplicationFlowStateCodeEnum.valueOfCode(currentState.getCode()),
						TransactionStatusCodeEnum.INTERVENTION_REQUIRED_NO_PREMIUM_AVAILABLE,
						busTransaction.getCurrentApplicationFlowState());
			} else if (ComEventEnum.RETRIEVE_ROADBLOCK_QUOTE.equals(event)) {
				// if the client try to retrieve a roadbloked quote (already binded, expired quote...) then we don't
				// change the current status no state change
			} else {
				// if we are not in a roadblock we need to update the transactionstatus for CRM display.
				// We update it even if we did not change state to make sure we dont keep a previous roadblock state ()
				// wich is based on event and not on state
				switch (currentState) {
				// same statement for every page before the offer page
				case INITIAL:
					// this is done to match DE376/VD180 in CRM <Pending>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.INITIAL,
							TransactionStatusCodeEnum.SUSPENDED, busTransaction.getCurrentApplicationFlowState());
					break;
				case VEHICLE:
					// this is done to match DE376/VD180 in CRM <Pending>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.VEHICLE,
							TransactionStatusCodeEnum.SUSPENDED, busTransaction.getCurrentApplicationFlowState());
					break;
				case DRIVER:
					// this is done to match DE376/VD180 in CRM <Pending>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.DRIVER,
							TransactionStatusCodeEnum.SUSPENDED, busTransaction.getCurrentApplicationFlowState());
					break;
				case VEHICLE_USAGE:
					// this is done to match DE376/VD180 in CRM <Pending>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.VEHICLE_USAGE,
							TransactionStatusCodeEnum.SUSPENDED, busTransaction.getCurrentApplicationFlowState());
					break;
				case INVALID_OFFER:
					// this is done to match DE376/VD180 in CRM <Pending>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.INVALID_OFFER,
							TransactionStatusCodeEnum.SUSPENDED, busTransaction.getCurrentApplicationFlowState());
					break;
				case OFFER:
					// this is done to match DE376/VD180 in CRM <Offer received>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.OFFER,
							TransactionStatusCodeEnum.GET_OFFER, busTransaction.getCurrentApplicationFlowState());
					break;
				case BIND:
					// this is done to match DE376/VD180 in CRM <Incomplete Bind>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.BIND,
							TransactionStatusCodeEnum.BIND_IN_PROGRESS_BUT_NOT_CONFIRM,
							ApplicationFlowStateCodeEnum.OFFER);
					break;
				case PURCHASE:
					// this is done to match DE376/VD180 in CRM <Complete Bind>
					updateBusinessTransaction(busTransaction, ApplicationFlowStateCodeEnum.PURCHASE,
							TransactionStatusCodeEnum.SUBMITTED_BY_THE_REQUESTER, ApplicationFlowStateCodeEnum.BIND);
					break;
				default:
					throw new StateException("unsuported state in StateBusinessTransactionObserver");
				}
			}
		}
	}

	/**
	 * Update the business transaction
	 *
	 * @param busTransaction The business transaction
	 * @param currentState current applicaton flow state
	 * @param transStatusCode transaction status code
	 */
	private static void updateBusinessTransaction(BusinessTransaction busTransaction,
			ApplicationFlowStateCodeEnum currentState, TransactionStatusCodeEnum transStatusCode,
			ApplicationFlowStateCodeEnum previouState) {
		busTransaction.setPreviousApplicationFlowState(previouState);
		busTransaction.setCurrentApplicationFlowState(currentState);
		busTransaction.setTransactionStatus(transStatusCode);
		busTransaction.setLastUpdateDateTime(Calendar.getInstance().getTime());
	}

	/**
	 * Find last added driver Id
	 *
	 * @param policyVersion details of the policy
	 * @return
	 */
	private Long findLastAddedDriverId(PolicyVersion policyVersion) {
		Long referenceId = null;
		Set<Party> drivers = null;
		if (policyVersion != null) {
			drivers = this.policyVersionHelper.getIndividualParties(policyVersion);
			if (!CollectionUtils.isEmpty(drivers)) {
				int numDrivers = drivers.size();

				for (Party p : drivers) {
					if (numDrivers == p.getDriverComplementInfo().getDriverSequence()) {
						referenceId = p.getId();
						break;
					}
				}
			}
		}
		return referenceId;
	}

	/**
	 * Find the last added vehicle id
	 *
	 * @param policyVersion Details of the policy
	 * @return
	 */
	private static Long findLastAddedVehicleId(PolicyVersion policyVersion) {
		Long referenceId = null;
		if (policyVersion != null && !CollectionUtils.isEmpty(policyVersion.getInsuranceRisks())) {
			int numVehicles = policyVersion.getInsuranceRisks().size();

			for (final InsuranceRisk ir : policyVersion.getInsuranceRisks()) {
				if (numVehicles == ir.getInsuranceRiskSequence()) {
					referenceId = ir.getVehicle().getId();
					break;
				}
			}
		}
		return referenceId;
	}

}
