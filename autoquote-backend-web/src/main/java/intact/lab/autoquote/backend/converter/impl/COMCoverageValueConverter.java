package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.enums.ComCoverageAmountTypeEnum;
import com.intact.com.offer.ComItemChoice;
import intact.lab.autoquote.backend.common.dto.CoverageValueDTO;
import intact.lab.autoquote.backend.common.enums.CoverageValueType;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.springframework.stereotype.Component;

@Component("comCoverageValueConverter")
public class COMCoverageValueConverter implements ICOMConverter<CoverageValueDTO, ComItemChoice> {

	@Override
	public ComItemChoice toCOM(CoverageValueDTO dto, ComItemChoice comItemChoice) {
		return null;
	}

	@Override
	public CoverageValueDTO toDTO(ComItemChoice comItemChoice) {
		CoverageValueDTO coverageValueDTO = new CoverageValueDTO();
		if (comItemChoice != null) {
			coverageValueDTO.setAmount(comItemChoice.getValue() != null ? (Integer) comItemChoice.getValue() : null);
			coverageValueDTO.setCoverageValueType(this.getCoverageValueType(comItemChoice.getCoverageAmountType()));
		}
		return coverageValueDTO;
	}


	private CoverageValueType getCoverageValueType(ComCoverageAmountTypeEnum comCoverageAmountType) {
		if (comCoverageAmountType != null) {
			switch (comCoverageAmountType) {
				case DEDUCTIBLE_AMOUNT:
					return CoverageValueType.DEDUCTIBLE_AMOUNT;
				case LIMIT_AMOUNT:
					return CoverageValueType.LIMIT_AMOUNT;
			}
		}
		return null;
	}


}
