package intact.lab.autoquote.backend.common.exception;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteConfigurationException extends AutoQuoteException {

	public static final String EXEC_DEFAULT_ERROR = "exec.configuration.default.error";
	public static final String EX_CONFIG_FACADE_ERROR = "exec.configuration.facade.error";
	public static final String PARAM_API_KEY_NULL_ERROR = "param.configuration.api.key.null.error";
	public static final String PARAM_LANGUAGE_NULL_ERROR = "param.configuration.language.null.error";
	public static final String PARAM_PROVINCE_NULL_ERROR = "param.configuration.province.null.error";
	private static final long serialVersionUID = -6599740918351163035L;
	private static Map<String, String> messages = null;

	public AutoQuoteConfigurationException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public AutoQuoteConfigurationException(String exceptionCode, Throwable cause, Object... parameters) {
		this(AutoQuoteConfigurationException.getMessage(exceptionCode, parameters), cause);
		this.setCode(exceptionCode);
	}

	public AutoQuoteConfigurationException(String message, Throwable cause) {
		super(message, cause);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteConfigurationException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteConfigurationException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteConfigurationException.getMessages() == null) {
			AutoQuoteConfigurationException.initMessages();
		}

		String messageFormat = AutoQuoteConfigurationException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteConfigurationException.getMessages().get(AutoQuoteConfigurationException.EXEC_DEFAULT_ERROR);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<>();

		messages.put(
				AutoQuoteConfigurationException.EXEC_DEFAULT_ERROR,
				"An unknown error occurred while using the configuration facade. This exception is not documented at this time.  The cause is {0}");
		messages.put(AutoQuoteConfigurationException.EX_CONFIG_FACADE_ERROR,
				"An error occured while retrieve the configuration using the language {0} and the province {1} and apiKey{2}.  The cause of this error is {0}");
		messages.put(AutoQuoteConfigurationException.PARAM_API_KEY_NULL_ERROR,
				"The api key passed as a parameter is null. Please, provice de none null parameter");
		messages.put(AutoQuoteConfigurationException.PARAM_LANGUAGE_NULL_ERROR,
				"The language passed as a parameter is null. Please, provice de none null parameter");
		messages.put(AutoQuoteConfigurationException.PARAM_PROVINCE_NULL_ERROR,
				"The province passed as a parameter is null. Please, provice de none null parameter");

		AutoQuoteConfigurationException.setMessages(messages);
	}


}
