/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid.impl;

import com.ing.canada.cif.domain.IAgreement;
import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.domain.IProductBusinessLines;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.CsioNbrEnum;
import com.ing.canada.cif.service.IClientRelationService;
import com.ing.canada.cif.service.IClientService;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.util.AgreementUtils;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.exception.SystemException;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.AssignmentOriginatorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IConsentHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.service.IPartyService;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.changebroker.IChangeBrokerBusinessProcess;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.singleid.ISingleIdService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * This class is used to manage the profile for the Intact quote.
 *
 * <AUTHOR>
 */
@ComponentLocal(application = ApplicationEnum.INTACT)
public class ProfileServiceIntact extends ProfileService {

	private static final Logger log = ESAPI.getLogger(ProfileServiceIntact.class);

	@Autowired
	private ICommonBusinessProcess commonBusinessProcess;

	@Autowired
	private IChangeBrokerBusinessProcess changeBrokerBusinessProcess;

	@Autowired
	private ISubBrokersService subBrokersService;

	public ProfileServiceIntact(ICommonBusinessProcess commonBusinessProcess, IPartyHelper partyHelper, IClientService clientService,
								IClientRelationService clientRelationService, IConsentHelper consentHelper,
								IPartyService partyService, ISingleIdService singleIdService) {
		super(commonBusinessProcess, partyHelper, clientService, clientRelationService, consentHelper, partyService, singleIdService);
	}

	@Override
	public void manageProfile(Long aPolicyVersionId, RoadBlockExceptionContextEnum context,
			final ApplicationEnum application) throws AutoQuoteRoadBlockException, SingleIdActiveProductException {

		PolicyVersion aPolicyVersion = this.commonBusinessProcess.loadPolicyVersion(aPolicyVersionId);
		Party plpParty = this.partyHelper.getNamedInsured(aPolicyVersion);

		List<Long> clientIDList = this.buildClientIdList(plpParty);

		// Clients found
		if (clientIDList != null && clientIDList.size() > 0) {

			// Loop over the client list to find a client we want to block
			for (Long clientID : clientIDList) {

				// validate existing AUTO policies
				List<IAgreement> autoPolicies = this.clientService.getPolicies(clientID,
						IProductBusinessLines.Types.AUTO);
				this.validateExistingPolicies(plpParty, autoPolicies, aPolicyVersion, clientID, context);

				// validate existing HOME policies
				List<IAgreement> homePolicies = this.clientService.getPolicies(clientID,
						IProductBusinessLines.Types.RESIDENTIAL);
				this.validateExistingPolicies(plpParty, homePolicies, aPolicyVersion, clientID, context);

			}
		}
	}

	/**
	 * retreive the client id list associated with the party.
	 *
	 * @param plpParty
	 * @return
	 */
	private List<Long> buildClientIdList(Party plpParty) {
		Address plpAddress = this.partyHelper.getCurrentResidentialAddress(plpParty);

		String clientInfo = "(" + plpParty.getFirstName() + ", " + plpParty.getLastName() + ", "
				+ plpAddress.getCivicNumber() + ", " + plpAddress.getPostalCode() + ")";

		log.debug(Logger.EVENT_SUCCESS, String.format("Perform client match. ClientInfo are: %s", clientInfo));
		// Find the list of possible clients
		String csioNbr = CsioNbrEnum.INTACT.getCode(plpAddress.getProvince().getCode());
		String adm = (plpAddress.getAddressDeleveryModeCode() == null) ? null : plpAddress.getAddressDeleveryModeCode()
				.getCode();
		List<IClient> clientIDList = null;
		if (this.isQuickQuote(plpParty.getPolicyVersion())) {
			clientIDList = 	this.clientService.findClientByCsioNumber(plpParty.getFirstName(),
							plpParty.getLastName(), plpAddress.getCivicNumber(), plpAddress.getPostalCode(), adm,
							plpAddress.getAddressDeleveryModeNumber(), plpParty.getBirthDate(), csioNbr);
		} else {
			clientIDList = 	this.clientService.findClientByCsioNumber(plpParty.getFirstName(),
					plpParty.getLastName(), plpAddress.getCivicNumber(), plpAddress.getPostalCode(), adm,
					plpAddress.getAddressDeleveryModeNumber(), csioNbr);
		}
		List<Long> listClientId = new ArrayList<Long>();
		for (IClient client : clientIDList) {
			listClientId.add(client.getClient());
		}

		return listClientId;
	}

	/**
	 * validate if the list of policies is eligible for a roadBlock.
	 *
	 * -in force policy -futur policy -cancelled critical policy
	 *
	 * @param plpParty
	 * @param policies
	 * @param context
	 * @throws AutoQuoteRoadBlockException
	 */
	private void validateExistingPolicies(Party plpParty, List<IAgreement> policies, PolicyVersion aPolicyVersion,
			Long clientID, RoadBlockExceptionContextEnum context) throws AutoQuoteRoadBlockException, SingleIdActiveProductException {

		// get the province and manufacturerCompany from the current policy in AQ
		String province = aPolicyVersion.getInsurancePolicy().getManufacturingContext().getProvince().getCode();
		String manufacturerCompany = aPolicyVersion.getInsurancePolicy().getManufacturerCompany().getCode();

		for (IAgreement anAgreement : policies) {

			AutoquoteRoadBlockExceptionEnum roadBlockException = this.checkForRoadBlock(province, manufacturerCompany, anAgreement);

			if (roadBlockException != null) {
				// TODO [pm] - refactor br5009 to abr
				// BR5009 Broker assigned to existing client on broker's web site :
				// When a customer is recognized to be an existing Intact customer, IF the quote
				// was initiated from the broker's web site, the assigned broker must not be changed.
				ExternalSystemOriginCodeEnum origin = aPolicyVersion.getInsurancePolicy().getExternalSystemOrigin();

				if (roadBlockException.equals(AutoquoteRoadBlockExceptionEnum.BR2565) && origin != null
						&& origin.equals(ExternalSystemOriginCodeEnum.WEB_BROKER)) {
					throw new SingleIdActiveProductException(roadBlockException, context);
				}

				this.linkQuoteToProfile(plpParty, clientID);
				this.changeAgreementSubBroker(aPolicyVersion.getId(), anAgreement, aPolicyVersion.getInsurancePolicy()
							.getManufacturerCompany());
					throw new SingleIdActiveProductException(roadBlockException, context);
			}
		}
	}

	/**
	 * Check for roadBlock exception.
	 *
	 * @param province the province
	 * @param manufacturerCompany the manufacturer company
	 * @param anAgreement the {@link IAgreement}
	 * @return {@LINK AutoquoteRoadBlockExceptionEnum}
	 */
	private AutoquoteRoadBlockExceptionEnum checkForRoadBlock(String province, String manufacturerCompany, IAgreement anAgreement) {
		if (AgreementUtils.isValidOrFuturPolicy(anAgreement, province, manufacturerCompany)) {
			// Client with VALID policy or FUTURE policy
			// BR2565
			return AutoquoteRoadBlockExceptionEnum.BR2565;
		} else if (AgreementUtils.isCancelledCriticalReason(anAgreement, province, manufacturerCompany)) {
			// Client with Cancelled critical policy
			// BR2506
			return AutoquoteRoadBlockExceptionEnum.BR2506;
		}
		return null;
	}

	/**
	 * Change the sub broker of the policyversion.
	 *
	 * @param policyVersionID
	 * @param anAgreement
	 *
	 * @throws Exception
	 */
	private void changeAgreementSubBroker(Long policyVersionID, IAgreement anAgreement,
			ManufacturerCompanyCodeEnum manufacturerCode) {
		// get the subBrokers associated with the agreement
		ISubBrokers subBrokers = this.subBrokersService.getSubBrokersForAgreements(anAgreement.getAgreementId(),manufacturerCode.getCode());

		// replace the current broker of the policyVersion with the one found on the existing policy.
		if (subBrokers != null) {
			this.changeBrokerBusinessProcess.changeClientBroker(policyVersionID, subBrokers.getSubBrokerNumber(),
					subBrokers.getCompanyNumber(), AssignmentReasonCodeEnum.EXISTING_CLIENT,
					AssignmentOriginatorTypeCodeEnum.SYSTEM);
		} else {
			// a older policy should always be associated with a sub broker even if the original broker
			// does not exist anymore. A new broker should have been assigned.
			log.error(Logger.EVENT_SUCCESS, String.format("no previous sub broker found for agreement : %s", anAgreement.getAgreementId()));
			throw new SystemException();
		}
	}
}
