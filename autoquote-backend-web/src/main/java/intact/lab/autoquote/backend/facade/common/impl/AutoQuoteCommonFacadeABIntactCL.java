/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
* without the written permission of Intact Insurance
* Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
*/
package intact.lab.autoquote.backend.facade.common.impl;

import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.converter.impl.COMQuoteConverter;
import intact.lab.autoquote.backend.facade.impl.AutoQuoteCommonFacade;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Locale;

@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AutoQuoteCommonFacadeABIntactCL extends AutoQuoteCommonFacade {

    @AutowiredLocal
    protected IDriverBusinessProcess driverBusinessProcess;

    AutoQuoteCommonFacadeABIntactCL(@Qualifier("cifSubBrokersService") ISubBrokersService subBrokersService,
                                    ICommonBusinessProcess commonBusinessProcess, COMQuoteConverter comQuoteConverter,
                                    RatingConfig ratingConfig, IInsurancePolicyService insurancePolicyService,
                                    VehicleHelper vehicleHelper, BloomMQHandlerService bloomMQHandlerService,
                                    BrokerService brokerService) {
        super(subBrokersService, commonBusinessProcess, comQuoteConverter, ratingConfig, insurancePolicyService,
                vehicleHelper, bloomMQHandlerService, brokerService);
    }

    @Override
    public RuleExceptionResult validateDriversHARDRB(DriverComplementInfo driver, Locale locale) {
        return this.driverBusinessProcess.validateQuickQuoteHardRoadblock(driver, locale);
    }

    @Override
    public void assignDriverToVehicle(CommunicationObjectModel quote) throws AutoquoteFacadeException {
        super.assignDriverToVehicle(quote);
        this.callCreditScore(quote);
    }

    @Override
    protected void ajustPrincipalDriverAndRegisterOwner(
            CommunicationObjectModel quote, ComVehicle vehicle) {
        // no implementation required here
    }
}
