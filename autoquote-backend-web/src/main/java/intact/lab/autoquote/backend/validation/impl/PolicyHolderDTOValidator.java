package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IPolicyHolderDTOValidator;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("PolicyHolderDTOValidator")
public class PolicyHolderDTOValidator implements IPolicyHolderDTOValidator {

	@Override
	public void validate(PolicyHolderDTO policyHolder, Errors errors) {
		PolicyHolderDTO policyHolderDTO = policyHolder;
		validatePartyId(policyHolderDTO.getPartyId(), errors);
	}

	private void validatePartyId(Integer partyId, Errors errors) {
		if (null == partyId) {
			errors.rejectValue("partyId", BRulesExceptionEnum.NotBlank.getErrorCode(), "[partyId]");
		}
	}

}
