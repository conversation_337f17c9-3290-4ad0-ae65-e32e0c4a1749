package intact.lab.autoquote.backend.services.business.offer;

import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.exception.BusinessRuleException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.rating.IRatingService;

public interface IOfferBusinessProcess {

    /**
     * Added for the QuickQuote project,  this method call a pega service to get the 'SmartValues'
     * needed to rate the quote.
     *
     * @param aPlPolicyVersion
     * @return
     * @throws AutoquoteBusinessException
     */
    PolicyVersion determineQuoteInfos(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException;

    /**
     * Rate policy.
     *
     * @param policyVersion the policy version
     * @param isAgent the is agent flag
     * @param isUbiEnabled
     *
     * @return the policy offer rating
     *
     * @throws AutoQuoteRoadBlockException the autoquote road block exception
     * @throws AutoquoteBusinessException business exception
     */
    PolicyOfferRating ratePolicy(PolicyVersion policyVersion, boolean isAgent, boolean isUbiEnabled)
            throws AutoQuoteRoadBlockException, AutoquoteBusinessException;

    /**
     * Process executed once: after the first rate.
     *
     * @param aPolicyVersion {@link PolicyVersion}
     */
    void executePostFirstRating(PolicyVersion aPolicyVersion);

    /**
     * Retreive the right implementation of the rate manager service
     *
     * @return {@link IRateManagerService} instance
     */
    IRateManagerService getRateManagerService();

    /**
     * Retreive the right implementation of the rating service
     *
     * @return {@link IRatingService} instance
     */
    IRatingService getRatingService();

    /**
     * Added for the QuickQuote project,  this method is called when user switches from one offer to another. It updates UBI status
     *
     * @param aPolicyVersion {@link PolicyVersion}
     */
    void manageUbiStatus(PolicyVersion aPolicyVersion);

    /**
     * Select offer.
     *
     * @param policyVersion the policy version
     * @param isUbiEnabled ubi enabled indicator
     *
     * @throws AutoquoteRatingException the rating exception
     * @throws BusinessRuleException
     */
    void selectOffer(PolicyVersion policyVersion, Boolean isUbiEnabled) throws AutoquoteRatingException,
            BusinessRuleException;

    /**
     * Validate post br.
     *
     * @param policyVersion the policy version
     *
     * @throws BusinessRuleException the road block exception
     */
    void validatePostBR(PolicyVersion policyVersion) throws BusinessRuleException;
}
