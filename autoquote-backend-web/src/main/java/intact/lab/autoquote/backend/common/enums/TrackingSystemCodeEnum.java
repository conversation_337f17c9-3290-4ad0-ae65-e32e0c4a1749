package intact.lab.autoquote.backend.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Getter
@RequiredArgsConstructor
public enum TrackingSystemCodeEnum {

    BANSHEE_ICALOCK("B"),
    ARMED_GUARD_SHERLOCK("A"),
    VIPER("V"),
    MERLIN("M"),
    HAWK_200_AUTOLAND("H"),
    <PERSON><PERSON><PERSON>("AU"),
    ADAV("AD"),
    AUTOGRAPH("AG"),
    AMERI_KOP("AK"),
    AUTOLUCK("AL"),
    AUTOMOBILE_MANUFACTURER("AM"),
    GUARDIAN("GU"),
    LARLOK("LA"),
    OTOPROTEC("OT"),
    SHERLOCK("SH"),
    VIN_LOCK("VI"),
    ULTRACAR("UL"),
    BOOMERANG_1("B1"),
    BOOMERANG_2("B2"),
    INTERCEPTER_STAR_TRACK("IN"),
    LYNX("LY"),
    MOBILIUS("MO"),
    NAVLYNX_AUTOGUARD("NA"),
    ON_STAR("OS"),
    SATELINX("SA"),
    SPAVTRACK("SP"),
    ECONOTRACK("ET"),
    TAG("TG"),
    DATADOTDNA("DA"),
    GLOBALGLOBALI("GL"),
    MICRODOTDNA("MC"),
    THREEEYETRACKING("3Y"),
    CELLUTRACK("CT"),
    LOJACKBOOMERANG("LJ"),
    MLINK("ML"),
    ORCA("OC"),
    VIGILGPS("VG"),
    BARRACUDA("BA"),
    KOLOMBO("KO");

    private final String code;

    public static TrackingSystemCodeEnum valueOfCode(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        for (TrackingSystemCodeEnum v : values()) {
            if (v.code.equals(value)) {
                return v;
            }
        }

        throw new IllegalArgumentException("No enum value found for code: " + value);
    }
}
