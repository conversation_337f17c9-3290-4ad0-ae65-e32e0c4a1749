package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.som.interfaces.risk.LegacyRatingInfoByPostalCode;
import intact.lab.autoquote.backend.datamediator.DMConstants;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToSOMLegacyRatingInfo;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Transactional
public class DataMediatorToSOMLegacyRatingInfo implements IDataMediatorToSOMLegacyRatingInfo {
    public void convertToSom(InsuranceRisk plInsuranceRisk, com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk) {
        if (plInsuranceRisk != null && somInsuranceRisk != null) {
            LegacyRatingInfoByPostalCode legacyRatingInfoByPostalCode = somInsuranceRisk.getTheLegacyRatingInfoByPostalCode();
            if (legacyRatingInfoByPostalCode == null) {
                legacyRatingInfoByPostalCode = somInsuranceRisk.createTheLegacyRatingInfoByPostalCode();
            }

            DataMediatorUtils.setAttributesByReflectionFromPLtoSOM(DMConstants.plLegacyRatingInfoByPostalCodes, DMConstants.somLegacyRatingInfoByPostalCodes, plInsuranceRisk, legacyRatingInfoByPostalCode, new Boolean[0]);
            legacyRatingInfoByPostalCode.setAutomobileTerritorySpecifiedPerils(legacyRatingInfoByPostalCode.getAutomobileTerritoryComprehensive());
            legacyRatingInfoByPostalCode.setAutomobileTerritoryPostalCodeAdjustment(legacyRatingInfoByPostalCode.getAutomobileTerritoryRating());
        }

    }
}
