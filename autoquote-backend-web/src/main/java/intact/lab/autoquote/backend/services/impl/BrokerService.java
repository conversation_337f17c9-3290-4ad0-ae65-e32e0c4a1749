package intact.lab.autoquote.backend.services.impl;

import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.LanguageEnum;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.ApplicationOriginEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.enums.PhoneNumberUsageEnum;
import com.ing.canada.cif.domain.helpers.SubBrokerHelper;
import com.ing.canada.cif.domain.impl.BrokerAssignationParameterBean;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.exception.SubBrokerServiceException;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.BrokerException;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static org.owasp.esapi.Logger.EVENT_UNSPECIFIED;

@Service
public class BrokerService {

    protected static final Logger LOG = ESAPI.getLogger(BrokerService.class);

    private final ISubBrokersService subBrokersService;

    public BrokerService(@Qualifier("cifSubBrokersService") ISubBrokersService subBrokersService) {
        this.subBrokersService = subBrokersService;
    }

    @Transactional
    public ComBrokerInfo getBrokerInfo(ComContext aComContext) {
        ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();
        ManufacturingContext mCtxt = MediatorUtils.convertContext(aComContext);
        ISubBrokers subBroker = this.buildBroker(aComContext, mCtxt, brokerInfo);
        if(subBroker != null && subBroker.getSubBrokerId() > 0) {
            this.manageBrokerInfo(aComContext, subBroker);
            return brokerInfo;
        } else {
            throw new BrokerException("Invalid sub broker");
        }
    }

    public ISubBrokers buildBroker(ComContext aComContext, ManufacturingContext mCtxt, ComBrokerInfo brokerInfo) {
        String postalCode = brokerInfo.getPostalCode();
        String companyNumber = mCtxt.getManufacturerCompany().getCode();
        String subBrokerNumber = aComContext.getBrokerInfo().getSubBrokerNumber();
        String brokerWebSiteOrigin = (brokerInfo.getBrokerWebsiteOrigin() == null) ? null : brokerInfo.getBrokerWebsiteOrigin().getCode();
        String lineOfBusiness = aComContext.getLineOfBusiness() != null && ComLineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(aComContext.getLineOfBusiness())
                ? ComLineOfBusinessCodeEnum.COMMERCIAL_LINES.getCode() : ComLineOfBusinessCodeEnum.PERSONAL_LINES.getCode();

        String comLangCode = aComContext.getLanguage().getCode();
        BrokerAssignationParameterBean parameter = new BrokerAssignationParameterBean.Builder()
                .applicationId(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode())
                .company(companyNumber)
                .language(LanguageEnum.valueOfIsoCode(comLangCode.toLowerCase()).get3CharLanguageCode())
                .lineOfBusiness(lineOfBusiness)
                .postalCode(postalCode)
                .quoteSource((StringUtils.equalsIgnoreCase(brokerWebSiteOrigin, ComBrokerWebSiteOriginEnum.INTACT.getCode()) ? null : brokerWebSiteOrigin))
                .subBrokerNumber(subBrokerNumber)
                .fetchSubBroker(true)
                .build();


        LOG.debug(EVENT_UNSPECIFIED, String.format("findBroker for parameters: %s.", parameter.toString()));
        try {
            return this.subBrokersService.findBroker(parameter);
        } catch (SubBrokerServiceException e) {
            throw new BrokerException("Error while retrieving broker information for tracking number" + aComContext.getTrackingNumber()
                    + "subBrokerNumber" + subBrokerNumber);
        }
    }

    private void manageBrokerInfo(ComContext aComContext, ISubBrokers subBroker) {
        ApplicationOriginEnum origin = ComBrokerWebSiteOriginEnum.BROKER.equals(aComContext.getBrokerInfo().getBrokerWebsiteOrigin()) ? ApplicationOriginEnum.CNT : ApplicationOriginEnum.WINI;
        String section = aComContext.getLineOfBusiness() == ComLineOfBusinessCodeEnum.COMMERCIAL_LINES ? "OFFPG" : null;
        IContextualPhoneNumbers contextualPhoneNumber = subBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, aComContext.getLineOfBusiness() == null ? LineOfBusinessEnum.PERSONAL_LINE : LineOfBusinessEnum.COMMERCIAL_LINE, origin, section, PhoneNumberUsageEnum.BUSINESS_PHONE);

        aComContext.getBrokerInfo().setPhoneNumber(contextualPhoneNumber != null ? contextualPhoneNumber.toDashedString() : null);
        aComContext.getBrokerInfo().setLogo(SubBrokerHelper.getLogo(aComContext.getLanguage().getCode(), subBroker));
        aComContext.getBrokerInfo().setCallBackAvailable(subBroker.getAllowClientSchCallbackInd());
    }
}
