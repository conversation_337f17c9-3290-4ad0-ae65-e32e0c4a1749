package intact.lab.autoquote.backend.services;

/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */

import intact.lab.autoquote.backend.common.model.ValidValueBO;

import java.util.List;
import java.util.Locale;


/**
 * Defines a ValueDomain service.
 */
public interface IValueDomainService {

	/**
	 * Gets the list of number of kilometers driven annually applicable for a quick quote.
	 *
	 * @param aLocale {@link Locale}
	 * @param ascending {@link Boolean}
	 * @return {@link List} of {@link ValidValueBO}
	 */
	List<ValidValueBO> getListKmAnnuallyQf(Locale aLocale, Boolean ascending);

	/**
	 * Gets the list of years continuously insured
	 *
	 * @param aLocale {@link Locale}
	 * @return {@link List} of {@link ValidValueBO}
	 */
	List<ValidValueBO> getListYearsContinuouslyInsuredQf(Locale aLocale);

	/**
	 * Gets the list of driver license type applicable to a quick quote.
	 *
	 * @param aLocale {@link Locale}
	 * @return {@link List} of {@link ValidValueBO}
	 */
	List<ValidValueBO> getListDriverLicenceTypeQf(Locale aLocale);


	/**
	 * Gets the list of possible values for the period of time living at the current address applicable to a quick quote.
	 * */
	List<ValidValueBO> getListLastMoveDateRangeQf(Locale locale);//QQID-22=list_last_move_date_range_fr_CA.properties

	/**
	 * Gets the list tracking system brand.
	 *
	 * @param locale the locale
	 * @return the list tracking system brand
	 */
	List<ValidValueBO> getListTrackingSystemBrandQf(Locale locale);//QQID-30=list_tracking_system_brand_en_CA_QC.properties

	/**
	 * Gets the list anti theft device.
	 *
	 * @param locale the locale
	 * @return the list anti theft device
	 */
	List<ValidValueBO> getListAntiTheftDeviceQf(Locale locale);//QQID-29=list_anti_theft_device_en_CA_ON.properties

	/**
	 * Gets the list marital status.
	 *
	 * @param locale the locale
	 * @return the list marital status
	 * list_marital_status-qf_en_CA_ON.properties
	 */
	List<ValidValueBO> getListMaritalStatusQf(Locale locale);//QQID-36 list_marital_status_en_CA_ON

	/**
	 * Gets the list group discount qf.
	 *
	 * @param locale the locale
	 * @return the list group discount qf
	 * Now List for Group Discount QuickQuote
	 */
	List<ValidValueBO> getListCarHomeDiscountQf(Locale locale);//QQID-32 list_car_home_discount_qf_en_CA_ON.properties

	/**
	 * Gets the list claim nature.
	 *
	 * @param locale the locale
	 * @return the list claim nature
	 */
	List<ValidValueBO> getListClaimNatureQf(Locale locale);//QQID-41 list_claim_nature_en_CA_ON.properties

	/**
	 * Gets the list of loss qf.
	 *
	 * @param locale the locale
	 * @return the list of loss qf
	 *  Now List for Group Discount Type Of lesse
	 */
	List<ValidValueBO> getListOfLossQf(Locale locale);//QQID-42 list_of_loss_qf_fr_CA_QC.properties

	/**
	 * Gets the list minor infractions.
	 *
	 * @param locale the locale
	 * @return the list minor infractions
	 */
	List<ValidValueBO> getListMinorInfractionsQf(Locale locale);//QQID-38 list_minor_infractions_qf_en_CA_QC.properties

	/**
	 * Gets the list annual km range business.
	 *
	 * @param locale the locale
	 * @return the list annual km range business
	 */
	List<ValidValueBO> getListAnnualKmRangeBusinessQf(Locale locale);//QQID-35=list_annual_km_range_business_en_CA_QC.properties

	/**
	 * Gets the list of street type.
	 *
	 * @param aLocale the a locale
	 * @return the street type
	 */
	List<ValidValueBO> getListStreetType(Locale aLocale);


	/**
	 * Gets the list of gender.
	 *
	 * @param locale the locale
	 * @return the list of gender
	 */
	List<ValidValueBO> getListGenderQf(Locale locale);

	/**
	 * Gets the list of months.
	 *
	 * @param locale the locale
	 * @return the list of months
	 */
	List<ValidValueBO> getListMonthsQf(Locale locale);

	/**
	 * Gets the list of years insured as principal driver.
	 *
	 * @param aLocale the a locale
	 *
	 * @return the list of years insured as principal
	 */
	List<ValidValueBO> getListYearsInsuredAsPrincipalQf(Locale aLocale);

	/**
	 * Returns the list of possible values for the date of a minor infraction.
	 * */
	List<ValidValueBO> getListMinorInfractionsYearQf(Locale locale);

	/**
	 * Gets the list claim nature year for Quick Quote.
	 *
	 * @param locale the locale
	 * @return the list claim nature year
	 */
	List<ValidValueBO> getListClaimNatureYearQf(Locale locale);//QQID-44=list_claim_nature_year_qf_LOCAL.properties

	/**
	 * Gets the list of last conviction date.
	 * */
	List<ValidValueBO> getListLastConvictionDateRangeQf(Locale locale);//list_last_conviction_date_range_qf_fr_CA_AB.properties

	/**
	 * Gets the list of most recent insurer qf for QQ Intact AB.
	 *
	 * @param locale the locale
	 * @return the list of most recent insurer
	 */
	List<ValidValueBO> getListMostRecentInsurerQf(Locale locale); //list_most_recent_insurer_qf_en_CA_AB.properties
}

