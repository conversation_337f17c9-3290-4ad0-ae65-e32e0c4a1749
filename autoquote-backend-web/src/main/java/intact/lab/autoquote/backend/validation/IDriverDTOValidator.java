package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.joda.time.LocalDate;
import org.springframework.validation.Errors;

public interface IDriverDTOValidator {

	void validate(DriverDTO driverDTO, Errors errors, ValidationContext context);

	void validateLicenseObtentionDate(LocalDate licenseObtentionDate, Errors errors, ValidationContext context);

	void validateDriverLicenseType(String driverLicenseType, Errors errors, ValidationContext context);

}
