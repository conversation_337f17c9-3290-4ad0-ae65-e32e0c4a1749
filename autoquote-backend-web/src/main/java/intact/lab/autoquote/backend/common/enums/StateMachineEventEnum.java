/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.common.enums;

/**
 * This enum represent event that can occur in the application. it was needed to be more granular vs the state.
 * 
 * <AUTHOR>
 */
public enum StateMachineEventEnum {

	// QUOTE EVENTS
	NEW_QUOTE, RETRIEVE_QUOTE, RETRIEVE_ROADBLOCK_QUOTE, UPDATE_SAVINGS,

	// VEHICLE EVENTS
	ADD_VEHICLE, VIEW_VEHICLE, SAVE_VEHICLE, DELETE_VEHICLE,

	// DRIVER EVENTS
	ADD_DRIVER, VIEW_DRIVER, SAVE_DRIVER, DELETE_DRIVER,

	// USAGE EVENTS
	VIEW_USAGE, SAVE_USAGE, VIEW_NOHIT,

	// OFFER EVENTS
	VIEW_OFFER, SAVE_OFFER, REC<PERSON><PERSON>LA<PERSON>, SECOND_CONSENT,

	// BIND EVENTS
	VIEW_BIND, BIND, VIEW_VEHICLE_DETAILS, VIEW_PAYMENT, VIEW_PROFILE, PURCHASE,

	// ROADBLOCK EVENTS
	ROADBLOCK;

	/**
	 * Default enum constructor.
	 */
	private StateMachineEventEnum() {
		// Nothing to do here
	}
}
