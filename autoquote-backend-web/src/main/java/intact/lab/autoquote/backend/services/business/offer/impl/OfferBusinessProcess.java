package intact.lab.autoquote.backend.services.business.offer.impl;

import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.NumberStabilityMonthsCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPolicyOfferRatingService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.ing.canada.ss.base.BaseException;
import com.intact.business.rules.exception.BusinessRuleException;
import com.intact.business.rules.offer.BR298_DoNotBindSimulationQuotation;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.AutoquoteBusinessException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPL;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import intact.lab.autoquote.backend.services.business.offer.IOfferBusinessProcess;
import intact.lab.autoquote.backend.services.business.sessionmanager.IConfigurator;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Calendar;

@Transactional
public abstract class OfferBusinessProcess implements IOfferBusinessProcess {

    private static final Logger LOG = ESAPI.getLogger(OfferBusinessProcess.class);

    @Autowired
    protected IDataMediatorToPL dataMediatorToPL;

    @Autowired
    DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

    @Autowired
    protected IPolicyVersionHelper policyVersionHelper;

    @Autowired
    protected IDateManagerService capiPolicyChangeDateService;

    @Autowired
    protected IPolicyVersionService policyVersionService;

    @Autowired
    private IConfigurator appConfig;

    @Autowired
    private IPolicyOfferRatingService policyOfferRatingService;

    @Autowired
    private BR298_DoNotBindSimulationQuotation br298;

    @Override
    @Transactional
    public PolicyVersion determineQuoteInfos(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException {

        StopWatch performanceWatch = new StopWatch();
        if (performanceWatch.isRunning()) {
            performanceWatch.stop();
        }

        // 1. Convert the PL PolicyVersion to the SOM format
        performanceWatch.start("    >> dataMediatorToSOM.convertTo_SOM");
        com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = dataMediatorToSOMServiceFactory
                .getService("dataMediatorToSOM").convertTo_SOM(aPlPolicyVersion);
        performanceWatch.stop();

        // 2. PEGA - Apply diagnostic for savings
        ManufacturingContext ctxt = aPlPolicyVersion.getInsurancePolicy().getManufacturingContext();
        performanceWatch.start(" >> determineQuoteInfo");

        try {

            somPolicyVersion = this.getExecuteService().determineQuoteInfos(ctxt, somPolicyVersion);

        } catch (BaseException bex) {
            throw new AutoquoteBusinessException("Determine quote infos for [" + aPlPolicyVersion + "] has failed!",
                    this.getRealException(bex));
        }

        performanceWatch.stop();

        // 3. Convert back to PL
        performanceWatch.start("    >> dataMediatorToPL.convertTo_PL");
        PolicyVersion plPolicyVersion = this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPlPolicyVersion, false);
        performanceWatch.stop();

        this.determineQuoteInfosPostProcess(plPolicyVersion);

        LOG.trace(Logger.EVENT_SUCCESS, new StringBuilder("agreement nbr: ")
                .append(aPlPolicyVersion.getInsurancePolicy().getAgreementNumber()).append("\n")
                .append(performanceWatch.prettyPrint()).toString());

        this.policyVersionService.persistCascadeAll(plPolicyVersion);

        return plPolicyVersion;
    }

    protected abstract IExecuteService getExecuteService();

    /**
     * @param aPlPolicyVersion
     * @throws AutoquoteBusinessException
     */
    protected void determineQuoteInfosPostProcess(PolicyVersion aPlPolicyVersion) throws AutoquoteBusinessException {

        if (aPlPolicyVersion.getInsurancePolicy().getManufacturerCompany()
                .equals(ManufacturerCompanyCodeEnum.BELAIRDIRECT)) {
            if (!ApplicationModeEnum.BUNDLE_QUOTE.equals(aPlPolicyVersion.getInsurancePolicy().getApplicationMode())) {
                PolicyHolder policyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(aPlPolicyVersion);
                if (policyHolder != null && policyHolder.getNumberStabilityMonths() != null) {
                    // Pega only set the NumberStabilityMonths. Convert the value and set the NumberStabilityMonthsCode
                    switch (policyHolder.getNumberStabilityMonths()) {
                        case 6:
                            policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.LESS_THAN_SIX);
                            break;
                        case 9:
                            policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.BETWEEN_SIX_AND_TWENTYFOUR);
                            break;
                        case 54:
                            policyHolder.setNumberStabilityMonthsCode(NumberStabilityMonthsCodeEnum.TWENTYFOUR_OR_MORE);
                            break;
                        default:
                            throw new AutoquoteBusinessException("NumberStabilityMonths with value ("
                                    + policyHolder.getNumberStabilityMonths() + ") cannot be used for Belair.");
                    }
                }
            }

            // populate the combinedPolicyScenarioCode from combinePolicyCode because pega only set one field.
            /*
             * aPlPolicyVersion.setCombinedPolicyScenarioCode(CombinedPolicyScenarioCodeEnum.valueOfCode(aPlPolicyVersion
             * .getCombinedPolicyCode().getCode()));
             */

            // set the dateOfLastMove based on NumberStabilityMonthsCode
            PolicyHolder policyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(aPlPolicyVersion);
            NumberStabilityMonthsCodeEnum stabilMonthCode = policyHolder != null ? policyHolder.getNumberStabilityMonthsCode() : null;
            if (stabilMonthCode != null) {
                Calendar dateOfLastMove = Calendar.getInstance();
                dateOfLastMove.setTime(this.capiPolicyChangeDateService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, aPlPolicyVersion.getId()));
                dateOfLastMove.add(Calendar.MONTH, -NumberStabilityMonthsCodeEnum.getNumberStabilityMonths(stabilMonthCode));
                policyHolder.getParty().setDateOfLastMove(dateOfLastMove.getTime());
            }
        }
    }

    /**
     * Rate policy.
     *
     * @param aPolicyVersion the policy version
     * @return the policy offer rating
     * @throws AutoQuoteRoadBlockException the autoquote road block exception
     * @throws AutoquoteBusinessException
     * @see IOfferBusinessProcess#ratePolicy(PolicyVersion, boolean, boolean)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PolicyOfferRating ratePolicy(final PolicyVersion aPolicyVersion, final boolean isAgent,
                                        final boolean isUbiEnabled) throws AutoQuoteRoadBlockException, AutoquoteBusinessException {

        OfferBusinessProcess.LOG.info(Logger.EVENT_SUCCESS, "Executing ratePolicy business process");

        PolicyVersion policyVersion = null;

        try {
            policyVersion = this.getRateManagerService().rateTheWholeQuotation(aPolicyVersion, isAgent, isUbiEnabled);

            this.setQuoteAnnualPremium(policyVersion);


            // select offer so that ubi can be enrolled
            this.manageUbiStatus(policyVersion);


        } catch (AutoquoteRatingException are) {
            throw new AutoquoteBusinessException("An error occured while rating the policy.", are);
        }

        return policyVersion.getLatestPolicyOfferRating();

    }

    /**
     * Sets the quote annual premium.
     *
     * @param policyVersion the new quote annual premium
     */
    private void setQuoteAnnualPremium(final PolicyVersion policyVersion) {

        Integer annualPremium = 0;

        for (InsuranceRisk risk : policyVersion.getInsuranceRisks()) {

            InsuranceRiskOffer selectedOffer = risk.getSelectedInsuranceRiskOffer();

            if (selectedOffer == null || selectedOffer.getAnnualPremium() == null) {
                policyVersion.getLatestPolicyOfferRating().setAnnualPremium(null);
                policyVersion.getLatestPolicyOfferRating().setFullTermPremium(null);
                policyVersion.getLatestPolicyOfferRating().setFullTermPremiumTaxable(null);
                return;
            }

            annualPremium += selectedOffer.getAnnualPremium();

        }

        double yearMultiplicator = 0;
        if (policyVersion.getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.TWELVE_MONTHS)) {
            yearMultiplicator = 1;
        } else if (policyVersion.getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.TWENTYFOUR_MONTHS)) {
            yearMultiplicator = 2;
        } else if (policyVersion.getPolicyTermInMonths().equals(PolicyTermInMonthsEnum.SIX_MONTHS)) {
            yearMultiplicator = 0.5;
        }

        policyVersion.getLatestPolicyOfferRating().setAnnualPremium(annualPremium);
        policyVersion.getLatestPolicyOfferRating().setFullTermPremium((int) (annualPremium * yearMultiplicator));

        double untaxablePremium = this.getRatingService().getUntaxableAmount(policyVersion.getLatestPolicyOfferRating());
        policyVersion.getLatestPolicyOfferRating().setFullTermPremiumTaxable(
                (int) ((annualPremium - untaxablePremium) * yearMultiplicator));

    }

    /**
     * Gets the real exception.
     *
     * @param e the BaseException
     * @return the real exception
     */
    protected Exception getRealException(BaseException e) {
        Exception cause = e;
        if (e.getRealException() != null) {
            cause = e.getRealException();
        }
        return cause;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void executePostFirstRating(final PolicyVersion aPolicyVersion) {
        // Not implemented at this level
    }

    @Override
    public void manageUbiStatus(final PolicyVersion aPolicyVersion) {
        this.getRatingService().manageUbiStatus(aPolicyVersion);
    }

    @Override
    public void selectOffer(final PolicyVersion policyVersion, Boolean isUbiEnabled) throws AutoquoteRatingException,
            BusinessRuleException {

        StopWatch performanceWatch = new StopWatch();
        if (performanceWatch.isRunning()) {
            performanceWatch.stop();
        }

        if (isUbiEnabled == null) {
            isUbiEnabled = this.appConfig.isUbi(policyVersion.getInsurancePolicy().getManufacturingContext()
                    .getProvince().getCode(), policyVersion.getDistributorCode() == null ? null : policyVersion.getDistributorCode().getCode());
        }

        LOG.info(Logger.EVENT_SUCCESS, "Executing selectOffer business process");

        // Call the select offer service; this will handle the CAA and other
        // rules
        this.getRateManagerService().selectOffer(policyVersion, performanceWatch, isUbiEnabled);

        performanceWatch.start("    >> OfferBusinessProcess.selectOffer second part");

        // Update the quote's annual premium after selecting the offer
        this.setQuoteAnnualPremium(policyVersion);

        this.policyOfferRatingService.persist(policyVersion.getLatestPolicyOfferRating());

        this.policyVersionService.persist(policyVersion);

        performanceWatch.stop();

        if (LOG.isTraceEnabled()) {
            LOG.trace(Logger.EVENT_SUCCESS, String.format("agreement nbr: %s\n%s", policyVersion.getInsurancePolicy().getAgreementNumber(), performanceWatch.prettyPrint()));
        }
    }

    @Override
    public void validatePostBR(final PolicyVersion aPolicyVersion) throws BusinessRuleException {
        this.br298.validate(aPolicyVersion);

    }
}
