package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.validation.IDriverDTOValidator;
import intact.lab.autoquote.backend.validation.IPartyDTOValidator;
import intact.lab.autoquote.backend.validation.IPolicyHolderDTOValidator;
import intact.lab.autoquote.backend.validation.IQuoteDTOValidator;
import intact.lab.autoquote.backend.validation.IValidatorFactory;
import intact.lab.autoquote.backend.validation.IVehicleDTOValidator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.Map;

@Component
public class ValidatorFactory implements IValidatorFactory {

	private final IPartyDTOValidator partyDTOValidator;
	private final IPolicyHolderDTOValidator policyHolderDTOValidator;
	private final IVehicleDTOValidator vehicleDTOValidator;
	private final IDriverDTOValidator driverDTOValidator;
	private final Map<String, IQuoteDTOValidator> quoteValidatorDynamicMap;

	public ValidatorFactory(
			@Qualifier("PartyDTOValidator") IPartyDTOValidator partyDTOValidator,
			@Qualifier("PolicyHolderDTOValidator") IPolicyHolderDTOValidator policyHolderDTOValidator,
			@Qualifier("VehicleDTOValidator") IVehicleDTOValidator vehicleDTOValidator,
			@Qualifier("DriverDTOValidator") IDriverDTOValidator driverDTOValidator,
			Map<String, IQuoteDTOValidator> quoteValidatorDynamicMap) {
		this.partyDTOValidator = partyDTOValidator;
		this.policyHolderDTOValidator = policyHolderDTOValidator;
		this.vehicleDTOValidator = vehicleDTOValidator;
		this.driverDTOValidator = driverDTOValidator;
		this.quoteValidatorDynamicMap = quoteValidatorDynamicMap;
	}

	public IQuoteDTOValidator getQuoteDTOValidator(ValidationContext context, Errors errors) {

		IQuoteDTOValidator quoteDTOValidator = this.quoteValidatorDynamicMap.get("quoteDTOValidator" + context.getProvince().toUpperCase());

		quoteDTOValidator.setContext(context);
		quoteDTOValidator.setPartyDTOValidator(partyDTOValidator);
		quoteDTOValidator.setPolicyHolderDTOValidator(policyHolderDTOValidator);
		quoteDTOValidator.setVehicleDTOValidator(vehicleDTOValidator);
		quoteDTOValidator.setDriverDTOValidator(driverDTOValidator);

		return quoteDTOValidator;
	}

}
