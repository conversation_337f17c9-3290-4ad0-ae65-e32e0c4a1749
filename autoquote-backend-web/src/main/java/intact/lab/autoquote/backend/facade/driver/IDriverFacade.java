/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2013, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.facade.driver;

import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;

import java.util.List;

/**
 * This facade contains the methods that are available for the Driver.
 * 
 * <AUTHOR> L.
 * 
 */
public interface IDriverFacade {


	/**
	 * Returns the matching municipality information for a given postal code. Any error during the call can be found in
	 * the 'ValidationErrors' collection of the returned object.
	 * 
	 * @param postalCode {@link String}
	 * @param comContext {@link ComContext}
	 * @return {@link ComMunicipalityInfo}
	 * @throws AutoQuoteException
	 */
	ComMunicipalityInfo getMunicipalityByPostalCode(ComContext comContext, String postalCode) throws AutoQuoteException;

	/**
	 * Returns the work sectors list for for the corresponding context.
	 * @param comContext {@link ComContext}
	 * @return {@link List}<{@link ValidValue}>
	 * @throws AutoQuoteException
	 */
	List<ValidValue> getWorkSectorsList(ComContext comContext) throws AutoQuoteException;

	/**
	 * Returns the occupations list for a corresponding work sector code.
	 *
	 * @param comContext {@link ComContext}
	 * @param workSector work sector
	 * @return {@link List}<{@link ValidValue}>
	 * @throws AutoQuoteException
	 */
	List<ValidValue> getOccupationsList(ComContext comContext, String workSector) throws AutoQuoteException, AutoquoteFacadeException;

	/**
	 * Returns the list of affinity groups based on the current context.
	 *
	 * @param comContext {@link ComContext}
	 * @return {@link List}<{@link ValidValue}>
	 * @throws AutoquoteFacadeException
	 */
	List<ValidValue> getGroupList(ComContext comContext);
}
