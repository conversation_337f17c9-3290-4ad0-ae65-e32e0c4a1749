package intact.lab.autoquote.backend.quotestatemanager.impl;

import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.offer.BR15680_NoMonthlyPremiumWhenBadCreditScore;

@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class QuoteStateManagerABIntactCL extends QuoteStateManagerIntact {


    private final BR15680_NoMonthlyPremiumWhenBadCreditScore br15680;

    public QuoteStateManagerABIntactCL(BR15680_NoMonthlyPremiumWhenBadCreditScore br15680) {
        this.br15680 = br15680;
    }

    protected boolean isMonthlyPaymentsEligible(PolicyVersion aPolicyVersion) {
        if (br15680.isMonthlyPremiumAvailable(aPolicyVersion)) {
            // br15680 OK; now check the super impl for other rules.
            return super.isMonthlyPaymentsEligible(aPolicyVersion);
        }
        return false; // br15680 returned monthly premium not available
    }
}
