package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.ApplicationOriginEnum;
import com.ing.canada.cif.domain.enums.BrokerWebAccessTypeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.enums.PhoneNumberUsageEnum;
import com.ing.canada.cif.domain.helpers.SubBrokerHelper;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.ApplicationFlowStateCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ConditionVehicleWhenBoughtCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComRoadBlockTypeEnum;
import com.intact.com.util.ComRoadBlock;
import com.intact.com.vehicle.ComVehicle;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.enums.BuildPhoneNumberActionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.exception.BrokerException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.common.utils.MediatorUtil;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.converter.impl.COMQuoteConverter;
import intact.lab.autoquote.backend.facade.ICommonFacade;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.joda.time.DateTime;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;


@Slf4j
public class AutoQuoteCommonFacade extends AbstractCommonFacade {

    protected static final Logger LOG = ESAPI.getLogger(AutoQuoteCommonFacade.class);

    private final ISubBrokersService subBrokersService;
    protected final RatingConfig ratingConfig;
    protected final COMQuoteConverter comQuoteConverter;
    private final IInsurancePolicyService insurancePolicyService;
    private final VehicleHelper vehicleHelper;
    private final BloomMQHandlerService bloomMQHandlerService;
    private final BrokerService brokerService;

    @AutowiredLocal
    protected IVehicleBusinessProcess vehicleBusinessProcess;

    @AutowiredLocal
    protected IDriverBusinessProcess driverBusinessProcess;

    @Autowired
    public AutoQuoteCommonFacade(@Qualifier("cifSubBrokersService") ISubBrokersService subBrokersService,
                                 ICommonBusinessProcess commonBusinessProcess, COMQuoteConverter comQuoteConverter,
                                 RatingConfig ratingConfig, IInsurancePolicyService insurancePolicyService, VehicleHelper vehicleHelper,
                                 BloomMQHandlerService bloomMQHandlerService, BrokerService brokerService) {
        this.subBrokersService = subBrokersService;
        this.vehicleHelper = vehicleHelper;
        this.commonBusinessProcess = commonBusinessProcess;
        this.comQuoteConverter = comQuoteConverter;
        this.ratingConfig = ratingConfig;
        this.insurancePolicyService = insurancePolicyService;
        this.bloomMQHandlerService = bloomMQHandlerService;
        this.brokerService = brokerService;
    }

    @Override
    public CommunicationObjectModel createQuote(ComContext aComContext) throws AutoQuoteQuoteServiceException {

        CommunicationObjectModel com = new CommunicationObjectModel();
        PolicyVersion policyVersion;

        try{
            ManufacturingContext mCtxt = MediatorUtil.convertContext(aComContext);

            ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();

            String subBrokerIdString = "";
            if (brokerInfo.getSubBrokerNumber() != null) {
                long subBrokerId = this.buildBrokerInfoBeforeCreateQuote(aComContext, mCtxt);
                subBrokerIdString = String.valueOf(subBrokerId);
            }

            ComBrokerWebSiteOriginEnum comBrokerWebSiteOrigin = aComContext.getBrokerInfo() != null ? aComContext.getBrokerInfo().getBrokerWebsiteOrigin() : null;

            MediatorUtil.getPLPdistributor(aComContext.getDistributor());

            policyVersion = this.newQuote(aComContext, mCtxt, subBrokerIdString, ApplicationModeEnum.QUICK_QUOTE, ApplicationFlowStateCodeEnum.VEHICLE, comBrokerWebSiteOrigin,
                    LineOfBusinessCodeEnum.COMMERCIAL_LINES);
            this.commonBusinessProcess.createActivity(policyVersion, BusinessTransactionActivityCodeEnum.INITIAL_QUOTE_OR_POLICY_CREATION, UserTypeCodeEnum.CLIENT);

            com.setContext(aComContext);
        } catch (Exception e) {
            if (LOG.isErrorEnabled()) {
                LOG.error(Logger.EVENT_SUCCESS, String.format("an exception occured: %s", e.getMessage()), e);
            }

            throw new AutoQuoteQuoteServiceException(AutoQuoteQuoteServiceException.EXEC_CREATE_QUOTE_ERROR_CODE, e.getMessage());
        }

        return this.buildCom(policyVersion, com);
    }

    private long buildBrokerInfoBeforeCreateQuote(ComContext aComContext, ManufacturingContext mCtxt) throws BrokerException {
        ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();
        ISubBrokers subBroker = brokerService.buildBroker(aComContext, mCtxt, brokerInfo);
        if (subBroker != null && subBroker.getSubBrokerId() > 0) {
            brokerInfo.setLogo(SubBrokerHelper.getLogo(aComContext.getLanguage().getCode(), subBroker));
            this.setPhoneNumberForAction(BuildPhoneNumberActionEnum.FOR_CREATE, subBroker, aComContext);
            return subBroker.getSubBrokerId();
        }

        throw new BrokerException("Invalid sub broker");
    }

    private void setPhoneNumberForAction(BuildPhoneNumberActionEnum action, ISubBrokers subBroker, ComContext aComContext) {
        ComBrokerInfo brokerInfo = aComContext.getBrokerInfo();
        ApplicationOriginEnum originPhone = ExternalSystemOriginCodeEnum.WEB_BROKER.equals(brokerInfo.getBrokerWebsiteOrigin()) ? ApplicationOriginEnum.CNT : ApplicationOriginEnum.WINI;
        LineOfBusinessEnum lineOfBusiness = aComContext.getLineOfBusiness() == null ? LineOfBusinessEnum.PERSONAL_LINE : LineOfBusinessEnum.COMMERCIAL_LINE;
        IContextualPhoneNumbers contextualPhoneNumber;

        switch (action) {
            case FOR_CREATE:
                contextualPhoneNumber = subBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, lineOfBusiness, originPhone, "OFFPG", PhoneNumberUsageEnum.BUSINESS_PHONE);

                // Set the Broker Phone Number for the offer page.
                if (contextualPhoneNumber != null) {
                    brokerInfo.setPhoneNumber(contextualPhoneNumber.toDashedString());
                }
                brokerInfo.setSubBrokerNumber(subBroker.getSubBrokerNumber());

                break;

            case FOR_ROADBLOCK:
                contextualPhoneNumber = subBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, lineOfBusiness, originPhone, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
                if (contextualPhoneNumber != null) {
                    brokerInfo.setPhoneNumber(contextualPhoneNumber.toDashedString());
                }

                break;
        }
    }

    @Override
    public CommunicationObjectModel retrievePolicyByUUID(ComContext context, String uuid) throws AutoQuoteQuoteServiceException {
        CommunicationObjectModel comResponse;
        try {
            LOG.debug(Logger.EVENT_SUCCESS, ">> Service : start retrievePolicyByUUID... ");

            comResponse = this.getPolicyByUuid(context, uuid);
            LOG.debug(Logger.EVENT_SUCCESS, ">> Service : end retrievePolicyByUUID... ");
        } catch (Exception e) {
            LOG.error(Logger.EVENT_FAILURE, ">> Service : Exception occurred while retrievePolicyByUUID calling", e);
            throw new AutoQuoteQuoteServiceException(AutoQuoteQuoteServiceException.EXEC_RETRIEVE_QUOTE_ERROR_CODE, new Object[]{context, uuid, e});
        }
        return comResponse;
    }

    @Override
    public CommunicationObjectModel save(CommunicationObjectModel aCom, boolean force) throws AutoQuoteQuoteServiceException, AutoquoteFacadeException {

        StopWatch performanceWatch = new StopWatch();
        if (performanceWatch.isRunning()) {
            performanceWatch.stop();
        }

        // overwrite effective date
        this.managePolicyVersionEffectiveDate(aCom);

        if (LOG.isDebugEnabled()) {
            performanceWatch.start("QuickquoteCommonFacade.save - parent save");
        }
        CommunicationObjectModel quote = super.save(aCom, true);
        if (LOG.isDebugEnabled()) {
            performanceWatch.stop();
            LOG.debug(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
        }

        if (LOG.isDebugEnabled()) {
            performanceWatch.start("QuickquoteCommonFacade.save - assignDriver");
        }
        // We must always assign the driver to the vehicle in case changes were to claims on other modification affecting the risk on the quote.
        this.assignDriverToVehicle(quote);
        if (LOG.isDebugEnabled()) {
            performanceWatch.stop();
            LOG.debug(Logger.EVENT_SUCCESS, performanceWatch.prettyPrint());
        }

        PolicyVersion aPolicyVersion = this.policyVersionService.findById(quote.getPolicyVersionId());
        try {
            quote = this.buildCom(aPolicyVersion, quote);

            // Current broker
            this.setBrokerInfo(aPolicyVersion, quote);

            this.updateVehicleConditionWhenBought(aPolicyVersion, quote);

            // --------------------------
            // -- ROADBLOCK VALIDATION --
            // --------------------------

            // 46231 need to figure out first if there are details for the vehicle and
            // if the vehicle group is not 69 and over because save or rate will fail.
            // since glm, regular validation doesn't work for QC. Other provinces are ok.
            if (this.validateVehiclesDEFAULTRB(aCom, MediatorUtils.getLocale(aCom.getContext()))) {
                List<ComRoadBlock> result = new ArrayList<ComRoadBlock>();
                result.add(new ComRoadBlock("BR10482", ComRoadBlockTypeEnum.HARD));
                aCom.setRoadblock(result);

                bloomMQHandlerService.sendMessageToMQ(aPolicyVersion.getInsurancePolicy().getUuId(), aPolicyVersion.getInsurancePolicy().getManufacturerCompany());

                return aCom;
            }
            this.validateRoadblocks(quote);
            // --------------------------

        } catch (Exception e) {
            LOG.error(Logger.EVENT_FAILURE, String.format("an exception occured: %s", e.getMessage()), e);
            throw new AutoquoteFacadeException(e);
        }

        bloomMQHandlerService.sendMessageToMQ(aPolicyVersion.getInsurancePolicy().getUuId(), aPolicyVersion.getInsurancePolicy().getManufacturerCompany());

        return quote;
    }

    @Override
    protected PolicyVersion clonePolicyVersion(CommunicationObjectModel aCom, PolicyVersion policyVersion) {
        PolicyVersion returnPV = policyVersion;

        if (aCom.getState().getHasOffer() != null && aCom.getState().getHasOffer()) {
            String cifClientIdStr = null;
            if (aCom.getCifClientId() != null) {
                cifClientIdStr = Long.toString(aCom.getCifClientId());
            }

            if (CollectionUtils.isNotEmpty(policyVersion.getParties())) {
                for (Party p : policyVersion.getParties()) {
                    if (p != null && p.getDriverComplementInfo() != null) {
                        LOG.info(Logger.EVENT_SUCCESS, String.format(">>> pre-clone p.ubiDistcountCriteria: %s", p.getDriverComplementInfo().getUbiDiscountCriteriaCode()));
                    }

                }
            }


            returnPV = this.cloneService.clone(policyVersion, cifClientIdStr, aCom.getContext().getMarketingPromotionCode());
            aCom.setPolicyVersionId(returnPV.getId());

            if (CollectionUtils.isNotEmpty(policyVersion.getParties())) {
                for (Party p : returnPV.getParties()) {
                    if (p != null && p.getDriverComplementInfo() != null) {
                        LOG.info(Logger.EVENT_SUCCESS, String.format(">>> post-clone p.ubiDistcountCriteria: %s", p.getDriverComplementInfo().getUbiDiscountCriteriaCode()));
                    }
                }
            }
        }

        return returnPV;
    }

    /**
     * Gets the overriding rating date from autoquote-rating.properties and set it to policyEffectiveDate.
     */
    private void managePolicyVersionEffectiveDate(CommunicationObjectModel aCom) {
        ComContext context = aCom.getContext();
        Date overridingRatingDate = this.ratingConfig.getOverridingRatingDate(context.getProvince().getCode());
        if (overridingRatingDate != null) {
            aCom.setPolicyEffectiveDate(comQuoteConverter.convertToComDate(new DateTime(overridingRatingDate.getTime())));
        }
    }

    /**
     * Assign driver to vehicle.
     *
     * @param quote {@link CommunicationObjectModel}
     * @throws AutoquoteFacadeException any {@link AutoquoteFacadeException}
     */
    protected void assignDriverToVehicle(CommunicationObjectModel quote) throws AutoquoteFacadeException {

        if (CollectionUtils.isEmpty(quote.getVehicles())) {
            return;
        }

        ComVehicle vehicle = quote.getVehicles().getFirst();
        if (vehicle == null) {
            throw new AutoquoteFacadeException("Must have one vehicle on the quote.");
        }
        this.ajustPrincipalDriverAndRegisterOwner(quote, vehicle);

        // We need to save the vehicle information before calling the assignDriver.
        quote = super.save(quote, true);

        this.assignDriver(quote);
    }

    protected void ajustPrincipalDriverAndRegisterOwner(CommunicationObjectModel quote, ComVehicle vehicle) {
        Integer fristDriverId = quote.getDriver(0).getDriverId();
        vehicle.setPrincipalDriver(fristDriverId);
        vehicle.setRegisterOwner(fristDriverId);
    }

    protected void setBrokerInfo(PolicyVersion aPolicyVersion, CommunicationObjectModel quote) throws Exception {
        ISubBrokers subBroker;
        try {
            subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
        } catch (Exception e) {
            ManufacturingContext mCtxt = MediatorUtils.convertContext(quote.getContext());
            long subBrokerId = this.buildBrokerInfoBeforeCreateQuote(quote.getContext(), mCtxt);
            this.newQuoteBusinessProcess.assignBrokerToQuote(aPolicyVersion.getInsurancePolicy(), aPolicyVersion, String.valueOf(subBrokerId));
            // Save the insurance policy
            this.insurancePolicyService.persist(aPolicyVersion.getInsurancePolicy());
            if (LOG.isDebugEnabled()) {
                String strInsurancePolicy = ToStringBuilder.reflectionToString(aPolicyVersion.getInsurancePolicy());
                LOG.debug(Logger.EVENT_SUCCESS, String.format("policyVersion before saving = %s", strInsurancePolicy));
            }

            // Save the policy version
            aPolicyVersion = this.policyVersionService.persist(aPolicyVersion);
            if (LOG.isDebugEnabled()) {
                LOG.debug(Logger.EVENT_SUCCESS, "policyVersion after saving = %s" + ToStringBuilder.reflectionToString(aPolicyVersion));
            }
            subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
        }

        ComBrokerInfo brokerInfo = quote.getContext().getBrokerInfo();
        if (brokerInfo != null) {
            subBroker = this.subBrokersService.getSubBrokerById(aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId());
            if (subBroker != null && subBroker.getSubBrokerId() > 0) {
                IContextualSubBrokerGnInfos contextualSubBrokerGnInfos = subBroker.getAccessType(ApplicationIdEnum.WEB_QUOTE, LineOfBusinessEnum.PERSONAL_LINE);
                String webAccessType = contextualSubBrokerGnInfos.getWebAccessType();

                brokerInfo.setLogo(SubBrokerHelper.getLogo(quote.getContext().getLanguage().getCode(), subBroker));
                brokerInfo.setCallBackAvailable(subBroker.getAllowClientSchCallbackInd());
                brokerInfo.setSubBrokerNumber(subBroker.getSubBrokerNumber());

                if (webAccessType != null && (webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue())
                        || webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue())
                        || webAccessType.equals(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue()))) {
                    brokerInfo.setHomeQuoteAvailable(true);
                } else {
                    brokerInfo.setHomeQuoteAvailable(false);
                }

                this.setPhoneNumberForAction((CollectionUtils.isEmpty(quote.getRoadblock()) ? BuildPhoneNumberActionEnum.FOR_CREATE : BuildPhoneNumberActionEnum.FOR_ROADBLOCK), subBroker, quote.getContext());
            }
        }
    }

    protected void updateVehicleConditionWhenBought(PolicyVersion aPolicyVersion, CommunicationObjectModel aCom) {
        List<Vehicle> vehicles = this.vehicleHelper.getVehicles(aPolicyVersion);
        ComVehicle comVehicle = aCom.getVehicles().getFirst();

        Vehicle aVehicle = vehicles.getFirst();

        // one year or less
        int year = (new GregorianCalendar().get(Calendar.YEAR)) - 1;

        if (comVehicle != null && aVehicle != null) {
            if (Integer.parseInt(comVehicle.getYear()) >= year) {
                comVehicle.setConditionWhenBought(ConditionVehicleWhenBoughtCodeEnum.NEW.getCode());
                aVehicle.setConditionOfVehicleWhenBought(ConditionVehicleWhenBoughtCodeEnum.NEW);
            } else {
                comVehicle.setConditionWhenBought(ConditionVehicleWhenBoughtCodeEnum.USED.getCode());
                aVehicle.setConditionOfVehicleWhenBought(ConditionVehicleWhenBoughtCodeEnum.USED);
            }
        }

        if (aCom.getVehicles().size() > 1) {

            ComVehicle comVehicle2 = aCom.getVehicles().get(1);
            Vehicle aVehicle2 = vehicles.get(1);

            if (comVehicle2 != null && aVehicle2 != null) {
                if (Integer.parseInt(comVehicle2.getYear()) >= year) {
                    comVehicle2.setConditionWhenBought(ConditionVehicleWhenBoughtCodeEnum.NEW.getCode());
                    aVehicle2.setConditionOfVehicleWhenBought(ConditionVehicleWhenBoughtCodeEnum.NEW);
                } else {
                    comVehicle2.setConditionWhenBought(ConditionVehicleWhenBoughtCodeEnum.USED.getCode());
                    aVehicle2.setConditionOfVehicleWhenBought(ConditionVehicleWhenBoughtCodeEnum.USED);
                }
            }
        }
        // Save the policy version
        this.policyVersionService.persist(aPolicyVersion);
    }

    @Override
    public RuleExceptionResult validateVehiclesHARDRB(Vehicle aVehicle, Province aProvince, Language aLanguage){
        return this.vehicleBusinessProcess.validateHardRoadblock(aVehicle, aProvince, aLanguage);
    }

    @Override
    public List<RuleExceptionResult> validateVehiclesSOFTRB(Vehicle aVehicle, Province aProvince, Language aLanguage, PolicyVersion aPolicyVersion) {
        return this.vehicleBusinessProcess.validateQuickQuoteSoftRoadblock(aVehicle, aProvince, aLanguage);
    }

    @Override
    public RuleExceptionResult validateDriversHARDRB(DriverComplementInfo driver, Locale locale) {
        return null;
    }

    @Override
    public List<RuleExceptionResult> validateDriversSOFTRB(PolicyVersion policyVersion, ProvinceCodeEnum provinceCodeEnum) {
        return this.driverBusinessProcess.validateQuickQuoteSoftRoadblock(policyVersion, provinceCodeEnum);
    }

    @Override
    public List<RuleExceptionResult> validateDriversSOFTRB(DriverComplementInfo driver, ProvinceCodeEnum provinceCodeEnum) {
        return this.driverBusinessProcess.validateQuickQuoteSoftRoadblock(driver, provinceCodeEnum);
    }

    /**
     * Retrieves the localized facade component matching the company and province found in the provided instance of.
     *
     * @param aComContext {@link ComContext}
     * @return {@link ICommonFacade}
     * @throws AutoQuoteException the autoquote facade exception
     * {@link ComContext}.
     */
    public static ICommonFacade getInstance(ComContext aComContext) throws AutoQuoteException {
        return (ICommonFacade) ContextUtil.getInstance(AutoQuoteCommonFacade.class, aComContext);
    }
}
