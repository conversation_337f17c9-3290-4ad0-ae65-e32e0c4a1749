package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.springframework.validation.Errors;

import java.util.Calendar;
import java.util.Date;

public abstract class DateOfBirthValidationRule {

	public static void validate(String dateOfbirthYear, String dateOfbirthMonth, String dateOfbirthDay, String fieldName, Errors errors) {
		Date date = new Date(); // your date
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH);
		int day = cal.get(Calendar.DAY_OF_MONTH);
		final int nbreYears = ValidationUtilities.getNbreYearsBetweenDate(
                dateOfbirthDay + "/" + dateOfbirthMonth + "/" +
                        dateOfbirthYear, day + "/" + (month + 1) +
                        "/" + year);
		if (nbreYears > 99 || nbreYears < 16) {
			errors.rejectValue(fieldName, BRulesExceptionEnum.ERR_DRIVER_DATEBIRTHY_BR7934.getErrorCode(), ValidationUtilities.bracket(fieldName));
		}
	}

}
