package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.offer.ComCoverageItem;
import com.intact.com.offer.ComOffer;
import intact.lab.autoquote.backend.common.dto.CoverageDTO;
import intact.lab.autoquote.backend.common.dto.OfferDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component("comOfferConverter")
public class COMOfferConverter implements ICOMConverter<OfferDTO, ComOffer> {

	@Resource(name = "comCoverageConverter")
	private ICOMConverter<CoverageDTO, ComCoverageItem> comCoverageConverter;

	@Override
	public OfferDTO toDTO(ComOffer comOffer) {
		OfferDTO offerDTO = new OfferDTO();
		List<CoverageDTO> coverageDTOs = new ArrayList<>();
		if (comOffer != null) {
			offerDTO.setOfferCode(comOffer.getOfferType());
			List<ComCoverageItem> coverageItems = comOffer.getAllCoverages();
			for (ComCoverageItem coverageItem : coverageItems) {
				coverageDTOs.add(comCoverageConverter.toDTO(coverageItem));
			}
			offerDTO.setAnnualPremium(new BigDecimal(comOffer.getAnnualPremium()));
			offerDTO.setMonthlyPremium(new BigDecimal(comOffer.getMonthlyPremium()));
			offerDTO.setCoverages(coverageDTOs);
			//addOptionsToCoverages(comOffer, offerDTO.getCoverages());
		}

		return offerDTO;
	}

	@Override
	public ComOffer toCOM(OfferDTO offerDTO, ComOffer initialComOffer) {
		// no implementation required
		return null;
	}
}
