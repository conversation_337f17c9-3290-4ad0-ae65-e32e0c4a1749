package intact.lab.autoquote.backend.services.business.offer;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.services.rating.IRatingService;
import org.springframework.util.StopWatch;

public interface IRateManagerService {

    /**
     * Rate the whole quotation.
     *
     * @param aPolicyVersion the a policy version
     * @param isAgent the is agent
     * @return the policy version
     * @throws AutoquoteRatingException the rating exception
     */
    PolicyVersion rateTheWholeQuotation(PolicyVersion aPolicyVersion, boolean isAgent, boolean isUbiEnabled)
            throws AutoquoteRatingException;

    /**
     * Retrun the rating service used in the instance
     *
     * @return the instance of {@link IRatingService}
     */
    IRatingService getRatingService();

    /**
     * Select offer.
     *
     * @param aPolicyVersion the a policy version
     * @throws AutoquoteRatingException the rating exception
     */
    void selectOffer(PolicyVersion aPolicyVersion, StopWatch performanceWatch, boolean isUbiEnabled)
            throws AutoquoteRatingException;
}
