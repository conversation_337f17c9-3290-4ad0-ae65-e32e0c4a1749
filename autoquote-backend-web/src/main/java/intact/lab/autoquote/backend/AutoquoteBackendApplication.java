package intact.lab.autoquote.backend;

import intact.lab.security.recaptcha.api.client.RecaptchaConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

@Log4j2
@SpringBootApplication(exclude = {MongoAutoConfiguration.class})
@ComponentScan(basePackages = {
        "intact.lab.autoquote.backend", "com.intact.business", "com.ing.canada.common",
        "com.intact.common", "com.intact.moneris", "com.intact.globaladmin", "com.intact.intact.rating",
})
@Import({RecaptchaConfig.class})
public class AutoquoteBackendApplication {

    private static final String SEQUENCE_INCREMENT_SIZE_MISMATCH_STRATEGY = "hibernate.id.sequence.increment_size_mismatch_strategy";

    public static void main(String[] args) {
        log.info("Application start");
        System.setProperty(SEQUENCE_INCREMENT_SIZE_MISMATCH_STRATEGY, "FIX");
        SpringApplication.run(AutoquoteBackendApplication.class, args);
        log.info("Application started successfully");
    }

}
