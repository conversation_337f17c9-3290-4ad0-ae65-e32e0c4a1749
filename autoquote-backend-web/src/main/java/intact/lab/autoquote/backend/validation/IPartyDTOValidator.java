package intact.lab.autoquote.backend.validation;

import intact.lab.autoquote.backend.common.dto.ConsentDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.validation.impl.ValidationContext;
import org.springframework.validation.Errors;

import java.util.List;

public interface IPartyDTOValidator {

	void validate(PartyDTO partyDTO, Errors errors, ValidationContext context);

	void validateConsents(List<ConsentDTO> consents, PartyTypeEnum partyTypeEnum, Errors errors);

}
