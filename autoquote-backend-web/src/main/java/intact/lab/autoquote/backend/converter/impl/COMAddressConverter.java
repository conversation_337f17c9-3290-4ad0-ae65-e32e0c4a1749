package intact.lab.autoquote.backend.converter.impl;


import com.intact.com.address.ComAddress;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component("comAddressConverter")
public class COMAddressConverter implements ICOMConverter<AddressDTO, ComAddress> {

	@Override
	public AddressDTO toDTO(ComAddress address) {
		AddressDTO addressDTO = new AddressDTO();
		if (address != null) {
			addressDTO.setPostalCode(address.getPostalCode());
			addressDTO.setMunicipalityCode(address.getCityCode());
			addressDTO.setCity(address.getCity());
			addressDTO.setCivicNbr(address.getCivicNumber());
			addressDTO.setCountry(address.getCountry());
			addressDTO.setProvince(address.getProvince());
			addressDTO.setStreet(address.getStreetName());
		}
		return addressDTO;
	}

	@Override
	public ComAddress toCOM(AddressDTO dto, ComAddress initialAddress) {
		ComAddress comAddress = initialAddress == null ? new ComAddress() : initialAddress;
		if (dto != null) {
			if (StringUtils.isNotEmpty(dto.getPostalCode())) {
				comAddress.setPostalCode(dto.getPostalCode().replaceAll("[_.,-/ ]", "").toUpperCase());
			}
			comAddress.setCityCode(dto.getMunicipalityCode());
			comAddress.setCity(dto.getCity());
			comAddress.setStreetName(dto.getStreet());
			comAddress.setProvince(dto.getProvince());
		}
		return comAddress;
	}

}
