/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2014, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

/**
 * Payment adapter
 */
public interface ICOMPaymentAdapter {
	/**
	 * Apply specific calculation rules
	 * 
	 * @param quotationDetails
	 * @param policyVersion
	 */
	abstract void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails, PolicyVersion policyVersion);
}
