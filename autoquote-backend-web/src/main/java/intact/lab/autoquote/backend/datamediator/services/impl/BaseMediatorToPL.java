package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.usertype.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.owasp.esapi.ESAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class BaseMediatorToPL {

    private final IBaseEntityDAO baseEntityDAO;

    @Autowired
    public BaseMediatorToPL(IBaseEntityDAO baseEntityDAO) {
        this.baseEntityDAO = baseEntityDAO;
    }

    public BaseEntity getBeanFromPLGraphWithCriteria(Long uniqueId, Class<?> plBeanClass, PolicyVersion plPolicyVersion) {
        if (log.isDebugEnabled()) {
            log.debug(ESAPI.encoder().encodeForHTML(String.format("getBean %s=%d for pv=%d", plBeanClass.getSimpleName(), uniqueId, plPolicyVersion.getId())));
        }

        return this.baseEntityDAO.findEntityById(plBeanClass, uniqueId);
    }
}
