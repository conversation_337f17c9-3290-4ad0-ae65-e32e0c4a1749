package intact.lab.autoquote.backend.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

public enum ConvictionCodeEnum {

	MINOR_CONVICTION("OT1"),
	MAJOR_CONVICTION("OT2"),
	DISTRACTED_DRIVING("214.2 1");

	private String code;

	private ConvictionCodeEnum(String code) {
		this.code = code;
	}

	public static ConvictionCodeEnum valueOfCode(String value) {

		if (StringUtils.isEmpty(value)) {
			return null;
		}

		for (ConvictionCodeEnum v : values()) {
			if (v.code.equals(value)) {
				return v;
			}

		}

		throw new IllegalArgumentException("no enum value found for code: " + value);

	}

	@JsonValue
	public String getCode() {
		return code;
	}

}
