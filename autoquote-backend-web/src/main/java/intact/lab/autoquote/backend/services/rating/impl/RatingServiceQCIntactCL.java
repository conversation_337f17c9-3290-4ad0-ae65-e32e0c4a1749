/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.GoodDriverCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.PolicyVersionTypeCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.CoveragePremiumOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.ing.canada.som.interfaces.intermediary.DistributorRepositoryEntry;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Distributor;
import com.ing.canada.som.interfaces.risk.Coverage;
import com.ing.canada.ss.base.BaseException;
import com.intact.business.rules.offer.BR2484_CalPolicyThermsMonths;
import com.intact.rating.IPremiumDerivationService;
import com.intact.rating.exception.RatingException;
import intact.lab.autoquote.backend.common.exception.AutoquoteRatingException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.datamediator.services.impl.DataMediatorToSOMServiceFactory;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.ing.canada.plp.domain.enums.ProvinceCodeEnum.QUEBEC;

/**
 * The Class RatingService.
 */
@ComponentLocal(province = QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class RatingServiceQCIntactCL extends RatingService {
	
	private static final Logger log = ESAPI.getLogger(RatingServiceQCIntactCL.class);
	
	@Autowired
	protected IVehicleHelper vehicleHelper;
	
	@Autowired
	private BR2484_CalPolicyThermsMonths calPolicyThermsMonths;

	@Autowired
	private DataMediatorToSOMServiceFactory dataMediatorToSOMServiceFactory;

	@Autowired
	private RatingConfig ratingConfig;

	@Autowired
	private IInsuranceRiskHelper insuranceRiskHelper;

	@Autowired
	private IDateManagerService dateManagerService;

	@Autowired
	@Qualifier("executeServiceQCIntact")
	protected ExecuteServiceQCIntact executeService;

	@Autowired
	@Qualifier("application-id")
	protected String applicationId;

	@Override
	@Transactional
	public PolicyVersion rateOffer(PolicyVersion aPlPolicyVersion, boolean isAgent, boolean isUbiEnabled)
			throws AutoquoteRatingException {
		
		if (log.isDebugEnabled()) {
			log.debug(Logger.EVENT_SUCCESS, "Calling the rating Quebec");
		}
		StopWatch performanceWatch = new StopWatch();
		if (performanceWatch.isRunning()) {
			performanceWatch.stop();
		}

		try {
			dataMediatorToPL.setIsKeepNonEligibleCoverages(Boolean.TRUE);
			
			/* Define referenceOffer */
			this.dataMediatorToPL.setReferenceOffer(this.getReferenceOfferType(aPlPolicyVersion.getInsurancePolicy().getApplicationMode().getCode()));
			
			/* PreprocessPL - Set the current date for the first rating date */
			this.preprocessPL(aPlPolicyVersion);

			/* Convert the PL PolicyVersion to the SOM format */
			performanceWatch.start("    >> dataMediatorToSOM.convertTo_SOM");
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion = dataMediatorToSOMServiceFactory
					.getService("dataMediatorToSOM").convertTo_SOM(aPlPolicyVersion);
			performanceWatch.stop();

			/* Define originalInceptionDate */
			GregorianCalendar originalInceptionDate = somPolicyVersion.getTheInsurancePolicy()
					.getOriginalInceptionDate(); // null
			
			GregorianCalendar clientOfBrokerSinceAuto = somPolicyVersion.getClientOfBrokerSinceAuto(); // null
			String combinedPolicyCode = somPolicyVersion.getCombinedPolicyCode(); // null
			
			this.preprocessSOM(somPolicyVersion, aPlPolicyVersion);

			// set the Policy expiry date
			this.setPolicyTermAndExpiry(aPlPolicyVersion, somPolicyVersion);

			/* Initialize the information to call PEGA services */
			ManufacturingContext aCtxt = aPlPolicyVersion.getInsurancePolicy().getManufacturingContext();
			Map<String, Object> ilParams = SSSUtils.getDelegateParameters(aCtxt, this.applicationId);
			if (log.isDebugEnabled()) {
				log.debug(Logger.EVENT_SUCCESS, String.format("Agreement Number %s", aPlPolicyVersion.getInsurancePolicy().getAgreementNumber()));
				log.debug(Logger.EVENT_SUCCESS, String.format("PolicyVersion Id %s", aPlPolicyVersion.getId()));
			}
			performanceWatch.start("    >> manageProducts");
			/* first manageProducts call*/
			somPolicyVersion = this.executeService.manageProducts(somPolicyVersion, ilParams);
			performanceWatch.stop();
			
			performanceWatch.start("    >> codePl");
			/* codePl call*/
			somPolicyVersion = this.executeService.codePl(somPolicyVersion, ilParams);
			performanceWatch.stop();
			
			performanceWatch.start("    >> manageProduct");
			/* second manageProducts call*/
			somPolicyVersion = this.executeService.manageProducts(somPolicyVersion, ilParams);
			performanceWatch.stop();
			
			performanceWatch.start("    >> getAnnualPremiumCoverages");
			somPolicyVersion = this.executeService.getAnnualPremiumCoverages(somPolicyVersion,ilParams);
			performanceWatch.stop();
				
			// . 3.1 Copy the attribute value of the root InsuranceRisk in all
			// the branch InsuranceRisk of SOM

			this.copyInsuranceRiskToOffers(somPolicyVersion);

			// . 3.2 - Select the offer that will be used for Rating in the
			// subsequent service
			// - IMPORTANT : Do not call the PEGA service that select the offer
			// because the PEGA service is used for
			// UI client selection.
			// - In the rating sequence, we always use the recommended
			this.selectOfferForRating(somPolicyVersion, this.getReferenceOfferType(aPlPolicyVersion.getInsurancePolicy().getApplicationMode().getCode()));
			
			// PEGA - Valid user selection on multiple offers and set CAA
			// Jerome requested we remove that call
			// internal billing indicator correctly
			//performanceWatch.start(" >> validUserSelectionOnMultipleOffers");
			//somPolicyVersion = this.executeService.validUserSelectionOnMultipleOffers(aCtxt, somPolicyVersion);
			//performanceWatch.stop();

			performanceWatch.start(" >> setGoodDriver");
			// Set the good driver on all insuranceRiskOffer except the Custom
			this.setGoodDriver(aPlPolicyVersion, somPolicyVersion, aCtxt);
			performanceWatch.stop();

			// add our version of rateTheOffe here
			
			somPolicyVersion = rateTheOffer(somPolicyVersion, aPlPolicyVersion.getInsurancePolicy().getManufacturingContext(), performanceWatch);
			
			somPolicyVersion = this.selectOfferType(performanceWatch, aCtxt, somPolicyVersion);

			// Create all the branch InsuranceRisk of SOM in PL
			this.createRiskOffers(aPlPolicyVersion, somPolicyVersion);

			/* Post process on SOM - Reset the initial values */
			this.postprocessSOM(somPolicyVersion, originalInceptionDate, clientOfBrokerSinceAuto, combinedPolicyCode);

			/* Convert back to PL */
			performanceWatch.start("    >> dataMediatorToPL.convertTo_PL");
			this.dataMediatorToPL.convertTo_PL(somPolicyVersion, aPlPolicyVersion, false);
			performanceWatch.stop();

			/* Post process on PL - Mostly to set the LastRatingSequence */
			this.postprocessPL(aPlPolicyVersion);
			
			this.manageUbiStatus(aPlPolicyVersion);

			if (log.isTraceEnabled()) {
				log.trace(Logger.EVENT_SUCCESS, "agreement nbr: "
						+ aPlPolicyVersion.getInsurancePolicy().getAgreementNumber() + "\n"
						+ performanceWatch.prettyPrint());
			}
			return aPlPolicyVersion;

		} catch (BaseException e) {
			throw new AutoquoteRatingException("The rate offer sequence for PL PolicyVersion [" + aPlPolicyVersion
					+ "] has failed!", this.getRealException(e));
		} catch (RatingException re) {
			throw new AutoquoteRatingException("The rate offer sequence for PL PolicyVersion [" + aPlPolicyVersion
					+ "] has failed!", re);
		}
	}

	@Override
	protected void postprocessPL(PolicyVersion aPlPolicyVersion) {
		super.postprocessPL(aPlPolicyVersion);
		// For IRCA hardcode the vehicleUsage to OTHER after the rating.
		// It is required for rating in GoBrio
		if (aPlPolicyVersion != null &&
			aPlPolicyVersion.getInsuranceRisks() != null &&
			!aPlPolicyVersion.getInsuranceRisks().isEmpty()) {

			InsuranceRisk insuranceRisk = aPlPolicyVersion.getInsuranceRisks().iterator().next();
			Vehicle vehicle = insuranceRisk.getVehicle();
			if (vehicle != null) {
    			vehicle.setVehicleUsage(UseOfVehicleCodeEnum.OTHER);
			}
		}
	}

	@Override
	protected void preprocessSOM(com.ing.canada.som.interfaces.agreement.PolicyVersion aSomPolicyVersion,
			PolicyVersion aPolicyVersion) {
		super.preprocessSOM(aSomPolicyVersion, aPolicyVersion);

		// convert the value to something the services can use
		if (aPolicyVersion.getInsurancePolicy().getExternalSystemOrigin() == null) {
			aSomPolicyVersion.getTheInsurancePolicy().setExternalSystemOrigin("I");
		} else if (ExternalSystemOriginCodeEnum.WEB_BROKER.equals(aPolicyVersion.getInsurancePolicy().getExternalSystemOrigin())) {
			aSomPolicyVersion.getTheInsurancePolicy().setExternalSystemOrigin("B");
		}

		// create a distributor in som, base on the insurancePolicy sub
		// BrokerAssignment
		SubBrokerAssignment subBrokerAssignment = aPolicyVersion.getInsurancePolicy().getLatestSubBrokerAssignment();

		Distributor distributor = aSomPolicyVersion.createTheDistributor();
		DistributorRepositoryEntry distributorRepositoryEntry = distributor.createTheDistributorRepositoryEntry();

		distributorRepositoryEntry.setSubBrokerNumber(String.valueOf(subBrokerAssignment.getCifSubBrokerId()));

		aSomPolicyVersion.setPolicyVersionType(PolicyVersionTypeCodeEnum.INDIVIDUAL_AUTOMOBILE_COMMERCIAL.getCode());
		aSomPolicyVersion.getTheEnvironmentContext().setApplicationIdentification("AQQK");

		com.ing.canada.som.interfaces.physicalObject.Vehicle vehicle;
		for (com.ing.canada.som.interfaces.risk.InsuranceRisk ir : aSomPolicyVersion.getTheInsuranceRisk()) {
			ir.setNumberOfAdditionalInterests(0);
			vehicle = ir.getTheVehicle();
			vehicle.setRatingTableIdentification("C");
			ir.setRatingTableIdentification("C");
		}
	}
	
	protected com.ing.canada.som.interfaces.agreement.PolicyVersion rateTheOffer(
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
			ManufacturingContext ctxt, StopWatch performanceWatch) throws BaseException, RatingException {

		performanceWatch.start("    >> getMultiplicativeRatingFactors");
		com.ing.canada.som.interfaces.agreement.PolicyVersion modifiedSomPolicyVersion = this.executeService
				.getMultiplicativeRatingFactors(ctxt, somPolicyVersion);
		performanceWatch.stop();

		return modifiedSomPolicyVersion;
	}
	
	@Override
	protected void preprocessPL(PolicyVersion aPlPolicyVersion) {
		// Set the current date for the first rating date
		if (aPlPolicyVersion.getRatingDate() == null) {
			Date ratingDate = new Date();
			ManufacturingContext ctxt = aPlPolicyVersion.getInsurancePolicy().getManufacturingContext();
			Date overridingRatingDate = this.ratingConfig.getOverridingRatingDate(ctxt.getProvince().getCode());
			if (overridingRatingDate != null) {
				ratingDate = overridingRatingDate;
			}
			aPlPolicyVersion.setRatingDate(ratingDate);
			aPlPolicyVersion.getReferenceDate().setTransactionFirstRatingReferenceDate(ratingDate);
			if (overridingRatingDate != null) {
				aPlPolicyVersion.getReferenceDate().setClaimReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setCreditScoreReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setDriverAgeReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setLicenseObtainedPeriodReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setTransactionCreationReferenceDate(overridingRatingDate);
				aPlPolicyVersion.getReferenceDate().setVehicleAgeReferenceDate(overridingRatingDate);
			}

			Calendar calendar = Calendar.getInstance();
			calendar.setTime(aPlPolicyVersion.getReferenceDate().getTransactionFirstRatingReferenceDate());

			calendar.add(Calendar.DAY_OF_MONTH, aPlPolicyVersion.getInsurancePolicy().getQuoteValidityPeriodInDays());
			aPlPolicyVersion.getInsurancePolicy().setQuotationValidityExpiryDate(calendar.getTime());
		}

		super.preprocessPL(aPlPolicyVersion);

		List<Vehicle> vehicles = vehicleHelper.getVehicles(aPlPolicyVersion);
		for (Vehicle vehicle : vehicles) {
			if (vehicle.getVehicleDetailSpecificationRepositoryEntry() != null) {
				InsuranceRisk vehIR = vehicle.getInsuranceRisk();
				vehIR.setValuationAmount(vehicle.getVehicleDetailSpecificationRepositoryEntry().getRetailPriceWithGst());
			}
		}
	}
	
	@Override
	public void manageUbi(PolicyVersion aPlPolicyVersion, Map<Integer, Boolean> ubiSelectedByRiskSeq,
			StopWatch performanceWatch) throws AutoquoteRatingException {
		// no implementation required since PEGA takes care of it completely now.
	}

	
	/**
	 * BR2484 : variation of BR251
	 * <p>
	 * Updates the expiry date and the policyTerm. This can only be done once we
	 * know the insured group used so we can calculate the payment plans
	 * </p>
	 *
	 * @param aPlPolicyVersion
	 *            the a pl policy version
	 * @param somPolicyVersion
	 *            the som policy version
	 */
	@Override
	protected void setPolicyTermAndExpiry(PolicyVersion aPlPolicyVersion,
			com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {

		aPlPolicyVersion.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);
		somPolicyVersion.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS.getCode());

		// Set the policy version expiry date to be the inception date + the
		// duration in months of the policy term
		Date inceptionDate = aPlPolicyVersion.getPolicyInceptionDate();
		Calendar aCal = Calendar.getInstance();
		aCal.setTime(inceptionDate);
		aCal.add(Calendar.MONTH, PolicyTermInMonthsEnum.TWELVE_MONTHS.getCode());
		aPlPolicyVersion.setPolicyExpiryDate(aCal.getTime());
		somPolicyVersion.setPolicyExpiryDate((GregorianCalendar) aCal);
	}
	
	/**
	 * Creates all the branch InsuranceRisk of SOM in PL.
	 *
	 * @param plPolicyVersion the PL policy version
	 * @param somPolicyVersion the SOM policy version
	 */
	protected void createRiskOffers(PolicyVersion plPolicyVersion,
                                    com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion) {

		PolicyOfferRating plPolicyOfferRating = plPolicyVersion.getLatestPolicyOfferRating();
		Set<InsuranceRiskOffer> insuranceRiskOffers = plPolicyOfferRating.getInsuranceRisksOffers();
		OfferTypeCodeEnum offerTypeCodeEnum = null;
		for (InsuranceRiskOffer insuranceRiskOffer : insuranceRiskOffers) {
			if (insuranceRiskOffer.getOfferType() != null) {
				offerTypeCodeEnum = insuranceRiskOffer.getOfferType();
				break;
			}
		}
		if (offerTypeCodeEnum != null) {
			for (InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {
				
				// Create the InsuranceRiskOffer and their RatingRiskOffer in PL
				// for the current offer type
				this.createRiskOffer(plPolicyOfferRating, plInsuranceRisk, offerTypeCodeEnum, Boolean.TRUE);
			}
		}
		
		this.policyVersionService.persistCascadeAll(plPolicyVersion);
	}

	/**
	 * Sets the good Driver Indicator on all the offer except the custom
	 *
	 * @param plPolicyVersion  the pl policy version
	 * @param somPolicyVersion the som policy version
	 * @param ctx              the manufacturing context
	 */
	protected void setGoodDriver(PolicyVersion plPolicyVersion,
								 com.ing.canada.som.interfaces.agreement.PolicyVersion somPolicyVersion,
								 ManufacturingContext ctx) {

		for (com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk : plPolicyVersion.getInsuranceRisks()) {

			int seq = plInsuranceRisk.getInsuranceRiskSequence();
			// We go through all the offers except the custom wich is on the root
			for (com.ing.canada.som.interfaces.agreement.PolicyVersion pv : somPolicyVersion.getThePolicyVersionOffer()) {
				for (com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk : pv.getTheInsuranceRisk()) {
					if (somInsuranceRisk.getInsuranceRiskSequence().equals(seq)) {
						this.setGoodDriver(plInsuranceRisk, somInsuranceRisk, ctx);
						break;
					}
				}
			}
		}
	}

	/*
	 * Based on the BR A1COVE43
	 */
	protected void setGoodDriver(com.ing.canada.plp.domain.insurancerisk.InsuranceRisk plInsuranceRisk,
								 com.ing.canada.som.interfaces.risk.InsuranceRisk somInsuranceRisk,
								 ManufacturingContext ctxt) {

		Coverage coverageNV1 = null;
		Coverage coverageNV2 = null;
		for (Coverage cov : somInsuranceRisk.getTheCoverage()) {
			if (EndorsementCodeEnum.NV1.getCode().equals(cov.getCoverageCode())) {
				coverageNV1 = cov;
			} else if (EndorsementCodeEnum.NV2.getCode().equals(cov.getCoverageCode())) {
				coverageNV2 = cov;
			}
		}

		// if NV present
		if (coverageNV1 != null || coverageNV2 != null) {
			Date dateToCompare = this.dateManagerService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING,
					plInsuranceRisk.getPolicyVersion().getId());
			if (this.insuranceRiskHelper.getGoodDriverInd(plInsuranceRisk, ctxt.getProvince(), dateToCompare)) {

				// if it is a goodDriver and NV selected
				if ((coverageNV1 != null && "Y".equals(coverageNV1.getCoverageSelectedInd()))
						|| (coverageNV2 != null && "Y".equals(coverageNV2.getCoverageSelectedInd()))) {
					somInsuranceRisk.setGoodDriverInd(GoodDriverCodeEnum.GOOD_DRIVER_RECORD.getCode());
				} else {
					// if it is a goodDriver and NV not selected
					somInsuranceRisk.setGoodDriverInd(GoodDriverCodeEnum.ADMISSIBLE_TO_GOOD_DRIVER_RECORD.getCode());
				}
			} else {
				somInsuranceRisk.setGoodDriverInd(GoodDriverCodeEnum.NO_GOOD_DRIVER_RECORD.getCode());
			}
		} else {
			somInsuranceRisk.setGoodDriverInd(GoodDriverCodeEnum.NO_GOOD_DRIVER_RECORD.getCode());
		}
	}

	@Override
	public OfferTypeCodeEnum getReferenceOfferType(String applicationMode) throws AutoquoteRatingException {
		return OfferTypeCodeEnum.PREMIUM;
	}

	@Override
	protected IPremiumDerivationService getPremiumDeviationService() {
		return null;
	}

	/**
	 * Gets the ammount that as no taxes on a policyOffer.
	 *
	 * @param currentPolicyOfferRating the current policy offer rating
	 * @return the untaxable amount
	 */
	@Override
	public double getUntaxableAmount(PolicyOfferRating currentPolicyOfferRating) {
		double untaxablePremium = 0;
		for (InsuranceRiskOffer insuranceRiskOffer : currentPolicyOfferRating.getInsuranceRisksOffers()) {
			if (insuranceRiskOffer.getSelectedForPolicyOfferRatingIndicator() != null && insuranceRiskOffer.getSelectedForPolicyOfferRatingIndicator()) {
				for (CoverageOffer coverageOffer : insuranceRiskOffer.getCoverageOffers()) {
					if ("P1".equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode()) || "P2".equals(coverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
						for (CoveragePremiumOffer coveragePremiumOffer : coverageOffer.getCoveragePremiumOffers()) {
							if (coveragePremiumOffer.getAnnualPremium() != null) {
								untaxablePremium += coveragePremiumOffer.getAnnualPremium();
							}
						}
					}
				}
			}
		}
		return untaxablePremium;
	}

	public void manageUbi4SelectOffer(PolicyVersion aPlPolicyVersion, StopWatch performanceWatch)
			throws AutoquoteRatingException {
		// no implementation required since PEGA takes care of it completely now.
	}
}
