package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ApplicationFlowStateCodeEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComOfferTypeCodeEnum;
import com.intact.com.state.ComState;
import com.intact.common.datamediator.com.utils.MediatorUtils;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.exception.NoHitException;
import intact.lab.autoquote.backend.facade.ICommonFacade;
import intact.lab.autoquote.backend.services.INewQuoteBusinessProcessService;
import intact.lab.autoquote.backend.services.business.common.ICreditScoreBusinessProcess;
import intact.lab.autoquote.backend.services.business.nohit.INoHitBusinessProcess;
import intact.lab.autoquote.backend.services.transaction.ITransactionHistoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public abstract class AbstractCommonFacade extends BaseFacade implements ICommonFacade {

    private static final Logger LOG = ESAPI.getLogger(AbstractCommonFacade.class);

    @Autowired
    protected INewQuoteBusinessProcessService newQuoteBusinessProcess;

    @Autowired
    private ITransactionHistoryService transactionHistoryService;

    @Autowired
    protected ICreditScoreBusinessProcess creditScoreBusinessProcess;

    @Autowired
    private INoHitBusinessProcess noHitBusinessProcess;

    @Override
    public CommunicationObjectModel save(CommunicationObjectModel aCom, boolean force) throws AutoQuoteQuoteServiceException,
            AutoquoteFacadeException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format(">> save COM for policyVersion.id='%s'", aCom.getPolicyVersionId()));
        }

        CommunicationObjectModel returnCom = null;

        try {
            Assert.notNull(aCom, "CommunicationObjectModel is required to save");
            Assert.notNull(aCom.getPolicyVersionId(), "CommunicationObjectModel.policyVersionId is required to save");

            MediatorUtils.clearRoadblocks(aCom);

            if (CollectionUtils.isNotEmpty(aCom.getValidationErrors())) {
                aCom.getValidationErrors().clear();
            }

            PolicyVersion policyVersion = this.getPolicyVersion(aCom.getPolicyVersionId());


            if(policyVersion != null){
                //-- clone if needed

                policyVersion = this.clonePolicyVersion(aCom, policyVersion);

                //-- convert com-to-pl
                this.comToPLAdapter.convertCOMtoPL(aCom, policyVersion);

                //-- save the policy version
                this.commonBusinessProcess.savePolicyVersion(policyVersion);

                //-- update transaction history
                this.transactionHistoryService.updateTransactionHistory(aCom.getComEvent(), policyVersion);

                //-- buildCom
                returnCom = this.buildCom(policyVersion, aCom);
            }

            //-- validate roadblocks
            this.validateRoadblocks(returnCom);
        } catch (Exception e) {
            if (LOG.isErrorEnabled()) {
                LOG.error(Logger.EVENT_SUCCESS, String.format("An error occured: %s", e.getMessage()), e);
            }

            throw new AutoQuoteQuoteServiceException(AutoQuoteQuoteServiceException.EXEC_SAVE_QUOTE_ERROR_CODE);
        }

        return returnCom;
    }

    /**
     * QQ Belair: the types of offers returned are decided by the the frontend via the ComState object, WHAT??!!!!
     * <p>
     * AQ Belair: the types of offers returned are decided by the backend (BaseFacade) QQ Intact: the types of offers
     * returned are decided by the backend (BaseFacade)
     *
     * @param quoteState {@link ComState}
     * @param comContext {@link ComContext}
     * @return list of {@link OfferTypeCodeEnum}
     */
    protected List<OfferTypeCodeEnum> defineOfferTypes(final ComState quoteState, ComContext comContext) {

        // 1- try to retrieve offer types from the provided ComState
        List<ComOfferTypeCodeEnum> comOfferTypes = quoteState != null ? quoteState.getOfferTypes() : null;
        if (!CollectionUtils.isEmpty(comOfferTypes)) {
            return this.convertOfferType(comOfferTypes);
        }

        // 2- otherwise return default
        return this.baseFacade.getOfferTypes(comContext);
    }

    private List<OfferTypeCodeEnum> convertOfferType(List<ComOfferTypeCodeEnum> comOfferTypeCodeEnum) {
        List<OfferTypeCodeEnum> offerTypeCodeEnum = new ArrayList<OfferTypeCodeEnum>();
        for (ComOfferTypeCodeEnum comOfferTypeEnum : comOfferTypeCodeEnum) {
            offerTypeCodeEnum.add(OfferTypeCodeEnum.valueOf(comOfferTypeEnum.name()));
        }
        return offerTypeCodeEnum;
    }

    /**
     * Call the new quote business process for regular and quick quote.  QuickQuote doesn't not yet support broker.
     *
     * @param aComContext {@link ComContext}
     * @param mCtxt {@link ManufacturingContext}
     * @param subBrokerId {@link String}
     * @param appModeEnum {@link ApplicationModeEnum}
     * @param appFlowState {@link ApplicationFlowStateCodeEnum}
     * @return {@link PolicyVersion}
     */
    protected PolicyVersion newQuote(ComContext aComContext, ManufacturingContext mCtxt, String subBrokerId, ApplicationModeEnum appModeEnum, ApplicationFlowStateCodeEnum appFlowState,
                                     ComBrokerWebSiteOriginEnum origin, LineOfBusinessCodeEnum lineOfBusiness) {
        return this.newQuoteBusinessProcess.newQuote(aComContext, mCtxt, subBrokerId, appModeEnum, appFlowState, origin, lineOfBusiness);
    }

    @Override
    public void validateRoadblocks(CommunicationObjectModel aCom) throws Exception {
        // -- validate vehicles
        final PolicyVersion updatedPV = this.getPolicyVersion(aCom.getPolicyVersionId());

        List<RoadBlockException> roadblocks = new ArrayList<>();

        // -- validate vehicle related roadblock
        if (updatedPV.getInsuranceRisks().size() > 0) {
            roadblocks.addAll(this.validateRoadblocksVehicles(updatedPV, aCom));
        }

        // -- validate drivers related roadblock
        if (updatedPV.getParties().size() > 0) {
            roadblocks.addAll(this.validateRoadblocksDrivers(updatedPV, aCom));
        }

        // -- save roadblocks
        this.persistRoadblocks(updatedPV, roadblocks, aCom.getState());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommunicationObjectModel callCreditScore(CommunicationObjectModel com) throws AutoquoteFacadeException {
        try {
            PolicyVersion policyVersion = this.getPolicyVersion(com.getPolicyVersionId());

            this.creditScoreBusinessProcess.callTheCreditScore(policyVersion);
            boolean showNoHit = false;
            try {
                this.usageBusinessProcess.processTheNoHitException(policyVersion);

            } catch (NoHitException e) {
                this.noHitBusinessProcess.updateNoHitFlag(policyVersion);
                showNoHit = true;
            }


            CommunicationObjectModel updatedCom = this.buildCom(policyVersion, com);
            updatedCom.getState().setShowNoHit(showNoHit);

            return updatedCom;

        } catch (Exception e) {
            if (LOG.isErrorEnabled()) {
                LOG.error(Logger.EVENT_SUCCESS, String.format("An error occured: %s", e.getMessage()).toString());
            }
            throw new AutoquoteFacadeException(e);
        }
    }

    /*
     * Assign Drivers to Vehicles
     */
    protected Boolean assignDriver(final CommunicationObjectModel aCom) throws AutoquoteFacadeException {
        Boolean isAssigned = Boolean.FALSE;
        final PolicyVersion aPolicyVersion = this.getPolicyVersion(aCom.getPolicyVersionId());

        // only assignDrivers if current state allows it
        ComState currentState = aCom.getState();
        if (aPolicyVersion != null && currentState != null && currentState.isCanRate()) {
            try {
                this.usageBusinessProcess.assignDrivers(aPolicyVersion);
                isAssigned = Boolean.TRUE; // gets here so we assume assignDrivers passed
            } catch (Exception e) {
                if (LOG.isErrorEnabled()) {
                    LOG.error(Logger.EVENT_SUCCESS, String.format("An error occured: %s", e.getMessage()).toString(), e);
                }
                throw new AutoquoteFacadeException("Exception occured in assignDrivers", e);
            }
        }
        return isAssigned;
    }

    public boolean validateVehiclesDEFAULTRB(CommunicationObjectModel aCom, Locale locale) {
        return false;
    }

    @Override
    protected RoadBlockException convertRuleExceptionResultInRoadBlockException(
            InsuranceRisk ir, RuleExceptionResult hardrb) {
        return new RoadBlockException(hardrb.getMessageKey(), ir, hardrb.getBusinessRuleCode());
    }

    @Override
    protected RoadBlockException convertRuleExceptionResultInRoadBlockException(
            Party party, RuleExceptionResult hardrb) {
        return new RoadBlockException(hardrb.getMessageKey(), party, hardrb.getBusinessRuleCode());
    }
}
