package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.util.SSSUtils;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.base.SSDC;
import intact.lab.autoquote.backend.services.rating.IExecuteService;
import intact.lab.autoquote.backend.services.rating.IGenericPegaService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ExecuteServiceQCIntact implements IExecuteService {

    @Autowired
    @Qualifier("determineQuoteInfos_0x00")
    private IGenericPegaService determineQuoteInfos;

    @Autowired
    @Qualifier("manageProducts0x00")
    private IGenericPegaService manageProducts0x00;

    @Autowired
    @Qualifier("codePlPolicyIntact0x00")
    private IGenericPegaService codePlPolicyIntact0x00;

    @Autowired
    @Qualifier("getAnnualPremiumCoverages31x00")
    private IGenericPegaService getAnnualPremiumCoveragesByPolicyVersion;

    @Autowired
    @Qualifier("getMultiplicativeRatingFactors2x00")
    private IGenericPegaService getMultiplicativeRatingFactors;

    private static final Logger LOG = ESAPI.getLogger(ExecuteServiceQCIntact.class);

    @Override
    public PolicyVersion determineQuoteInfos(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Pega::determineQuoteInfos_0x00");
        }
        return (PolicyVersion) this.determineQuoteInfos.executeService(aCtxt, aPolicy);
    }

    /**
     * Calls manageProducts
     *
     * @param aPolicy policyVersion
     * @param ilParams  the a params
     * @throws BaseException the base exception
     */
    public PolicyVersion manageProducts(PolicyVersion aPolicy, Map<String, Object> ilParams) throws BaseException {
        PolicyVersion response = (PolicyVersion) this.manageProducts0x00.executeService(ilParams, aPolicy);
        String traceId = SSSUtils.getTraceId(ilParams);
        String serviceStatus = SSSUtils.getServiceStatus(ilParams);
        if (!SSDC.SERVICE_STATUS_VALUE_OK.equalsIgnoreCase(serviceStatus)) {
            LOG.error(Logger.EVENT_SUCCESS, "manageProducts0x00 Service status > " + serviceStatus + " traceId > " + traceId);
        }
        return response;
    }

    /**
     * Calls codePl
     *
     * @param aPolicy policyVersion
     * @param ilParams  the a params
     * @throws BaseException the base exception
     */
    public PolicyVersion codePl(PolicyVersion aPolicy, Map<String, Object> ilParams) throws BaseException {
        PolicyVersion response = (PolicyVersion) this.codePlPolicyIntact0x00.executeService(ilParams, aPolicy);
        String traceId = SSSUtils.getTraceId(ilParams);
        String serviceStatus = SSSUtils.getServiceStatus(ilParams);
        if (!SSDC.SERVICE_STATUS_VALUE_OK.equalsIgnoreCase(serviceStatus)) {
            LOG.error(Logger.EVENT_SUCCESS, "codePlPolicyIntact0x00 Service status > " + serviceStatus + " traceId > " + traceId);
        }
        return response;
    }

    /**
     * Gets the annual premium coverages.
     *
     * @param aPolicy policyVersion
     * @param ilPegaParams  the a params
     * @throws BaseException the base exception
     */
    public PolicyVersion getAnnualPremiumCoverages(PolicyVersion aPolicy, Map<String, Object> ilPegaParams) throws BaseException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Pega::getAnnualPremiumCoveragesByPolicyVersion");
        }

        PolicyVersion policyVersion = (PolicyVersion) this.getAnnualPremiumCoveragesByPolicyVersion.executeService(ilPegaParams, aPolicy);

        String traceId = SSSUtils.getTraceId(ilPegaParams);
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, ">" + traceId);
        }

        return policyVersion;
    }

    /**
     * Gets the multiplicative rating factors.
     *
     * @param aCtxt   the a ctxt
     * @param aPolicy the a policy
     * @return the multiplicative rating factors
     * @throws BaseException the base exception
     */
    public PolicyVersion getMultiplicativeRatingFactors(ManufacturingContext aCtxt, PolicyVersion aPolicy) throws BaseException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, "Pega::getMultiplicativeRatingFactors");
        }

        return (PolicyVersion) this.getMultiplicativeRatingFactors.executeService(aCtxt, aPolicy);
    }
}
