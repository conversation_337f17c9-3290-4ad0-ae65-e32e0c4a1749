package intact.lab.autoquote.backend.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import jakarta.validation.Valid;
import lombok.Data;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.List;

@Data
public class PartyDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;

    private String firstName;

    private String lastName;

    private String gender;

    private PartyTypeEnum partyType;

    private String unstructuredName;

    private Integer id;

    @Valid
    private AddressDTO address;

    private String emailAddress;

    private String phoneNumber;

    private List<PartyRoleDTO> partyRoles = new ArrayList<>();

    private List<ConsentDTO> consents = new ArrayList<>();
}
