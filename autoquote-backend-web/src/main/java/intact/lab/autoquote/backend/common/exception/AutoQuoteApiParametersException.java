package intact.lab.autoquote.backend.common.exception;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class AutoQuoteApiParametersException extends AutoQuoteException {

	public static final String EXEC_DEFAULT_ERROR = "exec.application.default.error";
	public static final String PARAM_API_KEY_NULL_ERROR = "param.api.info.api.key.null.error";
	public static final String PARAM_CONTEXT_ID_NULL_ERROR = "param.api.info.api.key.null.error";
	public static final String PARAM_LANGUAGE_NULL_ERROR = "param.api.info.language.null.error";
	public static final String PARAM_PROVINCE_NULL_ERROR = "param.api.info.province.null.error";
	public static final String PARAM_PROVINCE_INVALID_ERROR = "param.api.info.province.invalid.error";
	public static final String PARAM_LANGUAGE_INVALID_ERROR = "param.api.info.language.invalid.error";
	public static final String PARAM_UNVALID_API_KEY_ERROR = "param.api.info.api.key.invalid.error";
	public static final String EXEC_BUILD_USER_ERROR = "exec.build.user.context.error";
	private static final long serialVersionUID = -4683495659146222785L;
	private static Map<String, String> messages = null;

	public AutoQuoteApiParametersException(String message, Throwable cause) {
		super(message, cause);
	}

	public AutoQuoteApiParametersException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public AutoQuoteApiParametersException(String exceptionCode, Throwable cause, Object... parameters) {
		this(AutoQuoteApiParametersException.getMessage(exceptionCode, parameters), cause);
		this.setCode(exceptionCode);
	}

	public static Map<String, String> getMessages() {
		return AutoQuoteApiParametersException.messages;
	}

	public static void setMessages(Map<String, String> messages) {
		AutoQuoteApiParametersException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Object... parameters) {

		if (AutoQuoteApiParametersException.getMessages() == null) {
			AutoQuoteApiParametersException.initMessages();
		}

		String messageFormat = AutoQuoteConfigurationException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = AutoQuoteConfigurationException.getMessages().get(AutoQuoteApiParametersException.EXEC_DEFAULT_ERROR);
		}

		return MessageFormat.format(messageFormat, parameters);
	}

	protected static synchronized void initMessages() {

		Map<String, String> messages = new HashMap<>();

		messages.put(AutoQuoteApiParametersException.PARAM_API_KEY_NULL_ERROR,
				"The api key passed as a parameter is null. Please, provide de non null parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_LANGUAGE_NULL_ERROR,
				"The language passed as a parameter is null. Please, provide de non null parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_PROVINCE_NULL_ERROR,
				"The province passed as a parameter is null. Please, provide de non null parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_CONTEXT_ID_NULL_ERROR,
				"The contextId passed as a parameter is null. Please, provide de non null parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_PROVINCE_INVALID_ERROR,
				"The province passed as a parameter is invalid. Please, provide a valid parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_LANGUAGE_INVALID_ERROR,
				"The language passed as a parameter is invalid. Please, provide a valid parameter");
		messages.put(AutoQuoteApiParametersException.PARAM_UNVALID_API_KEY_ERROR,
				"The apiKey provided by the user is not valid.");
		messages.put(AutoQuoteApiParametersException.EXEC_BUILD_USER_ERROR,
				"An error occured while trying to build the user context with language {0}, province {1} and apiKey {2} . The cause of the error is {0}");
		AutoQuoteConfigurationException.setMessages(messages);
	}

}
