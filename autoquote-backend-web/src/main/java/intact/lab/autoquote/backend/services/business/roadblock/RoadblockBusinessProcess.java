/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.roadblock;

import com.ing.canada.common.exception.RoadBlockException;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.roadblock.Roadblockable;
import com.ing.canada.plp.service.IRoadblockService;
import intact.lab.autoquote.backend.services.business.common.IBusinessTransactionLoggingService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 *
 *         This service manages the persistence of roadblock states and entities.
 */
@Component
public class RoadblockBusinessProcess implements IRoadblockBusinessProcess {

	private static final Logger LOG = ESAPI.getLogger(RoadblockBusinessProcess.class);

	@Autowired
	private IBusinessTransactionLoggingService businessTrxLoggingService;

	@Autowired
	private IRoadblockService roadblockService;

	/**
	 * {@inheritDoc}
	 */
	@Override
	@Transactional
	public void persistRoadblock(Long policyVersionId, RoadBlockException aRoadblockableException) {

		if (policyVersionId != null) {
			if (LOG.isInfoEnabled()) {
				LOG.info(Logger.EVENT_SUCCESS, "persisting sub activity and sub activity complementary info related to the triggered roadblock.");
			}

			// Persists the sub activity and sub activity complementary info related to the triggered roadblock.
			long subActivityId = this.businessTrxLoggingService.createSubActivity(
					BusinessTransactionSubActivityCodeEnum.ROADBLOCK, policyVersionId, 0);

			this.businessTrxLoggingService.createSubActivityComplInfo("ROADBLOCK_CODE",
					aRoadblockableException.getMessageKey(), subActivityId);

			Roadblockable roadblockable = aRoadblockableException.getRoadblockable();

			if (roadblockable == null) {
				if (LOG.isWarningEnabled()) {
					LOG.warning(Logger.EVENT_SUCCESS, "no roadblocked object, we cannot persist the roadblock indicators in the database");
				}
				return;
			}

			if (LOG.isInfoEnabled()) {
				LOG.info(Logger.EVENT_SUCCESS, "marking " + roadblockable + " as roadblocked.");
			}
			roadblockable.markAsRoadblocked();
			this.roadblockService.persistRoadblock(roadblockable);
		}

	}
}
