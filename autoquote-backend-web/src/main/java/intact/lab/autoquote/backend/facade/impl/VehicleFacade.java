package intact.lab.autoquote.backend.facade.impl;

import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.ModelDTO;
import intact.lab.autoquote.backend.common.exception.AutoQuoteVehicleException;
import intact.lab.autoquote.backend.common.utils.ContextUtil;
import intact.lab.autoquote.backend.common.utils.MediatorUtil;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.validation.impl.GeneralValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class VehicleFacade implements IVehicleFacade {

    @AutowiredLocal
    private IVehicleBusinessProcess vehicleBusinessProcess;

    public List<MakeDTO> getVehicleMakeList(String year, String province, String language) {

        GeneralValidator.validateApiParameter(language, province);
        this.validate(year);
        List<MakeDTO> vehicleMakes = new ArrayList<>();

        try {
            ManufacturingContext mCtxt = MediatorUtil.convertContext(ContextUtil.loadInitialComContext(null, province, language,
                    null, null, null));

            List<String> aVehicleMakeList = this.vehicleBusinessProcess.getVehicleManufacturerListByYear(year, mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness(), mCtxt.getProvince());

            if (CollectionUtils.isNotEmpty(aVehicleMakeList)) {
                for (String aMake : aVehicleMakeList) {
                    vehicleMakes.add(new MakeDTO(aMake, aMake));
                }
            }
            return vehicleMakes;
        } catch (Exception e) {
            throw new AutoQuoteVehicleException("Error retrieving vehicle makes for year: " + year);
        }
    }

    public List<ModelDTO> getVehicleModels(String year, String makeId, String province, String language) {

        GeneralValidator.validateApiParameter(language, province);
        this.validateVehicleYearAndMake(year, makeId);
        List<ModelDTO> vehicleModels = new ArrayList<>();

        try {
            ManufacturingContext mCtxt = MediatorUtil.convertContext(ContextUtil.loadInitialComContext(null, province, language,
                    null, null, null));

            List<VehicleModel> aVehicleModelList = this.vehicleBusinessProcess.getVehicleModelList(Language.fromLocaleCode(language), makeId, year,
                    mCtxt.getDistributionChannel(), mCtxt.getInsuranceBusiness(), mCtxt.getProvince());

            if (CollectionUtils.isNotEmpty(aVehicleModelList)) {
                for (VehicleModel aModel : aVehicleModelList) {
                    vehicleModels.add(new ModelDTO(aModel.getCode(), aModel.getModel()));
                }
            }
            return vehicleModels;
        } catch (Exception e) {
            throw new AutoQuoteVehicleException("Error retrieving vehicle makes for year: " + year);
        }
    }

    private void validate(String year) throws AutoQuoteVehicleException {
        if (StringUtils.isEmpty(year)) {
            throw new AutoQuoteVehicleException(AutoQuoteVehicleException.PARAM_YEAR_NULL);
        }

        try {
            Integer.parseInt(year);
        } catch (Exception ex) {
            throw new AutoQuoteVehicleException(AutoQuoteVehicleException.PARAM_VEHICLE_YEAR_INVALID);
        }
    }

    private void validateVehicleYearAndMake(String year, String make) throws AutoQuoteVehicleException {

        this.validate(year);

        if (make == null) {
            throw new AutoQuoteVehicleException(AutoQuoteVehicleException.PARAM_VEHICLE_MAKE_NULL);
        }
    }
}
