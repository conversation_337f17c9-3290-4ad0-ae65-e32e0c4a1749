/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
* without the written permission of Intact Insurance
* Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
*/
package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.DriverLicenseClassCodeEnum;
import com.ing.canada.plp.domain.enums.DriverLicenseTypeCodeEnum;
import com.ing.canada.plp.domain.enums.HolderAutoInsuranceSinceCodeEnum;
import com.ing.canada.plp.domain.enums.InsuredWithCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UBIProviderCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.PartyRoleInRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.policyversion.PriorCarrierPolicyInfo;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.business.util.DriverHelper;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.apache.commons.collections4.CollectionUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> L.
 *
 */
@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class COMtoPLAdapterONIntactCL extends COMtoPLAdapterIntact {

    private static final String CARRIER_CODE_NONE = "NONE";
    private static final Logger LOG = ESAPI.getLogger(COMtoPLAdapterONIntactCL.class);

    @Autowired
    private UBIProviderCodeEnum ubiProviderONIntact;

    @Autowired
    protected IVehicleHelper vehicleHelper;

    @Autowired
    DriverHelper driverHelper;

    public COMtoPLAdapterONIntactCL(ICommonBusinessProcess commonBusinessProcess, IMediatorComPlp mediatorComPlp,
                                    IMediatorVehiclePlp mediatorVehiclePlp, IMediatorDriverPlp mediatorDriverPlp,
                                    IMediatorClaimPlp mediatorClaim, MediatorPaymentPlp mediatorPaymentPlp, IVehicleHelper vehicleHelper,
                                    IPartyHelper partyHelper, IMediatorAdvisor mediatorAdvisor, IPolicyVersionHelper plpPolicyVersionHelper,
                                    IInsuranceRiskHelper plpInsuranceRiskHelper, IDateManagerService capiPolicyChangeDateService) {
        super(commonBusinessProcess, mediatorComPlp, mediatorVehiclePlp, mediatorDriverPlp, mediatorClaim, mediatorPaymentPlp,
                vehicleHelper, partyHelper, mediatorAdvisor, plpPolicyVersionHelper, plpInsuranceRiskHelper,
                capiPolicyChangeDateService);
    }

    @Override
    protected void localizedPostMediationForParty(ComEvent event, Party party, Map<String, String> insuredGroups, ManufacturingContext context, Locale locale) {
        // Applies to the driver only - manages driver license info same as PL
        if (isDriver(party)) {
            this.manageDriverLicenseObtainedAge(party);
        }

        // For ON CL, it needs to be set to null in order for Pega to set the related smart default numberOfYearsContinuouslyInsuredWithPriorCarrier.
        PriorCarrierPolicyInfo priorCarrierPolicyInfo = party.getPolicyVersion().getPriorCarrierPolicyInfo();
        if (priorCarrierPolicyInfo != null && CARRIER_CODE_NONE.equals(priorCarrierPolicyInfo.getCarrierCode())) {
            priorCarrierPolicyInfo.setCarrierCode(null);
        }
    }

    protected void manageDriverLicenseObtainedAge(Party party) {

        DriverComplementInfo driver = party.getDriverComplementInfo();
        driver.clearDriverLicenseClass();

        if (driver.getDateDriverLicenseObtained() != null) {
            if (this.driverHelper.isBornBeforeApril1994(driver)) {
                this.manageDriverLicenseObtainedBeforeFirstApril1994(driver);
            } else {
                this.manageDriverLicenseObtainedAfter31March1994(driver);
            }
        } else {
            driver.setAgeDriverLicenseObtained(null);
            driver.setDateDriverLicenseObtained(null);
        }

        DriverComplementInfo driverComplementInfo = party.getDriverComplementInfo();

        PriorCarrierPolicyInfo priorCarrierPolicyInfo = party.getPolicyVersion().getPriorCarrierPolicyInfo();
        Byte numberYearInsured = priorCarrierPolicyInfo.getNumberOfYearsContinuouslyInsuredWithPriorCarrier();
        Calendar now = Calendar.getInstance();
        Date insuredSince = null;
        HolderAutoInsuranceSinceCodeEnum holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.LESS_THAN_1_YEAR;
        InsuredWithCompanyCodeEnum insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.LESS_THAN_1_YEAR;

        if (numberYearInsured != null) {

            now.add(Calendar.YEAR, -numberYearInsured);
            insuredSince = now.getTime();

            if (numberYearInsured == 0) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.LESS_THAN_1_YEAR;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.LESS_THAN_1_YEAR;
            } else if (numberYearInsured == 1 || numberYearInsured < 2) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.ONE_YEAR_BUT_LESS_THAN_TWO;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.ONE_YEAR_BUT_LESS_THAN_TWO;
            } else if (numberYearInsured == 2 || numberYearInsured < 3) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.TWO_YEARS_BUT_LESS_THAN_THREE;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.TWO_YEARS_BUT_LESS_THAN_THREE;
            } else if (numberYearInsured == 3 || numberYearInsured < 4) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.THREE_YEARS_BUT_LESS_THAN_FOUR;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.THREE_YEARS_BUT_LESS_THAN_FOUR;
            } else if (numberYearInsured == 4 || numberYearInsured < 5) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.FOUR_YEARS_BUT_LESS_THAN_FIVE;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.FOUR_YEARS_BUT_LESS_THAN_FIVE;
            } else if (numberYearInsured == 5 || numberYearInsured < 6) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.FIVE_YEARS_BUT_LESS_THAN_SIX;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.FIVE_YEARS_BUT_LESS_THAN_SIX;
            } else if (numberYearInsured >= 6) {
                holderAutoInsuranceSinceCodeEnum = HolderAutoInsuranceSinceCodeEnum.MORE_THAN_SIX;
                insuredWithCompanyCodeEnum = InsuredWithCompanyCodeEnum.MORE_THAN_SIX;
            }
        }

        PolicyVersion aPlPolicyVersion = party.getPolicyVersion();

        PartyRoleInRisk principalDriver = null;

        if (CollectionUtils.isNotEmpty(aPlPolicyVersion.getInsuranceRisks())) {
            com.ing.canada.plp.domain.insurancerisk.InsuranceRisk insuranceRisk = aPlPolicyVersion.getInsuranceRisks().iterator().next();
            principalDriver = this.vehicleHelper.getPrincipalDriver(insuranceRisk);
        }


        if (insuredSince != null) {
            aPlPolicyVersion.setHolderOfAutoInsuranceSince(insuredSince);
            aPlPolicyVersion.setHolderOfAutoInsuranceSinceCode(holderAutoInsuranceSinceCodeEnum);
            // 68102 code 0 (less than a year is used for not currently insured but was in the past
            if (holderAutoInsuranceSinceCodeEnum == HolderAutoInsuranceSinceCodeEnum.LESS_THAN_1_YEAR) {
                aPlPolicyVersion.setHolderAutoInsuranceIndicator(Boolean.FALSE);
            } else {
                aPlPolicyVersion.setHolderAutoInsuranceIndicator(Boolean.TRUE);
            }
            aPlPolicyVersion.setHasHeldFullTermAutoInsuranceIndicator(Boolean.TRUE);
            driverComplementInfo.setInsuredWithCompanyCode(insuredWithCompanyCodeEnum);
            driverComplementInfo.setContinuouslyInsuredSinceDate(insuredSince);
        } else {
            if (principalDriver != null) {
                principalDriver.setAlreadyBeenPrincipalDriverIndicator(Boolean.FALSE);
            }
            priorCarrierPolicyInfo.setCarrierCode("NONE"); //68102
            aPlPolicyVersion.setHasHeldFullTermAutoInsuranceIndicator(Boolean.FALSE);
            aPlPolicyVersion.setHolderAutoInsuranceIndicator(Boolean.FALSE);
            driverComplementInfo.setInsuredWithCompanyCode(null);
            driverComplementInfo.setContinuouslyInsuredSinceDate(null);
        }
    }

    protected void manageDriverLicenseObtainedBeforeFirstApril1994(DriverComplementInfo aDriverComplementInfo) {
        // BR2640
        aDriverComplementInfo.setDriverLicenseType(DriverLicenseTypeCodeEnum.REGULAR_LICENSE);
        DriverLicenseClass currentClass = this.partyHelper.getCurrentDriverLicenseClass(aDriverComplementInfo);

        if (currentClass == null) {
            currentClass = new DriverLicenseClass();
        }

        currentClass.setDriverLicenseClass(DriverLicenseClassCodeEnum.GRADUATED_PERMIT);
        currentClass.setEffectiveDate(aDriverComplementInfo.getDateDriverLicenseObtained());
        aDriverComplementInfo.addDriverLicenseClass(currentClass);
    }

    /**
     * Set driver license details obtained after 31 March 1994
     */
    protected void manageDriverLicenseObtainedAfter31March1994(DriverComplementInfo aDriverComplementInfo) {
        LOG.debug(Logger.EVENT_SUCCESS, "> Execute BR2641");
        Date g1ObtentionDate = aDriverComplementInfo.getDateDriverLicenseObtained();
        if (DriverLicenseTypeCodeEnum.REGULAR_LICENSE.equals(aDriverComplementInfo.getDriverLicenseType())) {
            if (aDriverComplementInfo.getObtainedG2OrGClassInLast12MonthsIndicator() != null && aDriverComplementInfo.getObtainedG2OrGClassInLast12MonthsIndicator().equals(Boolean.TRUE)) {
                this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.GRADUATED_PERMIT, this.addYearsToDate(Calendar.getInstance().getTime(), -1));
            } else {
                this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.GRADUATED_PERMIT,
                        this.licenseObtainedLast12Monts(g1ObtentionDate) ? this.addYearsToDate(g1ObtentionDate, -1) : this.addYearsToDate(g1ObtentionDate, 2));
            }
            this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.PROBATIONARY_PERMIT,
                    this.addYearsToDate(g1ObtentionDate, 1));
            this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.LEARNER_PERMIT, g1ObtentionDate);
        } else if (DriverLicenseTypeCodeEnum.PROBATIONARY_LICENSE.equals(aDriverComplementInfo.getDriverLicenseType())) {
            if (aDriverComplementInfo.getObtainedG2OrGClassInLast12MonthsIndicator() != null && aDriverComplementInfo.getObtainedG2OrGClassInLast12MonthsIndicator().equals(Boolean.TRUE)) {
                this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.PROBATIONARY_PERMIT, this.addYearsToDate(Calendar.getInstance().getTime(), -1));
            } else {
                this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.PROBATIONARY_PERMIT,
                        this.licenseObtainedLast12Monts(g1ObtentionDate) ? this.addYearsToDate(g1ObtentionDate, -1) : this.addYearsToDate(g1ObtentionDate, 1));
            }
            this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.LEARNER_PERMIT, g1ObtentionDate);
        } else if (DriverLicenseTypeCodeEnum.LEARNER_LICENSE.equals(aDriverComplementInfo.getDriverLicenseType())) {
            this.addDriverLicenseClass(aDriverComplementInfo, DriverLicenseClassCodeEnum.LEARNER_PERMIT, g1ObtentionDate);
        }
    }

    private void addDriverLicenseClass(DriverComplementInfo aDriverComplementInfo,
                                       DriverLicenseClassCodeEnum driverLicenseClass, Date effectiveDate) {
        DriverLicenseClass currentClass = new DriverLicenseClass();

        currentClass.setDriverLicenseClass(driverLicenseClass);
        currentClass.setEffectiveDate(effectiveDate);
        aDriverComplementInfo.addDriverLicenseClass(currentClass);
    }

    private Date addYearsToDate(Date original, int yearsToAdd) {
        Calendar o = Calendar.getInstance();
        o.setTime(original);
        o.add(Calendar.YEAR, yearsToAdd);
        return o.getTime();
    }

    private boolean licenseObtainedLast12Monts(Date original) {
        Calendar o = Calendar.getInstance();
        o.setTime(original);
        Calendar now = Calendar.getInstance();
        now.add(Calendar.YEAR, -1);
        return now.before(o);
    }

    @Override
    protected void setUbiProvider(DriverComplementInfo driverComplementInfo) {
        driverComplementInfo.setUBIProvider(ubiProviderONIntact);
    }

    @Override
    protected void setUbiProgramVersionCode(DriverComplementInfo driverComplementInfo) {
        // do nothing here.
    }

    private boolean isDriver(Party party) {
        return party.getDriverComplementInfo() != null;
    }
}
