/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.DriverLicenseTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LicenseJurisdictionCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.MaritalStatusCodeEnum;
import com.ing.canada.plp.domain.enums.NumberOfKmsUsedOutsideProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.PurposeUseOutsideOfPvCodeEnum;
import com.ing.canada.plp.domain.enums.RelationshipToNamedInsuredCodeEnum;
import com.ing.canada.plp.domain.enums.UBIProviderCodeEnum;
import com.ing.canada.plp.domain.enums.UseOfVehicleCategoryCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.util.Assert;

import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> L.
 */
@ComponentLocal(province = ProvinceCodeEnum.QUEBEC, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class COMtoPLAdapterQCIntactCL extends COMtoPLAdapterIntact {

	protected static final Logger LOG = ESAPI.getLogger(COMtoPLAdapterQCIntactCL.class);

    private final UBIProviderCodeEnum ubiProviderQCIntact;

	public COMtoPLAdapterQCIntactCL(ICommonBusinessProcess commonBusinessProcess, IMediatorComPlp mediatorComPlp,
									IMediatorVehiclePlp mediatorVehiclePlp, IMediatorDriverPlp mediatorDriverPlp,
									IMediatorClaimPlp mediatorClaim, MediatorPaymentPlp mediatorPaymentPlp,
									IVehicleHelper vehicleHelper, IPartyHelper partyHelper, IMediatorAdvisor mediatorAdvisor,
									IPolicyVersionHelper plpPolicyVersionHelper, IInsuranceRiskHelper plpInsuranceRiskHelper,
									IDateManagerService capiPolicyChangeDateService, UBIProviderCodeEnum ubiProviderQCIntact) {
		super(commonBusinessProcess, mediatorComPlp, mediatorVehiclePlp, mediatorDriverPlp, mediatorClaim, mediatorPaymentPlp,
				vehicleHelper, partyHelper, mediatorAdvisor, plpPolicyVersionHelper, plpInsuranceRiskHelper, capiPolicyChangeDateService);
		this.ubiProviderQCIntact = ubiProviderQCIntact;
	}

	/* this method is a cut-and-past of *QCBelair */
	@Override
	protected void completeDriverAssignment(Vehicle vehicle) {
		if (vehicle != null) {
			Boolean usedOutsideQuebec = vehicle.getUsedOutsideProvinceOrCountryIndicator();
			if (BooleanUtils.isTrue(usedOutsideQuebec)) {

				UseOfVehicleCategoryCodeEnum vehicleUsageType = vehicle.getUseOfVehicleCategory();

				// DE263 - Km outside QC for business - BR0474
				if (UseOfVehicleCategoryCodeEnum.BUSINESS.equals(vehicleUsageType)
						|| UseOfVehicleCategoryCodeEnum.BUSINESS_OCCASIONALLY.equals(vehicleUsageType)) {
					vehicle.setPurposeUseOutsideOfProvince(PurposeUseOutsideOfPvCodeEnum.BUSINESS);

					// DE262 - Km outside QC - BR0473
				} else if (UseOfVehicleCategoryCodeEnum.PLEASURE.equals(vehicleUsageType)) {
					vehicle.setPurposeUseOutsideOfProvince(PurposeUseOutsideOfPvCodeEnum.PLEASURE);
				}
			}
		}
	}

	@Override
	protected void localizedPostMediationForParty(ComEvent event, Party party, Map<String, String> insuredGroups, ManufacturingContext context, Locale locale) {
		this.addSpecificInsuredGroups(party, context, insuredGroups);
		this.driverBusinessProcess.manageUniversityDegree(party, insuredGroups.get("Uni"));

		if (party.getDriverComplementInfo() != null) {
			this.setDateDriverLicenseObtained(party);
			// TODO: remove these 2 lines once PEGA populates them via SmartValues.
			party.getDriverComplementInfo().setDriverLicenseType(DriverLicenseTypeCodeEnum.REGULAR_LICENSE);
			party.getDriverComplementInfo().setRelationshipToNamedInsured(RelationshipToNamedInsuredCodeEnum.INSURED);
		}
		// TODO: remove this line as well once PEGA populates them via SmartValues.
		party.setMaritalStatus(MaritalStatusCodeEnum.SINGLE);
	}

	@Override
	protected void localizedPostMediationForVehicle(Vehicle vehicle, ManufacturingContext context, Locale locale) {
		// DE262 - Km outside QC
		if (vehicle.getNumberOfKmsUsedOutsideOfProvince() == null) {
			vehicle.setNumberOfKmsUsedOutsideOfProvince(NumberOfKmsUsedOutsideProvinceCodeEnum.ZERO);
		} else {
			vehicle.setNumberOfKmsUsedOutsideOfProvinceBusiness(vehicle.getNumberOfKmsUsedOutsideOfProvince());
		}

		// DE263 - Km outside QC for business
		if (vehicle.getNumberOfKmsUsedOutsideOfProvinceBusiness() == null) {
			vehicle.setNumberOfKmsUsedOutsideOfProvinceBusiness(NumberOfKmsUsedOutsideProvinceCodeEnum.ZERO);
		}

		if (vehicle.getUseOfVehicleCategory() == null) {
			vehicle.setUseOfVehicleCategory(UseOfVehicleCategoryCodeEnum.COMMERCIAL);
		}
	}

	@Override
	protected void localizedDriversLicenseJurisdiction(Party party) {
		party.getDriverComplementInfo().setLicenseJurisdiction(LicenseJurisdictionCodeEnum.QUEBEC);
	}

	@Override
	protected void setUbiProvider(DriverComplementInfo driverComplementInfo) {
		driverComplementInfo.setUBIProvider(ubiProviderQCIntact);
	}

	@Override
	protected void setUbiProgramVersionCode(DriverComplementInfo driverComplementInfo) {
        String ubiProgramVersionCode = "ADD_VALUE";
        if (LOG.isInfoEnabled()) {
			LOG.info(null, String.format("setting UbiProgramVersionCode=%s", ubiProgramVersionCode));
		}
		driverComplementInfo.setUbiProgramVersionCode(ubiProgramVersionCode);
	}

	/*
	 * (non-javadoc)
	 *
	 * For Québec, set the dateLicenseObtained from ageLicenseObtained when the date license obtained is not present
	 */
	private void setDateDriverLicenseObtained(Party party) {
		Assert.notNull(party, "Party parameter is required.");
		Assert.notNull(party.getDriverComplementInfo(), "Party.driverComplementInfo parameter is required.");

		DriverComplementInfo driver = party.getDriverComplementInfo();
		// when date driver has provided the age obtained for the driver's license and date of birth is set
		if (driver.getAgeDriverLicenseObtained() != null && party.getBirthDate() != null) {
			DateTime dateLicObt = new DateTime(party.getBirthDate());
			dateLicObt = dateLicObt.plusYears(driver.getAgeDriverLicenseObtained().intValue());

			driver.setDateDriverLicenseObtained(dateLicObt.toDate());
		}
	}
}
