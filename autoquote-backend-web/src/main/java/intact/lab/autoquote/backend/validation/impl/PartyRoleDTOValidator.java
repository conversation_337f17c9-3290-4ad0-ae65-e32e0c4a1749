package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.PartyRoleDTO;
import intact.lab.autoquote.backend.common.enums.RoleTypeEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.validation.IPartyRoleDTOValidator;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component("PartyRoleDTOValidator")
public class PartyRoleDTOValidator implements IPartyRoleDTOValidator {

	@Override
	public void validate(PartyRoleDTO partyRoleDTO, Errors errors) {
		validateRoleType(partyRoleDTO.getRoleType(), errors);
	}

	private void validateRoleType(RoleTypeEnum roleType, Errors errors) {
		if (null == roleType) {
			errors.rejectValue("roleType", BRulesExceptionEnum.NotBlank.getErrorCode(), "[roleType]");
		}
	}

}
