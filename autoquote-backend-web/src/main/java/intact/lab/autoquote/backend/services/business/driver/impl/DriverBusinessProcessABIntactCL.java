/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.driver.impl;

import com.ing.canada.cif.service.IPostalCodeService;
import com.ing.canada.common.exception.RoadBlockExceptionContextEnum;
import com.ing.canada.common.services.api.affinity.IAffinityGroupService;
import com.ing.canada.common.services.api.municipality.IMunicipalitiesByPostalCodeService;
import com.ing.canada.common.services.api.municipality.IMunicipalityDetailService;
import com.ing.canada.common.services.api.party.IInsuredGroupService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.ClaimTypeCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.KindOfLossCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.insurancerisk.KindOfLoss;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyGroupHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.driver.BR13620_TwoClaims;
import com.intact.business.rules.driver.BR15547_FourMinorConvictions;
import com.intact.business.rules.driver.BR15974_MainDriverForLessThen3YearsForHeavyVehicle;
import com.intact.business.rules.driver.BR2604_CurrentInsurerIntact;
import com.intact.business.rules.driver.BR2623_8_MajorConvictions;
import com.intact.business.rules.driver.BR2948_CurrentInsurerIsAXA;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import intact.lab.autoquote.backend.common.enums.AutoquoteRoadBlockExceptionEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteRoadBlockException;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;
import intact.lab.autoquote.backend.services.business.common.IOccupationService;
import intact.lab.autoquote.backend.services.singleid.IProfileService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Business process specific to Intact Alberta CL. Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 * <AUTHOR> && andho && cpopovic
 */
@ComponentLocal(province = ProvinceCodeEnum.ALBERTA, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class DriverBusinessProcessABIntactCL extends DriverBusinessProcess {

	BR15974_MainDriverForLessThen3YearsForHeavyVehicle br15974_mainDriverForLessThen3Years;
	private final BR2623_8_MajorConvictions br2623;
	private final BR13620_TwoClaims br13620;
	private final BR15547_FourMinorConvictions br15547;
	private final BR2604_CurrentInsurerIntact br2604;
	private final BR2948_CurrentInsurerIsAXA br2948;

	@AutowiredLocal
	protected IProfileService profileService;

	public DriverBusinessProcessABIntactCL(IPolicyVersionHelper policyVersionHelper, IMunicipalityDetailService municipalityDetailsService,
                                           IMunicipalitiesByPostalCodeService municipalityByPostalCodeService,
                                           IPostalCodeService postalCodeService, IPartyHelper partyHelper,
                                           IFormFieldValidatorService formFieldValidatorService, IPartyGroupHelper partyGroupHelper,
                                           IAffinityGroupService affinityGroupService, IInsuredGroupService insuredGroupService,
                                           IPartyRoleInRiskService partyRoleInRiskService, IPartyService partyService,
                                           BR2623_8_MajorConvictions br2623, BR13620_TwoClaims br13620, BR15547_FourMinorConvictions br15547,
                                           BR2604_CurrentInsurerIntact br2604, BR2948_CurrentInsurerIsAXA br2948,
                                           BR15974_MainDriverForLessThen3YearsForHeavyVehicle br15974_mainDriverForLessThen3Years, IOccupationService occupationService, IOccupationService occupationService1) {
		super(policyVersionHelper, municipalityDetailsService, municipalityByPostalCodeService, postalCodeService, partyHelper,
				formFieldValidatorService, partyGroupHelper, affinityGroupService, insuredGroupService, partyRoleInRiskService, partyService, occupationService);
		this.br2623 = br2623;
		this.br13620 = br13620;
		this.br15547 = br15547;
		this.br2604 = br2604;
		this.br2948 = br2948;
		this.br15974_mainDriverForLessThen3Years = br15974_mainDriverForLessThen3Years;
    }


	/**
	 * For AB CL, we must use harmonized code for collision (PD20).
	 *
	 * @param aClaim            {@link Claim}
	 * @param claimNatureAmount
	 * @return
	 */
	@Override
	public Claim setClaimKindOfLoss(Claim aClaim, String claimNatureAmount) {
		KindOfLoss kol = this.extractKindOfLoss(aClaim);

		switch (aClaim.getNatureOfClaim()) {
			case AT_FAULT_ACCIDENT:
				this.setKindOfLoss(kol, aClaim, KindOfLossCodeEnum.A_BODILY_INJURY_CLAIMS_BY_PASSEN,
						CoverageTypeCodeEnum.COLLISION, BasicCoverageCodeEnum.COLLISION_PD20, (short) 100, Boolean.TRUE,
						ClaimTypeCodeEnum.COLLISION, new BigDecimal(1));
				break;
			default:
				throw new RuntimeException(String.format("Unexpected claim nature: %s", aClaim.getNatureOfClaim().getCode()));

		}
		return aClaim;
	}

	@Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(DriverComplementInfo driver, ProvinceCodeEnum aProvince) {

		List<RuleExceptionResult> roadblocks = new ArrayList<>();

		//BR2623: When a driver has had 1 major or serious conviction ("Serious or major infractions in the last 3 years"), he must be referred to his broker.
		roadblocks.add(this.br2623.validateBR(driver.getParty()));

		//BR13620 when a driver has 2 claims and more
		roadblocks.add(this.br13620.validateBR(driver.getParty()));

		//BR15547 when a driver select 4 minor convictions and more
		roadblocks.add(this.br15547.validateBR(driver.getParty()));

		roadblocks.add(this.br15974_mainDriverForLessThen3Years.validate(driver.getParty()));

		return roadblocks;
	}

	@Override
	public RuleExceptionResult validateQuickQuoteHardRoadblock(DriverComplementInfo aDriver, Locale locale) {
		RuleExceptionResult resultBr2604 = this.br2604.validateBR2604(aDriver.getParty());
		if (resultBr2604.hasFailed()) {
			return resultBr2604;
		}
		RuleExceptionResult resultBr2948 = this.br2948.validateBR2948(aDriver.getParty());
		if (resultBr2948.hasFailed()) {
			return resultBr2948;
		}
		return null;
	}

	@Override
	public void manageProfile(PolicyVersion aPolicyVersion, ApplicationEnum application)
			throws SingleIdActiveProductException, AccessManagerException {

		RoadBlockExceptionContextEnum context = RoadBlockExceptionContextEnum.SAVE_DRIVER;
		try {
			this.profileService.manageProfile(aPolicyVersion.getId(), context, application);
		} catch (AutoQuoteRoadBlockException e) {
			throw new SingleIdActiveProductException(AutoquoteRoadBlockExceptionEnum.BR551, context);
		}
	}

	@Override
	public void manageMunicipalityRepositoryEntry(Locale aLocale, Address address, String postalCode, ManufacturingContext context) {
		// TODO ALBERTA MIGRATION 4: VALIDATES TRUE FOR AB
		// For intact Ontario we dont manage the repository entry for the municipality
	}
}
