package intact.lab.autoquote.backend.services.impl;

import com.intact.com.ajax.ValidValue;
import com.intact.com.context.ComContext;
import com.intact.com.enums.ComApplicationEnum;
import com.intact.com.enums.ComCompanyEnum;
import com.intact.com.enums.ComDistributionChannelCodeEnum;
import com.intact.com.enums.ComLanguageCodeEnum;
import com.intact.com.enums.ComLineOfBusinessCodeEnum;
import com.intact.com.enums.ComProvinceCodeEnum;
import intact.lab.autoquote.backend.common.enums.ContentsMappingEnum;
import intact.lab.autoquote.backend.common.enums.LocaleEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.facade.driver.impl.DriverFacade;
import intact.lab.autoquote.backend.services.IValueDomainService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * The Class AutoQuoteServiceCache
 */
@Service
public class AutoQuoteServiceCache {

	private static final int VEHICLE_CUTOFF_YEARS_AB = 20;

	private static final int VEHICLE_CUTOFF_YEARS_QC = 20;

	private static final int VEHICLE_CUTOFF_YEARS_ON = 25;

	private static final String COMPANY = "INTACT";
	/**
	 * The Constant logger.
	 */
	private static final Logger logger = ESAPI.getLogger(AutoQuoteServiceCache.class);
	private static final Set<String> OCCUPATIONS_WHITE_LIST = new HashSet<>();

	static {
		OCCUPATIONS_WHITE_LIST.add("0012"); // Accountant
		OCCUPATIONS_WHITE_LIST.add("0083"); // Architects
		OCCUPATIONS_WHITE_LIST.add("0084"); // Civil servants
		OCCUPATIONS_WHITE_LIST.add("0131"); // Dental hygienist
		OCCUPATIONS_WHITE_LIST.add("0086"); // Dentists
		OCCUPATIONS_WHITE_LIST.add("0133"); // Educational profesionnal
		OCCUPATIONS_WHITE_LIST.add("0091"); // Engineers
		OCCUPATIONS_WHITE_LIST.add("0092"); // Financial institution employees
		OCCUPATIONS_WHITE_LIST.add("0093"); // Firefighters
		OCCUPATIONS_WHITE_LIST.add("0134"); // Government
		OCCUPATIONS_WHITE_LIST.add("0094"); // Lawyers
		OCCUPATIONS_WHITE_LIST.add("0096"); // Licensed general insurance brokers
		OCCUPATIONS_WHITE_LIST.add("0135"); // Medical doctor
		OCCUPATIONS_WHITE_LIST.add("0136"); // Medical professional
		OCCUPATIONS_WHITE_LIST.add("0080"); // Police officer
		OCCUPATIONS_WHITE_LIST.add("0137"); // Student
		OCCUPATIONS_WHITE_LIST.add("0006"); // Other
	}

	@Autowired
	protected IValueDomainService valueDomainService;
	@Autowired
	protected MessageSource messageSource;

	private Map<String, Locale> locales = new HashMap<>();
	private Map<String, List<ValidValueBO>> listValuesBO = new HashMap<>();
	private Map<String, List<ValidValue>> listValues = new HashMap<>();
	private Map<String, byte[]> brokerLogos = new HashMap<>();
	private String tri = "_";
	private int initDataLoadedDate;

	/*
	 * Gets the com context with local.
	 *
	 * @param localeKey the locale key
	 * @return the com context with local
	 */
	private ComContext getComContextWithLocal(final String localeKey) {
		ComContext ctx = new ComContext();
		ctx.setCompany(ComCompanyEnum.valueOf(COMPANY));

		String provinceCode = localeKey.substring(0, 2);
		switch (provinceCode) {
			case AutoQuoteConstants.STR_ON:
				ctx.setProvince(ComProvinceCodeEnum.ONTARIO);
				break;
			case AutoQuoteConstants.STR_QC:
				ctx.setProvince(ComProvinceCodeEnum.QUEBEC);
				break;
			case AutoQuoteConstants.STR_AB:
				ctx.setProvince(ComProvinceCodeEnum.ALBERTA);
				break;
		}
		ctx.setLanguage(localeKey.substring(3, 5).toUpperCase().equals(AutoQuoteConstants.STR_FR) ? ComLanguageCodeEnum.FRENCH : ComLanguageCodeEnum.ENGLISH);
		ctx.setApplication(ComApplicationEnum.QUICKQUOTE);
		ctx.setDistributionChannel(ComDistributionChannelCodeEnum.THROUGH_BROKERS);
		ctx.setLineOfBusiness(ComLineOfBusinessCodeEnum.COMMERCIAL_LINES);
		ctx.setMobile(false);

		return ctx;
	}

	/***/
	//@PostConstruct
	public synchronized void initData() {
		this.listValuesBO.clear();
		this.listValues.clear();
		this.locales.clear();
		initLocalData();
		initCommonData();
	}

	/***/
	private void initLocalData() {
		this.locales.put(LocaleEnum.QC_FR.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.QC_FR.getLocaleLabel()));
		this.locales.put(LocaleEnum.QC_EN.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.QC_EN.getLocaleLabel()));
		this.locales.put(LocaleEnum.ON_FR.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.ON_FR.getLocaleLabel()));
		this.locales.put(LocaleEnum.ON_EN.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.ON_EN.getLocaleLabel()));
		this.locales.put(LocaleEnum.AB_FR.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.AB_FR.getLocaleLabel()));
		this.locales.put(LocaleEnum.AB_EN.getLocaleCode(), StringUtils.parseLocaleString(LocaleEnum.AB_EN.getLocaleLabel()));
	}

	/**
	 * Inits the common data.
	 */
	private void initCommonData() {
		ComContext context = null;
		for (Map.Entry<String, Locale> locale : locales.entrySet()) {
			context = getComContextWithLocal(locale.getKey());

			final String key = tri + locale.getKey();
			this.listValuesBO.put(ContentsMappingEnum.YEARS.name() + key, getVehicleYearRange(context));
			this.listValuesBO.put(ContentsMappingEnum.ANNUAL_KM.name() + key, this.valueDomainService.getListKmAnnuallyQf(locale.getValue(), false));
			this.listValuesBO.put(ContentsMappingEnum.TYPE_OF_LICENCE.name() + key, this.valueDomainService.getListDriverLicenceTypeQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.YEARS_WITH_CURRENT_INSURER.name() + key, this.valueDomainService.getListYearsContinuouslyInsuredQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.YEARS_INSURED_AS_PRINCIPAL.name() + key, this.valueDomainService.getListYearsInsuredAsPrincipalQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.YEARS_AT_THIS_ADRESS.name() + key, this.valueDomainService.getListLastMoveDateRangeQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.OTHER_ANTI_THEFT_DEVICE_INDICATOR.name() + key, this.valueDomainService.getListAntiTheftDeviceQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.BUSINESS_KM.name() + key, this.valueDomainService.getListAnnualKmRangeBusinessQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.MARITAL_STATUS.name() + key, this.valueDomainService.getListMaritalStatusQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.DATE1.name() + key, this.valueDomainService.getListClaimNatureYearQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.DATE2.name() + key, this.valueDomainService.getListClaimNatureYearQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.HOME_AND_AUTO_IND.name() + key, this.valueDomainService.getListCarHomeDiscountQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.LOSSES_YEARS_IND.name() + key, this.valueDomainService.getListClaimNatureQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.MINOR_INFRACTION_COUNT.name() + key, this.valueDomainService.getListMinorInfractionsQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.YN.name() + key, getListContentsYesNo(locale.getValue()));

			this.listValuesBO.put(ContentsMappingEnum.GENDER.name() + key, this.valueDomainService.getListGenderQf(locale.getValue()));
			this.listValuesBO.put(ContentsMappingEnum.MONTHS.name() + key, this.valueDomainService.getListMonthsQf(locale.getValue()));

			if (locale.getKey().startsWith("QC_")) {
				initDataQC(locale, context);

			} else if (locale.getKey().startsWith("ON_")) {
				initDataON(locale);

			} else if (locale.getKey().startsWith("AB_")) {
				initDataAB(locale, context);
			}

			context = null;
		}
	}

	/**
	 * Inits the data qc.
	 *
	 * @param locale the locale
	 */
	private void initDataQC(final Map.Entry<String, Locale> locale, final ComContext context) {
		String key = String.format("%s%s", tri, locale.getKey());
		List<ValidValue> workSectors = null;
		try {
			workSectors = DriverFacade.getInstance(context).getWorkSectorsList(context);
			List<ValidValue> groupList = DriverFacade.getInstance(context).getGroupList(context);
			this.listValues.put(ContentsMappingEnum.GROUPE_CODE.name() + key, groupList);
		} catch (AutoQuoteException e) {
			logger.warning(Logger.EVENT_FAILURE, "Unable to get list of work sectors: " + e.getMessage());
		}

		this.listValuesBO.put(String.format("%s%s", ContentsMappingEnum.TRACKING_SYSTEM.name(), key), this.valueDomainService.getListTrackingSystemBrandQf(locale.getValue()));
		this.listValuesBO.put(String.format("%s%s", ContentsMappingEnum.TYPE1.name(), key), this.valueDomainService.getListOfLossQf(locale.getValue()));
		this.listValuesBO.put(String.format("%s%s", ContentsMappingEnum.TYPE2.name(), key), this.valueDomainService.getListOfLossQf(locale.getValue()));

		List<ValidValueBO> listWorkSector = convertFromValidValueListToValidValueBOList(workSectors);
		this.listValuesBO.put(String.format("%s%s", ContentsMappingEnum.WORK_SECTOR.name(), key), listWorkSector);

		for (ValidValueBO next : listWorkSector) {
			try {
				List<ValidValue> occupations = DriverFacade.getInstance(context).getOccupationsList(context, next.getValue());
				List<ValidValueBO> listOccupations = convertFromValidValueListToValidValueBOList(occupations);
				this.listValuesBO.put(String.format("%s%s%s%s", ContentsMappingEnum.OCCUPATION.name(), tri, next.getValue(), key), listOccupations);
			} catch (AutoquoteFacadeException e) {
				logger.warning(Logger.EVENT_FAILURE, "Unable to get list of occupations: " + e.getMessage());
			}
		}
	}

	/**
	 * Inits the data qc.
	 *
	 * @param locale the locale
	 */
	private void initDataON(final Map.Entry<String, Locale> locale) {
		String key = tri + locale.getKey();
		this.listValuesBO.put(ContentsMappingEnum.TYPE1.name() + key, this.valueDomainService.getListOfLossQf(locale.getValue()));
		this.listValuesBO.put(ContentsMappingEnum.TYPE2.name() + key, this.valueDomainService.getListOfLossQf(locale.getValue()));
		this.listValuesBO.put(ContentsMappingEnum.MINOR_INFRACTION_YEARS.name() + key, this.valueDomainService.getListMinorInfractionsYearQf(locale.getValue()));

		List<ValidValueBO> insuranceCarriersList = this.valueDomainService.getListMostRecentInsurerQf(locale.getValue());
		this.listValuesBO.put(ContentsMappingEnum.INSURANCE_CARRIERS.name() + this.tri + locale.getKey(), insuranceCarriersList);
	}

	/**
	 * Inits the data qc.
	 *
	 * @param locale the locale
	 */
	private void initDataAB(final Map.Entry<String, Locale> locale, final ComContext context) {
		String key = tri + locale.getKey();
		this.listValuesBO.put(ContentsMappingEnum.TYPE1.name() + key, this.valueDomainService.getListOfLossQf(locale.getValue()));
		this.listValuesBO.put(ContentsMappingEnum.TYPE2.name() + key, this.valueDomainService.getListOfLossQf(locale.getValue()));
		this.listValuesBO.put(ContentsMappingEnum.LAST_CONVICTION_YEARS.name() + key, this.valueDomainService.getListLastConvictionDateRangeQf(locale.getValue()));

		List<ValidValueBO> insuranceCarriersList = this.valueDomainService.getListMostRecentInsurerQf(locale.getValue());
		this.listValuesBO.put(ContentsMappingEnum.INSURANCE_CARRIERS.name() + this.tri + locale.getKey(), insuranceCarriersList);


		final String workSectorCode = "9999";

		List<ValidValue> occupationList;
		try {
			occupationList = DriverFacade.getInstance(context).getOccupationsList(context, workSectorCode);
			CollectionUtils.filter(occupationList, new Predicate() {
				@Override
				public boolean evaluate(Object object) {
					if (object instanceof ValidValue) {
						ValidValue validValue = (ValidValue) object;
						return OCCUPATIONS_WHITE_LIST.contains(validValue.getValue());
					}

					return false;
				}
			});
			this.listValuesBO.put(ContentsMappingEnum.OCCUPATION.name() + key, convertFromValidValueListToValidValueBOList(occupationList));
		} catch (AutoquoteFacadeException e) {
			logger.warning(Logger.EVENT_FAILURE, "Unable to init data properly for AB", e);
		}
	}

	/**
	 * Gets the list by province and locale bo.
	 *
	 * @param listId   the list id
	 * @param province the province
	 * @param langage  the langage
	 * @return the list by province and locale bo
	 */
	public List<ValidValueBO> getListByProvinceAndLocaleBO(final String listId, final String province, final String langage) {
		int year = Calendar.getInstance().get(Calendar.YEAR);

		if (initDataLoadedDate != year || this.locales.size() == 0) {
			synchronized (this) {
				if (initDataLoadedDate != year || this.locales.size() == 0) {
					initData();
					initDataLoadedDate = year;
				}
			}
		}
		StringBuilder key = new StringBuilder().append(listId).append(tri).append(province.toUpperCase()).append(tri).append(langage.toUpperCase());
		return this.listValuesBO.get(key.toString());
	}

	/**
	 * Returns the list of possible values for a vehicle year, in a given context.
	 *
	 * @return a list of {@link ValidValueBO} instances where both the key and the value attributes represent a year.
	 */
	private List<ValidValueBO> getVehicleYearRange(ComContext context) {
		List<ValidValueBO> yearList = new ArrayList<>();

		int difference = 0;
		switch (context.getProvince()) {
			case QUEBEC:
				difference = VEHICLE_CUTOFF_YEARS_QC;
				break;
			case ONTARIO:
				difference = VEHICLE_CUTOFF_YEARS_ON;
				break;
			case ALBERTA:
				difference = VEHICLE_CUTOFF_YEARS_AB;
				break;
			default:
				throw new IllegalArgumentException("An unsupported context was provided.");
		}

		Calendar now = Calendar.getInstance();
		int cutoff = now.get(Calendar.YEAR) - difference;

		now.add(Calendar.YEAR, 1);

		for (int i = now.get(Calendar.YEAR); i >= cutoff; i--) {
			ValidValueBO value = new ValidValueBO(String.valueOf(i), String.valueOf(i), i);
			yearList.add(value);
		}

		return yearList;
	}

	/**
	 * Gets the list contents yes no.
	 *
	 * @param locale the locale
	 * @return the list contents yes no
	 */
	private List<ValidValueBO> getListContentsYesNo(Locale locale) {
		String no = this.messageSource.getMessage("button.no", null, "?", locale);
		String yes = this.messageSource.getMessage("button.yes", null, "?", locale);
		List<ValidValueBO> listValidValueBO = new ArrayList<ValidValueBO>();
		listValidValueBO.add(new ValidValueBO(AutoQuoteConstants.STR_FALSE, no, 0));
		listValidValueBO.add(new ValidValueBO(AutoQuoteConstants.STR_TRUE, yes, 1));
		return listValidValueBO;
	}

	public Locale getLocaleFor(String province, String language) {
		if (this.locales.size() == 0) {
			synchronized (this) {
				if (this.locales.size() == 0) {
					initData();
				}
			}
		}
		return locales.get(province + "_" + language);
	}

	/**
	 * Get list of work sectors
	 *
	 * @param province the province
	 * @return list of work sectors
	 */
	public List<ValidValueBO> getWorkSectors(final String province, final String langage) {
		String key = tri + province + tri + langage;
		return listValuesBO.get(ContentsMappingEnum.WORK_SECTOR.name() + key);
	}

	/**
	 * Get list of occupations for a given work sector
	 *
	 * @param province the province
	 * @param langage the language
	 * @param workSector thwe work sector
	 * @return list of occupations
	 */
	public List<ValidValueBO> getOccupations(final String province, final String langage, final String workSector) {
		String key = tri + province + tri + langage;
		return listValuesBO.get(ContentsMappingEnum.OCCUPATION.name() + tri + workSector + key);
	}

	public byte[] getBrokerLogo(String brokerId) {
		return brokerLogos.get(brokerId);
	}

	public void addBrokerLogo(String brokerId, byte[] logo) {
		brokerLogos.put(brokerId, logo);
	}

	private List<ValidValueBO> convertFromValidValueListToValidValueBOList(List<ValidValue> validValueList) {
		List<ValidValueBO> aList = new ArrayList<>();
		for (ValidValue validValue : validValueList) {
			ValidValueBO validValueBO = new ValidValueBO(validValue.getValue(), validValue.getLabel(), validValue.getIndex());
			aList.add(validValueBO);
		}
		return aList;
	}
}
