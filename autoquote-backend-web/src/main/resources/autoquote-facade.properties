
# Configuration for barcodeService in /autoquote-backend-project/autoquote-facade/src/main/resources/autoquote-facade-beans.xml
# Select and configure one of the implementations. All parameters are required even if values are empty.

# Age in days.
GapiService.ageOfValidReport=30
GapiService.requestor=AQQK
GapiService.trackingNumberPrefix=AQQK-
GapiService.underwritingCompany=A

# Filter out any claim older than this age in years.
MediatorDomCom.claimMaxAgeProv1=ON
MediatorDomCom.claimMaxAgeProv2=AB
MediatorDomCom.claimMaxAgeVal1=10
MediatorDomCom.claimMaxAgeVal2=10
#Filter out any vehicle that comes from a contract older than this age in days.
MediatorDomCom.policyMaxDays=365
#Filter out any vehicle older than this age in years.
MediatorDomCom.vehicleMaxAge=19
#Maximum number of claims in COM.
MediatorDomCom.maxClaimCountProv1=ON
MediatorDomCom.maxClaimCountProv2=AB
MediatorDomCom.maxClaimCountVal1=3
MediatorDomCom.maxClaimCountVal2=3
#Annual Km ranges. Pipe-separated list of "rangeCode:rangeMax" pairs, per province.
#Should match /.../resources/com/intact/autoquote/intact/resources/list_km_annually_qf_fr_CA_*.properties
MediatorDomCom.annualKm=ON||001000:1000|002000:2000|003000:3000|004000:4000|005000:5000|006000:6000|007000:7000|009000:9000|011000:11000|014000:14000|016000:16000|019000:19000|020000:20000|024000:24000|025000:25000|026000:26000|027000:27000|030000:30000|034000:34000|035000:35000|036000:36000|037000:37000|038000:38000|039000:39000|040000:40000|050000:50000|050001:999999999|||QC||001000:1000|002000:2000|003000:3000|004000:4000|005000:5000|006000:6000|007000:7000|008000:8000|009000:9000|010000:10000|011000:11000|012000:12000|013000:13000|014000:14000|015000:15000|016000:16000|017000:17000|018000:18000|019000:19000|020000:20000|025000:25000|030000:30000|040000:40000|040001:999999999|||AB||001999:1999|003999:3999|005999:5999|008999:8999|010999:10999|013999:13999|015999:15999|018999:18999|020999:20999|025999:25999|029999:29999|049999:49999|050001:999999999
#Business Km ranges. Pipe-separated list of "rangeCode:rangeMax" pairs, per province.
#Should match /.../resources/com/intact/autoquote/intact/resources/list_annual_km_range_business_qf_en_CA_*.properties
MediatorDomCom.businessKm=ON||000000:0|000001:2999|003000:4999|005000:6999|007000:9999|010000:11999|012000:14999|015000:999999999|||QC||000000:0|000001:3200|003201:5000|005001:999999999|||AB||000000:0|000001:3200|003201:5000|005001:999999999
#Term duration ranges. Pipe-separated list of "rangeCode:rangeMax" pairs, per province.
#Should match /.../resources/com/intact/autoquote/intact/resources/list_years_continuously_insured_qf_en_CA_*.properties
MediatorDomCom.termDuration=ON||0:0|1:3|3:6|6:8|8:10|10:999999999|||QC||0:0|1:1|2:2|3:3|4:4|5:5|6:999999999|||AB||0:0|1:1|2:2|3:3|4:4|5:5|6:999999999
#Claim age ranges. Pipe-separated list of "rangeCode:rangeMax" pairs, per province.
#Should match /.../resources/com/intact/autoquote/intact/resources/list_claim_nature_year_qf_en_CA_*.properties
MediatorDomCom.claimAge=ON||00:0|01:1|02:2|03:3|04:4|05:5|06:999999999|||QC||00:0|01:1|02:2|03:3|04:4|05:5|null:999999999|||AB||00:0|01:1|02:2|03:3|04:4|05:5|06:6|07:999999999

#Configuration for barcode image and data logger
barcodeLogger.imageDirectory=/logs/license
barcodeLogger.maxThreads=5

