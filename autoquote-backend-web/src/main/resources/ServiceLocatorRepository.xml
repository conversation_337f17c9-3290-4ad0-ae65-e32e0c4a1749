<?xml version="1.0" encoding="iso-8859-1"?>
<ServiceLocatorData>

	<ServiceRequestHandlerList>
		<!-- Pega 7.3 DEV-APP REST-->
		<ServiceRequestHandler name="com.ing.canada.ss.stub.rest.pega.ServiceRequestHandlerRESTPegaRemoteBuilder" protocol="https" hostName="uat-pega.iad.ca.inet" port="443" connectTimeout="600000" contentType="application/xml"/>
	</ServiceRequestHandlerList>

	<!-- IL services -->
	<ServiceRequestHandlerList>
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILBusinessRulesServices" /> 
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILPartyServices"/>
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILPlaceServices"/>
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILProductServices"/>
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILUnderwritingServices"/>
		<ServiceRequestHandler name="com.ing.canada.ss.stub.java.ServiceRequestHandlerJavaBuilder" className="com.ing.canada.ss.stub.il.ILRatingExtServices"/>
	</ServiceRequestHandlerList>

</ServiceLocatorData>
