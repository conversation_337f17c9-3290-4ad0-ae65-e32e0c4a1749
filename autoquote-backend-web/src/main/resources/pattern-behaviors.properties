#----------------------------------------------------------------------
# file:   pattern-behaviors.properties
# author: <PERSON>
#----------------------------------------------------------------------
# This file maps the application behaviors according to the market 
# segments determined by a service Pega.
# 
# IMPORTANT: The properties of this file are ORDERED from the highest
#            to the lowest priority.  The conflicting behaviors (more 
#            than one segment applying for the same behavior) will be
#            be override with first property (highest in file).
#
# General syntax:
#   PROVINCE.segmentType.segmentName.behavior=value
#
# Default value syntax ("_default" is a reserved segment name and type):
#    PROVINCE._default.behavior=default value
#
# NOTE : This file is dynamically reloaded.
#----------------------------------------------------------------------

#==============================================================
# Quï¿½bec
#==============================================================

#-------------------------------------------
# Business Logic Attacks (AutoQuote)
#
# Valid restrictionType values are: RDBK1 (Roadblock #1), RDBK2 (Roadblock #2), CAPT (Captcha)
#-------------------------------------------
QC.webattack.level_1.IpRestrictionType=RDBK1
QC.webattack.level_1.IpRestrictionDurationMinutes=180
QC.webattack.level_1.IpRestrictionSeverity=1
QC.webattack.level_1.SubBrokerNoToTransferTo=0590

QC.webattack.level_2.IpRestrictionType=CAPT
QC.webattack.level_2.IpRestrictionDurationMinutes=1
QC.webattack.level_2.IpRestrictionSeverity=2
QC.webattack.level_2.SubBrokerNoToTransferTo=0590

QC.webattack.level_3.IpRestrictionType=RDBK2
QC.webattack.level_3.IpRestrictionDurationMinutes=43200
QC.webattack.level_3.IpRestrictionSeverity=3
QC.webattack.level_3.SubBrokerNoToTransferTo=0590

#-------------------------------------------
# Business Logic Attacks (QuickQuote)
#
#    Valid restrictionType values are: RDBK1 (Roadblock #1), RDBK2 (Roadblock #2)
#-------------------------------------------
QC.webattack_qquote.level_1.IpRestrictionType=RDBK1
QC.webattack_qquote.level_1.IpRestrictionDurationMinutes=180
QC.webattack_qquote.level_1.IpRestrictionSeverity=1
QC.webattack_qquote.level_1.SubBrokerNoToTransferTo=0590

QC.webattack_qquote.level_2.IpRestrictionType=RDBK2
QC.webattack_qquote.level_2.IpRestrictionDurationMinutes=43200
QC.webattack_qquote.level_2.IpRestrictionSeverity=2
QC.webattack_qquote.level_2.SubBrokerNoToTransferTo=0590


#==============================================================
# Ontario
#==============================================================

#-------------------------------------------
# Business Logic Attacks (QuickQuote)
#
#    Valid restrictionType values are: RDBK1 (Roadblock #1), RDBK2 (Roadblock #2)
#-------------------------------------------
ON.webattack_qquote.level_1.IpRestrictionType=RDBK1
ON.webattack_qquote.level_1.IpRestrictionDurationMinutes=180
ON.webattack_qquote.level_1.IpRestrictionSeverity=1

ON.webattack_qquote.level_2.IpRestrictionType=RDBK2
ON.webattack_qquote.level_2.IpRestrictionDurationMinutes=43200
ON.webattack_qquote.level_2.IpRestrictionSeverity=2


#==============================================================
# Alberta
#==============================================================

