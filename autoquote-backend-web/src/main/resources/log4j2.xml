<?xml version="1.0" encoding="UTF-8" ?>
<Configuration packages="com.intact.autoquote.logging" status="warn">

  <!--============================================================================ -->
  <!--== APPENDERS =============================================================== -->
  <!--============================================================================ -->
  <Appenders>
    <Console name="RatingPerformanceLogger" target="SYSTEM_OUT" follow="true">
      <PatternLayout
        pattern="%d{dd MM yyyy HH:mm:ss} %m ;prov=[%.2X{province}] sn=[%.35X{sn}] tn=[%.12X{tn}] %n"/>
    </Console>

    <!-- CONSOLE APPENDER -->
    <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
      <PatternLayout
        pattern="%d{dd MM yyyy HH:mm:ss} %p %x %l: %m ;prov=[%.2X{province}] sn=[%.35X{sn}] tn=[%.12X{tn}] %n"/>
    </Console>

    <MongoAppender name="mongo"
      hosts="${mongo.logbook.hosts}"
      databaseName="${mongo.logbook.databaseName}"
      collectionName="logs"
      userName="${mongo.logbook.userName}"
      password="${mongo.logbook.password}"
      authenticationDatabase="${mongo.logbook.authenticationDatabase}"
      replicaset="${mongo.logbook.replicaset}">
      <PatternLayout pattern="%m%n"/>
    </MongoAppender>

    <Console name="UPDATECOVERAGES" target="SYSTEM_OUT" follow="true">
      <PatternLayout
        pattern="%d %-5p %c{1} - %m ;prov=[%.2X{province}] sn=[%.35X{sn}] tn=[%.12X{tn}] %n"/>
    </Console>

    <Console name="statsAppender" target="SYSTEM_OUT" follow="true">
      <PatternLayout
        pattern="%d %-5p %c{1} - %m ;prov=[%.2X{province}] sn=[%.35X{sn}] tn=[%.12X{tn}] %n"/>
    </Console>

  </Appenders>

  <!--=============================================================== -->
  <!--== AUTOQUOTE LOGGERS ========================================== -->
  <!--=============================================================== -->
  <Loggers>
    <Logger name="com.ing.canada" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.common" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.cif" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.plp" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.ss" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.common.web.filter.InsurancePolicyOptimisticLockFilter"
      additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>

    <Logger name="com.intact" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.ws.endpoint" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.webservice" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.aop" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.common.datamediator" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.aop" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.business.usage.impl" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.rating.impl" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.rating.RatingPerformanceLogger" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.controller.updatecoverage" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.ing.canada.common.util.localizedcontext" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.intact.autoquote.belair.mongo.MongoLogger" additivity="false" level="INFO">
      <AppenderRef ref="mongo"/>
    </Logger>

    <!-- Performance logs BEGIN -->
    <Logger name="com.intact.autoquote.services.impl.RateManagerService" additivity="false" level="DEBUG"><AppenderRef ref="RatingPerformanceLogger"/></Logger>

    <!-- Rating simplification -->
    <Logger name="RatingOffersDescription" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>
    <!-- Service calls -->
    <Logger name="ServicesCallLogger" additivity="false" level="DEBUG"><AppenderRef ref="ConsoleAppender"/></Logger>

    <!--=============================================================== -->
    <!--== COMMON LOGGERS ============================================= -->
    <!--=============================================================== -->
    <Logger name="com.ibm.ws.webservices" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.springframework" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.apache.struts" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.apache.velocity.runtime.log" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.apache.commons" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.SQL" additivity="false" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="net.sf" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <!-- NOTE: The class org.apache.commons.httpclient.Wire uses this non standard named logger -->
    <Logger name="httpclient.wire" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.apache.http" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="net.jawr.web.servlet" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="net.jawr.web.resource" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="com.mongodb" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.apache.cxf" level="INFO"><AppenderRef ref="ConsoleAppender"/></Logger>

    <Logger name="bitronix.tm" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.springframework.transaction" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.transaction" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.resource.jdbc" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.internal.SessionImpl" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.internal.SessionFactoryImpl" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>
    <Logger name="org.hibernate.engine.transaction.internal.jta.JtaTransaction" level="WARN"><AppenderRef ref="ConsoleAppender"/></Logger>

    <Root level="DEBUG">
      <AppenderRef ref="ConsoleAppender"/>
    </Root>
  </Loggers>

</Configuration>
