# Key greater than 128 bits requires the "unlimited jurisdiction policy files": local_policy.jar and US_export_policy.jar. 
# They are available from Wiki or "http://www-128.ibm.com/developerworks/java/jdk/security/50".
# 128 bit encryption key. 32 hexadecimal characters.
IngAesCipher.key=A61B6401E2F362C97B4D7A37AE6301BC

# Application id passed to IL message in the ClientHostName parameter.
#
# Possible value
#  AUTOQUOTE = BWAQ
#  PORTFOLIO = BWPF
#  POLICY_CHANGE = BWPC
#  TESTS = BWTS
application-id=BWAQ

# the maximum time used for a request to IL
il-timeout=30000

# Application id passed to PEGA in the delegate parameters.
# These are used by PEGA to match the request to the ruleset. Ex.: ECOM-D-QC-R.
pega-application-id=EBTP

# Environnement level IL message in the field SYSTEM_ORIGIN.ENVIRONMENT_LEVEL.
# put in properties "DE1' for dev 'TE1' for test 'PR1' for PROD"
environnement-level=${environnement-level}

# Definition of the smtp server to send mail
mail-smtp-host=${mail.smtp.host}

# URL of the WebService used to create the workitem.
#workitem-webservice-endpoint=https://uat-workitems-a.iad.ca.inet:465/INGWorkitemWebService/services/WorkitemWebService
# temporary, will have to revert to the line above.
workitem-webservice-endpoint=

# Username we need to call the workitem webservice.
workitem-webservice-username=
# Password we need to call the workitem webservice.
workitem-webservice-password=

# Set the implementation of the data mediator for each use case
#dataMediatorToSOMGetAutoPolicy=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOM
#dataMediatorToSOMUsage=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOM
#dataMediatorToSOMBuildOffer=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOM
dataMediatorToSOM=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOM
dataMediatorToSOMGetAutoPolicy=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOMGetAutoPolicy
dataMediatorToSOMUsage=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOMUsage
dataMediatorToSOMBuildOffer=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOMBuildOffer
dataMediatorToSOMUpload=com.intact.common.datamediator.pl2som.impl.DataMediatorToSOMUpload


#Utilise par Portfolio seulement. laisser vide
application-image-url=
application-email-from-name=
application-email-from-address=<EMAIL>
application-email-from-name-french=
portfolio-register-url=
portfolio-login-url=
retrieve-quote-url=
get-quote-url=
#home-page-url must link to the WEP, the WEP is in charge of returning the client to the homepage for Autoquote
#in dev or test env, if the link is null, then it will be build automaticaly in the java code for AQ only
home-page-url=${home.page.url}
mobile-home-page-url=

autoquote-agent-role=autoquote-agents-qc-intact

# [HelpTextUrl tag] 
# See com.ing.canada.common.web.tags.HelpTextUrl.HelpTextUrl for more detail 
helptext.documentRoot=${helptext.document.root.url}
helptext.extension=.html


# [ErrorForProperty tag]
# See com.ing.canada.common.web.tags.HelpTextUrl.ErrorForProperty for more detail 
# Id pattern for the block element displaying the error related to a field 
# {0}=is the related property id (field with the error)
errorForProperty.id.pattern=errormsg_{0}

tamwebseal.silent.login.switch=false
tamwebseal.silent.login.url=
tamwebseal.authentication.cookie.name=

# Webmethods endpoint prefix
webmethods-endpoint=${webmethods.endpoint.prefix}

# Config for Deny of Service
dos.rate.counter.lifespan.in.minutes = 60
dos.max.simultaneous.ratings = 5
dos.code.exceeding.limit.simultaneous.ratings = "QQ_MCASI"
