#
# Autoquote Notifications settings.
#

mail.smtp.host= ${mail.smtp.host}
mail.smtp.username= ${mail.smtp.username}
mail.smtp.password= ${mail.smtp.password}
mail.smtp.auth= ${mail.smtp.auth}
mail.smtp.port= ${mail.smtp.port}
mail.transport.protocol= ${mail.transport.protocol}
mail.smtp.starttls.enable= ${mail.smtp.starttls.enable}
mail.smtp.ssl.enable= ${mail.smtp.ssl.enable}
mail.debug= ${mail.debug}

mail.cc.address=autoquote.${notif.env}@intact.net
#Regex pattern that a email address must match to send a notification. Must be uppercase. Keep empty to match all
mail.pattern=

public.images.uri= ${public.images.uri}
public.webzone.access.uri= ${public.webzone.access.uri}

# for retrieve quote
base.url.*.*.*= ${base.url}

# Debug data
debug.mode=false
force.email=

# Distribution Channel (B=Broker, D=Direct)
distribution.channel=B


# xpham RANB-12 : email new images are temporary added in AutoQuote, waiting for static website managed by MMCM to be under our control   
public.autoquote.images.uri= ${public.autoquote.images.uri}
mail.from.address.*.*.*=<EMAIL>
mail.from.address.QC.*.*=autoquote.quebec${notif.env}@intact.net
mail.from.address.ON.*.*=autoquote.ontario${notif.env}@intact.net
mail.from.address.AB.*.*=autoquote.alberta${notif.env}@intact.net
mail.from.address.BC.*.*=<EMAIL>
mail.cc.address.QC=autoquote.quebec${notif.env}@intact.net
mail.cc.address.ON=autoquote.ontario${notif.env}@intact.net
mail.cc.address.AB=autoquote.alberta${notif.env}@intact.net
mail.cc.address.BC=
mail.to.intact.address.BC=

#RANB-172 for incomplete quote email, if not blank, will override the send to so 
#brokers don't receive notifications.
incomplete.quote.override.broker.address= ${notif.incomplete.quote.override}
